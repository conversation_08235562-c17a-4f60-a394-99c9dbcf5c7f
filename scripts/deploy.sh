#!/bin/bash

set -ex


ENV=$1

if [[ "$ENV" != "uat" && "$ENV" != "prod" ]]; then
    echo "Error: Invalid environment specified. Please use 'uat' or 'prod'."
    exit 1
fi

# Export the path to the gcloud SDK
export PATH=$PATH:/var/lib/jenkins/google-cloud-sdk/bin

# GCR Registry URI

APP_NAME="oa-website-$ENV"

TAG=$(git rev-parse --abbrev-ref HEAD)-$(git describe --always)
CURRENT_TS=$(date +%s)


if [ "$ENV" == "uat" ]; then
    REPLICA_COUNT=1
    GCR_URI="asia-south1-docker.pkg.dev/oneassure-non-prod/oneassure-non-prod"
    HOSTNAME=site.stage.oneassure.in
    # DOCKER_FILE="Dockerfile_stage"
    DOCKER_FILE="Dockerfile"
    cp .env.staging .env.local

elif [ "$ENV" == "prod" ]; then
    GCR_URI="asia-south1-docker.pkg.dev/oneassure-prod/oneassure"
    REPLICA_COUNT=2
    HOSTNAME=oneassure.in
    DOCKER_FILE="Dockerfile"
    cp .env.production .env.local

fi


docker build -f $DOCKER_FILE --target deps -t $APP_NAME-deps:$TAG-$CURRENT_TS . || exit 1
docker build -f $DOCKER_FILE --target builder -t $APP_NAME-builder:$TAG-$CURRENT_TS . || exit 1
docker build -f $DOCKER_FILE -t $GCR_URI/$APP_NAME:$TAG-$CURRENT_TS --build-arg CACHEBUST=$CURRENT_TS . || exit 1
docker push $GCR_URI/$APP_NAME:$TAG-$CURRENT_TS || exit 1

# kubectl create secret generic oa-app-secret-$env -n oa-$env --dry-run=client --from-file=.env.local=.env.local -o yaml | kubectl apply -f -

# (sed "s/{{env_name}}/$ENV/g; 
#      s/{{image_id}}/$TAG/g;
#      s/{{mapping_hostname}}/$HOSTNAME/g;
#      s/{{replica_count}}/$REPLICA_COUNT/g;" k8s/app.yaml | kubectl apply -f -) || exit 1

# kubectl rollout status deployment/oa-website-deployment-$ENV -n oa-$ENV || exit 1

set +ex
