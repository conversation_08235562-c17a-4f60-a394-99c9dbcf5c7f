/** @type {import('next').NextConfig} */

const { redirectionRouters } = require("./utils/redirectionRoutes");

const nextConfig = {
  images: {
    unoptimized: process.env.OPTIMISATION === "true" ? false : true,
    dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "cdn.oasr.in",
        port: "",
        pathname: "/oa-site/cms-uploads/**",
      },
      {
        protocol: "https",
        hostname: "cdn.oasr.in",
        port: "",
        pathname: "/stream/oa-site/cms-uploads/**",
      },
      {
        protocol: "https",
        hostname: "www.tataaig.com",
      },
      {
        protocol: "https",
        hostname: "oneassure-static-files.s3.ap-south-1.amazonaws.com",
      },
      {
        protocol: "https",
        hostname: "cdn.non-prod.oasr.in",
        port: "",
        pathname: "/oa-site/cms-uploads/**",
      },
      {
        protocol: "https",
        hostname: "cdn.non-prod.oasr.in",
        port: "",
        pathname: "/stream/oa-site/cms-uploads/**",
      },
      {
        protocol: "https",
        hostname: "d1wamg5uas0pit.cloudfront.net",
        // port: "",
        // pathname: "**/oa-site/cms-uploads/**",
      },
      {
        protocol: "https",
        hostname: "cdn.oneassure.in",
        // port: "",
        // pathname: "/oa-site/cms-uploads/**",
      },
      {
        protocol: "https",
        hostname: "admin.oneassure.in",
        // port: "",
        // pathname: "/oa-site/cms-uploads/**",
      },
      {
        protocol: "https",
        hostname: "avatars.slack-edge.com",
        // port: "",
        // pathname: "/oa-site/cms-uploads/**",
      },
      {
        protocol: "https",
        hostname: "secure.gravatar.co",
        // port: "",
        // pathname: "/oa-site/cms-uploads/**",
      },
      {
        protocol: "https",
        hostname: "secure.gravatar.com",
        // port: "",
        // pathname: "/oa-site/cms-uploads/**",
      },
    ],
    domains: ['images.unsplash.com'],
  },
  reactStrictMode: true,
  distDir: "build",
  async redirects() {
    // Define your existing routes
    // return [
    //   {
    //     source: "/dental-insurance",
    //     destination: "/",
    //     permanent: false,
    //   },
    //   {
    //     source: "/abhi",
    //     destination: "/health-insurance",
    //     permanent: false,
    //   },
    //   {
    //     source: "/policy-wallet",
    //     destination: "/",
    //     permanent: false,
    //   },
    //   {
    //     source: "/super-topup",
    //     destination: "/",
    //     permanent: false,
    //   },
    //   {
    //     source: "/diabetics-health-insurance",
    //     destination: "/health-insurance",
    //     permanent: false,
    //   },
    //   {
    //     source: "/maternity-insurance",
    //     destination: "/",
    //     permanent: false,
    //   },
    //   {
    //     source: "/health-insurance-claims",
    //     destination: "/health-insurance",
    //     permanent: false,
    //   },
    //   {
    //     source: "/health-insurance-embedded-insurance",
    //     destination: "/health-insurance",
    //     permanent: false,
    //   },
    //   {
    //     source: "/health-insurance-partners",
    //     destination: "/",
    //     permanent: false,
    //   },
    //   {
    //     source: "/senior-citizens",
    //     destination: "/",
    //     permanent: false,
    //   },
    //   {
    //     source: "/family",
    //     destination: "/",
    //     permanent: false,
    //   },
    //   {
    //     source: "/icici-lombard",
    //     destination: "/health-insurance",
    //     permanent: false,
    //   },
    // ];
    const redirects = Object.entries(redirectionRouters).map(([source, destination]) => ({
      source, // Original URL pattern
      destination, // New URL to redirect to
      permanent: true, // This makes it a 301 permanent redirect
    }));

    // Add wildcard redirects for unmatched URLs
    const wildcardPatterns = [
      '/insurance/health-insurance/:path*',
      '/insurance/posp/:path*',
      '/insurance/page/:path*',
      '/insurance/uncategorized/:path*',
      '/insurance/group-health-insurance/:path*',
      '/insurance/term-insurance/:path*',
      '/insurance/category/:path*',
      '/insurance/retirement-health-insurance/:path*',
      '/insurance/health-insurance-for-parents/:path*'
    ];

    // Add insurance wildcard redirects
    wildcardPatterns.forEach(pattern => {
      redirects.push({
        source: pattern,
        destination: '/insurance',
        permanent: false,
      });
    });

    // Note: Removed catch-all wildcard redirect to allow legitimate routes like /claims, /about to work normally
    
    
    return redirects;
  },
};

module.exports = nextConfig;
