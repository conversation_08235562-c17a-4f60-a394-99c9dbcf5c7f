# Stage 1
FROM node:20.9-alpine3.18 AS deps

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock (if available)
COPY package*.json package-lock.json ./

# Install project dependencies
RUN npm install

# Stage 2
FROM node:20.9-alpine3.18 AS builder

WORKDIR /app

COPY --from=deps /app/node_modules ./node_modules

COPY src ./src
COPY public ./public
COPY utils ./utils
COPY package.json next.config.js tsconfig.json tailwind.config.ts postcss.config.js package-lock.json next-sitemap.config.js ./
COPY .env.local ./

ARG CACHEBUST=1

# Stage 3
FROM node:20.9-alpine3.18

WORKDIR /app

# Copy all necessary files from builder
COPY --from=builder /app/src ./src
COPY --from=builder /app/public ./public
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/utils ./utils
COPY --from=builder /app/tsconfig.json ./
COPY --from=builder /app/tailwind.config.ts ./
COPY --from=builder /app/postcss.config.js ./
COPY --from=builder /app/next-sitemap.config.js ./
COPY --from=builder /app/.env.local ./

# Expose port 3000
EXPOSE 3000

# Run development server
CMD ["npm", "run", "dev"]





