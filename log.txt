
> oneassure-site@0.1.0 build
> next build

  ▲ Next.js 14.2.0
  - Environments: .env.local, .env.production

   Creating an optimized production build ...
 ✓ Compiled successfully
   Linting and checking validity of types ...

./src/components/Company/components/SideMenu.tsx
8:9  Warning: The 'sideMenu' array makes the dependencies of useEffect Hook (at line 53) change on every render. To fix this, wrap the initialization of 'sideMenu' in its own useMemo() Hook.  react-hooks/exhaustive-deps

./src/components/HealthInsurance/components/QuickQuoteForm.tsx
43:6  Warning: React Hook useEffect has a missing dependency: 'callBack'. Either include it or remove the dependency array. If 'callBack' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
133:6  Warning: React Hook useEffect has missing dependencies: 'setFormPart', 'setQuickQuoteRecommendation', and 'setShowRecommendation'. Either include them or remove the dependency array. If 'setFormPart' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/QuickQuote/components/RecommendationCard.tsx
41:7  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
52:11  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
65:9  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/globals/FakeIntersection.tsx
16:6  Warning: React Hook useEffect has a missing dependency: 'navbarStore'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/globals/MobileNavbar.tsx
33:6  Warning: React Hook useEffect has a missing dependency: 'business'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/globals/Navbar.tsx
77:6  Warning: React Hook useEffect has a missing dependency: 'business'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/globals/Navbar_New.tsx
164:6  Warning: React Hook useEffect has a missing dependency: 'business'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/globals/New_Navbar.tsx
164:6  Warning: React Hook useEffect has a missing dependency: 'business'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/globals/Products/WhyOneAssure.tsx
89:6  Warning: React Hook useEffect has a missing dependency: 'goToNext'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/basic-features/eslint#disabling-rules
   Collecting page data ...
   Generating static pages (0/393) ...
ific needs, that an average person would find hard to comprehend. Luckily, I had help from OneAssure's partner, who not only understood my requirements and assisted me in choosing the right one but went a step further, by confirming the compliance of my pre-existing conditions with the policy underwriters. Thank you for the extensive support.",
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  },
  {
    id: 3,
    name: 'Mitali',
    statement: 'When I approached OneAssure, I had a very tough deadline as it was my father’s 60th birthday in a few days. OneAssure stepped in and explained all the features and the possible plans. It was a relief to get unbiased advice from a team that was focused on getting us the right policy. The team was very flexible and assisted me, even when I reached out at odd hours. Would recommend OneAssure wholeheartedly! Way to go team!',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 4,
    name: 'Maria Martin',
    statement: 'A fantastic way to quickly find the best health plan. I recently got one health insurance plan for my mom. Despite all the information available online, it could be confusing which plan to buy. OneAssure partner solved it for me.',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  },
  {
    id: 5,
    name: 'Varun Mishra',
    statement: 'On January 18th, I was admitted to the hospital, anticipating a lengthy wait for insurance approval based on past experiences. However, thanks to OneAssure, the approval came through surprisingly quickly, within just 45 minutes. Abhishek D from the OneAssure team provided invaluable assistance in navigating the intricacies of insurance policies and expediting the approval process. I am immensely grateful to the team for their exceptional support during this time.',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 6,
    name: 'Akhilesh Kataria',
    statement: 'Chethana, I want to express my gratitude for your outstanding support and assistance throughout the claims process. Your dedication and professionalism truly made a difference. Thank you. Ruchir, I extend my best wishes for your continued success and prosperity.',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  },
  {
    id: 7,
    name: 'Neeraj Kumar',
    statement: 'I recently received invaluable assistance from Chethana and Abhishek at OneAssure during my claim settlement process with ICICI Lombard Mediclaim. Their consistent communication and proactive approach ensured that all issues were addressed promptly at each stage, leading to a timely settlement of my claim. Both Chethana and Abhishek exhibited professionalism and thoroughness in their interactions, keeping me informed of the progress regularly. I am sincerely grateful to them for their exceptional support.',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 8,
    name: 'Lingeshwar S',
    statement: 'Abhishek and his team were exceptional in ensuring a smooth claim process. They were transparent in addressing my concerns and provided unwavering support throughout. A big thanks to the team—I highly recommend their services!',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  }
]
[
  {
    id: 12,
    name: 'Mehler Engineered Products - Deepak Shetty',
    statement: 'I am extremely pleased with the seamless service provided by OneAssure. Despite not having made any claims yet, I am confident in the reliability and efficiency of their processes. Their attention to detail and commitment to customer satisfaction have made my experience with them exceptionally positive',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 13,
    name: 'Cyber NX technologies- Sayali P',
    statement: 'The team at OneAssure demonstrates exceptional coordination and responsiveness, ensuring that all my queries and concerns are addressed promptly and effectively. Their professionalism and dedication to providing top-notch service have truly impressed me, making my overall experience with them highly satisfactory.',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  },
  {
    id: 14,
    name: 'Cogno AI Alliance Research - Ashutosh Garg',
    statement: "I am thoroughly impressed by OneAssure's exemplary support and immediate responsiveness. Their team goes above and beyond to assist with any issues or questions I may have, ensuring that I feel supported and valued as a customer. Their commitment to delivering exceptional service sets them apart, making my interaction with them truly gratifying.",
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  }
]
[
  {
    id: 9,
    name: 'Mehler Engineered Products - Deepak Shetty',
    statement: 'I am extremely pleased with the seamless service provided by OneAssure. Despite not having made any claims yet, I am confident in the reliability and efficiency of their processes. Their attention to detail and commitment to customer satisfaction have made my experience with them exceptionally positive',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 11,
    name: 'Cyber NX technologies- Sayali P',
    statement: 'The team at OneAssure demonstrates exceptional coordination and responsiveness, ensuring that all my queries and concerns are addressed promptly and effectively. Their professionalism and dedication to providing top-notch service have truly impressed me, making my overall experience with them highly satisfactory.',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  },
  {
    id: 10,
    name: 'Cogno AI Alliance Research - Ashutosh Garg',
    statement: "I am thoroughly impressed by OneAssure's exemplary support and immediate responsiveness. Their team goes above and beyond to assist with any issues or questions I may have, ensuring that I feel supported and valued as a customer. Their commitment to delivering exceptional service sets them apart, making my interaction with them truly gratifying.",
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  }
]
[
  {
    id: 38,
    name: 'Gaurav Agarwal',
    statement: 'The importance of health insurance plans is wasted on millennials. OneAssure guided me through the benefits of insuring my health above all and how I could benefit from the same in so many ways. Sorting out the best insurance plan that fits my personal needs was just what I needed. Thank you OneAssure and I would definitely recommend you guys to friends and family!\n' +
      '\n',
    backgroundColor: 'blue',
    thumbnail: { data: [Object] }
  },
  {
    id: 42,
    name: 'Gokul Subramanya',
    statement: "I was negligent towards the necessity of health insurance until I came across OneAssure. There are a lot of policies available today, each catering to specific needs, that an average person would find hard to comprehend. Luckily, I had help from OneAssure's partner, who not only understood my requirements and assisted me in choosing the right one but went a step further, by confirming the compliance of my pre-existing conditions with the policy underwriters. Thank you for the extensive support.\n" +
      '\n',
    backgroundColor: 'blue',
    thumbnail: { data: [Object] }
  },
  {
    id: 39,
    name: 'Mitali',
    statement: 'When I approached OneAssure, I had a very tough deadline as it was my father’s 60th birthday in a few days. OneAssure stepped in and explained all the features and the possible plans. It was a relief to get unbiased advice from a team that was focused on getting us the right policy. The team was very flexible and assisted me, even when I reached out at odd hours. Would recommend OneAssure wholeheartedly! Way to go team!\n' +
      '\n',
    backgroundColor: 'blue',
    thumbnail: { data: [Object] }
  },
  {
    id: 41,
    name: 'Maria Martin',
    statement: 'A fantastic way to quickly find the best health plan. I recently got one health insurance plan for my mom. Despite all the information available online, it could be confusing which plan to buy. OneAssure partner solved it for me.\n' +
      '\n',
    backgroundColor: 'blue',
    thumbnail: { data: [Object] }
  },
  {
    id: 40,
    name: 'Varun Mishra',
    statement: 'On January 18th, I was admitted to the hospital, anticipating a lengthy wait for insurance approval based on past experiences. However, thanks to OneAssure, the approval came through surprisingly quickly, within just 45 minutes. Abhishek D from the OneAssure team provided invaluable assistance in navigating the intricacies of insurance policies and expediting the approval process. I am immensely grateful to the team for their exceptional support during this time.\n' +
      '\n',
    backgroundColor: 'blue',
    thumbnail: { data: [Object] }
  },
  {
    id: 43,
    name: 'Akhilesh Kataria',
    statement: 'Chethana, I want to express my gratitude for your outstanding support and assistance throughout the claims process. Your dedication and professionalism truly made a difference. Thank you. Ruchir, I extend my best wishes for your continued success and prosperity.\n' +
      '\n',
    backgroundColor: 'blue',
    thumbnail: { data: [Object] }
  },
  {
    id: 44,
    name: 'Neeraj Kumar',
    statement: 'I recently received invaluable assistance from Chethana and Abhishek at OneAssure during my claim settlement process with ICICI Lombard Mediclaim. Their consistent communication and proactive approach ensured that all issues were addressed promptly at each stage, leading to a timely settlement of my claim. Both Chethana and Abhishek exhibited professionalism and thoroughness in their interactions, keeping me informed of the progress regularly. I am sincerely grateful to them for their exceptional support.\n' +
      '\n',
    backgroundColor: 'blue',
    thumbnail: { data: [Object] }
  },
  {
    id: 45,
    name: 'Lingeshwar S',
    statement: 'Abhishek and his team were exceptional in ensuring a smooth claim process. They were transparent in addressing my concerns and provided unwavering support throughout. A big thanks to the team—I highly recommend their services!\n' +
      '\n',
    backgroundColor: 'blue',
    thumbnail: { data: [Object] }
  }
]
TypeError: First argument must be a string
    at t.default (/Users/<USER>/Documents/oneassure-site/build/server/chunks/623.js:1:83780)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5300
    at Array.map (<anonymous>)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5225
    at Array.map (<anonymous>)
    at d (/Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:4660)
    at nj (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:46251)
    at nM (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:47571)
    at nN (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:64546)
    at nB (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:67538) {
  digest: '**********'
}

Error occurred prerendering page "/health-insurance/hdfc-ergo/hdfc-ergo-energy-gold-with-copay". Read more: https://nextjs.org/docs/messages/prerender-error

TypeError: First argument must be a string
    at t.default (/Users/<USER>/Documents/oneassure-site/build/server/chunks/623.js:1:83780)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5300
    at Array.map (<anonymous>)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5225
    at Array.map (<anonymous>)
    at d (/Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:4660)
    at nj (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:46251)
    at nM (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:47571)
    at nN (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:64546)
    at nB (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:67538)
   Generating static pages (98/393) 
TypeError: First argument must be a string
    at t.default (/Users/<USER>/Documents/oneassure-site/build/server/chunks/623.js:1:83780)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5300
    at Array.map (<anonymous>)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5225
    at Array.map (<anonymous>)
    at d (/Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:4660)
    at nj (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:46251)
    at nM (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:47571)
    at nN (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:64546)
    at nB (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:67538) {
  digest: '**********'
}

Error occurred prerendering page "/health-insurance/care-health-insurance/care-joy". Read more: https://nextjs.org/docs/messages/prerender-error

TypeError: First argument must be a string
    at t.default (/Users/<USER>/Documents/oneassure-site/build/server/chunks/623.js:1:83780)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5300
    at Array.map (<anonymous>)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5225
    at Array.map (<anonymous>)
    at d (/Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:4660)
    at nj (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:46251)
    at nM (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:47571)
    at nN (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:64546)
    at nB (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:67538)
TypeError: First argument must be a string
    at t.default (/Users/<USER>/Documents/oneassure-site/build/server/chunks/623.js:1:83780)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5300
    at Array.map (<anonymous>)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5225
    at Array.map (<anonymous>)
    at d (/Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:4660)
    at nj (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:46251)
    at nM (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:47571)
    at nN (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:64546)
    at nB (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:67538) {
  digest: '**********'
}

Error occurred prerendering page "/health-insurance/icici-lombard/icici-golden-shield". Read more: https://nextjs.org/docs/messages/prerender-error

TypeError: First argument must be a string
    at t.default (/Users/<USER>/Documents/oneassure-site/build/server/chunks/623.js:1:83780)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5300
    at Array.map (<anonymous>)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5225
    at Array.map (<anonymous>)
    at d (/Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:4660)
    at nj (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:46251)
    at nM (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:47571)
    at nN (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:64546)
    at nB (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:67538)
TypeError: First argument must be a string
    at t.default (/Users/<USER>/Documents/oneassure-site/build/server/chunks/623.js:1:83780)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5300
    at Array.map (<anonymous>)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5225
    at Array.map (<anonymous>)
    at d (/Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:4660)
    at nj (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:46251)
    at nM (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:47571)
    at nN (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:64546)
    at nB (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:67538) {
  digest: '**********'
}

Error occurred prerendering page "/health-insurance/godigit/digit-health-care-plus-policyinfinity-wallet". Read more: https://nextjs.org/docs/messages/prerender-error

TypeError: First argument must be a string
    at t.default (/Users/<USER>/Documents/oneassure-site/build/server/chunks/623.js:1:83780)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5300
    at Array.map (<anonymous>)
    at /Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:5225
    at Array.map (<anonymous>)
    at d (/Users/<USER>/Documents/oneassure-site/build/server/chunks/115.js:33:4660)
    at nj (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:46251)
    at nM (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:47571)
    at nN (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:64546)
    at nB (/Users/<USER>/Documents/oneassure-site/node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js:12:67538)
   Generating static pages (196/393) 
[
  {
    id: 1,
    name: ' Gaurav Agarwal',
    statement: 'The importance of health insurance plans is wasted on millennials. OneAssure guided me through the benefits of insuring my health above all and how I could benefit from the same in so many ways. Sorting out the best insurance plan that fits my personal needs was just what I needed. Thank you OneAssure and I would definitely recommend you guys to friends and family!',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 2,
    name: 'Gokul Subramanya',
    statement: "I was negligent towards the necessity of health insurance until I came across OneAssure. There are a lot of policies available today, each catering to specific needs, that an average person would find hard to comprehend. Luckily, I had help from OneAssure's partner, who not only understood my requirements and assisted me in choosing the right one but went a step further, by confirming the compliance of my pre-existing conditions with the policy underwriters. Thank you for the extensive support.",
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  },
  {
    id: 3,
    name: 'Mitali',
    statement: 'When I approached OneAssure, I had a very tough deadline as it was my father’s 60th birthday in a few days. OneAssure stepped in and explained all the features and the possible plans. It was a relief to get unbiased advice from a team that was focused on getting us the right policy. The team was very flexible and assisted me, even when I reached out at odd hours. Would recommend OneAssure wholeheartedly! Way to go team!',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 4,
    name: 'Maria Martin',
    statement: 'A fantastic way to quickly find the best health plan. I recently got one health insurance plan for my mom. Despite all the information available online, it could be confusing which plan to buy. OneAssure partner solved it for me.',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  },
  {
    id: 5,
    name: 'Varun Mishra',
    statement: 'On January 18th, I was admitted to the hospital, anticipating a lengthy wait for insurance approval based on past experiences. However, thanks to OneAssure, the approval came through surprisingly quickly, within just 45 minutes. Abhishek D from the OneAssure team provided invaluable assistance in navigating the intricacies of insurance policies and expediting the approval process. I am immensely grateful to the team for their exceptional support during this time.',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 6,
    name: 'Akhilesh Kataria',
    statement: 'Chethana, I want to express my gratitude for your outstanding support and assistance throughout the claims process. Your dedication and professionalism truly made a difference. Thank you. Ruchir, I extend my best wishes for your continued success and prosperity.',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  },
  {
    id: 7,
    name: 'Neeraj Kumar',
    statement: 'I recently received invaluable assistance from Chethana and Abhishek at OneAssure during my claim settlement process with ICICI Lombard Mediclaim. Their consistent communication and proactive approach ensured that all issues were addressed promptly at each stage, leading to a timely settlement of my claim. Both Chethana and Abhishek exhibited professionalism and thoroughness in their interactions, keeping me informed of the progress regularly. I am sincerely grateful to them for their exceptional support.',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 8,
    name: 'Lingeshwar S',
    statement: 'Abhishek and his team were exceptional in ensuring a smooth claim process. They were transparent in addressing my concerns and provided unwavering support throughout. A big thanks to the team—I highly recommend their services!',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  }
]
   Generating static pages (294/393) 
[
  {
    id: 15,
    name: 'Ankit Gajera ',
    statement: 'I express my sincere thanks to Oneassure team for their outstanding support in handling my health insurance claim. They were prompt, professional, and always dedicated to resolving my concerns.',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 16,
    name: 'Sandeep Kopuri  ',
    statement: "Oneassure helped me buy an appropriate family floater health insurance. 3 years on, when I made a claim for my mother's surgery they provided prompt assistance.",
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  },
  {
    id: 18,
    name: 'Akash Kumar ',
    statement: 'I recently had the pleasure of purchasing health insurance through Oneassure, and my experience was exceptional. I highly recommend this company to anyone looking for health insurance.',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 17,
    name: 'Abhishek Giridhar Bhat ',
    statement: 'I highly recommend the One Assure Support team to anyone in need of insurance assistance. Their expertise, efficiency, and dedication set them apart. ',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  }
]
<ref *1> t {
  parent: t {
    parent: t {
      parent: null,
      prev: null,
      next: null,
      startIndex: null,
      endIndex: null,
      children: [Array],
      name: 'p',
      attribs: [Object],
      type: 'tag'
    },
    prev: null,
    next: null,
    startIndex: null,
    endIndex: null,
    children: [ [t], [Circular *1], [t] ],
    name: 'span',
    attribs: { style: '' },
    type: 'tag'
  },
  prev: t {
    parent: t {
      parent: [t],
      prev: null,
      next: null,
      startIndex: null,
      endIndex: null,
      children: [Array],
      name: 'span',
      attribs: [Object],
      type: 'tag'
    },
    prev: null,
    next: [Circular *1],
    startIndex: null,
    endIndex: null,
    data: 'ICICI Lombard Health Insurance offers comprehensive coverage with unlimited sum insured, 100% restoration benefits, and no waiting',
    type: 'text'
  },
  next: t {
    parent: t {
      parent: [t],
      prev: null,
      next: null,
      startIndex: null,
      endIndex: null,
      children: [Array],
      name: 'span',
      attribs: [Object],
      type: 'tag'
    },
    prev: [Circular *1],
    next: null,
    startIndex: null,
    endIndex: null,
    data: 'period for chronic conditions. It provides global and maternity coverage, along with up to 15% discount for customers with a good CIBIL score. Their critical illness plan covers 92 major and minor illnesses, making it one of the most extensive in the market. Additionally, a user-friendly customer app with chatbot support enhances customer experience, making it a strong contender for those seeking robust health insurance.',
    type: 'text'
  },
  startIndex: null,
  endIndex: null,
  children: [
    t {
      parent: [Circular *1],
      prev: null,
      next: null,
      startIndex: null,
      endIndex: null,
      data: ' ',
      type: 'text'
    }
  ],
  name: 'strong',
  attribs: {},
  type: 'tag'
}
<ref *1> t {
  parent: t {
    parent: t {
      parent: null,
      prev: null,
      next: null,
      startIndex: null,
      endIndex: null,
      children: [Array],
      name: 'p',
      attribs: [Object],
      type: 'tag'
    },
    prev: null,
    next: null,
    startIndex: null,
    endIndex: null,
    children: [ [Circular *1], [t] ],
    name: 'span',
    attribs: { style: '' },
    type: 'tag'
  },
  prev: null,
  next: t {
    parent: t {
      parent: [t],
      prev: null,
      next: null,
      startIndex: null,
      endIndex: null,
      children: [Array],
      name: 'span',
      attribs: [Object],
      type: 'tag'
    },
    prev: [Circular *1],
    next: null,
    startIndex: null,
    endIndex: null,
    data: 'We guide your loved ones every step of the way to ensure they receive the full benefit of their coverage.',
    type: 'text'
  },
  startIndex: null,
  endIndex: null,
  children: [
    t {
      parent: [Circular *1],
      prev: null,
      next: null,
      startIndex: null,
      endIndex: null,
      data: ' ',
      type: 'text'
    }
  ],
  name: 'strong',
  attribs: {},
  type: 'tag'
}
[
  {
    id: 19,
    name: 'Siddhartha Saha ',
    statement: 'I found and bought the best health and life policy options available in the market under guidance of Oneassure despite having pre-existing conditions.',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  },
  {
    id: 21,
    name: 'Dainey Francis ',
    statement: 'Staff is very polite and understands what the customers want. Wonderful experience.',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 20,
    name: 'V G ',
    statement: 'Bought a life insurance policy from Oneasure. The team was very helpful and guided me to choose the best policy for my family, thank you.',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  }
]
[
  {
    id: 31,
    name: 'Taran Singhania ',
    statement: 'OneAssure team helped me a lot through my entire claim process and made it super easy and were always responsive.',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 32,
    name: 'Ankit Gajera ',
    statement: 'I express my sincere thanks to OneAssure team for their outstanding support in handling my health insurance claim. They were prompt, professional, and always dedicated to resolving my concerns.',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  },
  {
    id: 33,
    name: 'Sandeep Kopuri  ',
    statement: "OneAssure helped me buy an appropriate family floater health insurance. 3 years on, when I made a claim for my mother's surgery they provided prompt assistance.",
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  },
  {
    id: 34,
    name: 'Akash Kumar',
    statement: 'I recently had the pleasure of purchasing health insurance through OneAssure, and my experience was exceptional. I highly recommend this company to anyone looking for health insurance.',
    backgroundColor: 'secondary-2',
    thumbnail: { data: [Object] }
  },
  {
    id: 35,
    name: 'Abhishek Giridhar Bhat ',
    statement: 'I highly recommend the OneAssure Support team to anyone in need of insurance assistance. Their expertise, efficiency, and dedication set them apart.',
    backgroundColor: 'primary-3',
    thumbnail: { data: [Object] }
  }
]
 ✓ Generating static pages (393/393)

> Export encountered errors on following paths:
	/health-insurance/[company]/[product]/page: /health-insurance/care-health-insurance/care-joy
	/health-insurance/[company]/[product]/page: /health-insurance/godigit/digit-health-care-plus-policyinfinity-wallet
	/health-insurance/[company]/[product]/page: /health-insurance/hdfc-ergo/hdfc-ergo-energy-gold-with-copay
	/health-insurance/[company]/[product]/page: /health-insurance/icici-lombard/icici-golden-shield
