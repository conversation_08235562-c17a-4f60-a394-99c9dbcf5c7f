"use client";

import Hero from "./components/Hero";
import InsuranceDesc from "../globals/InsuranceAllProducts/InsuranceDesc";
import WhyBuyTI from "./components/WhyBuyTI";
import { TermInsuranceLPDataObject } from "./types";
import { Suspense, useState, useEffect } from "react";
import Modal from "../globals/Modal";
import BookACallBtn from "../globals/BookACall";
import HowTiWork from "./components/HowTiWork";
import WhoShouldBuy from "./components/WhoShouldBuy";
import HowToBuy from "./components/HowToBuy";
import Testimonials from "../globals/Testimonials";
import Container from "../globals/Container";
import PartnerCompanies from "../Home/components/PartnerCompanies";
import RidersForTermInsurance from "./components/RidersForTermInsurance";
import HowToBuyInsuranceFromOA from "../globals/HowToBuyInsuranceFromOA";
import DocsRequired from "../globals/DocsRequired";
import Eligibility from "../globals/Eligibility";
import ContactForm from "../globals/ContactForm";
import TalkToOAExpert from "../globals/TalkToOAExpert";
import HowToChooseHI from "../globals/HIinIndia";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import { useSessionStorage } from "usehooks-ts";

const LifeInsurance = ({
  data,
  otherCompanies,
}: {
  data: TermInsuranceLPDataObject;
  otherCompanies: any;
}) => {
  const [openModal, setOpenModal] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);
  useEffect(() => {
    if (typeof window !== "undefined") {
      router.replace(
        `${pathname}?${utm_source !== null ? `utm_source=${utm_source}` : ""}${
          utm_medium !== null ? `&utm_medium=${utm_medium}` : ""
        }${utm_campaign !== null ? `&utm_campaign=${utm_campaign}` : ""}${
          utm_content !== null ? `&utm_content=${utm_content}` : ""
        }${utm_term !== null ? `&utm_term=${utm_term}` : ""}`
      );
    }
  }, []);

  return (
    <Container>
      <div className="relative font-manrope">
        <Hero
          data={data.attributes.hero}
          setOpenModal={setOpenModal}
          category="term"
          companyData={otherCompanies}
        />

        <InsuranceDesc
          title={data.attributes.whatIsTI.title}
          description={data.attributes.whatIsTI.description}
        />
        <Suspense>
          <PartnerCompanies partners={otherCompanies} />
        </Suspense>

        <HowTiWork data={data.attributes.HowDoesTIWorks} />
        <HowToChooseHI data={data.attributes.benefitsOfTI} />
        <WhoShouldBuy data={data.attributes.whoShouldBuyTI} />
        <WhyBuyTI data={data.attributes.whyToBuyTI} />
        <HowToBuy data={data.attributes.howToBuyTI} />
        <RidersForTermInsurance data={data.attributes.riderForTI} />

        <HowToBuyInsuranceFromOA data={data.attributes.howToBuyTIOneAssure} />

        <Eligibility data={data.attributes.TIEligibility} />

        <DocsRequired data={data.attributes.requiredDocsTI} />
        <TalkToOAExpert />
        <Testimonials testimonials={data.attributes.testimonials.testimonial} />
        <Suspense>
          <ContactForm />
        </Suspense>
        <Suspense>
          <Modal
            open={openModal}
            handleModal={() => setOpenModal(false)}
            msg="Looking for insurance purchase assistance. Thank you!"
            utmSrc="Liability_Insurance"
          />
        </Suspense>

        {/* <div className="bg-green-1 inline-block fixed bottom-10 right-5 md:bottom-20 md:right-0 md:transform md:translate-x-12 md:-rotate-90 md:rounded-t-2xl md:rounded-b-none rounded-full z-30">
          <BookACallBtn className="py-3 px-6 md:px-8 transparent text-ntrl-white" />
          </div> */}
      </div>
    </Container>
  );
};

export default LifeInsurance;
