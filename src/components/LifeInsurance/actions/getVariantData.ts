"use server";


export default async function getTermVariantData<T>(variant_slug: string): Promise<T> {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/term-variants?filters[slug][$eq]=${variant_slug}&populate[company][populate][fields][0]=name&populate[company][populate][fields][1]=slug&populate[company][populate][logo][fields][0]=url&populate[faqs][fields][0]=question&populate[faqs][fields][1]=ans&populate[whyOneAssure][fields][0]=title&populate[whyOneAssure][fields][1]=description&populate[features][fields][0]=title&populate[features][fields][1]=description&populate[features][populate][listedFeatures][fields][0]=feature&populate[eligibility][fields][0]=title&populate[eligibility][fields][1]=description&populate[blog][populate][fields][0]=Title&populate[blog][populate][fields][1]=slug&populate[blog][populate][fields][2]=subtitle&populate[blog][populate][category][fields][0]=slug&populate[blog][populate][Thumbnail][fields][0]=url&populate[exclusions][fields][0]=exclusion&populate[hero][fields][0]=title&populate[hero][fields][1]=claimSettlementRatio&populate[policyDocs][fields][0]=label&populate[policyDocs][populate][document][fields][0]=url&populate[addOns][fields][0]=title&populate[addOns][fields][1]=description&populate[seo][fields][0]=metaTitle&populate[seo][fields][1]=metaDescription&populate[seo][fields][2]=keyword&populate[variants][populate][relatedVariant][fields][0]=name&populate[variants][populate][relatedVariant][fields][1]=slug&populate[variants][populate][relatedVariant][populate][company][fields][0]=slug&populate[variants][populate][relatedVariant][populate][company][fields][1]=name&populate[variants][populate][relatedVariant][populate][company][populate][logo][fields][0]=url&populate[variants][populate][features][fields][0]=feature&populate[womenBenifits][fields][0]=description&populate[smokingDownsides][fields][0]=description&populate[notes][fields][0]=title&populate[notes][fields][1]=description`,
    { headers }
  );

  return res.json() as Promise<T>;
}
