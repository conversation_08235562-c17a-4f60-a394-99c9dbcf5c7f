import Container from "@/components/globals/Container";
import parse from "html-react-parser";
import { Element } from "html-react-parser";
import { BuyFromOA } from "../types";
// import WhyTICard from "./WhyTICard";
// import { Importance } from "../type";

const HowToBuyFromOA = ({ data }: { data: BuyFromOA }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-blue400 text-3xl font-semibold my-5";
          break;

        case "h3":
          attrs.className = "text-blue400 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-blue400 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5 text-right";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;
      }
      return domNode;
    }
  };
  return (
    <div className="md:py-20 py-10">
      <Container>
        {/* Heading */}
        <div className="mb-12">
          <div className="flex items-center justify-center font-generalSans md:text-[48px]/[58px] text-[24px]/[32px] text-ntrl-black gap-2 mb-4">
            <h2 className="font-normal text-center">
              {"How do you buy "}
              <span className="font-medium">
                term life insurance from Oneassure?
              </span>
            </h2>
          </div>

          <p className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-grey1">
            Buy Health Insurance with Oneassure in 4 Simple Steps
          </p>
        </div>

        {/* Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-[30px] mb-12">
          {data.step.map((feat, idx) => (
            <div
              className="bg-primary-3 rounded-xl p-5 relative min-h-[200px] flex justify-end"
              key={idx}
            >
              <h3 className="text-ntrl-grey2 text-9xl font-semibold absolute top-0 left-0 z-0">
                {idx + 1}
              </h3>

              <div className="w-[80%]">
                <h2 className="text-xl font-semibold text-ntrl-grey1 text-right z-50 mb-3">
                  {feat.title}
                </h2>
                <>
                  {/* @ts-ignore */}
                  {parse(feat.description, { replace })}
                </>
              </div>
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
};

export default HowToBuyFromOA;
