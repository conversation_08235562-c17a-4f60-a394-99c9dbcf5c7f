import Container from "@/components/globals/Container";
import { Riders } from "../types";

const Rider = ({ data }: { data: Riders }) => {
  return (
    <div className="my-20">
      <Container>
        {/* Heading */}
        <div className="mb-12">
          <div className="flex items-center justify-center font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black gap-2 mb-4 ">
            <h2 className="font-normal">Rider for your</h2>
            <h2 className="font-medium">Term insurance</h2>
          </div>

          <p className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-grey1">
            Make wise insurance decisions with OneAssure. Receive 100% unbiased
            recommendations
          </p>
        </div>

        <div className="md:grid grid-cols-3 gap-[30px] hidden">
          {data.termRiders.map((rider, idx) => (
            <div
              className="border border-ntrl-grey2 bg-secondary-2 rounded-xl p-8"
              key={idx}
            >
              <h6 className="text-[24px]/[30px] font-medium text-ntrl-black font-generalSans mb-6">
                {`0${idx + 1}.`}
              </h6>
              <h6 className="text-[24px]/[30px] font-medium text-ntrl-black font-generalSans mb-2">
                {rider.title}
              </h6>
              <p className="text-[16px]/[24px] font-normal text-ntrl-grey1">
                {rider.description}
              </p>
            </div>
          ))}
        </div>
      </Container>

      {/* Mobile Screen */}
      <div className="md:hidden flex items-stretch gap-[30px] mb-12 overflow-x-auto px-5">
        {data.termRiders.map((rider, idx) => (
          <div
            className="border border-ntrl-grey2 bg-secondary-2 rounded-xl p-8 min-w-[284px]"
            key={idx}
          >
            <h6 className="text-[24px]/[30px] font-medium text-ntrl-black font-generalSans mb-6">
              {`0${idx + 1}.`}
            </h6>
            <h6 className="text-[24px]/[30px] font-medium text-ntrl-black font-generalSans mb-2">
              {rider.title}
            </h6>
            <p className="text-[16px]/[24px] font-normal text-ntrl-grey1">
              {rider.description}
            </p>
          </div>
        ))}
      </div>
      {/* Mobile Screen */}
    </div>
  );
};

export default Rider;
