"use client";

import { useState } from "react";
import { XMarkIcon, PlusIcon } from "@heroicons/react/24/outline";
import parse from "html-react-parser";
import { Element } from "html-react-parser";
import { HowToBuyTI } from "../types";

const AccordionOption = ({
  step,
  position,
}: {
  step: {
    desc: string;
    title: string;
  };
  position: number;
}) => {
  const [open, setOpen] = useState(position === 1 ? true : false);
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "p":
          // attrs.className = "mb-6";
          break;

        case "a":
          attrs.className = "text-primary-2 no-underline";
          break;
      }
      return domNode;
    }
  };
  return (
    <div className="bg-ntrl-white rounded-lg" onClick={() => setOpen(!open)}>
      <div className="bg-gradient-to-r from-green1000 to-blue1200 rounded-3xl p-[0.5px]">
        <div
          className={`flex justify-between items-center md:p-5 p-3 bg-ntrl-white ${
            open ? "rounded-t-3xl" : "rounded-3xl"
          }`}
        >
          <div className="w-[90%] md:w-[80%] flex items-center">
            <h2 className="font-semibold text-[16px]/[24px] text-ntrl-black font-manrope px-5">
              {position}.{step.title}
            </h2>
          </div>

          {open ? (
            <XMarkIcon
              className="font-bold text-ntrl-black text-xl w-6 h-6 "
              onClick={() => setOpen(!open)}
            />
          ) : (
            <PlusIcon className="font-bold text-ntrl-black text-xl w-6 h-6 " />
          )}
        </div>

        {open && (
          <div className="font-light text-[12px] text-ntrl-grey1 pl-10 bg-ntrl-white border-b rounded-b-3xl pb-5">
            {/* @ts-ignore */}
            {parse(step.desc, { replace })}
          </div>
        )}
      </div>
    </div>
  );
};

const HowToBuy = ({ data }: { data: HowToBuyTI }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-blue400 text-3xl font-semibold my-5";
          break;

        case "h3":
          attrs.className = "text-blue400 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-blue400 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "font-normal text-[14px]/[20px] text-ntrl-black mb-5";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;

        case "table":
          attrs.className =
            "mx-auto font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5 drop-shadow-lg";
          break;

        case "tr":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;
        case "td":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;
      }
      return domNode;
    }
  };

  return (
    <section className="md:mb-14 mb-5" id="claims">
      <div>
        {/* Heading */}
        <div className="mb-6 text-center">
          <div className="font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black gap-2 flex items-center justify-center mb-2">
            <h2 className="md:text-3xl text-lg font-medium text-ntrl-black-1">
              {data.title}
            </h2>
          </div>

          {/* @ts-expect-error unknown type */}
          {parse(data.description, { replace })}
        </div>

        {/* Content */}
        <div className="columns-1 md:columns-2 space-y-4 md:space-y-8 gap-4">
          {data.sectionData.map((feat, idx) => {
            return (
              <div key={idx} className="break-inside-avoid">
                <AccordionOption
                  position={idx + 1}
                  step={{ title: feat.title, desc: feat.description }}
                />
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default HowToBuy;
