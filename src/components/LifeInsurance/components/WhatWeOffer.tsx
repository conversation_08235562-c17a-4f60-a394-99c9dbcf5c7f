"use client";

import Container from "@/components/globals/Container";
import { WhatWeOffer as TWhatWeOffer } from "../types";
import Image from "next/image";
import parse, { attributesToProps } from "html-react-parser";
import { Element } from "html-react-parser";
import { useState } from "react";

const WhatYouGetOption = ({
  title,
  subtitle,
  icon,
}: {
  title: string;
  subtitle: string;
  icon: string;
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "p":
          attrs.className = "font-normal text-[18px]/[28px] mb-5";
          break;

        case "a":
          attrs.className = "text-ntrl-white font-semibold my-0";
          break;
      }
      return domNode;
    }
  };

  return (
    <div className="flex md:flex-col md:items-center md:justify-center">
      <div className="md:w-20 md:h-20 w-10 h-10 rounded-full flex items-center justify-center bg-ntrl-white mb-4">
        <div className="md:w-10 md:h-10 w-5 h-5 rounded-full flex items-center justify-center bg-ntrl-white relative">
          <Image
            src={icon}
            fill={true}
            style={{ objectFit: "cover" }}
            alt={title}
          />
        </div>
      </div>
      <div className=" ml-4 md:ml-0">
        <h3 className="md:text-[24px]/[30px] text-[20px]/[28px] text-ntrl-white font-medium font-generalSans mb-2 md:text-center">
          {title}
        </h3>
        {/* <p className="text-[16px]/[24px] text-ntrl-white font-normal md:text-center">
          {subtitle}
        </p> */}
        <div
          className="w-64 p-4 text-white text-center rounded-lg cursor-pointer"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {"<Read More>"}
          <div
            className={`overflow-hidden transition-max-height duration-500 ease-in-out ${
              isHovered
                ? "max-h-[1500px] text-primary-1 bg-ntrl-white"
                : "max-h-0 text-primary-1"
            } mt-2 p-4 rounded-lg text-left font-normal`}
          >
            {/* @ts-ignore */}
            {parse(subtitle, { replace })}
          </div>
        </div>
      </div>
    </div>
  );
};

const WhatWeOffer = ({ data }: { data: TWhatWeOffer }) => {
  return (
    <div className="md:py-20 py-10 bg-primary-1">
      <Container>
        <div className="mb-12">
          {/* Heading */}
          <div
            className="flex items-center justify-center font-generalSans 
         text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-white gap-2 mb-4 "
          >
            <h2 className="font-normal">
              {"Why Choose Oneassure For "}
              <span className="font-medium">Buying Term Insurance?</span>
            </h2>
          </div>

          <p className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-white font-normal">
            We are committed to helping you secure your financial future with
            confidence.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
          {data.offer.map((b, idx) => (
            <WhatYouGetOption
              key={idx}
              title={b.title}
              subtitle={b.subTitle}
              icon={b.icon.data[0].attributes.url}
            />
          ))}
        </div>
      </Container>
    </div>
  );
};

export default WhatWeOffer;
