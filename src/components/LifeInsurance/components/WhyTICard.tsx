import Image from "next/image";

const WhyTICard = ({
  style,
  title,
  subTitle,
  thumbnail,
  bgColor,
}: {
  style?: string;
  title: string;
  subTitle: string;
  thumbnail: string;
  bgColor?: string;
}) => {
  return (
    <div
      className={`bg-ntrl-white ${bgColor} ${style} grid ${
        style === "col-span-6" ? "grid-cols-2" : "grid-cols-1"
      } rounded-lg`}
    >
      {/* Heading */}
      <div className="h-48 px-6 py-8">
        <h3 className="text-[24px]/[30px] font-generalSans font-medium text-ntrl-black mb-4">
          {title}
        </h3>
        <p className="text[16px]/[24px] text-ntrl-grey1 font-normal">
          {subTitle}
        </p>
      </div>
      {/* Image */}
      <div className={`bg-ntrl-grey2 h-52 relative rounded-b-lg`}>
        <Image
          src={thumbnail}
          fill={true}
          style={{ objectFit: "cover", objectPosition: "left top" }}
          alt={title}
          className="rounded-b-lg"
        />
      </div>
    </div>
  );
};

export default WhyTICard;
