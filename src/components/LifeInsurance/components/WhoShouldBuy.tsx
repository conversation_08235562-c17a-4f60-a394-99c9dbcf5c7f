import { WhoShouldBuyTI } from "../types";
import parse from "html-react-parser";
import { Element } from "html-react-parser";
import Image from "next/image";

const WhoShouldBuy = ({ data }: { data: WhoShouldBuyTI }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-blue400 text-3xl font-semibold my-5";
          break;

        case "h3":
          attrs.className = "text-blue400 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-blue400 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "font-normal text-[14px]/[20px] text-ntrl-black mb-5";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;

        case "table":
          attrs.className =
            "mx-auto font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5 drop-shadow-lg";
          break;

        case "tr":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;
        case "td":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;
      }
      return domNode;
    }
  };

  return (
    <div className="md:mb-14 mb-5">
      {/* Heading */}
      <div className="">
        <div className="flex items-center justify-center font-generalSans md:text-[30px]/[40px] text-[24px]/[32px] text-ntrl-black gap-2 mb-4 ">
          <h2 className="font-medium text-ntrl-black-1 md:text-3xl text-lg">
            {data.title}
          </h2>
        </div>
      </div>

      <div className="text-start mb-4 md:mb-12">
        {/* @ts-expect-error unknown type */}
        {parse(data.description, { replace })}

        <div className="grid grid-cols-2 md:grid-cols-6 gap-4 md:gap-10 mt-8  px-4 md:px-16">
          {data.sectionData.map((reason, index) => (
            <div key={index}>
              <div className="w-12 h-12 relative mb-2">
                <Image
                  src={reason?.icon?.data?.attributes?.url || ""}
                  alt="vector"
                  fill
                  style={{ objectFit: "contain" }}
                />
              </div>
              <h3 className="text-[14px]/[20px] font-bold text-ntrl-black text-left mb-5 ">
                {reason.title}
              </h3>
              {/* @ts-expect-error unknown type */}
              {parse(reason.description, { replace })}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WhoShouldBuy;
