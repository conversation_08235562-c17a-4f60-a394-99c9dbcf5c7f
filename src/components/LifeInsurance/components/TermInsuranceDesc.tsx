import Container from "@/components/globals/Container";
import { AboutTI } from "../types";
import parse from "html-react-parser";
import { Element } from "html-react-parser";
import Image from "next/image";

const TermInsuranceDesc = ({ data }: { data: AboutTI }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "p":
          attrs.className = "mb-6";
          break;
      }
      return domNode;
    }
  };

  return (
    <div className="mb-20">
      <Container>
        <div
          className={`rounded-xl bg-secondary-1 grid grid-cols-1 md:grid-cols-3 items-center md:min-w-[816px] p-2 snap-start`}
        >
          <div className="py-6 md:py-12 px-4 md:px-0 md:pl-12 md:pr-9 md:col-span-2">
            {/* Heading */}
            <div className="mb-6 flex items-center gap-2">
              <h2 className="text-ntrl-white font-generalSans md:text-[48px]/[58px] text-[24px]/[32px] font-normal">
                What Is
              </h2>
              <h2 className="text-ntrl-white font-generalSans md:text-[48px]/[58px] text-[24px]/[32px] font-medium">
                Term Insurance
              </h2>
            </div>

            {/* Description */}
            <div className="text-ntrl-white md:text-[16px]/[24px] text-[14px]/[22px] font-regular">
              {/* @ts-ignore */}
              {parse(data.description, { replace })}
            </div>
          </div>

          {/* Image here */}
          <div className="rounded-xl min-h-[262px] md:h-full bg-white relative">
            <Image
              src={data.thumbnail.data.attributes.url}
              fill={true}
              style={{ objectFit: "cover" }}
              alt="What is Term"
              className="rounded-xl"
            />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default TermInsuranceDesc;
