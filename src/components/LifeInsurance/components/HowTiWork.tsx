import { HowDoesTIWorks as THowTiWork } from "../types";
import parse, { Element } from "html-react-parser";

const HowTiWork = ({ data }: { data: THowTiWork }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-blue400 text-3xl font-semibold my-5";
          break;

        case "h3":
          attrs.className = "text-blue400 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-blue400 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "font-normal text-[14px]/[20px] text-ntrl-black mb-5";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;

        case "table":
          attrs.className =
            "mx-auto font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5 drop-shadow-lg";
          break;

        case "tr":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;
        case "td":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;
      }
      return domNode;
    }
  };
  return (
    <div className="md:mb-14 mb-5">
      <div className="p-[1px] bg-gradient-to-r from-primary-green-2 to-primary-blue-3 rounded-3xl">
        {/* Heading */}
        <div className="bg-ntrl-white rounded-3xl p-5">
          <div className="mb-6">
            <div className="flex items-center justify-center font-generalSans  text-[24px]/[32px] gap-2 ">
              <h2 className="font-medium bg-gradient-to-r from-primary-green-2 to-primary-blue-3 bg-clip-text text-transparent md:text-3xl text-lg ">
                How Does Term Insurance Work?
              </h2>
            </div>
            {/* <p className="text-center text-ntrl-black text-[14px]/[20px] font-light">
              Given the unpredictable nature of health issues, medical insurance
              is a vital investment. It safeguards financial stability and
              health security.
            </p> */}
          </div>

          <div className="no-underline">
            {/* @ts-expect-error unknown type */}
            {parse(data.description, { replace })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HowTiWork;
