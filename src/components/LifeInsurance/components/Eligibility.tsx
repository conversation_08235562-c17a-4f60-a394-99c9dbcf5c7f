import Container from "@/components/globals/Container";
import parse, { attributesToProps } from "html-react-parser";
import { Element } from "html-react-parser";
import { Eligibility as TEligibility } from "../types";

const Eligibility = ({ data }: { data: TEligibility }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-blue400 text-3xl font-semibold my-5";
          break;

        case "h3":
          attrs.className = "text-blue400 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-blue400 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;

        case "table":
          attrs.className =
            "mx-auto font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5 drop-shadow-lg";
          break;

        case "tr":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;
        case "td":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;
      }
      return domNode;
    }
  };
  return (
    <div className="bg-secondary-2 md:py-20 py-10">
      <Container>
        {/* Heading */}
        <div className="mb-12">
          <div className="flex items-center justify-center font-generalSans md:text-[48px]/[58px] text-[24px]/[32px] text-ntrl-black gap-2 mb-4 ">
            <h2 className="font-normal text-center">
              Term Insurance Plan
              <span className="font-medium"> Eligibility Criteria</span>
            </h2>
          </div>

          <p className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-grey1">
            Buy Term Insurance with Oneassure in 4 Simple Steps
          </p>
        </div>

        <div className="no-underline">
          {/* @ts-ignore */}
          {parse(data.description, { replace })}
        </div>
      </Container>
    </div>
  );
};

export default Eligibility;
