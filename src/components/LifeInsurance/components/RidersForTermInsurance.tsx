import parse from "html-react-parser";
import { Element } from "html-react-parser";
import Image from "next/image";
import { RiderForTI } from "../types";

const RidersForTermInsurance = ({ data }: { data: RiderForTI }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-blue400 text-3xl font-semibold my-5";
          break;

        case "h3":
          attrs.className = "text-blue400 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-blue400 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className = "font-normal text-[14px]/[20px] text-white mb-5";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;

        case "table":
          attrs.className =
            "mx-auto font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5 drop-shadow-lg";
          break;

        case "tr":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;
        case "td":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;
      }
      return domNode;
    }
  };

  return (
    <div className="relative mb-5 md:mb-14 bg-gradient-to-r from-blue1000 to-blue900 bg-200%  rounded-3xl text-center w-full shadow-whyOneassure overflow-hidden">
      <div className="mt-6 mx-4 md:mx-0 md:ml-12 justify-items-center ">
        <h2 className="text-white md:text-3xl text-lg font-medium text-center md:text-left">
          {data.title}
        </h2>
        {/* @ts-expect-error unknown type */}
        {parse(data.description, { replace })}
      </div>

      <div className="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={570} // Increased width
          height={570} // Increased height
          objectFit="cover"
        />
      </div>

      <div className="absolute bottom-0 md:left-12 left-0 transform -translate-x-1/2 translate-y-1/2">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={570}
          height={570}
          objectFit="cover"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-7  gap-3 md:gap-6 mx-4 md:mx-0 md:ml-12">
        {data.sectionData.map((rider, index) => (
          <div key={index} className="mt-2 flex md:flex-col items-center gap-2">
            <div className="text-white text-left w-12 h-12 relative">
              <Image
                src={rider?.icon?.data?.attributes?.url || ""}
                alt="eligibility"
                fill
                style={{ objectFit: "contain" }}
              />
            </div>
            <div className="w-[80%] md:w-full">
              <h3 className="text-[14px]/[20px] font-bold mt-2 text-white text-left md:text-center">
                {rider.title}
              </h3>
              <div className="text-[12px]/[18px] font-normal text-white text-left md:text-center my-3">
                {/* @ts-expect-error unknown type */}
                {parse(rider.description, { replace })}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RidersForTermInsurance;
