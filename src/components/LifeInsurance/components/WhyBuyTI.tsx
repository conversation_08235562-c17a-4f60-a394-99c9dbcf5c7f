import { WhyToBuyTI } from "../types";
import Image from "next/image";
import parse, { Element } from "html-react-parser";

const WhyBuyTI = ({ data }: { data: WhyToBuyTI }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-blue400 text-3xl font-semibold my-5";
          break;

        case "h3":
          attrs.className = "text-blue400 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-blue400 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className = "font-normal text-[14px]/[20px] text-white mb-5";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;

        case "table":
          attrs.className =
            "mx-auto font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5 drop-shadow-lg";
          break;

        case "tr":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;
        case "td":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;
      }
      return domNode;
    }
  };
  return (
    <div className="p-4 relative bg-gradient-to-r from-blue1000 to-blue900 bg-200%  rounded-3xl text-center w-full shadow-whyOneassure overflow-hidden md:mb-14 mb-5">
      <div className="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={570} // Increased width
          height={570} // Increased height
          objectFit="cover"
        />
      </div>

      <div className="absolute bottom-0 md:left-12 left-0 transform -translate-x-1/2 translate-y-1/2">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={570}
          height={570}
          objectFit="cover"
        />
      </div>

      <p className="pt-6 md:text-3xl text-lg font-medium text-white">
        {data.title}
      </p>
      {/* @ts-expect-error unknown type */}
      {parse(data.description, { replace })}

      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4 justify-items-center">
        {data.sectionData.map((r, index) => (
          <div key={index} className="flex flex-col items-center">
            <div className="w-fit p-5 rounded-full bg-white shadow-md flex justify-center items-center mb-4">
              <Image
                src={r?.icon?.data?.attributes?.url || ""}
                alt="image"
                width={100}
                height={100}
                className="size-10"
              />
            </div>

            <div className="mb-2 text-center text-white">
              <p className="md:text-[24px]/[30px] text-base font-medium">
                {r.title}
              </p>
            </div>
            <div>
              {/* @ts-expect-error unknown type */}
              {parse(r.description, { replace })}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WhyBuyTI;
