import React, { Suspense } from "react";
import Image from "next/image";
import ProductDropdown from "../../globals/InsuranceAllProducts/ProductDropdown";
import { LPHero as HeroType } from "../../HealthInsurance/types";
import { Dispatch, SetStateAction } from "react";
import parse, { Element } from "html-react-parser";

type InsuranceType = "health" | "term";
interface HeroProps {
  data: HeroType;
  setOpenModal: Dispatch<SetStateAction<boolean>>;
  category: string;
  companyData: any;
}

const Hero: React.FC<HeroProps> = ({ data, category, companyData }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      // if (attrs.style) {
      //   attrs.style = "";
      // }
      if (domNode.name === "strong") {
      }
      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black-1 text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black-1 font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black-1 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[16px]/[22px] md:text-[18px]/[24px] text-ntrl-black-1 font-light";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );
        case "strong":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[24px] text-ntrl-black-1 font-semibold";
          break;

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  return (
    <div className="mt-4 md:mb-14 mb-5">
      <div className="grid grid-cols-1 md:grid-cols-2 items-center">
        <div className="">
          {/* Left Side */}
          <div className="md:text-[60px]/[70px] text-black mb-4">
            <h1 className="text-ntrl-black-1 font-bold text-[24px]/[36px] text-start mb-3 md:text-[60px]/[70px] md:w-[60%]">
              {data.title.split(" ").map((word, index, array) => {
                const isLastTwoWords = index >= array.length - 2;
                return (
                  <span
                    key={index}
                    className={isLastTwoWords ? "text-secondary-green-3" : ""}
                  >
                    {word}{" "}
                  </span>
                );
              })}
            </h1>
          </div>

          {/* {data?.highlights?.map((h, idx) => (
            <div className="flex items-center" key={idx}>
              <h5 className="font-[24px]/[30px] font-normal text-ntrl-black-1">
                {h.highlight}
              </h5>
            </div>
          ))} */}
        </div>

        {/* Right Side */}
        <div className="flex flex-col md:pl-6 justify-between gap-4">
          <h2 className="text-black100 font-medium text-[18px]/[30px] font-manrope md:mt-12 mt-4 text-start">
            {/* @ts-expect-error unknown type */}
            {parse(data.subTitleRichText, { replace })}
          </h2>
          <Suspense>
            <ProductDropdown
              category={category as InsuranceType}
              companyData={companyData}
            />
          </Suspense>

          {/* Highlights Section */}
        </div>
      </div>
    </div>
  );
};

export default Hero;
