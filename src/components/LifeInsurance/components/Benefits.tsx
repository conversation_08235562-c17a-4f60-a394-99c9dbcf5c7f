import Container from "@/components/globals/Container";
import Image from "next/image";
import { title } from "process";
import { Benefits as TBenefits } from "../types";
import parse, { attributesToProps } from "html-react-parser";
import { Element } from "html-react-parser";

const Benefits = ({ data }: { data: TBenefits }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-blue400 text-3xl font-semibold my-5";
          break;

        case "h3":
          attrs.className = "text-blue400 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-blue400 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;

        case "table":
          attrs.className =
            "mx-auto font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5 drop-shadow-lg";
          break;

        case "tr":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;
        case "td":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;
      }
      return domNode;
    }
  };
  return (
    <div className="md:py-20 py-10">
      <Container>
        {/* Heading */}
        <div className="mb-12">
          <div className="font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black mb-4">
            <h2 className="font-normal text-center md:text-left">
              {"Benefits of "}
              <span className="font-medium">Term Insurance</span>
            </h2>
          </div>

          <p className="md:text-[18px]/[28px] text-[16px]/[24px] text-ntrl-grey1">
            {`Long-term life insurance benefits your long-term financial goals. `}
          </p>
        </div>

        <div className="flex flex-col md:flex-row items-center justify-center md:justify-start">
          {/* Photo here */}
          <div className="rounded-xl min-h-[470px] md:min-w-[496px] w-[100%] relative">
            <Image
              src="https://cdn.oasr.in/oa-site/cms-uploads/media/health_expert_f133c9709e.jpg"
              fill={true}
              style={{ objectFit: "cover" }}
              alt="about-team-member"
              className="rounded-xl"
            />
          </div>

          {/* Use same Speak to Expert cards here as used in carousel */}
          <div className="mt-6 md:mt-0 md:ml-7 ml-0  grid grid-cols-1 md:grid-cols-3 gap-5">
            {data.benefit.map((b, idx) => (
              <div
                className="rounded-lg p-5 border border-ntrl-outline bg-ntrl-white mb-6 bg-primary-3"
                key={idx}
              >
                <div className="w-10 h-10 rounded-full bg-primary-1 flex items-center justify-center mb-6">
                  <Image
                    src={b.icon.data.attributes.url}
                    width={20}
                    height={20}
                    alt="health-experts"
                  />
                </div>

                <h2 className="font-generalSans font-medium text-ntrl-black text-[24px]/[30px] mb-6">
                  {b.title}
                </h2>

                <div className="no-underline">
                  {/* @ts-ignore */}
                  {parse(b.subtitle, { replace })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default Benefits;
