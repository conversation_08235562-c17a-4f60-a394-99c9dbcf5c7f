import Image from "next/image";
import parse, { Element } from "html-react-parser";
import Accordian from "@/components/globals/Accordian";
const Eligibility: React.FC<{
  id: string;
  eligibility: {
    title: string;
    description: string;
  }[];
}> = (props) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }
      if (domNode.name === "strong") {
      }
      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[16px]/[30px] md:text-[16px]/[24px] text-ntrl-black font-light";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-black font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );
        case "strong":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[24px] text-ntrl-black font-semibold";
          break;

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  const generateEligibilityHtml = (eligibility: {
    title: string;
    description: string;
  }): string => {
    if (eligibility.description.startsWith("<p>")) {
      return `<p><strong>${
        eligibility.title
      } - </strong>${eligibility.description.slice(3)}</div>`;
    } else {
      return `<strong>${eligibility.title} - </strong>${eligibility.description}`;
    }
  };
  return (
    <section
      id={props.id}
      className="mt-5 md:px-[50px] md:py-7 scroll-m-28 bg-white rounded-3xl"
    >
      <div className="hidden md:flex text-custom-32 text-ntrl-black gap-2 items-center py-2">
        <h2 className="font-semibold">Eligibility</h2>
      </div>
      <div className="hidden md:flex flex-col rounded-3xl border-[0.5px] border-black md:mt-2 md:mb-3">
        {props.eligibility.map((el, index) => (
          <div
          key={index}
            className={`flex items-center border-black ${
              props.eligibility.length !== index + 1 ? "border-b-[0.5px]" : ""
            }`}
          >
            <div className="w-1/2 pl-12 py-4 border-r-[0.5px] border-black">
              <h3 className="text-[16px]/[30px] font-medium">
                • {el.title} :{" "}
              </h3>
            </div>
            <div className="w-1/2 pl-12">
              {/* @ts-ignore */}
              {parse(el.description, { replace })}
            </div>
          </div>
        ))}
      </div>
      <Accordian
        name="Eligibility"
        className="md:hidden"
        listStyle={true}
        description={`<ul>${props.eligibility
          .map((el, index) => {
            return `<li>${generateEligibilityHtml(el)}</li>`;
          })
          .join("")}</ul>`}
      />
    </section>
  );
};

export default Eligibility;
