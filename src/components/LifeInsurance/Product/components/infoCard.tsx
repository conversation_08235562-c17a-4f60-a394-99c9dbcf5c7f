import Image from "next/image";
import parse, { Element } from "html-react-parser";

const InfoCard: React.FC<{
  title: string;
  data: { title?: string; description: string }[];
  icon?: "WOMEN" | "CIGARETTE";
  listStyle?: boolean;
}> = (props) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }
      if (domNode.name === "strong") {
      }
      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[14px]/[24px] md:text-[16px]/[26px] text-ntrl-black font-light";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-black font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );
        case "strong":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[30px] text-ntrl-black font-semibold";
          break;

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  const transformHTML = (note: { title?: string; description: string }) => {
    if (note.title) {
      if (note.description.startsWith("<p>")) {
        return `<p><strong>${note.title}: </strong>${note.description.slice(
          3
        )}`;
      } else {
        return `<strong>${note.title}: </strong>${note.description}`;
      }
    } else {
      return note.description;
    }
  };
  const womenIMG =
    "https://cdn.oasr.in/oa-site/cms-uploads/media/famicons_woman_outline_fdd89bfcb1.png";
  const cigaretteIMG =
    "https://cdn.oasr.in/oa-site/cms-uploads/media/cil_smoke_b8bd7f29e4.png";
  return (
    <section className="mt-5 p-6 md:px-[50px] md:py-7 scroll-m-28 bg-sky-grey rounded-3xl">
      <div className="flex text-ntrl-black gap-2 items-center py-2">
        {props.icon && (
          <Image
            className="h-8 w-8"
            width={100}
            height={100}
            src={props.icon === "WOMEN" ? womenIMG : cigaretteIMG}
            alt={props.icon}
          />
        )}
        <h2 className="font-semibold text-[24px]/[36px]">{props.title}</h2>
      </div>
      <div className="flex flex-col">
        <ol>
          {props.data.map(({ title, description }, index) => (
            <li key={index} className={`${props.listStyle ? "list-disc ml-5" : ""}`}>
              <div className="ml-2 py-2">
                {/* @ts-ignore */}
                {parse(transformHTML({ title, description }), { replace })}
              </div>
            </li>
          ))}
        </ol>
      </div>
    </section>
  );
};

export default InfoCard;
