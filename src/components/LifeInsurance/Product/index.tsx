"use client";

import { Suspense, useEffect } from "react";
import SideMenu from "@/components/globals/SideMenu";
import Hero from "@/components/globals/Hero";
import Verdict from "@/components/globals/Products/Verdict";
import About from "@/components/globals/Products/About";
import Features from "@/components/globals/Products/Features";
import Exclusions from "@/components/globals/Products/Exclusions";
import AddOns from "@/components/globals/Products/AddOns";
import WhyOneAssure from "@/components/globals/Products/WhyOneAssure";
import BookACall from "@/components/globals/Products/BookACall";
import Faqs from "@/components/globals/Products/Faq";
import { RelatedBlogs } from "@/components/globals/Products/RelatedBlogs";
import { TermVariantData } from "../types";
import Eligibility from "./components/Eligibility";
import InfoCard from "./components/infoCard";
import Container from "@/components/globals/Container";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import { useSessionStorage } from "usehooks-ts";
import Breadcrumb from "@/components/globals/Breadcrumb";
import Plans from "@/components/Company/components/Plans";

const TermProductRoot: React.FC<{
  product: TermVariantData;
  category?: string;
}> = (props) => {
  const { product, category = "term" } = props;
  const menuItems = [
    ...(product.verdict ? [{ title: "Our Verdict", id: "verdict" }] : []),
    ...(product.aboutThePlan
      ? [{ title: "About the Plan", id: "aboutThePlan" }]
      : []),
    ...(product.eligibility.length > 0
      ? [{ title: "Eligibility", id: "eligibility" }]
      : []),
    ...(product.features.length > 0
      ? [{ title: "Features", id: "features" }]
      : []),
    ...(product.exclusions.length > 0
      ? [{ title: "Exclusions", id: "exclusions" }]
      : []),
    ...(product.addOns.length > 0 ? [{ title: "Add-Ons", id: "addOns" }] : []),
    ...(product.variants.length > 0
      ? [{ title: "Plan Variants", id: "variants" }]
      : []),
    ...(product.company?.data?.attributes?.legecy
      ? [{ title: "About the Company", id: "aboutTheCompany" }]
      : []),
    ...(product.faqs.length > 0 ? [{ title: "FAQs", id: "faqs" }] : []),
    ...(product.blogs?.data.length > 0
      ? [{ title: "Related Blogs", id: "relatedBlogs" }]
      : []),
  ];

  const pathname = usePathname();
  const router = useRouter();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      router.replace(
        `${pathname}?${utm_source !== null ? `utm_source=${utm_source}` : ""}${
          utm_medium !== null ? `&utm_medium=${utm_medium}` : ""
        }${utm_campaign !== null ? `&utm_campaign=${utm_campaign}` : ""}${
          utm_content !== null ? `&utm_content=${utm_content}` : ""
        }${utm_term !== null ? `&utm_term=${utm_term}` : ""}`
      );
    }
  }, []);

  const transformedData = product.variants.map((variant: any, idx: number) => {
    const variantSlug = variant.relatedVariant.data.attributes.slug;
    const variantName = variant.relatedVariant.data.attributes.name;
    const companyLogo =
      variant.relatedVariant.data.attributes.company.data.attributes.logo.data
        .attributes.url;
    const companyName =
      variant.relatedVariant.data.attributes.company.data.attributes.name;
    const companySlug =
      variant.relatedVariant.data.attributes.company.data.attributes.slug;
    return {
      id: idx,
      attributes: {
        name: variantName,
        slug: variantSlug,
        logo: companyLogo,
        companyName: companyName,
        companySlug: companySlug,
      },
    };
  });

  return (
    <div className="px-5 lg:px-[100px] bg-ntrl-white md:bg-soft-grey font-manrope pb-5 tracking-tightest border-t-blue-5 md:border-0 border-[.5px]">
      <Container>
        <div className="pt-3 pb-1 md:py-5">
          <Suspense>
            <Breadcrumb
              path={[
                "home",
                "term-insurance",
                product.company.data.attributes.slug,
                product.slug,
              ]}
            />
          </Suspense>
        </div>
        <div className="grid grid-cols-12 gap-5">
          <SideMenu
            menuItems={menuItems}
            classsName={menuItems.length < 3 ? "lg:hidden" : ""}
          />
          <div
            className={`col-span-12 ${
              menuItems.length < 3 ? "lg:col-span-12" : "lg:col-span-10"
            }`}
          >
            <Suspense>
              <Hero
                name={product.name}
                hero={product.hero}
                company={product.company.data.attributes}
                category={category}
              />
            </Suspense>
            {product.verdict && (
              <Verdict
                oneAssureVerdict={product.verdict}
                docs={product.policyDocs}
              />
            )}
            {product.aboutThePlan && (
              <About
                id="aboutThePlan"
                title="About the Plan"
                about={product.aboutThePlan}
              />
            )}
            {product.eligibility.length > 0 && (
              <Eligibility id="eligibility" eligibility={product.eligibility} />
            )}
            {product.features.length > 0 && (
              <Features features={product.features} />
            )}
            {product.exclusions.length > 0 && (
              <Exclusions exclusions={product.exclusions} />
            )}
            {product.addOns.length > 0 && <AddOns addOns={product.addOns} />}
            {product.variants.length > 0 && (
              <Plans
                title="Related Variants"
                variants={transformedData}
                category={category}
              />
            )}
            {product.womenBenifits.length > 0 && (
              <InfoCard
                icon="WOMEN"
                listStyle={true}
                title="If you are a woman"
                data={product.womenBenifits}
              />
            )}
            {product.smokingDownsides.length > 0 && (
              <InfoCard
                icon="CIGARETTE"
                title="If you smoke"
                listStyle={true}
                data={product.smokingDownsides}
              />
            )}
            {product.notes.length > 0 && (
              <InfoCard title="Note :" data={product.notes} />
            )}
            <div className="grid gap-x-8 grid-cols-5">
              <BookACall
                classname={` col-span-5  p-5 ${
                  menuItems.length < 3 ? "" : "lg:col-span-3"
                }`}
              />
              {product.whyOneAssure.length > 0 && (
                <WhyOneAssure
                  className="col-span-5 lg:col-span-2"
                  data={product.whyOneAssure}
                />
              )}
            </div>
            {product.company?.data?.attributes?.legecy && (
              <About
                id="aboutTheCompany"
                title="About the Company"
                about={product.company.data.attributes.legecy}
              />
            )}
            {product.faqs.length > 0 && (
              <Faqs title="Frequency Asked Questions" faqs={product.faqs} />
            )}
            {product.blogs?.data.length > 0 && (
              <RelatedBlogs blogs={product.blogs} />
            )}
          </div>
        </div>
      </Container>
    </div>
  );
};
export default TermProductRoot;
