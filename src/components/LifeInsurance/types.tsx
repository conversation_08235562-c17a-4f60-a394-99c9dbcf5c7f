import { Meta } from "@/types";
import { LPSectionData, LPHero, LPTestimonial } from "../HealthInsurance/types";
import { Blog, PolicyDocs, RelatedVariants } from "../globals/types";
import { VariantFeatures } from "../globals/types";
import { VariantCompany } from "../globals/types";

// Reuse existing types
type Thumbnail = {
  data: {
    id: number;
    attributes: {
      url: string;
    };
  };
};

export type Highlight = {
  id: number;
  highlight: string;
};

type WhatIsTI = {
  id: number;
  title: string;
  description: string;
};

export type HowDoesTIWorks = {
  id: number;
  title: string;
  description: string;
};

type BenefitsOfTI = {
  id: number;
  title: string;
  description: string;
  sectionData: LPSectionData[];
};

export type WhoShouldBuyTI = {
  id: number;
  title: string;
  description: string;
  sectionData: LPSectionData[];
};

export type WhyToBuyTI = {
  id: number;
  title: string;
  description: string;
  sectionData: LPSectionData[];
};

export type HowToBuyTI = {
  id: number;
  title: string;
  description: string;
  sectionData: LPSectionData[];
};

export type RiderForTI = {
  id: number;
  title: string;
  description: string;
  sectionData: LPSectionData[];
};

type HowToBuyTIOneAssure = {
  id: number;
  title: string;
  description: string;
  sectionData: LPSectionData[];
};

export type TIEligibility = {
  id: number;
  title: string;
  description: string | null;
  sectionData: LPSectionData[];
};

type RequiredDocsTI = {
  id: number;
  title: string;
  description: string;
  sectionData: LPSectionData[];
};

type TIAttributes = {
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  hero: LPHero & {
    highlights: Highlight[];
  };
  testimonials: {
    id: number;
    testimonial: LPTestimonial[];
  };
  whatIsTI: WhatIsTI;
  HowDoesTIWorks: HowDoesTIWorks;
  benefitsOfTI: BenefitsOfTI;
  whoShouldBuyTI: WhoShouldBuyTI;
  whyToBuyTI: WhyToBuyTI;
  howToBuyTI: HowToBuyTI;
  riderForTI: RiderForTI;
  howToBuyTIOneAssure: HowToBuyTIOneAssure;
  TIEligibility: TIEligibility;
  requiredDocsTI: RequiredDocsTI;
};

export type TermInsuranceLPDataObject = {
  id: number;
  attributes: TIAttributes;
  meta: Meta;
};
export type TermVariantResponse = {
  data: [
    {
      id: number;
      attributes: TermVariantData;
    }
  ];
};

export type TermVariantData = {
  name: string;
  slug: string;
  verdict: string;
  aboutThePlan: string;
  company: {
    data: {
      attributes: VariantCompany;
    };
  };
  faqs: {
    question: string;
    ans: string;
  }[];
  whyOneAssure: {
    title: string;
    description: string;
  }[];
  features: VariantFeatures;
  blogs: { data: [{ attributes: Blog }] };
  exclusions: {
    exclusion: string;
  }[];
  addOns: {
    title: string;
    description: string;
  }[];
  eligibility: {
    title: string;
    description: string;
  }[];
  hero: TermVariantHero;
  policyDocs: PolicyDocs;
  variants: RelatedVariants;
  womenBenifits: {
    description: string;
  }[];
  smokingDownsides: {
    description: string;
  }[];
  notes: {
    title: string;
    description: string;
  }[];
  seo?: {
    metaTitle: string;
    metaDescription: string;
    keyword: string;
  };
};
export type TermVariantHero = {
  title: string;
  claimSettlementRatio: number;
};

type IconData = {
  id: number;
  attributes: {
    url: string;
  };
};

export type Hero = {
  id: number;
  title: string;
  subTitle: string;
  getQuoteUrl: string;
  highlights: Highlight[];
};

export type AboutTI = {
  id: number;
  title: string;
  description: string;
  thumbnail: Thumbnail;
};

type ImportanceItem = {
  id: number;
  title: string;
  subTitle: string;
  thumbnail: Thumbnail;
};

export type Importance = {
  id: number;
  whyBuyTI: ImportanceItem[];
};

type Rider = {
  id: number;
  title: string;
  description: string;
};

export type Riders = {
  id: number;
  termRiders: Rider[];
};

type FAQItem = {
  id: number;
  question: string;
  ans: string;
};

type FAQs = {
  id: number;
  faq: FAQItem[];
};

export type HowTiWork = {
  id: number;
  description: string;
};

export type Benefits = {
  id: number;
  benefit: {
    id: number;
    title: string;
    subtitle: string;
    icon: {
      data: {
        id: number;
        attributes: {
          url: string;
        };
      };
    };
  }[];
};

export type WhoBuysTi = {
  id: number;
  description: string;
};

export type HowToBuy = {
  id: number;
  howToBuyFeat: {
    title: string;
    description: string;
  }[];
};

export type Eligibility = {
  id: number;
  description: string;
};

export type Documents = {
  id: number;
  documentItem: {
    id: number;
    title: string;
  }[];
};

export type BuyFromOA = {
  id: number;
  step: {
    id: number;
    title: string;
    description: string;
  }[];
};

export type Testimonials = {
  id: number;
  testimonial: {
    id: number;
    name: string;
    statement: string;
    backgroundColor: string;
    thumbnail: {
      data: {
        id: number;
        attributes: {
          url: string;
        };
      };
    };
  }[];
};

export type WhyBuyTi = {
  id: number;
  reason: {
    id: number;
    title: string;
    desc: string;
    backgroundColor: string;
    thumbnail: {
      data: {
        id: number;
        attributes: {
          url: string;
        };
      };
    };
  }[];
};

export type WhatWeOffer = {
  id: number;
  offer: {
    id: number;
    title: string;
    subTitle: string;
    icon: {
      data: [
        {
          id: number;
          attributes: {
            url: string;
          };
        }
      ];
    };
  }[];
};
