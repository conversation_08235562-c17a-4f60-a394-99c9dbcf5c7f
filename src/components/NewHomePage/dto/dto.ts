import { HomePageType } from "../types";
import { HomePageApiResponse } from "./types";

const transformData = (data: HomePageApiResponse): HomePageType => {

  const homePageData = data.site_home_page[0];

  const marqueeImages: string[] = [];
  data.health_insurers.forEach((insurer) => {
    marqueeImages.push(insurer.logo_url);
  });
  data.term_insurers.forEach((insurer) => {
    marqueeImages.push(insurer.logo_url);
  });

  const healthInsurers = data.health_insurers.map((insurer)=>{
    const productVariants: {id: string, temp_slug: string, variant_name: string}[] = [];
    insurer.products.forEach((product) => {
      product.product_variants.forEach((variant) => {
        productVariants.push({id: variant.id, temp_slug: variant.temp_slug, variant_name: variant.variant_name});
      });
    });
    return {
      logo_url: insurer.logo_url,
      temp_slug: insurer.temp_slug,
      name: insurer.name,
      id: insurer.id,
      product_variants: productVariants,
    };
  })

  const termInsurers = data.term_insurers.map((insurer)=>{
    const productVariants: {id: string, temp_slug: string, variant_name: string}[] = [];
    insurer.products.forEach((product) => {
      product.product_variants.forEach((variant) => {
        productVariants.push({id: variant.id, temp_slug: variant.temp_slug, variant_name: variant.variant_name});
      });
    });
    return {
      logo_url: insurer.logo_url,
      temp_slug: insurer.temp_slug,
      name: insurer.name,
      id: insurer.id,
      product_variants: productVariants,
    };
  })

  const HeroSection = {
    title: homePageData.hero_title,
    spanText: homePageData.hero_tagline,
    subtitle: homePageData.hero_subtitle,
    marqueeImages,
    insuranceTypes: [
      {
        type: "health",
        insurers: healthInsurers,
      },
      {
        type: "term",
        insurers: termInsurers,
      },
    ],
  }

  const whyToChooseUs = {
    pill_Content: homePageData.home_page_why_choose_us?.pill_content,
    title: homePageData.home_page_why_choose_us?.title,
    cards: homePageData.home_page_why_choose_us?.home_page_why_choose_us_cards.map((card) => {
      return {
        title: card.title,
        icon: card.icon_url,
        description: card.description,
      };
    }),
  };

  const howOneAssureWork = {
    pill_Content: homePageData.home_page_how_oneassure_works?.pill_content,
    title: homePageData.home_page_how_oneassure_works?.title,
    cards: homePageData.home_page_how_oneassure_works?.home_page_how_oneassure_works_cards.map((card) => {
      return {
        title: card.title,
        description: card.description,
        icon: card.icon_url,
      };
    }),
  };

  const askAnythingSection = {
    pill_Content: homePageData.home_page_ai_chat_section?.pill_content ?? "",
    title: homePageData.home_page_ai_chat_section?.title ?? "",
    subheading: homePageData.home_page_ai_chat_section?.description ?? "",
    slides:
      homePageData.home_page_ai_chat_section?.home_page_ai_chat_cards?.map(
        (card) => ({
          title: card.title,
          subtitle: card.subtitle,
          chats: card.home_page_ai_card_chats?.map((chat) => ({
            id: chat.id,
            type: chat.type,
            content: chat.content,
          })) ?? [],
        })
      ) ?? [],
  };

  const testimonialSection = {
    pill_Content: homePageData.home_page_testimonial_section?.pill_content,
    title: homePageData.home_page_testimonial_section?.title,
    story: {
      title: homePageData.home_page_testimonial_section?.home_page_testimonial_story.title,
      points: homePageData.home_page_testimonial_section?.home_page_testimonial_story.points,
    },
    testimonials: homePageData.home_page_testimonial_section?.home_page_testimonials.map((testimonial) => {
      return {
        name: testimonial.name,
        content: testimonial.content,
        video_url: testimonial.video_url,
      };
    }),
  };

  const meetTheTeam = {
    title: homePageData.home_page_meet_the_team?.title,
    image: homePageData.home_page_meet_the_team?.image_url,
    description: homePageData.home_page_meet_the_team?.description,
  };
  return {
    HeroSection,
    whyToChooseUs,
    howOneAssureWork,
    askAnythingSection,
    testimonialSection,
    meetTheTeam,
  };
};

export default transformData;
