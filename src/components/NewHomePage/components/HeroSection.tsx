import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import { Button } from "@/components/UI/Button";
import Marquee from "react-fast-marquee";
import {
  HeadingSmall,
  HeadingXLarge,
  HeadingXLarge2,
} from "@/components/UI/Typography";
import Image from "next/image";
import { orufyHandler } from "@/utils/orufyHandler";
import { useSessionStorage } from "usehooks-ts";
import { useState } from "react";
import GetYourPlansModal from "./GetYourPlansModal";
import { htmlParser } from "@/utils/htmlParser";
import SectionContainer from "@/components/globals/SectionContainer";

type InsuranceTypes = {
  type: string;
  insurers: {
    id: string;
    logo_url: string;
    temp_slug: string;
    name: string;
    product_variants: {
      id: string;
      temp_slug: string;
      variant_name: string;
    }[];
  }[];
};

export type HeroSectionProps = {
  title: string;
  spanText: string;
  subtitle: string;
  marqueeImages: string[];
  insuranceTypes: InsuranceTypes[];
};

const HeroSection = ({
  title,
  spanText,
  subtitle,
  marqueeImages,
  insuranceTypes,
}: HeroSectionProps) => {
  // utms
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);
  const [openModal, setOpenModal] = useState(false);

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const images = marqueeImages;

  function bookACall() {
    const orufyLink = `${process.env.NEXT_PUBLIC_ONE_ASSURE_ORUFY_LINK}?utm_source=${utm_source}&utm_medium=${utm_medium}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&utm_term=${utm_term}`;
    orufyHandler(orufyLink);
  }

  return (
    <div className="md:mt-[-122px] relative overflow-x-clip mb-20 md:mb-40">
      <div className="relative bg-primary-100 md:pt-[122px]">
        <SectionContainer className="flex relative pt-6 md:pt-28 md:pb-64 md:!px-4">
          <div className="w-full md:w-1/2 flex flex-col items-center md:items-start">
            {/* <HeadingXLarge className=""> */}
            {htmlParser(title, {
              components: {
                p: (props: any) => <HeadingXLarge {...props} as="h1" />,
                em: (props: any) => <HeadingXLarge {...props} />,
              },
              classNames: {
                p: "text-neutral-1100 font-medium text-center md:text-left text-3xl leading-[36px] md:leading-[48px] mb-3 md:mb-8",
                em: "text-secondary-400 text-3xl",
              },
            })}
            {/* </HeadingXLarge> */}
            <HeadingSmall className="font-medium text-neutral-1100 text-center md:text-left mb-6 md:mb-8">
              {subtitle}
            </HeadingSmall>
            <div className="flex gap-4 md:gap-8">
              <div
                className="relative inline-block p-0.5 group z-50 cursor-pointer"
                onClick={bookACall}
              >
                <div
                  style={{
                    filter: "url(#squiggly)",
                  }}
                  className="absolute inset-0 border-[3px] border-primary-400 rounded-2xl z-10 pointer-events-none transition-all duration-300 group-hover:scale-105"
                />
                <Button
                  variant="primary"
                  className="relative cursor-pointer px-4 sm:px-6 py-4 sm:py-6 rounded-xl transform transition-transform group-hover:scale-105 group-hover:bg-primary-700"
                >
                  Talk To A Human
                </Button>
              </div>

              <div
                onClick={() => setOpenModal(true)}
                className="relative inline-block p-0.5 group z-50 cursor-pointer"
              >
                <div
                  style={{ filter: "url(#squiggly)" }}
                  className="absolute inset-0 border-[3px] border-primary-800 z-10 rounded-2xl pointer-events-none transition-all duration-300 group-hover:scale-105"
                />
                <Button
                  variant="secondary"
                  className="relative px-4 sm:px-6 py-4 sm:py-6 rounded-xl bg-white text-primary-800 transition-all duration-300 group-hover:scale-105 group-hover:bg-neutral-100"
                >
                  Get Your Plan
                </Button>
              </div>
            </div>

            {/* Image below buttons on small screens */}
            <div
              className="md:hidden w-full flex justify-start h-[250px] bg-contain bg-center bg-no-repeat"
              style={{
                backgroundImage:
                  "url('https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a72e7-0527-747f-9c88-618775e15b73/mobile-hero.png')",
              }}
            >
              {/* <Image
                src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a72f6-57bd-7338-bc76-3483de7b91ce/hero hero 22.png"
                alt=""
                width={335}
                height={219}
                className="w-full object-contain mt-[-20px] pb-4"
              /> */}
            </div>
          </div>

          {/* Image positioned absolutely on medium+ screens */}
          <Image
            src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a7516-97ae-7579-a405-c086ac768779/1345a0c4af468729669e2735d97454901ea0ed7a(2).png"
            alt=""
            width={1215}
            height={861}
            className="hidden md:block absolute bottom-0 left-1/2 -translate-x-[40%] z-0 w-auto"
          />
        </SectionContainer>
        <SectionContainerLarge className="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 border border-primary-300 rounded-xl bg-white py-2.5 md:py-8 !w-[calc(100%-2rem)] !px-0 !mb-0">
          <div>
            <HeadingSmall className="font-medium text-center text-primary-800 mb-4 md:mb-8">
              {"Backed By India's Most Trusted Insurers"}
            </HeadingSmall>
            <Marquee className="overflow-hidden" speed={100} gradient={true}>
              {images.map((src, index) => (
                <div
                  key={index}
                  className="relative w-16 md:w-40 h-8 md:h-10 bg-white mx-4 md:mx-8"
                >
                  <Image
                    src={src}
                    alt={`Insurance partner ${index + 1}`}
                    className="object-contain"
                    fill
                  />
                </div>
              ))}
            </Marquee>
          </div>
        </SectionContainerLarge>
      </div>
      {openModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={handleCloseModal}
        >
          <GetYourPlansModal
            onClose={handleCloseModal}
            title={"Know More About your Plans"}
            subtitle={"Know More About your Plans"}
            insuranceTypes={insuranceTypes}
            buttonText="Get Your Plans"
          />
        </div>
      )}
    </div>
  );
};

export default HeroSection;
