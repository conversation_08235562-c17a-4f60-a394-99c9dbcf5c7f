import {
  BodyMedium,
  DisplayMedium,
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import React from "react";
import Image from "next/image";
import { htmlParser } from "@/utils/htmlParser";
import SectionContainer from "@/components/globals/SectionContainer";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";

type MeetOurTeamProps = {
  title?: string;
  image: string;
  description?: string;
};

const MeetOurTeam: React.FC<MeetOurTeamProps> = ({
  title = "Meet The Team",
  image,
  description = "",
}) => {
  return (
    <SectionContainerLarge className="!mb-9 md:!mb-24 flex flex-col  overflow-hidden ">
      <SectionContainerLarge className="!mb-8 md:!mb-14">
        <HeadingXLarge className="text-primary-800 text-center font-semibold mb-2 md:mb-4">
          {title}
        </HeadingXLarge>
        <HeadingSmall>
          {description &&
            htmlParser(description, {
              components: {
                p: BodyMedium,
              },
              classNames: {
                p: "text-primary-800 text-center font-regular",
              },
            })}
        </HeadingSmall>
      </SectionContainerLarge>
      {/* Title */}

      {/* Team Image */}
      <SectionContainerMedium className="relative !px-0 md:px-0 !mb-0 md:!mb-0">
        <Image
          src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a72e8-c18b-7583-8bce-4316072eed83/Group 1272628257.svg"
          alt="blue-box"
          width={568}
          height={0}
          className="hidden md:block absolute -top-8 -left-8 z-0"
        />
        <Image
          src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a7330-23e1-7ec9-a71a-dbd9fc2e3d6e/Group 1272628258.svg"
          alt="blue-box"
          width={166}
          height={84}
          className="md:hidden absolute mx-6 -top-4 -left-10 z-0"
        />
        <div className="relative z-10 w-full h-[540px] rounded-xl shadow-lg overflow-hidden hidden md:block">
          <Image
          src={image}
          alt="our-team"
          fill
          className="object-cover"
        />
        </div>
        <div className="relative z-10 w-full h-[182px] rounded-xl shadow-lg overflow-hidden md:hidden">
          <Image
          src={image}
          alt="our-team"
          fill
          className="object-cover"
        />
        </div>
        </SectionContainerMedium>
     
     
    </SectionContainerLarge>
  );
};

export default MeetOurTeam;
