import React, { useState, useEffect } from "react";
import { MessageType } from "@/components/NewHomePage/types";
import { PolicyUpload } from "@/components/NewHomePage/components/Chat/PolicyUpload";
import { PolicySelection } from "@/components/NewHomePage/components/Chat/PolicySelection";
import { MarkdownMessage } from "@/components/NewHomePage/components/Chat/MarkdownMessage";
import { VerificationRequired } from "@/components/NewHomePage/components/Chat/VerificationRequired";
import { PolicyDocumentChoice } from "@/components/NewHomePage/components/Chat/PolicyDocumentChoice";
import { ProductCatalog } from "@/components/NewHomePage/components/Chat/ProductCatalog";
import PolicySuggestions from "@/components/NewHomePage/components/Chat/PolicySuggestions";
import { Policy } from "@/components/Chat/api/policyFetch";
import { Product } from "@/components/Chat/api/catalogFetch";
import { <PERSON>a<PERSON><PERSON>, FaCheck } from "react-icons/fa";

export const Message = React.memo(function Message({
  msg,
  onSelect,
  onUploadComplete,
  onPolicySelected,
  onVerifyPhone,
  onPolicyDocumentChoice,
  onProductSelected,
  onSuggestionClick,
  isProductSelected = false,
  isSuggestionSelected = false, // keep for compatibility, but not used for PolicySuggestions
  suggestionsContext = "policy",
  isSuggestionsDisabled = false,
  customerSelected = false,
  productSelectionCompleted = false,
  policyDocumentChoiceMade = false,
  policySelectionCompleted = false,
}: {
  msg: MessageType;
  onSelect?: (type: "existing" | "new") => void;
  onUploadComplete?: (
    file: File,
    documentKey?: string,
    policyDetails?: any
  ) => void;
  onPolicySelected?: (policy: Policy) => void;
  onVerifyPhone?: (phoneNumber?: string) => void;
  onPolicyDocumentChoice?: (hasDocument: boolean) => void;
  onProductSelected?: (product: Product & { insurer_name: string }) => void;
  onSuggestionClick?: (suggestion: string) => void;
  isProductSelected?: boolean;
  isSuggestionSelected?: boolean;
  suggestionsContext?: "policy" | "product";
  isSuggestionsDisabled?: boolean;
  customerSelected?: boolean;
  productSelectionCompleted?: boolean;
  policyDocumentChoiceMade?: boolean;
  policySelectionCompleted?: boolean;
}) {
  // Define all hooks at the component level to maintain consistent order
  const [showFeedback, setShowFeedback] = useState(false);
  const [feedback, setFeedback] = useState<"helpful" | "not-helpful" | null>(
    null
  );
  const [isCopied, setIsCopied] = useState(false);

  // Reset copy state after 1.5 seconds
  useEffect(() => {
    if (isCopied) {
      const timer = setTimeout(() => {
        setIsCopied(false);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [isCopied]);

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setIsCopied(true);
    });
  };

  if (msg.type === "ai" || msg.type === "ai-markdown" || msg.type === "user") {
    const isUser = msg.type === "user";
    const isMarkdown = msg.type === "ai-markdown";

    // Check for component
    const hasComponent =
      !isUser &&
      ((msg.type === "ai" && "component" in msg && msg.component) ||
        (msg.type === "ai-markdown" && "component" in msg && msg.component));

    // Get current time for the message
    const currentTime = new Date().toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });

    return (
      <div
        className={`flex mb-4 ${isUser ? "justify-end" : "justify-start"}`}
        role="listitem"
        aria-label={isUser ? "Your message" : "AI Assistant message"}
      >
        <div className="flex flex-col max-w-[85%]">
          <span className="text-xs text-gray-500 mb-1">
            {isUser ? "You" : "AI Assistant"} {currentTime}
          </span>
          <div
            className={`px-4 py-3 rounded-xl shadow-sm text-xs break-words
            ${
              isUser
                ? "bg-[#1c85c4] text-white"
                : "bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 text-gray-800"
            }
            hover:shadow-sm focus-within:ring-2 focus-within:ring-blue-300
            relative group
          `}
            tabIndex={0}
          >
            {isMarkdown ? (
              <div>
                <MarkdownMessage
                  content={msg.text}
                  className={isUser ? "text-white" : "text-gray-800"}
                />
                {hasComponent && <div className="mt-2">{msg.component}</div>}
              </div>
            ) : (
              <div>
                <div>{msg.text}</div>
                {/* Render custom component if present */}
                {hasComponent && <div className="mt-2">{msg.component}</div>}
              </div>
            )}

            {/* Feedback buttons for AI messages */}
            {!isUser && (
              <div className="mt-2 pt-2 border-t border-gray-200 flex items-center justify-between text-xs opacity-50 group-hover:opacity-100 transition-opacity">
                <div>
                  {!showFeedback ? (
                    <button
                      onClick={() => setShowFeedback(true)}
                      className="text-gray-500 hover:text-gray-700 text-xs transition-colors"
                      aria-label="Rate this response"
                    >
                      Was this helpful?
                    </button>
                  ) : (
                    <div className="flex space-x-3 items-center">
                      <button
                        onClick={() => setFeedback("helpful")}
                        className={`p-1 rounded ${
                          feedback === "helpful"
                            ? "bg-green-100 text-green-700"
                            : "hover:bg-gray-100 text-gray-600"
                        }`}
                        aria-label="Mark as helpful"
                      >
                        👍 Yes
                      </button>
                      <button
                        onClick={() => setFeedback("not-helpful")}
                        className={`p-1 rounded ${
                          feedback === "not-helpful"
                            ? "bg-red-100 text-red-700"
                            : "hover:bg-gray-100 text-gray-600"
                        }`}
                        aria-label="Mark as not helpful"
                      >
                        👎 No
                      </button>
                      {feedback && (
                        <span className="text-gray-500">
                          Thank you for your feedback
                        </span>
                      )}
                    </div>
                  )}
                </div>

                {/* Copy button - only show for markdown messages */}
                {isMarkdown && (
                  <button
                    onClick={() => handleCopy(msg.text)}
                    className="flex items-center gap-1 p-1 rounded text-blue-600 hover:text-blue-800 hover:bg-blue-50 transition-colors"
                    aria-label="Copy message to clipboard"
                    title="Copy to clipboard"
                  >
                    {isCopied ? (
                      <>
                        <FaCheck className="h-3 w-3 text-green-600" />
                        <span className="text-green-600">Copied</span>
                      </>
                    ) : (
                      <>
                        <FaCopy className="h-3 w-3" />
                      </>
                    )}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (msg.type === "select-customer") {
    return (
      <div
        className="flex flex-col items-start mb-4"
        role="listitem"
        aria-label="Customer type selection"
      >
        <div className="flex flex-col max-w-[85%]">
          <span className="text-xs text-gray-500 mb-1">AI Assistant</span>
          <div
            className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 text-gray-800 rounded-xl px-4 py-3 mb-3 text-xs shadow-sm"
            tabIndex={0}
          >
            Are you an <span className="font-medium">Existing Customer</span> or
            a <span className="font-medium">New Customer</span>?
          </div>
        </div>
        <div className="flex gap-3 mt-2">
          <button
            className={`px-5 py-2.5 rounded-xl text-xs font-medium shadow border transition focus:outline-none focus:ring-2 focus:ring-blue-300 ${
              customerSelected
                ? "bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200"
                : "bg-white text-gray-900 border-gray-200 hover:bg-gray-50 hover:shadow-md"
            }`}
            onClick={() => onSelect && onSelect("existing")}
            aria-label="Select existing customer"
            disabled={customerSelected}
          >
            Existing Customer
          </button>
          <button
            className={`px-5 py-2.5 rounded-xl text-xs font-medium shadow border transition focus:outline-none focus:ring-2 focus:ring-blue-300 ${
              customerSelected
                ? "bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200"
                : "bg-white text-gray-900 border-gray-200 hover:bg-gray-50 hover:shadow-md"
            }`}
            onClick={() => onSelect && onSelect("new")}
            aria-label="Select new customer"
            disabled={customerSelected}
          >
            New Customer
          </button>
        </div>
      </div>
    );
  }

  if (msg.type === "loading") {
    return (
      <div
        className="flex justify-start mb-4"
        role="listitem"
        aria-label="Loading response"
      >
        <div className="flex flex-col max-w-[85%]">
          <span className="text-xs text-gray-500 mb-1">AI Assistant</span>
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 text-gray-800 rounded-xl px-5 py-4 text-xs shadow-sm">
            <div className="flex space-x-1.5 justify-center items-center h-4">
              <div className="h-1.5 w-1.5 bg-gray-400 rounded-full animate-pulse [animation-delay:-0.2s]"></div>
              <div className="h-1.5 w-1.5 bg-gray-400 rounded-full animate-pulse [animation-delay:-0.1s]"></div>
              <div className="h-1.5 w-1.5 bg-gray-400 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (msg.type === "policy-upload") {
    return (
      <div
        className="flex justify-start mb-4 w-full"
        role="listitem"
        aria-label="Policy upload interface"
      >
        <div className="flex flex-col w-full max-w-[90%]">
          <span className="text-xs text-gray-500 mb-1">AI Assistant</span>
          <PolicyUpload
            onUploadComplete={(file, documentKey, policyDetails) => {
              if (onUploadComplete)
                onUploadComplete(file, documentKey, policyDetails);
            }}
          />
        </div>
      </div>
    );
  }

  if (msg.type === "policy-selection") {
    return (
      <div
        className="flex justify-start mb-4 w-full"
        role="listitem"
        aria-label="Policy selection interface"
      >
        <div className="flex flex-col w-full max-w-[90%]">
          <span className="text-xs text-gray-500 mb-1">AI Assistant</span>
          <PolicySelection
            phoneNumber={msg.phone}
            onPolicySelected={(policy) => {
              if (onPolicySelected) onPolicySelected(policy);
            }}
            disabled={policySelectionCompleted}
          />
        </div>
      </div>
    );
  }

  if (msg.type === "verification-required") {
    return (
      <div
        className="flex justify-start mb-4 w-full"
        role="listitem"
        aria-label="Verification required"
      >
        <div className="flex flex-col w-full max-w-[90%]">
          <span className="text-xs text-gray-500 mb-1">AI Assistant</span>
          <VerificationRequired onVerifyPhone={onVerifyPhone} />
        </div>
      </div>
    );
  }

  if (msg.type === "policy-document-choice") {
    return (
      <div
        className="flex justify-start mb-4 w-full"
        role="listitem"
        aria-label="Policy document choice"
      >
        <div className="flex flex-col w-full max-w-[90%]">
          <span className="text-xs text-gray-500 mb-1">AI Assistant</span>
          <PolicyDocumentChoice
            onChoice={(hasDocument) => {
              if (onPolicyDocumentChoice) onPolicyDocumentChoice(hasDocument);
            }}
            disabled={policyDocumentChoiceMade}
          />
        </div>
      </div>
    );
  }

  if (msg.type === "product-catalog") {
    return (
      <div
        className="flex justify-start mb-4 w-full"
        role="listitem"
        aria-label="Product catalog"
      >
        <div className="flex flex-col w-full max-w-[90%]">
          <span className="text-xs text-gray-500 mb-1">AI Assistant</span>
          <ProductCatalog
            onProductSelected={(product) => {
              if (onProductSelected) onProductSelected(product);
            }}
            disabled={productSelectionCompleted}
          />
        </div>
      </div>
    );
  }

  if (msg.type === "policy-suggestions") {
    return (
      <div
        className="flex justify-start mb-4 w-full"
        role="listitem"
        aria-label="Policy assistance suggestions"
      >
        <div className="flex flex-col w-full max-w-[90%]">
          <span className="text-xs text-gray-500 mb-1">AI Assistant</span>
          <PolicySuggestions
            onSuggestionClick={(suggestion) => {
              if (onSuggestionClick) onSuggestionClick(suggestion);
            }}
            disabled={isSuggestionsDisabled}
            context={suggestionsContext}
          />
        </div>
      </div>
    );
  }

  return null;
});
