import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

interface MarkdownMessageProps {
  content: string;
  className?: string;
}

// Define types for the code component props
interface CodeComponentProps {
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export const MarkdownMessage = ({
  content,
  className = "",
}: MarkdownMessageProps) => {
  return (
    <div
      className={`markdown-message ${className}`}
      style={{
        fontSize: "0.75rem",
        lineHeight: "1.5",
        wordWrap: "break-word",
        overflowWrap: "break-word",
      }}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          // Style headings
          h1: ({ ...props }) => (
            <h1
              style={{
                fontSize: "0.875rem",
                fontWeight: "600",
                margin: "0.5rem 0",
                color: "#1f2937",
              }}
              {...props}
            />
          ),
          h2: ({ ...props }) => (
            <h2
              style={{
                fontSize: "0.75rem",
                fontWeight: "600",
                margin: "0.5rem 0",
                color: "#1f2937",
              }}
              {...props}
            />
          ),
          h3: ({ ...props }) => (
            <h3
              style={{
                fontSize: "0.75rem",
                fontWeight: "600",
                margin: "0.5rem 0",
                color: "#1f2937",
              }}
              {...props}
            />
          ),
          h4: ({ ...props }) => (
            <h4
              style={{
                fontSize: "0.6875rem",
                fontWeight: "600",
                margin: "0.5rem 0",
                color: "#1f2937",
              }}
              {...props}
            />
          ),

          // Style paragraphs
          p: ({ ...props }) => (
            <p
              style={{
                margin: "0.5rem 0",
                lineHeight: "1.5",
                color: "#1f2937",
                fontSize: "0.75rem",
              }}
              {...props}
            />
          ),

          // Style lists
          ul: ({ ...props }) => (
            <ul
              style={{
                margin: "0.5rem 0",
                paddingLeft: "1.5rem",
                listStyleType: "disc",
                color: "#1f2937",
                fontSize: "0.75rem",
              }}
              {...props}
            />
          ),
          ol: ({ ...props }) => (
            <ol
              style={{
                margin: "0.5rem 0",
                paddingLeft: "1.5rem",
                listStyleType: "decimal",
                color: "#1f2937",
                fontSize: "0.75rem",
              }}
              {...props}
            />
          ),
          li: ({ ...props }) => (
            <li
              style={{
                lineHeight: "1.5",
                margin: "0.25rem 0",
                color: "#1f2937",
                fontSize: "0.75rem",
              }}
              {...props}
            />
          ),

          // Style links
          a: ({ ...props }) => (
            <a
              style={{
                color: "#2563eb",
                textDecoration: "underline",
                wordBreak: "break-word",
                fontSize: "0.75rem",
              }}
              target="_blank"
              rel="noopener noreferrer"
              {...props}
            />
          ),

          // Style emphasis
          strong: ({ ...props }) => (
            <strong
              style={{
                fontWeight: "600",
                color: "#1f2937",
                fontSize: "0.75rem",
              }}
              {...props}
            />
          ),
          em: ({ ...props }) => (
            <em
              style={{
                fontStyle: "italic",
                color: "#1f2937",
                fontSize: "0.75rem",
              }}
              {...props}
            />
          ),

          // Style code
          code: ({
            inline,
            className,
            children,
            ...props
          }: CodeComponentProps) =>
            inline ? (
              <code
                style={{
                  backgroundColor: "#f3f4f6",
                  padding: "0.125rem 0.25rem",
                  borderRadius: "0.25rem",
                  fontSize: "0.6875rem",
                  fontFamily:
                    'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                }}
                {...props}
              >
                {children}
              </code>
            ) : (
              <code
                style={{
                  display: "block",
                  backgroundColor: "#f3f4f6",
                  padding: "0.5rem",
                  borderRadius: "0.25rem",
                  fontSize: "0.6875rem",
                  margin: "0.5rem 0",
                  overflowX: "auto",
                  fontFamily:
                    'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                }}
                {...props}
              >
                {children}
              </code>
            ),

          // Style blockquotes
          blockquote: ({ ...props }) => (
            <blockquote
              style={{
                borderLeft: "4px solid #d1d5db",
                paddingLeft: "1rem",
                margin: "0.5rem 0",
                fontStyle: "italic",
                color: "#6b7280",
                fontSize: "0.75rem",
              }}
              {...props}
            />
          ),

          // Style horizontal rules
          hr: ({ ...props }) => (
            <hr
              style={{ border: "1px solid #d1d5db", margin: "0.75rem 0" }}
              {...props}
            />
          ),

          // Style tables
          table: ({ ...props }) => (
            <div
              style={{
                overflowX: "auto",
                margin: "0.5rem 0",
                fontSize: "0.6875rem",
              }}
            >
              <table
                style={{
                  minWidth: "100%",
                  border: "1px solid #d1d5db",
                  borderRadius: "0.25rem",
                  fontSize: "0.6875rem",
                }}
                {...props}
              />
            </div>
          ),
          thead: ({ ...props }) => (
            <thead
              style={{ backgroundColor: "#f9fafb", fontSize: "0.6875rem" }}
              {...props}
            />
          ),
          tbody: ({ ...props }) => <tbody {...props} />,
          tr: ({ ...props }) => (
            <tr
              style={{
                borderBottom: "1px solid #e5e7eb",
                fontSize: "0.6875rem",
              }}
              {...props}
            />
          ),
          th: ({ ...props }) => (
            <th
              style={{
                padding: "0.25rem 0.5rem",
                textAlign: "left",
                fontWeight: "500",
                color: "#374151",
                borderRight: "1px solid #e5e7eb",
                fontSize: "0.6875rem",
              }}
              {...props}
            />
          ),
          td: ({ ...props }) => (
            <td
              style={{ padding: "0.25rem 0.5rem", fontSize: "0.6875rem" }}
              {...props}
            />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};
