import { FaPlus } from "react-icons/fa";

// Chat header component
export const ChatHeader = ({ onNewChat }: { onNewChat?: () => void }) => (
  <header
    className="px-4 py-3 border-b border-gray-200 bg-white flex items-center justify-between"
    role="banner"
  >
    <div className="flex items-center space-x-3">
      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary-600 to-primary-400 flex items-center justify-center overflow-hidden"></div>
      <div>
        <h1 className="text-xs font-semibold text-gray-800">
          Oneassure AI Advisor
        </h1>
        <p className="text-xs text-gray-500">Insurance policy assistance</p>
      </div>
    </div>

    <div className="flex items-center space-x-2">
      {onNewChat && (
        <button
          onClick={onNewChat}
          className="flex items-center justify-center space-x-1.5 px-3 py-1.5 rounded-full bg-neutral-100 hover:bg-neutral-200 text-neutral-600 hover:text-neutral-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1"
          aria-label="Start new chat"
          title="Start new chat"
        >
          <FaPlus className="h-3 w-3" />
          <span className="text-xs font-medium">New</span>
        </button>
      )}
    </div>
  </header>
);
