import React from "react";

interface PolicySuggestionsProps {
  onSuggestionClick: (prompt: string) => void;
  disabled?: boolean;
  context?: "policy" | "product"; // New prop to determine suggestion context
}

const PolicySuggestions: React.FC<PolicySuggestionsProps> = ({
  onSuggestionClick,
  disabled = false,
  context = "policy", // Default to policy context
}) => {
  // Different suggestions based on context
  const policySuggestions = [
    {
      id: "summarize",
      displayText: "Summarize the policy",
      prompt:
        "Summarize my insurance policy with key details like coverage, deductibles, and premium.",
    },
    {
      id: "coverage",
      displayText: "Tell me about my coverage",
      prompt:
        "What is covered under my insurance policy and what are the limits?",
    },
    {
      id: "pros-cons",
      displayText: "Pros and Cons",
      prompt: "What are the pros and cons of my current insurance policy?",
    },
    {
      id: "claim-process",
      displayText: "How Do I Make a Claim",
      prompt:
        "How do I file a claim? What documents do I need and what's the process?",
    },
    {
      id: "coverage-details",
      displayText: "What's Covered",
      prompt: "What is covered and what is not covered under my policy?",
    },
  ];

  const productSuggestions = [
    {
      id: "coverage",
      displayText: "Tell me about the coverage",
      prompt: "What types of coverage does this insurance product offer?",
    },
    {
      id: "claim-process",
      displayText: "How to initiate a claim",
      prompt: "How do I file a claim with this insurance product?",
    },
    {
      id: "benefits",
      displayText: "What are the key benefits",
      prompt: "What are the main benefits of this insurance product?",
    },
    {
      id: "eligibility",
      displayText: "Who is eligible for this plan",
      prompt:
        "Who is eligible for this insurance plan? What are the requirements?",
    },
    {
      id: "premium",
      displayText: "What factors affect the premium",
      prompt:
        "What factors affect the premium cost for this insurance product?",
    },
  ];

  const suggestions =
    context === "product" ? productSuggestions : policySuggestions;

  const handleSuggestionClick = (suggestion: any) => {
    if (disabled) return;
    onSuggestionClick(suggestion.prompt);
  };

  return (
    <div className="mt-3">
      <div className="flex flex-wrap gap-2">
        {suggestions.map((suggestion) => (
          <button
            key={suggestion.id}
            onClick={() => handleSuggestionClick(suggestion)}
            disabled={disabled}
            className={`px-5 py-2.5 rounded-xl bg-white text-gray-900 text-xs font-medium shadow border border-gray-200 hover:bg-gray-50 hover:shadow-md transition focus:outline-none focus:ring-2 focus:ring-blue-300 ${
              disabled ? "cursor-not-allowed opacity-60" : "cursor-pointer"
            }`}
            aria-label={`Ask: ${suggestion.displayText}`}
          >
            {suggestion.displayText}
          </button>
        ))}
      </div>

      {!disabled && (
        <div className="mt-3 px-3 py-2 bg-gray-50 rounded-md border border-gray-100">
          <p className="text-xs text-gray-600">
            <strong className="font-medium">Tip:</strong> You can also type your
            own questions in the chat box below
          </p>
        </div>
      )}
    </div>
  );
};

export default PolicySuggestions;
