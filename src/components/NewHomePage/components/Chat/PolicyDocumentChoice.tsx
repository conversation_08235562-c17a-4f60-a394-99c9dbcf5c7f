export const PolicyDocumentChoice = ({
  onChoice,
  disabled = false,
}: {
  onChoice: (hasDocument: boolean) => void;
  disabled?: boolean;
}) => {
  return (
    <div
      className="flex flex-col items-start mb-4"
      role="listitem"
      aria-label="Policy document choice"
    >
      <div className="flex flex-col max-w-[85%]">
        <div
          className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 text-gray-800 rounded-xl px-4 py-3 mb-3 text-xs shadow-sm"
          tabIndex={0}
        >
          Do you have a <span className="font-medium">Policy Document</span> to
          upload, or would you like to{" "}
          <span className="font-medium">Explore Products</span>?
        </div>
      </div>
      <div className="flex gap-3 mt-2">
        <button
          className={`px-5 py-2.5 rounded-xl text-xs font-medium shadow border transition focus:outline-none focus:ring-2 focus:ring-blue-300 ${
            disabled
              ? "bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200"
              : "bg-white text-gray-900 border-gray-200 hover:bg-gray-50 hover:shadow-md"
          }`}
          onClick={() => onChoice(true)}
          aria-label="I have a policy document"
          disabled={disabled}
        >
          Upload Document
        </button>
        <button
          className={`px-5 py-2.5 rounded-xl text-xs font-medium shadow border transition focus:outline-none focus:ring-2 focus:ring-blue-300 ${
            disabled
              ? "bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200"
              : "bg-white text-gray-900 border-gray-200 hover:bg-gray-50 hover:shadow-md"
          }`}
          onClick={() => onChoice(false)}
          aria-label="I want to explore products"
          disabled={disabled}
        >
          Explore Products
        </button>
      </div>
    </div>
  );
};
