import { BodyLarge, HeadingSmall } from "@/components/UI/Typography";
import { htmlParser } from "@/utils/htmlParser";

export type TestimonialCardProps = {
  name: string;
  statement?: string;
  video_url?: string;
};
// at least one of video or statement is required

const TestimonialCard = ({
  name,
  statement,
  video_url,
}: TestimonialCardProps) => {
  return (
    <div className="bg-primary-800 flex flex-col gap-4 p-4 md:p-6 rounded-xl min-w-[300px] md:min-w-[420px] h-full">
      <div>
        {statement && htmlParser(statement, {
          components: {
            p: (props: any) => <BodyLarge {...props} weight="regular" />,
          },
          classNames: {
            p: "text-white font-normal text-justify",
          },
        })}
        {video_url && (
          <div className="p-2 bg-white rounded-xl">
            <video src={video_url} controls={true} className="w-full h-auto min-w-[300px] max-h-[420px] max-w-[420px] rounded-xl" />
          </div>
        )}
      </div>
      <div>
        <HeadingSmall weight="medium" className="text-white">{name}</HeadingSmall>
      </div>
    </div>
  );
};

export default TestimonialCard;
