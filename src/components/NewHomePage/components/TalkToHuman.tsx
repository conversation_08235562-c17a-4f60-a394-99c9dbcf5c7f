import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import {
  BodyMedium,
  Heading2XLarge,
  HeadingSmall,
} from "@/components/UI/Typography";
import React from "react";
import { Button } from "@/components/UI/Button";
import Image from "next/image";
import { orufyHandler } from "@/utils/orufyHandler";
import { useSessionStorage } from "usehooks-ts";

const TalkToHuman = () => {

  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);

  function bookACall() {
    const orufyLink = `${process.env.NEXT_PUBLIC_ONE_ASSURE_ORUFY_LINK}?utm_source=${utm_source}&utm_medium=${utm_medium}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&utm_term=${utm_term}`;
    orufyHandler(orufyLink);
  }

  return (
    <SectionContainerMedium className="mb-9 md:!mb-24 md:!px-8 lg:!px-0">
      <div className="hidden md:block bg-primary-200 rounded-[32px] relative ">
        <div className="flex flex-col relative z-10 md:py-12 px-4 md:px-16">
          <Heading2XLarge className="text-primary-800 mb-4" weight="medium">
            Still deciding? Talk to a human.
          </Heading2XLarge>
          <HeadingSmall
            weight="regular"
            className="text-neutral-800 max-w-[250px] md:max-w-[535px] mb-6"
          >
            {`Behind every policy, there's a bunch of humans who care about getting it right for you. Drop us a message - we'll be here.`}
          </HeadingSmall>
          <div className="flex justify-start ">
            {/* <div className="relative inline-block p-0.5 hover:cursor-pointer">
              <div
                style={{
                  filter: "url(#squiggly)",
                }}
                className="absolute inset-0 border-[3px] border-primary-400 rounded-xl z-10"
              />
              <Button
                variant="primary"
                className="px-6 py-6 text-base font-normal rounded-xl hover:scale-100"
              >
                Talk To A Human
              </Button>
            </div> */}

            <div
              className="relative inline-block p-0.5 group z-50 cursor-pointer"
              onClick={bookACall}
            >
              <div
                style={{
                  filter: "url(#squiggly)",
                }}
                className="absolute inset-0 border-[3px] border-primary-400 rounded-2xl z-10 pointer-events-none transition-all duration-300 group-hover:scale-105"
              />
              <Button
                variant="primary"
                className="relative cursor-pointer px-4 sm:px-6 py-4 sm:py-6 rounded-xl transform transition-transform group-hover:scale-105 group-hover:bg-primary-700"
              >
                Talk To A Human
              </Button>
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 right-0 hidden md:block translate-x-4 md:translate-x-8 lg:translate-x-12 ">
          <Image
            width={280}
            height={280}
            src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a7561-c89e-73d0-abc2-aa77971c141b/Untitled_Artwork 63 1.svg"
            alt="Girl illustration"
            className=" md:w-[300px] lg:w-[417px] h-auto object-contain"
          />
        </div>
        <div className="absolute bottom-0 -right-2 block md:hidden translate-x-4 md:translate-x-8 lg:translate-x-12 lg:translate-y-5">
          <Image
            width={190}
            height={165}
            src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a72c5-4c8b-7929-bde0-9c196a4509ef/Untitled_Artwork 63 1.svg"
            alt="Girl illustration"
            className="block md:hidden h-auto object-contain"
          />
        </div>
      </div>

      <div className="block md:hidden bg-primary-100 border border-primary-300 rounded-xl relative ">
        <div className="flex flex-col  z-10 p-6">
          <Heading2XLarge className="text-primary-800 mb-4" weight="medium">
            Still deciding? Talk to a human.
          </Heading2XLarge>
          <BodyMedium
            className="text-neutral-1100 max-w-[250px] md:max-w-[535px] mb-6"
            weight="regular"
          >
            {
              "Behind every policy, there's a bunch of humans who care about getting it right for you. Drop us a message - we'll be here."
            }
          </BodyMedium>
          <div className="flex justify-start  ">
            {/* <div className="relative inline-block  hover:cursor-pointer">
              <div
                style={{
                  filter: "url(#squiggly)",
                }}
                className="absolute inset-0 border-[3px] border-primary-400 rounded-xl z-10"
              />
              <Button
                variant="primary"
                className="px-6 py-3 text-xs font-normal rounded-xl hover:scale-100"
              >
                Talk To A Human
              </Button>
            </div> */}

            <div
              className="relative inline-block p-0.5 group z-50 cursor-pointer"
              onClick={bookACall}
            >
              <div
                style={{
                  filter: "url(#squiggly)",
                }}
                className="absolute inset-0 border-[3px] border-primary-400 rounded-2xl z-10 pointer-events-none transition-all duration-300 group-hover:scale-105"
              />
              <Button
                variant="primary"
                className="relative cursor-pointer px-4 sm:px-6 py-4 sm:py-6 rounded-xl transform transition-transform group-hover:scale-105 group-hover:bg-primary-700"
              >
                Talk To A Human
              </Button>
            </div>
          </div>
        </div>
        <div className=" absolute bottom-0 right-0 translate-x-4 md:translate-x-8 lg:translate-x-12 ">
          <Image
            width={150}
            height={130}
            src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a72c5-4c8b-7929-bde0-9c196a4509ef/Untitled_Artwork 63 1.svg"
            alt="Boy illustration"
            className=" md:w-[300px] lg:w-[368px]  h-auto object-contain"
          />
        </div>
      </div>
    </SectionContainerMedium>
  );
};

export default TalkToHuman;
