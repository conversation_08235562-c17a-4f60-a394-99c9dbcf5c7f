// export default function HandBorder() {
//   return (
//     <svg width="0" height="0" style={{ position: "absolute" }}>
//       <filter id="squiggly">
//         <feTurbulence baseFrequency="0.02" numOctaves="8" result="noise" />
//         <feDisplacementMap in="SourceGraphic" in2="noise" scale="2" />
//       </filter>
//     </svg>
//   );
// }
// export default function HandBorder() {
//   return (
//     <svg width="0" height="0" style={{ position: "absolute", zIndex: "99999" }}>
//       <filter id="squiggly" x="0%" y="0%" width="100%" height="100%">
//         {/* Organic noise */}
//         <feTurbulence
//           type="fractalNoise"
//           baseFrequency="0.1"
//           numOctaves="8"
//           result="noise"
//           seed="3"
//         />

//         {/* Light displacement to simulate hand wobble */}
//         <feDisplacementMap in="SourceGraphic" in2="noise" scale="2" />

//         {/* Slight blur for softer edges */}
//         <feGaussianBlur stdDeviation="0.3" />
//       </filter>
//     </svg>
//   );
// }

export default function HandBorder() {
  return (
    <svg width="0" height="0" style={{ position: "absolute" }}>
      <filter id="squiggly">
        <feTurbulence type="fractalNoise" baseFrequency="0.01" numOctaves="1" seed="1" result="noise"/>
        <feDisplacementMap in="SourceGraphic" in2="noise" scale="0.5" />
        <feGaussianBlur stdDeviation="0.3" />
      </filter>
    </svg>
  );
}
