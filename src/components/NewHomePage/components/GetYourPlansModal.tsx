import React, { useState } from "react";
import { BodySmall, HeadingLarge } from "@/components/UI/Typography";
import { X } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/UI/Select"; // Shadcn select import
import SectionContainerXSmall from "@/components/globals/SectionContainerXSmall";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerSmall from "@/components/globals/SectionContainerSmall";
import { Button } from "@/components/UI/Button";

type ProductVariant = {
  id: string;
  temp_slug: string;
  variant_name: string;
}

type Insurer = {
  id: string;
  logo_url: string;
  temp_slug: string;
  name: string;
  product_variants: ProductVariant[];
}

type InsuranceTypes = {
  type: string;
  insurers: Insurer[];
}

type GetYourPlansProps = {
  title: string;
  subtitle: string;
  insuranceTypes: InsuranceTypes[];
  buttonText: string;
  onClose?: () => void;
};

const GetYourPlansModal = ({
  title,
  subtitle,
  insuranceTypes,
  buttonText,
  onClose,
}: GetYourPlansProps) => {

  const [selectedInsurance, setSelectedInsurance] = useState<InsuranceTypes>({type: "", insurers: []});
  const [selectedInsurer, setSelectedInsurer] = useState<Insurer>({id: "",logo_url: "", temp_slug: "", name: "", product_variants: []});
  const [selectedPolicy, setSelectedPolicy] = useState<ProductVariant>({id: "", temp_slug: "", variant_name: ""});

  const insuranceOptions = insuranceTypes.map((item) => item.type);

  function handleClick(){
    console.log("selectedInsurance", selectedInsurance);
    console.log("selectedInsurer", selectedInsurer);
    console.log("selectedPolicy", selectedPolicy);

    // redirect to BASE_URL/health-insulance/insurer-slug/variant-slug
    window.location.href = `${process.env.NEXT_PUBLIC_BASE_URL}/${selectedInsurance.type}-insurance/${selectedInsurer.temp_slug}/${selectedPolicy.temp_slug}`;
  }

  const handleInsuranceSelect = (value: string)=>{
    setSelectedInsurance(insuranceTypes.find((item) => item.type === value) || {type: "", insurers: []});
  }

  const handleInsurerSelect = (value: string)=>{
    const insurer = selectedInsurance.insurers.find((item) => item.name === value);
    setSelectedInsurer(insurer || {id: "", logo_url: "", temp_slug: "", name: "", product_variants: []});
  }

  const handlePolicySelect = (value: string)=>{
    const policy = selectedInsurer.product_variants.find((item) => item.variant_name === value);
    setSelectedPolicy(policy || {id: "", temp_slug: "", variant_name: ""});
  }

  return (
    <div
      className="bg-black/80 min-h-screen flex justify-center items-center fixed inset-0 z-50"
      onClick={onClose}
    >
      <div
        className="w-full max-w-5xl mb-12 md:mb-14 px-6 md:px-0 mx-auto "
        onClick={(e) => e.stopPropagation()} // prevent modal close on inner click
      >
        <SectionContainerLarge className="bg-white rounded-xl px-6 py-4 flex flex-col gap-8">
          <SectionContainerLarge className="!mb-0">
            <HeadingLarge className="text-center text-neutral-1100 mb-2">{title}</HeadingLarge>
            <BodySmall className="text-center font-medium text-neutral-1100">
              {subtitle}
            </BodySmall>
          </SectionContainerLarge>
          <SectionContainerSmall className="flex flex-col md:flex-row gap-6 items-center justify-center">
            {/* Insurance */}
            <Select value={selectedInsurance.type} onValueChange={(value) => handleInsuranceSelect(value)}>
              <SelectTrigger   className="w-full rounded-xl px-6 py-3 bg-white shadow-none text-primary-800 !border !border-primary-100">
                <SelectValue placeholder="Select Insurance" />
              </SelectTrigger>
              <SelectContent className="border border-primary-100 text-primary-800">
                {insuranceOptions.map((item, i) => (
                  <SelectItem key={i} value={item}>
                    {item}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Only allow if insurance is selected */}
            <Select value={selectedInsurer.name} onValueChange={(value) => handleInsurerSelect(value)} disabled={selectedInsurance.type === ""}>
              <SelectTrigger   className={`w-full rounded-xl px-6 py-3 bg-white shadow-none text-primary-800 !border !border-primary-100 ${selectedInsurance.type === "" ? "opacity-50 hover:cursor-not-allowed" : ""}`}>
                <SelectValue placeholder="Select Insurer" />
              </SelectTrigger>
              <SelectContent className="border border-primary-100 text-primary-800">
                {selectedInsurance.insurers.map((item, i) => (
                  <SelectItem key={i} value={item.name}>
                    {item.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Only allow if insurer is selected */}
            <Select value={selectedPolicy.variant_name} onValueChange={(value) => handlePolicySelect(value)} disabled={selectedInsurer.name === ""}>
              <SelectTrigger   className={`w-full rounded-xl px-6 py-3 bg-white shadow-none text-primary-800 !border !border-primary-100 ${selectedInsurer.name === "" ? "opacity-50 hover:cursor-not-allowed" : ""}`}>
                <SelectValue placeholder="Select Policy" />
              </SelectTrigger>
              <SelectContent className="border border-primary-100 text-primary-800">
                {selectedInsurer.product_variants.map((item, i) => (
                  <SelectItem key={i} value={item.variant_name}>
                    {item.variant_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              variant="primary"
              className="h-full rounded-xl px-6 py-3 bg-primary-800 text-white font-medium"
              disabled={selectedInsurance.type === "" || selectedInsurer.name === "" || selectedPolicy.variant_name === ""}
              onClick={handleClick}
            >
              {buttonText}
            </Button>
          </SectionContainerSmall>
        </SectionContainerLarge>
      </div>
    </div>
  );
};

export default GetYourPlansModal;
