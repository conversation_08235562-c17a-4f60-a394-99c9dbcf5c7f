type HealthInsurer = {
  logo_url: string;
  product_variants: {
    temp_slug: string;
  }[];
}

type TermInsurer = {
  logo_url: string;
  product_variants: {
    temp_slug: string;
  }[];
}

import { ReactNode } from "react";

export type HomePageType = {
  HeroSection: {
    title: string;
    spanText: string;
    subtitle: string;
    marqueeImages: string[];
    insuranceTypes: {
      type: string;
      insurers: {
        id: string;
        logo_url: string;
        temp_slug: string;
        name: string;
        product_variants: {
          id: string;
          temp_slug: string;
          variant_name: string;
        }[];
      }[];
    }[];
  };
  whyToChooseUs: {
    pill_Content: string;
    title: string;
    cards: {
      title: string;
      icon: string;
      description: string;
    }[];
  };
  howOneAssureWork: {
    pill_Content: string;
    title: string;
    cards: {
      title: string;
      description: string;
      icon: string;
    }[];
  };
  askAnythingSection: {
    pill_Content: string;
    title: string;
    subheading: string;
    slides: {
      title: string;
      subtitle: string;
      chats: {
        id: string;
        type: "bot" | "human";
        content: string;
      }[];
    }[];
  };
  testimonialSection: {
    pill_Content: string;
    title: string;
    story: {
      title: string;
      points: string[];
    };
    testimonials: {
      name: string;
      content: string;
      video_url: string;
    }[];
  };
  meetTheTeam: {
    title: string;
    image: string;
    description: string;
  };
};



export type MessageType =
  | { type: "ai"; text: string; component?: ReactNode }
  | { type: "ai-markdown"; text: string; component?: ReactNode }
  | { type: "user"; text: string }
  | { type: "select-customer" }
  | { type: "loading" }
  | { type: "policy-upload" }
  | { type: "policy-selection"; phone: string }
  | { type: "verification-required" }
  | { type: "policy-document-choice" }
  | { type: "product-catalog" }
  | { type: "policy-suggestions" };

export type OnboardingState =
  | "select"
  | "existing-phone"
  | "existing-otp"
  | "existing-policy"
  | "new-upload"
  | "done"
  | "new-customer-phone"
  | "new-customer-otp"
  | "upload-error"
  | "new-policy-choice"
  | "new-catalog"
  | "policy-suggestions";