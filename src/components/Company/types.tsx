import { Meta } from "@/types";
import { AccordianType, Hero, <PERSON><PERSON> } from "../globals/types";

export type StatisticsProps = {
  companyName: string;
  graphs: Array<{
    title: string;
    industry: number;
    company: number;
    suffix: string;
    description?: string;
  }>;
  rating: Array<{
    title: string;
    rating: number;
    outOf: number;
  }>;
  ratingDescription?: string;
  networkHospitals?: number;
  claimSettlement?: number;
};

export interface Steps {
  data: {
    attributes: {
      steps: Array<AccordianType>;
    };
  };
}

export interface ClaimSettlement {
  title: string;
  cashless: Steps;
  reimbursement: Steps;
}

export interface Renewal {
  title: string;
  onlineRenewal: Steps;
  offlineRenewal: Steps;
}

export interface Plans {
  data: [
    {
      id: number;
      attributes: {
        name: string;
        slug: string;
      };
    }
  ];
}

export interface PolicyGuide {
  title: string;
  guidePoint: GuidePoint[];
}

export interface GuidePoint {
  title: string;
  description: string;
}

export interface CompanyAttributes {
  name: string;
  slug: string;
  hero: <PERSON>;
  logo: <PERSON><PERSON>;
  verdict: string;
  pros: Array<AccordianType>;
  cons: Array<AccordianType>;
  kycDocs: Array<{
    name: string;
  }>;
  legecy: string;
  faqs: {
    question: string;
    ans: string;
  }[];
  renewalKeyPoints: Array<{ point: string }>;
  claimSettlement: ClaimSettlement;
  renewalSteps: Renewal;
  customerSupport: {
    email: string;
    contactNumber: string;
  };
  health_variants?: Plans;
  term_variants?: Plans;
  policyGuide?: PolicyGuide;
  seo?: {
    metaTitle: string;
    metaDescription: string;
    keyword: string;
  } | null;
  category: string;
  testimonials?: {
    id: number;
    testimonial: {
      id: number;
      name: string;
      statement: string;
      backgroundColor: string;
      thumbnail: {
        data: {
          id: number;
          attributes: {
            url: string;
          };
        };
      };
    }[];
  };
  ongoingOffer?: {
    title: string;
    descriptipn: string;
  };
  ratings: {
    id: number;
    solvency: number | null;
    icr: number | null;
    growth: number | null;
    aum: number | null;
  };
  statistics: {
    grossDirectPremium: {
      industry: number;
      company: number;
      description?: string;
    } | null;
    icr: {
      industry: number;
      company: number;
      description?: string;
    } | null;
    premiumUnderwritten: {
      industry: number;
      company: number;
      description?: string;
    } | null;
    solvencyRatio: {
      industry: number;
      company: number;
      description?: string;
    } | null;
  };
  claimSettlementPercentage: number;
  networkHospitals: number;
  ratingDescription?: string;
}

export type Company = {
  id: number;
  attributes: CompanyAttributes;
};

export type CompanyData = {
  data: Company[];
} & Meta;
