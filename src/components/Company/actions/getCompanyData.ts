"use server";

async function getCompanyData<T>(company: string): Promise<T> {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[slug][$eq]=${company}&populate[logo][fields][0]=url&populate[hero][fields][0]=title&populate[hero][fields][1]=rating&populate[pros][fields][0]=title&populate[pros][fields][1]=description&populate[cons][fields][0]=title&populate[cons][fields][1]=description&populate[kycDocs][fields][0]=name&populate[claimSettlement][fields][0]=title&populate[claimSettlement][populate][cashless][populate][steps][fields][0]=title&populate[claimSettlement][populate][cashless][populate][steps][fields][1]=description&populate[claimSettlement][populate][reimbursement][populate][steps][fields][0]=title&populate[claimSettlement][populate][reimbursement][populate][steps][fields][1]=description&populate[renewalSteps][fields][0]=title&populate[renewalSteps][populate][onlineRenewal][populate][steps][fields][0]=title&populate[renewalSteps][populate][onlineRenewal][populate][steps][fields][1]=description&populate[renewalSteps][populate][offlineRenewal][populate][steps][fields][0]=title&populate[renewalSteps][populate][offlineRenewal][populate][steps][fields][1]=description&populate[renewalKeyPoints][fields][0]=point&populate[customerSupport][fields][0]=email&populate[customerSupport][fields][1]=contactNumber&populate[faqs][fields][0]=question&populate[faqs][fields][1]=ans&populate[ratings][fields][0]=solvency&populate[ratings][fields][1]=icr&populate[ratings][fields][2]=growth&populate[ratings][fields][3]=aum&populate[statistics][populate][grossDirectPremium][fields][0]=industry&populate[statistics][populate][grossDirectPremium][fields][1]=company&populate[statistics][populate][icr][fields][0]=industry&populate[statistics][populate][icr][fields][1]=company&populate[statistics][populate][premiumUnderwritten][fields][0]=industry&populate[statistics][populate][premiumUnderwritten][fields][1]=company&populate[statistics][populate][solvencyRatio][fields][0]=industry&populate[statistics][populate][solvencyRatio][fields][1]=company&populate[health_variants][fields][0]=name&populate[health_variants][fields][1]=slug&populate[term_variants][fields][0]=name&populate[term_variants][fields][1]=slug`,
    { headers }
  );

  return res.json() as Promise<T>;
}

export default getCompanyData;
