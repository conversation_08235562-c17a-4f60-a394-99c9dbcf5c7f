export const ratingCalculator = ({
  parameter,
  maxRating,
  minRating,
  paramMaxValue,
  paramMinValue,
  paramCurrentValue,
}: {
  parameter: string;
  maxRating: number;
  minRating: number;
  paramMaxValue: number;
  paramMinValue: number;
  paramCurrentValue: number;
}): number => {
  if (paramMaxValue === paramMinValue) {
    return maxRating;
  }
  switch (parameter) {
    case "SOLVENCY":
    case "GROWTH":
    case "AUM":
      // return parseFloat(
      //   (
      //     maxRating -
      //     minRating *
      //       ((paramMaxValue - paramCurrentValue) /
      //         (paramMaxValue - paramMinValue))
      //   ).toFixed(1)
      // );

      return parseFloat(
        (
          ((paramCurrentValue - paramMinValue) * (maxRating - minRating)) /
            (paramMaxValue - paramMinValue) +
          minRating
        ).toFixed(1)
      );
    case "ICR":
      // return parseFloat(
      //   (
      //     minRating +
      //     minRating *
      //       ((paramMaxValue - paramCurrentValue) /
      //         (paramMaxValue - paramMinValue))
      //   ).toFixed(1)
      // );

      return parseFloat(
        (
          maxRating -
          ((paramCurrentValue - paramMinValue) * (maxRating - minRating)) /
            (paramMaxValue - paramMinValue)
        ).toFixed(1)
      );
  }
  return 1;
};

// export const ratingCalculator = ({
//     parameter,
//     maxRating,
//     minRating,
//     paramMaxValue,
//     paramMinValue,
//     paramCurrentValue,
//   }: {
//     parameter: "SOLVENCY" | "GROWTH" | "AUM" | "ICR";
//     maxRating: number;
//     minRating: number;
//     paramMaxValue: number;
//     paramMinValue: number;
//     paramCurrentValue: number;
//   }): number => {
//     // Prevent division by zero and return maxRating as fallback
//     if (paramMaxValue === paramMinValue) {
//       return maxRating;
//     }

//     // Normalize value between 0 and 1
//     const normalizedValue =
//       (paramMaxValue - paramCurrentValue) / (paramMaxValue - paramMinValue);

//     // Determine rating based on parameter type
//     if (parameter === "ICR") {
//       return minRating - minRating * normalizedValue;
//     }

//     return maxRating - (maxRating - minRating) * normalizedValue;
//   };
