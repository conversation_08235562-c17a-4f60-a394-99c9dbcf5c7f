"use client";
import React, { Suspense, useEffect } from "react";
import About from "./components/About";
import Plans from "./components/Plans";
import Hero from "./components/Hero";
import OngoingOffer from "../globals/OngoingOffer";
import Verdict from "./components/Verdict";
import KycDocs from "./components/KycDocs";
import Container from "@/components/globals/Container";
import KeyPoints from "./components/KeyPoints";
import CustomerSupport from "./components/CustomerSupport";
import Faqs from "./components/Faq";
import OtherRelatedCompanies from "./components/OtherRelatedCompanies";
import SideMenu from "./components/SideMenu";
import Consideration from "./components/Considerations";
import Procedures from "./components/Procedures";
import Statistics from "./components/Statistics";
import { Company, StatisticsProps } from "./types";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import { useSessionStorage } from "usehooks-ts";
import Breadcrumb from "@/components/globals/Breadcrumb";

const CompanyRoot: React.FC<{
  allComp: any;
  company: Company;
  category?: string;
  statistics: StatisticsProps;
}> = (props) => {
  const { allComp, company, category = "health" } = props;
  let ratingArr = props.statistics.rating.map((r: any) => r.rating);
  let totalRating = parseFloat(
    ratingArr
      .reduce(
        (accumulator: number, currentValue: number) =>
          accumulator + currentValue,
        0
      )
      .toFixed(1)
  );

  const pathname = usePathname();
  const router = useRouter();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);
  useEffect(() => {
    if (typeof window !== "undefined") {
      router.replace(
        `${pathname}?${utm_source !== null ? `utm_source=${utm_source}` : ""}${
          utm_medium !== null ? `&utm_medium=${utm_medium}` : ""
        }${utm_campaign !== null ? `&utm_campaign=${utm_campaign}` : ""}${
          utm_content !== null ? `&utm_content=${utm_content}` : ""
        }${utm_term !== null ? `&utm_term=${utm_term}` : ""}`
      );
    }
  }, []);

  const logo = company?.attributes?.logo?.data?.attributes?.url;
  const companySlug = company?.attributes?.slug;
  const companyName = company?.attributes?.name;
  const transformedData = [
    ...(company.attributes.health_variants?.data || []),
    ...(company.attributes.term_variants?.data || []),
  ].map((company: any) => ({
    id: company.id,
    attributes: {
      name: company.attributes.name,
      slug: company.attributes.slug,
      logo: logo,
      companyName: companyName,
      companySlug: companySlug,
    },
  }));

  return (
    <div className="bg-soft-blue">
      <Container>
        <div className="pt-3 pb-1 md:pt-5 md:pb-3">
          <Suspense>
            <Breadcrumb
              path={[
                "home",
                category === "health" ? "health-insurance" : "term-insurance",
                company.attributes.slug,
              ]}
            />
          </Suspense>
        </div>
        <div className="grid grid-cols-12 gap-5">
          <SideMenu />
          <div className="col-span-12 md:col-span-10 ml-auto">
            {company.attributes.ongoingOffer && (
              <OngoingOffer
                offerDescription={company.attributes.ongoingOffer.descriptipn}
              />
            )}
            <Hero
              title={company.attributes.hero.title}
              rating={totalRating}
              logo={company.attributes.logo?.data?.attributes?.url}
              claimSettlementRatio={
                company.attributes.claimSettlementPercentage
              }
              networkHospitals={company.attributes.networkHospitals}
            />
            {company.attributes?.verdict && (
              <Verdict oneAssureVerdict={company.attributes.verdict} />
            )}
            {company.attributes?.pros && (
              <Consideration
                title="Pros"
                considerations={company.attributes.pros}
              />
            )}
            {company.attributes?.cons && (
              <Consideration
                title="Cons"
                considerations={company.attributes.cons}
              />
            )}
            <Statistics {...props.statistics} />
            {company.attributes?.legecy && (
              <About
                title="About the Company"
                company_desc={company.attributes.legecy}
              />
            )}
            {(company.attributes?.health_variants ||
              company.attributes?.term_variants) && (
              <Plans
                variants={transformedData}
                category={props.category ? props.category : "health"}
                title={"Insurance Policies"}
              />
            )}
            {company.attributes?.kycDocs && (
              <KycDocs
                title={"KYC Documents Required"}
                docs={company.attributes.kycDocs}
              />
            )}
            {company.attributes?.claimSettlement &&
              (category === "health" ? (
                <Procedures
                  id="claimSettlement"
                  title="Claim Settlement"
                  procedures={[
                    {
                      title: "Cashless Claim",
                      steps:
                        company.attributes.claimSettlement?.cashless.data
                          .attributes.steps,
                    },
                    {
                      title: "Reimbursement Claim",
                      steps:
                        company.attributes.claimSettlement?.reimbursement?.data
                          ?.attributes.steps,
                    },
                  ]}
                />
              ) : (
                <Procedures
                  id="claimSettlement"
                  title="Claim Settlement"
                  procedures={[
                    {
                      title: "Claim Procedure",
                      steps:
                        company.attributes.claimSettlement?.cashless.data
                          .attributes.steps,
                    },
                  ]}
                />
              ))}
            {company.attributes?.renewalSteps && (
              <Procedures
                id="renewals"
                title="Renewal"
                procedures={[
                  {
                    title: "Online Renewal",
                    steps:
                      company.attributes.renewalSteps.onlineRenewal.data
                        .attributes.steps,
                  },
                  {
                    title: "Offline Renewal",
                    steps:
                      company.attributes.renewalSteps.offlineRenewal.data
                        .attributes.steps,
                  },
                ]}
              />
            )}
            {company.attributes?.renewalKeyPoints && (
              <KeyPoints keyPoints={company.attributes?.renewalKeyPoints} />
            )}
            {company.attributes.customerSupport && (
              <CustomerSupport
                contactNumber={company.attributes.customerSupport.contactNumber}
                email={company.attributes.customerSupport.email}
                company={company.attributes.hero.title}
              />
            )}
            {allComp.length > 1 && (
              <OtherRelatedCompanies
                allComp={allComp}
                currentComp={company.attributes.name}
                category={category}
              />
            )}
            {company.attributes?.faqs && (
              <Faqs
                title={"Frequently Asked Questions"}
                faqs={company.attributes.faqs}
              />
            )}
            {/* {company.attributes.policyGuide && (
              <PolicyGuideRoot policy_guide={company.attributes.policyGuide} />
            )} */}
            {/* {company.attributes.testimonials && (
              <Testimonials
                testimonials={company.attributes.testimonials.testimonial}
              />
            )} */}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default CompanyRoot;
