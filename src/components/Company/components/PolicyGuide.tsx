"use client";

import Container from "@/components/globals/Container";
import React, { useState } from "react";
import { PlusIcon, XMarkIcon } from "@heroicons/react/24/outline";
import parse, { attributesToProps } from "html-react-parser";
import { Element } from "html-react-parser";
import Image from "next/image";
import { PolicyGuide } from "../types";

const GuideOption: React.FC<{
  question: string;
  guideDesc: string;
}> = (props) => {
  const [open, setOpen] = useState(false);
  const replace = (domNode: Element, index: number) => {
      if (domNode.attribs) {
        let attrs = domNode.attribs;
        if (attrs.style) {
          attrs.style = "";
        }
  
        switch (domNode.name) {
          case "h1":
  
          case "h2":
            attrs.className = "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
            break;
  
          case "h3":
            attrs.className = "text-ntrl-black font-generalSans text-xl font-semibold my-5";
            break;
  
          case "h4":
          case "h5":
          case "h6":
            attrs.className = "text-ntrl-black text-lg font-semibold my-5";
            break;
  
          case "p":
            attrs.className = "text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
            break;
  
          case "a":
            attrs.className = "text-primary-2";
            break;
          
          case "li":
            attrs.className = "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal my-2 mt-3";
            break;
          case "tr":
          case "td":
          case "th":
            attrs.className = "border border-gray800 p-5 text-center";
            break;
  
          case "figure":
            attrs.className = "overflow-x-auto min-w-[300px]";
            break;
  
          case "img":
            return (
              <div className="mx-auto h-64 aspect-video my-10 relative">
                <Image
                  src={attrs.src}
                  fill={true}
                  style={{ objectFit: "contain" }}
                  alt="alt"
                />
              </div>
            );
  
          case "iframe":
            attrs.className = "my-0 mx-auto";
        }
        return domNode;
      }
    };
  return (
    <div
      className="bg-ntrl-outline py-10 px-12 mb-6 rounded-2xl"
      onClick={() => setOpen(!open)}
    >
      <div className="flex justify-between items-center">
        <h2 className="font-medium text-[16px]/[24px] md:text-[24px]/[30px] text-ntrl-black font-generalSans w-[90%] md:w-[80%] ">
          {props.question}
        </h2>
        {open ? (
          <XMarkIcon
            className="font-bold text-ntrl-black text-xl w-6 h-6 "
            onClick={() => setOpen(!open)}
          />
        ) : (
          <PlusIcon className="font-bold text-ntrl-black text-xl w-6 h-6 " />
        )}
      </div>

      {open && (
        <div className="mt-2">
          {/* @ts-ignore */}
          {parse(props.guideDesc,{replace})}
        </div>
      )}
    </div>
  );
};

const PolicyGuideRoot: React.FC<{
  policy_guide: PolicyGuide;
}> = (props) => {
  return (
    <div className="pt-20">
      <Container>
        <div>
          {props.policy_guide.guidePoint.map((pg, idx) => {
            return (
              <GuideOption
                guideDesc={pg.description}
                question={pg.title}
                key={idx}
              />
            );
          })}
        </div>
      </Container>
    </div>
  );
};

export default PolicyGuideRoot;
