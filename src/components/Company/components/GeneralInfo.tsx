import Container from "@/components/globals/Container";
import Image from "next/image";

const GeneralInfo: React.FC<{
  overviewItems: { title: string; count: string; logo: { data: { attributes: { url: string } } | null } }[];
}> = ({ overviewItems }) => {
  return (
    <section className="mt-12">
      <Container>
        <div className="bg-primary-1 py-6 px-4 md:px-12 rounded-2xl">
          <div className="grid grid-cols-1 gap-y-6 md:gap-y-0 md:grid-cols-3 items-center">
            {overviewItems.map((item, index) => (
              <div className="flex items-center md:justify-center" key={index}>
                <div className="w-12 h-12 bg-ntrl-white rounded-full md:mr-6 mr-4 flex items-center justify-center">
                  {item.logo?.data?.attributes?.url && (
                    <div className="w-[30px] h-[30px]">
                      <Image
                      src={item.logo.data.attributes.url}
                      alt={item.title}
                      style={{ objectFit: "contain" }}
                      />
                    </div>
                  )}
                </div>
                <div>
                  <h2 className="md:text-[40px]/[40px] text-ntrl-white font-medium md:text-center text-[24px]/[32px]">
                    {item.count}
                  </h2>
                  <p className="text-ntrl-white md:text-[18px]/[28px] md:font-semibold text-center text-[14px]/[22px]">
                    {item.title}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default GeneralInfo;
