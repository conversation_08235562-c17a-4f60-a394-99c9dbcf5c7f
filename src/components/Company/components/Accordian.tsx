"use client";
import React, { useState } from "react";
import { PlusIcon } from "@heroicons/react/24/outline";
import parse from "html-react-parser";
import { Element } from "html-react-parser";
import Image from "next/image";

const Accordian: React.FC<{
  title?: string;
  name?: string;
  description?: string;
  listStyle?: boolean;
  className?: string;
}> = (props) => {
  const [open, setOpen] = useState(false);

  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":
        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[12px]/[22px] md:text-[14px]/[24px] text-ntrl-black font-normal";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className = `${
            props.listStyle ? "list-decimal list-outside ml-6" : ""
          }  text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-black font-medium my-2 mt-3`;
          break;

        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };

  return (
    <div
      className={`bg-[white] rounded-2xl py-[10px] px-5 mb-[10px] border-[0.5px] border-blue-5 ${
        props.description && "cursor-pointer"
      } ${props.className}`}
      onClick={() => setOpen(!open)}
    >
      <div className="flex justify-between items-center">
        <h2 className="font-normal text-[16px]/[24px] text-ntrl-black">
          {props.title}
          <span className="font-normal">{props.name}</span>
        </h2>
        {props.description && (
          <div
            className={`transition-transform duration-300 -mr-3 ${
              open ? "-rotate-45" : ""
            }`}
          >
            <PlusIcon
              className="font-bold text-ntrl-black text-xl w-6 h-6"
              onClick={() => setOpen(!open)}
            />
          </div>
        )}
      </div>

      {/* Accordion Content with Animation */}
      {props.description && (
        <div
          className={` transition-all duration-500 ${
            open ? " block" : "hidden"
          }`}
        >
          <hr className="my-4" />
          {/* @ts-ignore */}
          {parse(props.description, { replace })}
        </div>
      )}
    </div>
  );
};

export default Accordian;
