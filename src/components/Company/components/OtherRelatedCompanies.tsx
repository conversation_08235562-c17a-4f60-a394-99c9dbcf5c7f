import Container from "@/components/globals/Container";
import CustomLink from "@/components/globals/CustomLink";
import Image from "next/image";
import { useState } from "react";
import { SlugsAttribute, AllCompSlugs } from "../../HealthInsurance/types";

const OtherRelatedCompanies = ({
  allComp,
  currentComp,
  category,
}: {
  allComp: AllCompSlugs[];
  currentComp: string;
  category: string;
}) => {
  const [showAll, setShowAll] = useState(false);
  const utm = "";

  const companies = allComp.filter(
    (comp: { attributes: SlugsAttribute }) =>
      comp.attributes.name !== currentComp &&
      comp.attributes.logo?.data?.attributes?.url
  );

  return (
    <section
      className="mt-[10px] px-4 py-2 md:mb-5 md:mt-5 scroll-m-28 bg-white md:p-10 md:border rounded-2xl"
      id="other"
    >
      <div>
        <div className="font-generalSans text-[18px]/[28px] md:text-[32px]/[36px] text-ntrl-black gap-2 flex items-start py-[10px]">
          <h2 className="font-medium">Other related companies</h2>
        </div>

        <div className="grid-cols-4 gap-3 hidden md:grid md:mt-5">
          {companies
            .slice(0, showAll ? companies.length : 8)
            .map((comp, idx) => (
              <CustomLink
                key={idx}
                href={`/${category}-insurance/${comp.attributes.slug}`}
                utm={utm}
                style="bg-white shadow-md rounded-xl p-2 flex items-center justify-center h-[100px] hover:shadow-xl"
              >
                <div className="relative w-[50%] h-[50%]">
                  <Image
                    src={comp.attributes.logo?.data?.attributes?.url}
                    fill={true}
                    alt={comp.attributes.name}
                    style={{ objectFit: "contain" }}
                  />
                </div>
              </CustomLink>
            ))}
        </div>
        <div className="grid-cols-3 gap-3 grid md:hidden">
          {companies
            .slice(0, showAll ? companies.length : 6)
            .map((comp, idx) => (
              <CustomLink
                key={idx}
                href={`/${category}-insurance/${comp.attributes.slug}`}
                utm={utm}
                style="bg-white shadow-md rounded-xl p-2 flex items-center justify-center h-[100px] hover:shadow-xl"
              >
                <div className="relative w-[50%] h-[50%]">
                  <Image
                    src={comp.attributes.logo?.data?.attributes?.url}
                    fill={true}
                    alt={comp.attributes.name}
                    style={{ objectFit: "contain" }}
                  />
                </div>
              </CustomLink>
            ))}
        </div>
        {companies.length > 6 && (
          <div className="flex justify-end mt-[10px] md:mt-4">
            <button
              onClick={() => setShowAll((prev) => !prev)}
              className="px-2 py-1 bg-white text-primary-1 rounded-xl shadow-md font-medium border"
            >
              {showAll ? "View Less" : "View More"}
            </button>
          </div>
        )}
      </div>
    </section>
  );
};

export default OtherRelatedCompanies;
