import Image from "next/image";
import parse, { Element } from "html-react-parser";

const About: React.FC<{
  title: string;
  company_desc: string;
}> = (props) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black1 text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className = "text-ntrl-black text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[14px]/[24px] md:text-[16px]/[24px] text-ntrl-black font-normal";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );
        case "strong":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[24px] text-ntrl-black font-semibold";
          break;

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  return (
    // <section id="about" className="mt-[10px] md:mt-5 scroll-m-28">
    //   <div className="text-[18px]/[24px] md:text-[20px]/[24px] text-ntrl-black gap-2 flex items-start px-5 py-[10px]">
    //     <h2 className="font-medium">{props.title}</h2>
    //   </div>
    //   <div className="leading-relaxed flex-grow bg-[white] rounded-xl py-5 px-[15px]">

    //     {parse(props.company_desc, { replace })}
    //   </div>
    // </section>
    <section
      id="about"
      className="md:mt-5 px-2.5 md:px-[50px] md:py-7 scroll-m-28 bg-white rounded-3xl"
    >
      <div className="text-[32px] text-ntrl-black gap-2 flex items-start py-[10px]">
        <h2 className="font-medium">{props.title}</h2>
      </div>
      <div className="md:bg-[white] flex items-center flex-row rounded-xl ">
        <div className="">
          {/* @ts-ignore */}
          {parse(props.company_desc, { replace })}
        </div>
      </div>
    </section>
  );
};

export default About;
