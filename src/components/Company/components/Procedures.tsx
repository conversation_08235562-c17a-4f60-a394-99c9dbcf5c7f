import { AccordianType } from "@/components/globals/types";
import Accordian from "./Accordian";

interface Procedure {
  title: string;
  steps: Array<AccordianType>;
}
const Procedure: React.FC<Procedure> = (props) => {
  const modifyHtmlForMobile = (steps: Array<AccordianType>): string => {
    const listItems = steps
      .map((step, idx) => {
        if (step.description) {
          const description = step.description.startsWith("<p>")
            ? step.description.slice(3, -4)
            : step.description;
          return `<li class="pr-5"><span class="font-medium text-[14px]/[24px]">Step ${
            idx + 1
          }. ${step.title} : </span><br/><p class="px-2">${description}</li>`;
        } else {
          return `<li class="pr-5"><span class="font-medium text-[14px]/[24px]">Step ${
            idx + 1
          } : <p class="px-2">${step.title}</p>  </span></li>`;
        }
      })
      .join("");
    return `<ol>${listItems}</ol>`;
  };
  return (
    <section className="md:px-5">
      <div className="md:hidden block">
        <Accordian
          title={props.title}
          description={modifyHtmlForMobile(props.steps)}
        />
      </div>
      <div className="font-generalSans text-[18px]/[24px] md:text-[20px]/[24px] text-primary-1 gap-2 items-start md:px-0 px-5 py-[10px] hidden md:block">
        <h3 className="font-medium text-black">{props.title}</h3>
      </div>
      {props.steps.map((step, idx) => (
        <Accordian
          key={idx}
          title={`Step ${idx + 1} - `}
          name={step.title}
          description={step.description}
          listStyle={false}
          className="hidden md:block"
        />
      ))}
    </section>
  );
};

const Procedures: React.FC<{
  title: string;
  id: string;
  procedures: Array<Procedure>;
}> = (props) => {
  return (
    <section
      id={props.id}
      className="mt-[10px] px-4 py-2 md:p-5 scroll-m-28 bg-ntrl-white rounded-3xl"
    >
      <div className="font-generalSans text-[18px]/[28px] md:text-[32px]/[36px] text-ntrl-black gap-2 flex items-start px-5 md:px-3 py-[10px]">
        <h2 className="font-medium">{props.title}</h2>
      </div>
      {props.procedures.map((procedure) => (
        <Procedure
          key={procedure.title}
          title={procedure.title}
          steps={procedure.steps}
        />
      ))}
    </section>
  );
};

export default Procedures;
