import Container from "@/components/globals/Container";
import parse, { Element } from "html-react-parser";
import Image from "next/image";
import { WhyChose } from "../../HealthInsurance/types";

const WhyChoose: React.FC<{
  name: string;
  why_chose: Array<WhyChose>;
}> = (props) => {
  const replace = (domNode: Element, index: number) => {
          if (domNode.attribs) {
            let attrs = domNode.attribs;
            if (attrs.style) {
              attrs.style = "";
            }
      
            switch (domNode.name) {
              case "h1":
      
              case "h2":
                attrs.className = "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black1 text-3xl mb-12 my-5";
                break;
      
              case "h3":
                attrs.className = "text-ntrl-black font-generalSans text-xl font-semibold my-5";
                break;
      
              case "h4":
              case "h5":
              case "h6":
                attrs.className = "text-ntrl-black text-lg font-semibold my-5";
                break;
      
              case "p":
                attrs.className = "text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
                break;
      
              case "a":
                attrs.className = "text-primary-2";
                break;
              
              case "li":
                attrs.className = "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
                break;
              case "tr":
              case "td":
              case "th":
                attrs.className = "border border-gray800 p-5 text-center";
                break;
      
              case "figure":
                attrs.className = "overflow-x-auto min-w-[300px]";
                break;
      
              case "img":
                return (
                  <div className="mx-auto h-64 aspect-video my-10 relative">
                    <Image
                      src={attrs.src}
                      fill={true}
                      style={{ objectFit: "contain" }}
                      alt="alt"
                    />
                  </div>
                );
      
              case "iframe":
                attrs.className = "my-0 mx-auto";
            }
            return domNode;
          }
        };
  return (
    <section className="bg-secondary-2 md:py-20 py-10" id="features">
      <Container>
        <div>
          {/* Heading */}
          <div className="md:mb-12 mb-6">
            <div className="font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black flex md:flex-row flex-col md:items-center items-start gap-2">
              <h2 className="font-normal">Why Choose</h2>
              <h2 className="font-medium">{props.name}</h2>
            </div>
          </div>

          {/* Reasons */}
          <div className="grid md:grid-cols-3 grid-cols-1 gap-x-[30px] md:gap-y-12 gap-y-6">
            {props.why_chose.map((wc, idx) => (
              <div key={idx}>
                <h3 className="font-generalSans font-medium text-[20px]/[28px] md:text-[24px]/[30px] text-ntrl-black mb-4 md:mb-6">
                  {wc.title}
                </h3>
                  <div>
                    {/* @ts-ignore */}
                    {parse(wc.description,{replace})} 
                  </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default WhyChoose;
