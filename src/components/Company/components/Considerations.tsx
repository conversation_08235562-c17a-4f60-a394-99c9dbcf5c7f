import Image from "next/image";
import parse, { Element } from "html-react-parser";
import { XCircleIcon } from "@heroicons/react/24/solid";
import { CheckCircleIcon } from "@heroicons/react/16/solid";
import { AccordianType } from "@/components/globals/types";
import Accordian from "./Accordian";
const Consideration: React.FC<{
  title: "Pros" | "Cons";
  considerations: Array<AccordianType>;
}> = (props) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className = "text-[14px]/[24px] text-ntrl-black font-normal";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  const modifyHtmlForMobile = (
    considerations: Array<AccordianType>
  ): string => {
    const listItems = considerations
      .map((consideration) => {
        const description = consideration.description.startsWith("<p>")
          ? consideration.description.slice(3, -4)
          : consideration.description;
        return `<li><p class="font-regular"><span class="font-medium text-[14px]/[24px] text-ntrl-black">${consideration.title} - </span>${description}</li>`;
      })
      .join("");
    return `<ol>${listItems}</ol>`;
  };

  return (
    <div className="bg-white md:p-6 rounded-2xl md:border-[0.5px]  md:mt-5">
      <section id={props.title} className="mt-4 scroll-m-28">
        <div className="hidden md:flex text-[28px] md:text-[32px]/[36px] text-ntrl-black gap-2 md:px-6 items-center px-5 py-[10px]">
          <h2 className="font-medium">{props.title}</h2>
          {props.title === "Cons" ? (
            <XCircleIcon className="w-6 h-6 text-red-1 hidden md:block" />
          ) : (
            "" // <CheckCircleIcon className="w-6 h-6 text-secondary-1 hidden md:block" />
          )}
        </div>
        <div className="hidden md:block md:columns-2 md:gap-5 md:p-5">
          {props.considerations.map((consideration, idx) => (
            <div
              key={idx}
              className="mb-5 break-inside-avoid bg-white rounded-3xl border-[0.5px] border-blue-5"
            >
              <div className="px-5 py-4 text-left flex flex-col">
                <div className="flex flex-row gap-2 items-center pb-3 md:pb-4">
                  <span className="font-poppins text-[24px]/[18px] md:text-[30px]/[22px] font-bold">
                    {idx + 1 < 10 ? `0${idx + 1}` : idx + 1}
                  </span>
                  <h3 className="text-[20px]/[22px] font-medium mb-[6px]">
                    {consideration.title}
                  </h3>
                </div>

                <div className="leading-relaxed flex-grow">
                  {/* @ts-ignore */}
                  {parse(consideration.description, { replace })}
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className="md:hidden">
          <Accordian
            title={props.title}
            name=""
            description={modifyHtmlForMobile(props.considerations)}
            listStyle={true}
          />
        </div>
      </section>
    </div>
  );
};

export default Consideration;
