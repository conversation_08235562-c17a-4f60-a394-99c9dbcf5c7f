import BookACallBtn from "@/components/globals/BookACall";

const CustomerSupport: React.FC<{
  email: string;
  contactNumber: string;
  company: string;
}> = (props) => {
  return (
    <section
      id="contact"
      className="mt-[10px] p-5 md:mt-5 scroll-m-28 bg-gradient-to-r from-gradient-1-blue to-gradient-1-green rounded-2xl md:p-10"
    >
      <div className="bg-white/10 w-full p-3 md:p-5 rounded-2xl border-[0.5px] border-white text-white">
        <h2 className="font-medium text-[22px]/[28px] md:text-[32px]/[36px] text-ntrl-white text-left py-[10px]">
          Customer Support
        </h2>
        <p className="text-[16px]/[20px] md:text-[20px]/[24px] text-ntrl-white text-left py-[10px]">
          Talk to our experts for Insurance Assistance for FREE
        </p>
        <BookACallBtn className="bg-ntrl-white text-primary-blue-3 md:text-base py-2 px-4 rounded-xl" />
      </div>

      <div className="flex flex-col md:flex-row gap-2 mt-3 text-ntrl-white">
        <div>
          <h3>
            For <span className="font-medium">{props.company}</span> customer
            care :
          </h3>
        </div>
        <div className="font-medium">
          <p>{`Toll Free - ${props.contactNumber}`}</p>
          <p>{`E-mail - ${props.email}`}</p>
        </div>
      </div>
    </section>
  );
};

export default CustomerSupport;
