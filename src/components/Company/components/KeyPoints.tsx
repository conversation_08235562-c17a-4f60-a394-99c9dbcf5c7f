import Container from "@/components/globals/Container";
import { CheckBadgeIcon } from "@heroicons/react/24/solid";
import Image from "next/image";

const KeyPoints: React.FC<{
  keyPoints: Array<{ point: string }>;
}> = (props) => {
  return (
    <section id="exclusions" className=" w-full mt-5 scroll-m-28 mb-2 ">
      <div className="w-full mx-auto bg-[white] py-3 px-4 md:p-10 flex flex-col rounded-2xl md:border-none ">
        <h2 className="font-medium text-[22px]/[28px] md:text-[32px]/[36px] text-ntrl-black text-left py-[10px]">
          Key points
        </h2>
        <div className="flex flex-row gap-2 mt-5">
          <div className="flex flex-col gap-2 md:pr-10 md:pb-5">
            {props.keyPoints.map((point, index) => (
              <div
                key={index}
                className="flex flex-row gap-2 items-center text-[14px]/[17px] text-ntrl-black"
              >
                <CheckBadgeIcon className="min-w-[20px] w-5 text-green-500" />
                <b className="font-semibold text-[10px]/[24px] md:text-[16px]/[24px] text-left">
                  {point.point}
                </b>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default KeyPoints;
