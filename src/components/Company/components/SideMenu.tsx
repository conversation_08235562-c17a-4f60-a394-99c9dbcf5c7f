import { useEffect, useState } from "react";
import BookACallBtn from "@/components/globals/BookACall";
import useNavbarStore from "@/store/navbar";

const SideMenu: React.FC = () => {
  const [activeSection, setActiveSection] = useState<string>("");
  const navbarStore = useNavbarStore();
  const sideMenu = [
    { title: "OneAssure Verdict", id: "verdict" },
    { title: "Company Stats", id: "stats" },
    { title: "About the company", id: "about" },
    { title: "Insurance Policies", id: "policies" },
    { title: "KYC Documents", id: "kycDocs" },
    { title: "Claims", id: "claimSettlement" },
    { title: "Renewals", id: "renewals" },
    { title: "Contact", id: "contact" },
    { title: "Other companies", id: "other" },
    { title: "FAQs", id: "faqs" },
  ];
  useEffect(() => {
    // Create an intersection observer
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveSection(entry.target.id);
            // Update URL hash without scrolling
            const newUrl = `${window.location.pathname}#${entry.target.id}`;
            window.history.replaceState(null, "", newUrl);
          }
        });
      },
      {
        // Adjust these values based on when you want the highlight to trigger
        rootMargin: "-20% 0px -70% 0px",
        threshold: 0,
      }
    );

    // Observe all sections
    sideMenu.forEach((menu) => {
      const element = document.getElementById(menu.id);
      if (element) observer.observe(element);
    });

    return () => {
      // Cleanup observer
      sideMenu.forEach((menu) => {
        const element = document.getElementById(menu.id);
        if (element) observer.unobserve(element);
      });
    };
  }, [sideMenu]);

  const handleClick = (id: string) => {
    // Get the element
    if (typeof window === "undefined") return;

    const element = document.getElementById(id);
    if (!element) return;

    const currentScroll = window.scrollY;
    const navbar = document.querySelector("nav");
    const navbarHeight = navbar ? navbar.getBoundingClientRect().height * 2 : 0;

    const scrollToPosition =
      element.getBoundingClientRect().top + currentScroll - navbarHeight;

    if (navbarStore.isNavbarVisible) {
      window.scrollTo({ top: scrollToPosition, behavior: "smooth" });
    } else {
      element.scrollIntoView({ behavior: "smooth" });
    }
    // Update URL hash
    const newUrl = `${window.location.pathname}#${id}`;
    window.history.replaceState(null, "", newUrl);
  };
  return (
    <section id="sideMenu" className="hidden lg:block col-span-2 mb-[30px]">
      <div className="sticky top-32">
        <div className="bg-white px-5 py-7 rounded-3xl text-center">
          {sideMenu.map((menu) => (
            <div key={menu.id} className="my-5">
              <span
                onClick={() => handleClick(menu.id)}
                className={`
                cursor-pointer transition-all duration-200 text-nowrap text-[16px]/[20px]  
                ${
                  activeSection === menu.id
                    ? "text-ntrl-black font-semibold "
                    : "hover:text-primary-1 hover:font-semibold font-light"
                }
                `}
              >
                {menu.title}
              </span>
            </div>
          ))}
        </div>
        <div className="py-5 text-center flex justify-center">
          <BookACallBtn
            icon={true}
            className="py-2 px-3 w-full rounded-2xl bg-gradient-to-r from-gradient-1-blue to-gradient-1-green text-ntrl-white flex gap-4 items-center justify-center md:text-lg"
            label="Book a Call"
          />
        </div>
      </div>
    </section>
  );
};

export default SideMenu;
