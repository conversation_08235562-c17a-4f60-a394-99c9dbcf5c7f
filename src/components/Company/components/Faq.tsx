import { AccordianType } from "@/components/globals/types";
import Accordian from "./Accordian";

const Faqs: React.FC<{
  title: string;
  faqs: Array<{ question: string; ans: string }>;
}> = (props) => {
  const generateFAQsHtml = (aspects: Array<AccordianType>): string => {
    const listItems = aspects
      .map((aspect) => {
        const description = aspect.description.startsWith("<p>")
          ? aspect.description.slice(3, -4)
          : aspect.description;
        return `<li><p class="font-regular "><span class="font-medium text-[14px]/[24px] text-ntrl-black">${aspect.title} - </span>${description}</li>`;
      })
      .join("");
    return `<ol>${listItems}</ol>`;
  };
  return (
    <section
      id="faqs"
      className="mt-[10px] md:mt-5 scroll-m-28 mb-[30px] bg-white rounded-3xl p-5"
    >
      <div className="font-generalSans text-[18px]/[28px] md:text-[32px]/[36px] text-ntrl-black gap-2 flex items-start px-5 md:px-3 py-[10px]">
        <h2 className="font-medium">{props.title}</h2>
      </div>

      <div className=" mt-5 md:block hidden">
        {props.faqs.map((faq, idx) => (
          <Accordian
            key={idx}
            title={`${idx + 1}. ${faq.question}`}
            name=""
            description={faq.ans}
          />
        ))}
      </div>
      <Accordian
        title="Frequently Asked Questions"
        name=""
        description={generateFAQsHtml(
          props.faqs.map((faq) => {
            return { title: faq.question, description: faq.ans };
          })
        )}
        className="md:hidden"
        listStyle={true}
      />
    </section>
  );
};

export default Faqs;
