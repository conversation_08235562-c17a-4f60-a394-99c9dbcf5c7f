"use client";

import type React from "react";
interface RiskGaugeMeterProps {
  value: number;
  className?: string;
}

const RiskGaugeMeter: React.FC<RiskGaugeMeterProps> = ({
  value = 9,
  className,
}) => {
  const normalizedValue = Math.min(Math.max(value, 0), 10) / 10;

  return (
    <div className={`flex flex-col mx-auto ${className}`}>
      <svg viewBox="0 0 220 120" className="w-full">
        <defs>
          <linearGradient id="gauge-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#f30a0a" />
            <stop offset="50%" stopColor="#d69c41" />
            <stop offset="100%" stopColor="#63ef48" />
          </linearGradient>
        </defs>

        {/* Gauge background */}
        <path
          d="M10 110 A 100 100 0 0 1 210 110"
          fill="none"
          stroke="#f1ebeb"
          strokeWidth="20"
          strokeLinecap="round"
        />

        {/* Colored gauge */}
        <path
          d="M10 110 A 100 100 0 0 1 210 110"
          fill="none"
          stroke="url(#gauge-gradient)"
          strokeWidth="20"
          strokeLinecap="round"
          strokeDasharray="314.159"
          strokeDashoffset={314.159 * (1 - normalizedValue)}
          className="transition-all duration-500 ease-in-out"
        />

        {/* Value text */}
        <text
          x="110"
          y="110"
          textAnchor="middle"
          fill="#1a1a1a"
          fontSize="20px"
          fontWeight="bold"
          style={{ fontSize: "40px" }} // Ensure inline style as fallback
        >
          {parseFloat(value.toFixed(1))}/10
        </text>
      </svg>
      <p className="md:hidden flex text-[12px]/4 font-bold pt-2">
        OneAssure Rating
      </p>
    </div>
  );
};

export default RiskGaugeMeter;
