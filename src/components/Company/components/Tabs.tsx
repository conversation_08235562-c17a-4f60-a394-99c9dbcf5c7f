import Container from "@/components/globals/Container";

const Tabs = () => {
  const tabs = [
    { text: "Plans", link: "#plans" },
    { text: "Overview", link: "#overview" },
    { text: "Features", link: "#features" },
    { text: "KYC Documents", link: "#kyc" },
    { text: "Exclusions", link: "#exclusions" },
    { text: "Claims", link: "#claims" },
    // { text: "Documents Required", link: "#documents-required" },
    { text: "FAQ", link: "#faq" },
  ];

  return (
    <aside className="border-b bg-white border-ntrl-outline">
      {/* <aside className="border-b bg-white border-ntrl-outline sticky top-[96.61px] z-40"> */}
      <Container>
        <div className="flex items-center gap-8 overflow-x-scroll">
          {tabs.map((t, idx) => (
            <a
              key={idx}
              className="py-6 px-3 whitespace-nowrap cursor-pointer"
              href={t.link}
            >
              <span className="text-[24px]/[32px] text-ntrl-grey1">
                {t.text}
              </span>
            </a>
          ))}
        </div>
      </Container>
    </aside>
  );
};

export default Tabs;
