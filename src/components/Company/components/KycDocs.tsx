import { CheckBadgeIcon } from "@heroicons/react/24/solid";

import Container from "@/components/globals/Container";

const KycDocs: React.FC<{
  title: string;
  docs: Array<{
    name: string;
  }>;
}> = (props) => {
  return (
    <section className="md:mt-5 border-blue-5 px-2.5 md:px-[50px] md:py-7 scroll-m-28 bg-white rounded-xl border-[0.5px] mt-5 md:border-none" id="kycDocs">

        <div className=" text-[20px]/[28px] md:text-[32px]/[36px] text-ntrl-black gap-2 flex items-start py-[10px]">
          <h2 className="font-medium">{props.title}</h2>
        </div>

        <div className="w-full bg-[white] rounded-xl py-5 mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-y-5">
            {props.docs.map((doc, idx) => {
              return (
                <div className="flex gap-x-2 items-center" key={idx}>
                  <CheckBadgeIcon className="min-w-[20px] w-5 text-green-500" />

                  <h3 className="text-[16px]/[24px] md:text-[16px]/[24px] font-medium text-ntrl-black pr-2">
                    {doc.name}
                  </h3>
                </div>
              );
            })}
          </div>
        </div>

    </section>
  );
};

export default KycDocs;
