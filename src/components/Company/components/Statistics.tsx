"use client";
import { CheckBadgeIcon } from "@heroicons/react/24/solid";
import { FiInfo } from "react-icons/fi";
import { FaRegHospital } from "react-icons/fa6";
import { StatisticsProps } from "../types";

const Statistics: React.FC<StatisticsProps> = ({
  companyName,
  graphs,
  rating,
  ratingDescription,
  networkHospitals,
  claimSettlement,
}) => {
  const calculateHeight = (value: number, maxValue: number) => {
    return `${(value / maxValue) * 144}px`;
  };

  return (
    <section
      className="mt-[10px] border-[0.5]px md:mt-5 scroll-m-28 bg-white rounded-3xl mb-5 md:py-7"
      id="stats"
    >
      <div className="text-[22px]/[28px] md:text-[32px]/[36px] text-ntrl-black gap-2 flex items-start  py-[10px]">
        <h2 className="font-medium p-4 md:px-[50px] ">Company Stats</h2>
      </div>

      <div className="w-full bg-[white] rounded-3xl px-5 md:px-11 py-5 mx-auto mb:5">
        <div className="grid grid-cols-1 md:grid-cols-2 md:gap-9">
          <div className="flex py-5 md:pb-0">
            {graphs.map((graph, index) => {
              const maxValue = Math.max(graph.industry, graph.company);

              return (
                <div key={index} className="w-[50%]">
                  <div className="flex items-center justify-end">
                    {graph.description && (
                      <div className="relative inline-block">
                        <div className="peer p-2 -m-2 inline-flex items-center hover:cursor-pointer">
                          <FiInfo className="text-ntrl-black w-4 h-4" />
                        </div>
                        <div className="invisible opacity-0 scale-95 peer-hover:visible peer-hover:opacity-100 peer-hover:scale-100 transition-all duration-300 ease-in-out absolute z-10 right-3 top-2 w-48 bg-white text-ntrl-grey1 shadow-md text-xs rounded-lg py-2 px-3">
                          {graph.description}
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="h-48">
                    <div className="w-full flex items-end h-40 justify-center pr-5 mt-5">
                      <div className="flex flex-col items-center w-[40%]">
                        <div className="mt-2">
                          <p className="font-medium text-[8px]/[8px] text-nowrap">
                            Industry Avg.
                          </p>
                        </div>
                        <div className="w-full flex justify-center">
                          <div
                            className="w-4 bg-gradient-1-green mt-2"
                            style={{
                              height: calculateHeight(graph.industry, maxValue),
                            }}
                          ></div>
                        </div>
                        <hr className="ml-2 border-primary-blue-3 border-2 w-full rounded-l-full bg-primary-blue-3" />
                        <div className="mt-2">
                          <div className="text-primary-1 font-medium text-[12px]/[12px]">
                            {graph.industry}
                            {graph.suffix}
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-center w-[40%]">
                        <div className="mt-2">
                          <p className="font-medium text-[8px]/[8px] max-w-24 text-center break-words">
                            {companyName}
                          </p>
                        </div>
                        <div className="w-full flex justify-center">
                          <div
                            className="w-4 bg-gradient-1-blue mt-2"
                            style={{
                              height: calculateHeight(graph.company, maxValue),
                            }}
                          ></div>
                        </div>
                        <hr className="mr-2 border-primary-blue-3 border-2 w-full rounded-r-full bg-primary-blue-3" />
                        <div className="mt-2">
                          <div className="text-primary-1 font-medium text-[12px]/[12px]">
                            {graph.company}
                            {graph.suffix}
                          </div>
                        </div>
                      </div>
                    </div>
                    <p className="text-primary-1 text-center pt-2 text-[12px]/[12px]">
                      {graph.title}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
          <div className="space-y-4 p-2 pt-5">
            <div className="flex items-center justify-between">
              <h2 className="text-4/6 font-bold">
                Rating{" "}
                {parseFloat(
                  rating.reduce((acc, curr) => acc + curr.rating, 0).toFixed(1)
                )}
                /{rating.reduce((acc, curr) => acc + curr.outOf, 0)}
              </h2>
              {ratingDescription && (
                <div className="relative inline-block">
                  <div className="peer relative p-2 -m-2 inline-flex items-center transition hover:cursor-pointer">
                    <FiInfo className="text-gray-500 w-4 h-4" />
                  </div>
                  <div className="invisible opacity-0 scale-95 peer-hover:visible peer-hover:opacity-100 peer-hover:scale-100 transition-all duration-300 ease-in-out absolute z-10 right-3 top-2 w-48 bg-white text-ntrl-grey1 shadow-md text-xs rounded-lg py-2 px-3">
                    {ratingDescription}
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-4">
              {rating.map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-3/5">{item.title}</span>
                    <span className="text-3/5">
                      {item.rating}/{item.outOf}
                    </span>
                  </div>
                  <div className="h-2 bg-gray-200 rounded-full">
                    <div
                      className="h-full bg-gradient-to-r from-gradient-1-blue to-gradient-1-green rounded-full"
                      style={{
                        width: `${(item.rating / item.outOf) * 100}%`,
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        {(claimSettlement || networkHospitals) && (
          <div
            className={`md:pt-5 border-t grid gap-4 ${
              claimSettlement && networkHospitals
                ? "md:grid-cols-2 grid-cols-1"
                : "grid-cols-1 justify-center"
            }`}
          >
            {networkHospitals && (
              <div className="flex items-center justify-center gap-4">
                <FaRegHospital className="text-primary-1 w-10 h-10 p-1" />
                <span className="text-4/6 font-normal">
                  {networkHospitals.toLocaleString()} Network Hospitals
                </span>
              </div>
            )}

            {claimSettlement && (
              <div className="flex items-center justify-center gap-4">
                <CheckBadgeIcon className="text-primary-1 w-10 h-10 p-1" />
                <span className="text-4/6 font-normal">
                  {claimSettlement}% Claim settlement
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    </section>
  );
};

export default Statistics;
