"use client";
import Container from "@/components/globals/Container";
import parse, { Element } from "html-react-parser";
import Image from "next/image";

const InDepthOverview: React.FC<{
  overview: string;
}> = (props) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
        // attrs.className =
        //   "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
        // break;

        case "h3":
          attrs.className =
            "font-normal text-3xl text-center md:text-5xl text-ntrl-black mb-6";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "table":
          attrs.className =
            "mx-auto font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5 drop-shadow-lg";
          break;

        case "tr":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;
        case "td":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  return (
    <section className="overflow-hidden" id="overview">
      <Container>
        {/* @ts-ignore */}
        <div className="">{parse(props.overview, { replace })}</div>
      </Container>
    </section>
  );
};

export default InDepthOverview;
