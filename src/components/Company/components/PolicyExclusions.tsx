import Container from "@/components/globals/Container";

import { Exclusion } from "../../HealthInsurance/types";
import Image from "next/image";
import parse, { Element } from "html-react-parser";

const PolicyExclusion: React.FC<{
  title:string
  exclusions: Array<Exclusion>;
}> = (props) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className = "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className = "text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;
        
        case "li":
          attrs.className = "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  return (
    <section id="exclusions">
      <Container>
        <div className="bg-primary-3 px-4 py-10 md:p-16  rounded-xl">
          {/* Heading */}
          <div className="mb-12">
            <div className="font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black flex items-center gap-2">
              <h2 className="font-normal">{props.title}</h2>
            </div>
          </div>

          <div>
            {props.exclusions.map((exclusion, idx) => {
              return (
                <div key={idx}>
                  <div className="mb-[30px]">
                    {/* heading */}
                    <div className="flex items-center mb-6">
                      <div
                        className="w-[30px] h-[30px] mr-6 md:w-12 md:h-12 bg-primary-1 rounded-lg flex items-center justify-center
                      "
                      >
                        <Image
                          src={exclusion.icon.data?.attributes?.url || (idx==0?"https://cdn.oasr.in/oa-site/cms-uploads/media/ad_group_141a518a1a.png": "https://cdn.oasr.in/oa-site/cms-uploads/media/ad_group_off_c2aefca112.png")}
                          alt="exc"
                          width={24}
                          height={24}
                        />
                      </div>
                      <h2 className="font-generalSans font-medium text-[20px]/[28px] md:text-[24px]/[30px] text-ntrl-black">
                        {exclusion.title}
                      </h2>
                    </div>
                    <div className="">
                      {/* @ts-ignore */}
                      {parse(exclusion.description,{replace})}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default PolicyExclusion;
