import Container from "@/components/globals/Container";
import { CheckCircleIcon } from "@heroicons/react/24/outline";

const DocumentsList = () => {
  return (
    <div className="bg-primary-3">
      <Container>
        <div className="py-20">
          {/* Heading */}
          <div className="mb-12">
            <div className="font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black">
              <h2 className="font-normal">List of Documents</h2>
              <h2 className="font-medium">Required for Claim Process</h2>
            </div>
          </div>

          {/* Document list */}
          <div className="grid md:grid-cols-3 grid-cols-1 gap-[30px]">
            {new Array(3).fill(0).map((e, idx) => (
              <div
                className="bg-ntrl-white px-6 md:py-8 py-6 rounded-2xl"
                key={idx}
              >
                {/* icon */}
                <div className="md:w-14 md:h-14 w-9 h-9 bg-primary-2 mb-10"></div>

                {/* heading */}
                <div className="font-generalSans md:text-[24px]/[30px] text-[20px]/[28px] text-ntrl-black font-medium">
                  <h2>Claim Form and</h2>
                  <h2 className="">Identification</h2>
                </div>

                {/* divider */}
                <div className="border border-ntrl-outline mt-5 mb-6"></div>

                {/* document list */}
                <div>
                  <div className="flex items-center mb-4">
                    <CheckCircleIcon className="w-5 h-5 text-primary-1 mr-[10px]" />
                    <p className="text-ntrl-black text-[16px]/[24px]">
                      Completed and signed claim form
                    </p>
                  </div>
                  <div className="flex items-center mb-4">
                    <CheckCircleIcon className="w-5 h-5 text-primary-1 mr-[10px]" />
                    <p className="text-ntrl-black text-[16px]/[24px]">
                      Completed and signed claim form
                    </p>
                  </div>
                  <div className="flex items-center mb-4">
                    <CheckCircleIcon className="w-5 h-5 text-primary-1 mr-[10px]" />
                    <p className="text-ntrl-black text-[16px]/[24px]">
                      Completed and signed claim form
                    </p>
                  </div>
                  <div className="flex items-center mb-4">
                    <CheckCircleIcon className="w-5 h-5 text-primary-1 mr-[10px]" />
                    <p className="text-ntrl-black text-[16px]/[24px]">
                      Completed and signed claim form
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default DocumentsList;
