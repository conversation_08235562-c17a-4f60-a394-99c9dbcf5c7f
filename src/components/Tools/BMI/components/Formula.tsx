import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import {
  BodyLarge,
  HeadingLarge,
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import PillBadge from "@/components/globals/PillBadge";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import { FaCalculator, FaCheck, FaGlobe } from "react-icons/fa";

type FormulaCard = {
  iconBg: string;
  icon: React.ReactNode;
  title: string;
  description: string;
};

const formulaCards: FormulaCard[] = [
  {
    iconBg: "bg-secondary-100",
    icon: <FaCalculator className="text-secondary-500 text-xl" />,
    title: "Simple Calculation",
    description: "Weight ÷ Height² in meters",
  },
  {
    iconBg: "bg-purple-100",
    icon: <FaCheck className="text-purple-500 text-xl" />,
    title: "Quick Health Check",
    description: "Know if weight is balanced",
  },
  {
    iconBg: "bg-orange-100",
    icon: <FaGlobe className="text-orange-500 text-xl" />,
    title: "Globally Accepted",
    description: "Standard method used worldwide",
  },
];

const FormulaCard = ({ iconBg, icon, title, description }: FormulaCard) => (
  <div className="flex flex-col items-center gap-3 bg-white border border-primary-200 rounded-xl p-6 shadow-sm w-full">
    <div
      className={`w-16 h-16 ${iconBg} rounded-full flex items-center justify-center`}
    >
      {icon}
    </div>
    <HeadingSmall
      as="h3"
      className="text-neutral-1100 font-semibold text-center"
    >
      {title}
    </HeadingSmall>
    <BodyLarge as="p" className="text-neutral-900 text-center">
      {description}
    </BodyLarge>
  </div>
);

const Formula = () => {
  return (
    <SectionContainerLarge id="bmi-formula" className="!px-0">
      <PillBadge pill="BMI Calculator" />
      <HeadingXLarge as="h2" className="text-center mt-2 md:mt-3 mb-6">
        Standard BMI Formula
      </HeadingXLarge>
      <div className="flex flex-col gap-4 md:gap-6">
        <div className="border-2 border-primary-200 rounded-xl py-3 px-auto md:p-6 bg-white mx-6 md:mx-0">
          <HeadingLarge
            as="h3"
            className="text-neutral-1100 text-center font-semibold md:block hidden"
          >
            Body Mass Index (BMI) = Weight (kg) ÷ Height (m)²
          </HeadingLarge>
          <HeadingLarge
            as="h3"
            className="text-neutral-1100 text-center font-semibold md:hidden block"
          >
            BMI = Weight (kg) ÷ Height (m)²
          </HeadingLarge>
        </div>

        {/* Desktop Grid Layout */}
        <div className="hidden md:grid md:grid-cols-3 gap-4">
          {formulaCards.map((card, index) => (
            <FormulaCard key={index} {...card} />
          ))}
        </div>

        {/* Mobile Carousel Layout */}
        <div className="md:hidden">
          <MobileCarousel totalSlides={formulaCards.length}>
            {formulaCards.map((card, index) => (
              <MobileCarouselItem key={index}>
                <FormulaCard {...card} />
              </MobileCarouselItem>
            ))}
          </MobileCarousel>
        </div>
      </div>
    </SectionContainerLarge>
  );
};

export default Formula;
