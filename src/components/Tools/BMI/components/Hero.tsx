import React from "react";
import SectionHeader from "@/components/globals/SectionHeader";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import PageTopBar from "@/components/globals/PageTopBar";
import { usePathname } from "next/navigation";
import SectionContainer from "@/components/globals/SectionContainer";
import Calculator from "@/components/Tools/BMI/components/Calculator";

const breadcrumbPath = ["OneAssure", "BMI Calculator"];

const Hero = () => {
  const pathname = usePathname();
  const fullUrl = pathname;

  return (
    <SectionContainer className="mt-6 md:mt-8">
      <PageTopBar breadcrumbPath={breadcrumbPath} fullUrl={fullUrl} />
      <SectionContainerMedium className="mb-0 md:mb-0 !p-0">
        <SectionHeader
          pill="Body Mass Index"
          heading="BMI Calculator"
          subheading="Use our free BMI calculator for men and women to quickly check your Body Mass Index. Discover whether you're underweight, normal, overweight, or obese based on your height and weight - only at Oneassure."
          pillComponent="h2"
        />
        <Calculator />
      </SectionContainerMedium>
    </SectionContainer>
  );
};

export default Hero;
