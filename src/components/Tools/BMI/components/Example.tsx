import React from "react";
import {
  HeadingXLarge,
  HeadingSmall,
  BodyLarge,
  HeadingLarge,
  BodyMedium,
} from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";

type CardData = {
  title: string;
  content: string | string[];
};

type PersonMetric = {
  label: string;
  value: string;
};

type BMIExampleProps = {
  title?: string;
  topCardsData?: CardData[];
  bmiResultData?: CardData;
  mobilePersonMetrics?: PersonMetric[];
  mobileBMIValue?: string;
  mobileBMIInterpretation?: string;
};

type CardProps = {
  data: CardData;
  className?: string;
};

const Card: React.FC<CardProps> = ({ data, className = "" }) => {
  return (
    <div
      className={`bg-white rounded-xl p-6 border border-primary-200 shadow-sm ${className}`}
    >
      <HeadingSmall
        as="h3"
        className="text-neutral-1100 text-center font-semibold mb-4"
      >
        {data.title}
      </HeadingSmall>
      <div className="text-gray-700 text-center">
        {Array.isArray(data.content) ? (
          <div className="flex justify-around">
            {data.content.map((item, index) => (
              <BodyLarge key={index}>{item}</BodyLarge>
            ))}
          </div>
        ) : (
          <BodyLarge>{data.content}</BodyLarge>
        )}
      </div>
    </div>
  );
};

const BMIExample: React.FC<BMIExampleProps> = ({
  title = "BMI Calculation Example",
  topCardsData = [],
  bmiResultData,
  mobilePersonMetrics = [],
  mobileBMIValue,
  mobileBMIInterpretation,
}) => {
  return (
    <SectionContainerLarge id="bmi-example">
      {/* Main Title */}
      <HeadingXLarge
        as="h2"
        className="text-neutral-1100 text-center font-semibold mb-6"
      >
        {title}
      </HeadingXLarge>

      {/* Top Row - Three Cards */}
      {topCardsData.length > 0 && (
        <div className="md:grid grid-cols-1 md:grid-cols-3 gap-8 mb-4 hidden">
          {topCardsData.map((cardData, index) => (
            <Card key={index} data={cardData} />
          ))}
        </div>
      )}

      {/* BMI Result Card - Full Width */}
      {bmiResultData && (
        <div className="bg-white rounded-xl px-6 py-5 border border-primary-200 shadow-sm md:block hidden">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
            {/* BMI Result */}
            <div className="flex-shrink-0">
              <HeadingLarge
                as="h3"
                className="text-neutral-1100 text-center font-semibold"
              >
                {bmiResultData.title}
              </HeadingLarge>
            </div>

            {/* Description */}
            <div className="flex-1">
              <BodyMedium className="text-neutral-900">
                {bmiResultData.content}
              </BodyMedium>
            </div>
          </div>
        </div>
      )}

      {/* Mobile BMI Result Card */}
      {(mobilePersonMetrics.length > 0 ||
        mobileBMIValue ||
        mobileBMIInterpretation) && (
        <div className="md:hidden bg-white rounded-xl p-4 border border-primary-300 shadow-sm">
          <div className="space-y-4">
            {/* Personal Metrics */}
            {mobilePersonMetrics.length > 0 && (
              <div className="space-y-3 flex flex-col items-center">
                {mobilePersonMetrics.map((metric, index) => (
                  <div key={index} className="flex items-center">
                    <BodyMedium className="text-neutral-1100 font-semibold">
                      {metric.label} -
                    </BodyMedium>
                    <BodyMedium className="text-neutral-1100">
                      {metric.value}
                    </BodyMedium>
                  </div>
                ))}
              </div>
            )}
            {mobileBMIValue && (
              <div className="flex items-center justify-center">
                <HeadingSmall
                  as="h3"
                  className="text-neutral-1100 font-semibold"
                >
                  BMI: {mobileBMIValue}
                </HeadingSmall>
              </div>
            )}
            {/* BMI Interpretation */}
            {mobileBMIInterpretation && (
              <BodyMedium className="text-neutral-1100 text-justify">
                {mobileBMIInterpretation}
              </BodyMedium>
            )}
          </div>
        </div>
      )}
    </SectionContainerLarge>
  );
};

export default BMIExample;
