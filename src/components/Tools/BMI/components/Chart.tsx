"use client";
import React, { useState } from "react";
import { Download } from "lucide-react";
import {
  bmiData,
  heightRanges,
  weights,
} from "@/components/Tools/BMI/data/bmiData";
import {
  BodyLarge,
  BodyMedium,
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { FiArrowRight } from "react-icons/fi";

type BMIChartProps = {
  className?: string;
};

const BMIChart: React.FC<BMIChartProps> = ({ className = "" }) => {
  const [selectedHeightRange, setSelectedHeightRange] = useState("4ft-5ft");

  const handleDownload = () => {
    window.open(
      "https://cdn.oasr.in/oa-site/cms-uploads/media/BMI_Chart_Specific_dc2ed5fd7d.xlsx",
      "_blank"
    );
  };

  const currentRangeData = bmiData[selectedHeightRange as keyof typeof bmiData];
  const currentHeights = Object.keys(currentRangeData)
    .map(Number)
    .sort((a, b) => a - b);

  return (
    <SectionContainerLarge
      className={`${className} flex flex-col gap-3 md:gap-4`}
    >
      {/* Mobile Header */}
      <div className="md:hidden flex flex-col gap-4">
        {/* Title */}
        <HeadingXLarge
          as="h2"
          className="text-neutral-1100 font-semibold text-center"
        >
          Body Mass Index (BMI) Chart
        </HeadingXLarge>

        {/* Height Range Selector */}
        <div className="flex flex-col items-center gap-2">
          <HeadingSmall as="h3" className="text-primary-800 font-medium">
            Select Height Range
          </HeadingSmall>
          <div className="flex bg-primary-100 border border-primary-300 rounded-full p-1">
            {heightRanges.map((range, index) => (
              <button
                key={range.id}
                onClick={() => setSelectedHeightRange(range.id)}
                className={`transition-all duration-100 py-2 px-6 rounded-full ${
                  selectedHeightRange === range.id
                    ? "bg-primary-800 text-white shadow-md"
                    : "text-primary-800"
                } ${index === 0 ? "rounded-l-full" : ""} ${
                  index === heightRanges.length - 1 ? "rounded-r-full" : ""
                }`}
              >
                <BodyLarge>{range.label}</BodyLarge>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Desktop Header */}
      <div className="hidden md:flex flex-col md:flex-row items-center gap-6">
        {/* Title */}
        <HeadingXLarge
          as="h2"
          className="text-neutral-1100 font-semibold w-[45%]"
        >
          Body Mass Index (BMI) Chart
        </HeadingXLarge>

        {/* Height Range Selector */}
        <div className="flex flex-col md:flex-row items-center gap-4 pl-6 pr-2 py-2 border border-primary-300 rounded-full bg-primary-100 w-[55%]">
          <HeadingSmall as="h3" className="text-primary-800 font-medium">
            Select Height Range
          </HeadingSmall>
          <div className="flex gap-4">
            {heightRanges.map((range) => (
              <button
                key={range.id}
                onClick={() => setSelectedHeightRange(range.id)}
                className={`transition-all duration-100 py-2 px-8 rounded-full ${
                  selectedHeightRange === range.id
                    ? "bg-primary-800 text-white shadow-md"
                    : " text-primary-800"
                }`}
              >
                <BodyLarge>{range.label}</BodyLarge>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between md:justify-end gap-12">
        <button
          onClick={handleDownload}
          className="flex items-center gap-2 px-3 py-2 text-primary-800 hover:text-primary-700 transition-colors"
        >
          <Download className="w-4 h-4" />
          <BodyMedium>Download BMI Chart</BodyMedium>
        </button>
        <div className="flex items-center gap-3 text-primary-800">
          <BodyMedium>Swipe</BodyMedium>
          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-xs text-neutral-1100 group-hover:bg-primary-100 transition-colors">
            <FiArrowRight />
          </div>
        </div>
      </div>

      {/* BMI Chart Table */}
      <div className="overflow-x-auto border border-primary-300 rounded-xl relative">
        <table className="w-full border-collapse">
          <thead>
            <tr>
              {/* Diagonal Header Cell */}
              <th className="w-28 md:w-24 h-24 md:h-20 border border-primary-300 bg-white sticky left-0 z-10">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="transform -rotate-45 text-xs md:text-xs font-medium text-primary-800 leading-tight">
                    <div className="text-right">Weight</div>
                    <div className="text-left">Height</div>
                  </div>
                </div>
              </th>

              {/* Weight Headers */}
              {weights.map((weight) => (
                <th
                  key={weight}
                  className="w-24 md:w-20 h-20 md:h-20 border border-primary-300 text-center bg-neutral-50 text-primary-800 p-3 md:py-3"
                >
                  <BodyLarge className="text-sm md:text-base">
                    {weight}kgs
                  </BodyLarge>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {currentHeights.map((heightInches, index) => {
              const height = `${Math.floor(heightInches / 12)}'${
                heightInches % 12
              }"`;
              const weightData =
                currentRangeData[heightInches as keyof typeof currentRangeData];

              return (
                <tr key={heightInches}>
                  {/* Height Header */}
                  <th className="w-28 md:w-24 h-20 md:h-24 border border-primary-300 bg-neutral-50 text-center text-primary-800 p-3 md:py-3 sticky left-0 z-10">
                    <BodyLarge className="text-sm md:text-base">
                      {height}
                    </BodyLarge>
                  </th>

                  {/* BMI Values */}
                  {weights.map((weight) => {
                    const bmi = weightData?.[
                      weight as keyof typeof weightData
                    ] as number | undefined;
                    return (
                      <td
                        key={weight}
                        className="w-24 md:w-20 h-24 md:h-20 border border-primary-300 text-center text-neutral-800 p-3 md:py-3"
                      >
                        <BodyLarge className="text-sm md:text-base">
                          {bmi ? bmi.toFixed(1) : "-"}
                        </BodyLarge>
                      </td>
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </SectionContainerLarge>
  );
};

export default BMIChart;
