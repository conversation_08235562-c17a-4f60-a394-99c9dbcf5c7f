import React from "react";
import {
  HeadingXLarge,
  HeadingSmall,
  BodyMedium,
} from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import { FaClock, FaHeart, FaUserShield } from "react-icons/fa";

interface UsecaseCardProps {
  icon: React.ReactNode;
  iconBgColor: string;
  heading: string;
  benefit: string;
}

const UsecaseCard: React.FC<UsecaseCardProps> = ({
  icon,
  iconBgColor,
  heading,
  benefit,
}) => {
  return (
    <div className="flex flex-col items-center text-center p-6 bg-white rounded-xl border border-blue-200 shadow-sm gap-3 w-full">
      <div
        className={`w-16 h-16 rounded-full ${iconBgColor} flex items-center justify-center`}
      >
        {icon}
      </div>

      <HeadingSmall as="h3" weight="semibold" className="text-neutral-1100">
        {heading}
      </HeadingSmall>

      <BodyMedium className="text-neutral-1100 text-center">
        {benefit}
      </BodyMedium>
    </div>
  );
};

const Usecase: React.FC = () => {
  const usecaseData = [
    {
      icon: <FaClock className="w-6 h-6 text-teal-500" />,
      iconBgColor: "bg-teal-100",
      heading: "Instant & Accurate Results",
      benefit: "Just enter height and weight, get your BMI instantly.",
    },
    {
      icon: <FaHeart className="w-6 h-6 text-purple-500" />,
      iconBgColor: "bg-purple-100",
      heading: "Health Risk Awareness",
      benefit: "Know if you're underweight, healthy, overweight, or obese.",
    },
    {
      icon: <FaUserShield className="w-6 h-6 text-tertiary-orange-400" />,
      iconBgColor: "bg-orange-100",
      heading: "Insurance Preparedness",
      benefit:
        "Use BMI insights to choose the right health cover for your profile.",
    },
  ];

  return (
    <SectionContainerLarge className="w-full !px-0" id="why-our-bmi-calculator">
      <HeadingXLarge
        as="h2"
        className="text-center text-neutral-1100 mb-6 font-semibold px-6 md:px-0"
      >
        Why use our BMI Calculator ?
      </HeadingXLarge>

      {/* Desktop Grid Layout */}
      <div className="hidden md:grid md:grid-cols-3 gap-4">
        {usecaseData.map((card, index) => (
          <UsecaseCard
            key={index}
            icon={card.icon}
            iconBgColor={card.iconBgColor}
            heading={card.heading}
            benefit={card.benefit}
          />
        ))}
      </div>

      {/* Mobile Carousel Layout */}
      <div className="md:hidden">
        <MobileCarousel totalSlides={usecaseData.length}>
          {usecaseData.map((card, index) => (
            <MobileCarouselItem key={index}>
              <UsecaseCard
                icon={card.icon}
                iconBgColor={card.iconBgColor}
                heading={card.heading}
                benefit={card.benefit}
              />
            </MobileCarouselItem>
          ))}
        </MobileCarousel>
      </div>
    </SectionContainerLarge>
  );
};

export default Usecase;
