import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import {
  HeadingXLarge,
  HeadingSmall,
  BodyLarge,
} from "@/components/UI/Typography";
import { FaWeight, FaCalculator, FaPen, FaCheckCircle } from "react-icons/fa";
import React from "react";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";

// Define the data structure for each card
type ResultCardData = {
  id: string | number;
  iconKey?: "phone" | "calculator" | "graph" | "check" | string;
  title: string;
  description: string;
};

type HowToCalculateProps = {
  title?: string;
  cards: ResultCardData[];
};

const getIconByKey = (key?: string) => {
  switch (key) {
    case "phone":
      return <FaWeight className="w-4 h-4 text-tertiary-orange-400" />;
    case "calculator":
      return <FaCalculator className="w-4 h-4 text-tertiary-orange-400" />;
    case "graph":
      return <FaPen className="w-4 h-4 text-tertiary-orange-400" />;
    case "check":
      return <FaCheckCircle className="w-4 h-4 text-tertiary-orange-400" />;
    default:
      return <FaPen className="w-4 h-4 text-tertiary-orange-400" />;
  }
};

// Result Card Component
const ResultCard = ({ card }: { card: ResultCardData }) => (
  <div className="bg-white rounded-xl p-6 shadow-sm border border-primary-200 flex flex-col items-center gap-3 text-center w-full">
    <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
      {getIconByKey(card.iconKey)}
    </div>
    <HeadingSmall as="h3" className="font-semibold text-neutral-1100">
      {card.title}
    </HeadingSmall>
    <BodyLarge as="p" className="text-neutral-900">
      {card.description}
    </BodyLarge>
  </div>
);

const HowToCalculate = ({
  title = "How to calculate Body Mass Index?",
  cards,
}: HowToCalculateProps) => {
  return (
    <SectionContainerLarge id="how-to-calculate" className="!px-0">
      <HeadingXLarge
        as="h2"
        className="text-neutral-1100 mb-6 text-center font-semibold"
      >
        {title}
      </HeadingXLarge>

      {/* Desktop Grid Layout */}
      <div className="hidden md:grid md:grid-cols-4 gap-4">
        {cards.map((card) => (
          <ResultCard key={card.id} card={card} />
        ))}
      </div>

      {/* Mobile Carousel Layout */}
      <div className="md:hidden">
        <MobileCarousel totalSlides={cards.length}>
          {cards.map((card, index) => (
            <MobileCarouselItem key={card.id}>
              <ResultCard card={card} />
            </MobileCarouselItem>
          ))}
        </MobileCarousel>
      </div>
    </SectionContainerLarge>
  );
};

export default HowToCalculate;
