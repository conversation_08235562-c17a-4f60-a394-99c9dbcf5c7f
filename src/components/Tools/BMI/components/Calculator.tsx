"use client";
import React, { useState } from "react";
import { Button } from "@/components/UI/Button";
import { Input } from "@/components/UI/Input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/UI/Select";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import {
  FaCalendarAlt,
  FaUser,
  FaRuler,
  FaWeightHanging,
} from "react-icons/fa";
import {
  BodyLarge,
  BodyMedium,
  BodySmall,
  HeadingMedium,
} from "@/components/UI/Typography";
import { usePathname } from "next/navigation";

type BMIFormData = {
  age: string;
  gender: string;
  height: string;
  weight: string;
  heightUnit: "cm" | "ft";
  weightUnit: "kg" | "lb";
  heightFeet: string;
  heightInches: string;
};

const Calculator = () => {
  const pathname = usePathname();

  const [bmiFormData, setBmiFormData] = useState<BMIFormData>({
    age: "",
    gender: "",
    height: "",
    weight: "",
    heightUnit: "cm",
    weightUnit: "kg",
    heightFeet: "",
    heightInches: "",
  });

  const [bmiResult, setBmiResult] = useState<number | null>(null);
  const [currentStep, setCurrentStep] = useState<"bmi" | "results">("bmi");

  // Handle height unit change and automatically set weight unit
  const handleHeightUnitChange = (newHeightUnit: "cm" | "ft") => {
    setBmiFormData({
      ...bmiFormData,
      heightUnit: newHeightUnit,
      // Clear height values when switching units
      height: "",
      heightFeet: "",
      heightInches: "",
    });
  };

  // Handle weight unit change
  const handleWeightUnitChange = (newWeightUnit: "kg" | "lb") => {
    setBmiFormData({
      ...bmiFormData,
      weightUnit: newWeightUnit,
      // Clear weight value when switching units
      weight: "",
    });
  };

  const calculateBMI = () => {
    let heightInMeters = 0;
    let weightInKg = 0;

    // Convert height to meters
    if (bmiFormData.heightUnit === "cm") {
      if (!bmiFormData.height) return;
      heightInMeters = parseFloat(bmiFormData.height) / 100;
    } else {
      // ft/inches
      if (!bmiFormData.heightFeet || !bmiFormData.heightInches) return;
      const feet = parseFloat(bmiFormData.heightFeet);
      const inches = parseFloat(bmiFormData.heightInches);
      const totalInches = feet * 12 + inches;
      heightInMeters = totalInches * 0.0254;
    }

    // Convert weight to kg (now supports both kg and lb regardless of height unit)
    if (!bmiFormData.weight) return;

    if (bmiFormData.weightUnit === "kg") {
      weightInKg = parseFloat(bmiFormData.weight);
    } else {
      // pounds to kg conversion
      weightInKg = parseFloat(bmiFormData.weight) * 0.453592;
    }

    if (heightInMeters > 0 && weightInKg > 0) {
      const bmi = weightInKg / (heightInMeters * heightInMeters);
      setBmiResult(parseFloat(bmi.toFixed(1)));
    }
    setCurrentStep("results");
  };

  const getBMIColor = (bmi: number) => {
    if (bmi < 18.5) return "text-primary-800";
    if (bmi >= 18.5 && bmi < 25) return "text-secondary-400";
    if (bmi >= 25 && bmi < 30) return "text-tertiary-orange-400";
    return "text-tertiary-red-400";
  };

  const getBMICircleColor = (bmi: number) => {
    if (bmi < 18.5) return "text-primary-800"; // blue-600
    if (bmi >= 18.5 && bmi < 25) return "text-secondary-400"; // teal-600
    if (bmi >= 25 && bmi < 30) return "text-tertiary-orange-400"; // orange-500
    return "text-tertiary-red-400"; // red-600
  };

  const getBMICircleColorValue = (bmi: number) => {
    if (bmi < 18.5) return "#045591"; // blue-600
    if (bmi >= 18.5 && bmi < 25) return "#00a991"; // teal-600
    if (bmi >= 25 && bmi < 30) return "#F26C07"; // orange-500
    return "#E7000B"; // red-600
  };

  const resetCalculator = () => {
    setBmiFormData({
      age: "",
      gender: "",
      height: "",
      weight: "",
      heightUnit: "cm",
      weightUnit: "kg",
      heightFeet: "",
      heightInches: "",
    });
    setCurrentStep("bmi");
    setBmiResult(null);
  };

  // BMI categories data array
  const bmiCategories = [
    { label: "Underweight", color: "bg-primary-800" },
    { label: "Normal", color: "bg-secondary-400" },
    { label: "Overweight", color: "bg-tertiary-orange-400" },
    { label: "Obesity", color: "bg-tertiary-red-400" },
  ];

  return (
    <SectionContainerMedium className="bg-white !p-0">
      {/* Mobile Layout - Single Column */}
      <div className="block md:hidden">
        {currentStep === "bmi" && (
          <div className="border border-primary-300 rounded-xl p-4">
            <div className="flex flex-col gap-3">
              {/* Age and Gender Row */}
              <div className="grid grid-cols-2 gap-3">
                {/* Age Input */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <FaCalendarAlt className="w-3 h-3 text-primary-800" />
                    </div>
                    <BodyLarge className="text-neutral-1100 font-medium ">
                      Age
                    </BodyLarge>
                  </div>
                  <Input
                    type="number"
                    placeholder="Enter Age"
                    value={bmiFormData.age}
                    onChange={(e) =>
                      setBmiFormData({ ...bmiFormData, age: e.target.value })
                    }
                  />
                </div>

                {/* Gender Input */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <FaUser className="w-3 h-3 text-primary-800" />
                    </div>
                    <BodyLarge className="text-neutral-1100 font-medium ">
                      Gender
                    </BodyLarge>
                  </div>
                  <Select
                    value={bmiFormData.gender}
                    onValueChange={(value) =>
                      setBmiFormData({
                        ...bmiFormData,
                        gender: value,
                      })
                    }
                  >
                    <SelectTrigger className="w-full px-3 py-2 bg-neutral-100 rounded-xl border-none text-sm/[1.125rem] md:text-base text-neutral-800 appearance-none cursor-pointer">
                      <SelectValue placeholder="Select Gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Height Input */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <FaRuler className="w-3 h-3 text-primary-800" />
                  </div>
                  <BodyLarge className="text-neutral-1100 font-medium ">
                    Height
                  </BodyLarge>
                </div>
                {bmiFormData.heightUnit === "cm" ? (
                  <div className="flex gap-3">
                    <Input
                      type="number"
                      placeholder="Enter Height"
                      value={bmiFormData.height}
                      onChange={(e) =>
                        setBmiFormData({
                          ...bmiFormData,
                          height: e.target.value,
                        })
                      }
                    />
                    <Select
                      value={bmiFormData.heightUnit}
                      onValueChange={(value) =>
                        handleHeightUnitChange(value as "cm" | "ft")
                      }
                    >
                      <SelectTrigger className="px-3 py-2 bg-neutral-100 rounded-xl border-none text-sm/[1.125rem] md:text-base text-neutral-800 appearance-none cursor-pointer w-auto">
                        <SelectValue placeholder="Select Height Unit" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cm">cm</SelectItem>
                        <SelectItem value="ft">ft</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex gap-3">
                      <Input
                        type="number"
                        placeholder="Feet"
                        value={bmiFormData.heightFeet}
                        onChange={(e) =>
                          setBmiFormData({
                            ...bmiFormData,
                            heightFeet: e.target.value,
                          })
                        }
                      />
                      <Input
                        type="number"
                        placeholder="Inches"
                        value={bmiFormData.heightInches}
                        onChange={(e) =>
                          setBmiFormData({
                            ...bmiFormData,
                            heightInches: e.target.value,
                          })
                        }
                      />
                      <Select
                        value={bmiFormData.heightUnit}
                        onValueChange={(value) =>
                          handleHeightUnitChange(value as "cm" | "ft")
                        }
                      >
                        <SelectTrigger className="px-3 py-2 bg-neutral-100 rounded-xl border-none text-sm/[1.125rem] md:text-base text-neutral-800 appearance-none cursor-pointer w-auto">
                          <SelectValue placeholder="Select Height Unit" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="cm">cm</SelectItem>
                          <SelectItem value="ft">ft</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </div>

              {/* Weight Input */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <FaWeightHanging className="w-3 h-3 text-primary-800" />
                  </div>
                  <BodyLarge className="text-neutral-1100 font-medium ">
                    Weight
                  </BodyLarge>
                </div>
                <div className="flex gap-3">
                  <Input
                    type="number"
                    placeholder={`Enter Weight (${bmiFormData.weightUnit})`}
                    value={bmiFormData.weight}
                    onChange={(e) =>
                      setBmiFormData({
                        ...bmiFormData,
                        weight: e.target.value,
                      })
                    }
                  />
                  <Select
                    value={bmiFormData.weightUnit}
                    onValueChange={(value) =>
                      handleWeightUnitChange(value as "kg" | "lb")
                    }
                  >
                    <SelectTrigger className="px-3 py-2 bg-neutral-100 rounded-xl border-none text-sm/[1.125rem] md:text-base text-neutral-800 appearance-none cursor-pointer w-auto">
                      <SelectValue placeholder="Select Weight Unit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="kg">kg</SelectItem>
                      <SelectItem value="lb">lb</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="flex justify-center mt-4">
              <Button
                onClick={calculateBMI}
                variant="primary"
                className="px-8"
                disabled={
                  bmiFormData.heightUnit === "cm"
                    ? !bmiFormData.height || !bmiFormData.weight
                    : !bmiFormData.heightFeet ||
                      !bmiFormData.heightInches ||
                      !bmiFormData.weight
                }
              >
                Get BMI Results
              </Button>
            </div>
          </div>
        )}

        {currentStep === "results" && (
          <div className="border border-primary-300 rounded-2xl p-4">
            {/* Mobile Results Section */}
            <div className="text-center mb-3">
              <HeadingMedium className="text-neutral-1100 font-semibold">
                Get Your BMI Results
              </HeadingMedium>
            </div>

            {/* BMI Circle */}
            <div className="flex justify-center mb-2">
              <div className="relative w-36 h-36">
                <svg className="w-full h-full" viewBox="0 0 100 100">
                  {/* Background circle */}
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke="#E3EAF3"
                    strokeWidth="8"
                  />
                  {/* Colored perimeter circle based on BMI category */}
                  {bmiResult && currentStep === "results" ? (
                    <circle
                      cx="50"
                      cy="50"
                      r="45"
                      fill="none"
                      stroke={getBMICircleColorValue(bmiResult)}
                      strokeWidth="8"
                    />
                  ) : (
                    <circle
                      cx="50"
                      cy="50"
                      r="45"
                      fill="none"
                      stroke="#E3EAF3"
                      strokeWidth="8"
                    />
                  )}
                </svg>
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <span
                    className={`text-3xl font-bold ${
                      bmiResult && currentStep === "results"
                        ? getBMIColor(bmiResult)
                        : "text-neutral-400"
                    }`}
                  >
                    {bmiResult && currentStep === "results" ? bmiResult : "--"}
                  </span>
                </div>
              </div>
            </div>

            {/* BMI Categories Legend */}
            <div className="space-y-3">
              <div className="flex justify-center gap-20">
                {/* Left Column */}
                <div className="space-y-3 w-fit">
                  {bmiCategories.slice(0, 2).map((category, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div
                        className={`w-5 h-5 ${category.color} rounded-full`}
                      ></div>
                      <BodyLarge className="text-neutral-1100 font-semibold">
                        {category.label}
                      </BodyLarge>
                    </div>
                  ))}
                </div>
                {/* Right Column */}
                <div className="space-y-3">
                  {bmiCategories.slice(2, 4).map((category, index) => (
                    <div key={index + 2} className="flex items-center gap-3">
                      <div
                        className={`w-5 h-5 ${category.color} rounded-full`}
                      ></div>
                      <BodyLarge className="text-neutral-1100 font-semibold">
                        {category.label}
                      </BodyLarge>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Healthy BMI Range */}
            <div className="text-center mt-2">
              <BodyLarge>
                <span className="text-neutral-1100 font-semibold">
                  Healthy BMI Range -
                </span>{" "}
                <span className="text-neutral-800 font-medium">
                  18.5kg/m² - 24.9 kg/m²
                </span>
              </BodyLarge>
            </div>

            {/* Get Another Report Button */}
            <div className="flex justify-center mt-4">
              <Button
                onClick={resetCalculator}
                variant="primary"
                className="px-8"
                disabled={currentStep !== "results"}
              >
                Get another Report
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Desktop Layout - Two Columns (md: and above) */}
      <div className="hidden md:flex gap-14">
        {/* Left Section - Form */}
        <div className="border border-primary-300 rounded-xl p-6 w-1/2">
          {currentStep === "bmi" && (
            <>
              <div className="flex flex-col gap-4">
                {/* Age and Gender Row */}
                <div className="grid grid-cols-2 gap-4">
                  {/* Age Input */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <FaCalendarAlt className="w-4 h-4 text-primary-800" />
                      </div>
                      <BodyLarge className="text-neutral-1100 font-medium ">
                        Age
                      </BodyLarge>
                    </div>
                    <Input
                      type="number"
                      placeholder="Enter Age"
                      value={bmiFormData.age}
                      onChange={(e) =>
                        setBmiFormData({
                          ...bmiFormData,
                          age: e.target.value,
                        })
                      }
                    />
                  </div>

                  {/* Gender Input */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <FaUser className="w-4 h-4 text-primary-800" />
                      </div>
                      <BodyLarge className="text-neutral-1100 font-medium ">
                        Gender
                      </BodyLarge>
                    </div>
                    <Select
                      value={bmiFormData.gender}
                      onValueChange={(value) =>
                        setBmiFormData({
                          ...bmiFormData,
                          gender: value,
                        })
                      }
                    >
                      <SelectTrigger className="w-full px-3 py-2 bg-neutral-100 rounded-xl border-none text-sm/[1.125rem] md:text-base text-neutral-800 appearance-none cursor-pointer">
                        <SelectValue placeholder="Select Gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Height Input */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <FaRuler className="w-4 h-4 text-primary-800" />
                    </div>
                    <BodyLarge className="text-neutral-1100 font-medium ">
                      Height
                    </BodyLarge>
                  </div>
                  {bmiFormData.heightUnit === "cm" ? (
                    <div className="flex gap-3">
                      <Input
                        type="number"
                        placeholder="Enter Height"
                        value={bmiFormData.height}
                        onChange={(e) =>
                          setBmiFormData({
                            ...bmiFormData,
                            height: e.target.value,
                          })
                        }
                      />
                      <Select
                        value={bmiFormData.heightUnit}
                        onValueChange={(value) =>
                          handleHeightUnitChange(value as "cm" | "ft")
                        }
                      >
                        <SelectTrigger className="w-auto px-3 py-2 bg-neutral-100 rounded-xl border-none text-sm/[1.125rem] md:text-base text-neutral-800 appearance-none cursor-pointer">
                          <SelectValue placeholder="Select Height Unit" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="cm">cm</SelectItem>
                          <SelectItem value="ft">ft</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="flex gap-3">
                        <Input
                          type="number"
                          placeholder="Feet"
                          value={bmiFormData.heightFeet}
                          onChange={(e) =>
                            setBmiFormData({
                              ...bmiFormData,
                              heightFeet: e.target.value,
                            })
                          }
                        />
                        <Input
                          type="number"
                          placeholder="Inches"
                          value={bmiFormData.heightInches}
                          onChange={(e) =>
                            setBmiFormData({
                              ...bmiFormData,
                              heightInches: e.target.value,
                            })
                          }
                        />
                        <Select
                          value={bmiFormData.heightUnit}
                          onValueChange={(value) =>
                            handleHeightUnitChange(value as "cm" | "ft")
                          }
                        >
                          <SelectTrigger className="w-auto px-3 py-2 bg-neutral-100 rounded-xl border-none text-sm/[1.125rem] md:text-base text-neutral-800 appearance-none cursor-pointer">
                            <SelectValue placeholder="Select Height Unit" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="cm">cm</SelectItem>
                            <SelectItem value="ft">ft</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  )}
                </div>

                {/* Weight Input */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <FaWeightHanging className="w-4 h-4 text-primary-800" />
                    </div>
                    <BodyLarge className="text-neutral-1100 font-medium ">
                      Weight
                    </BodyLarge>
                  </div>
                  <div className="flex gap-3">
                    <Input
                      type="number"
                      placeholder={`Enter Weight (${bmiFormData.weightUnit})`}
                      value={bmiFormData.weight}
                      onChange={(e) =>
                        setBmiFormData({
                          ...bmiFormData,
                          weight: e.target.value,
                        })
                      }
                    />
                    <Select
                      value={bmiFormData.weightUnit}
                      onValueChange={(value) =>
                        handleWeightUnitChange(value as "kg" | "lb")
                      }
                    >
                      <SelectTrigger className="w-auto px-3 py-2 bg-neutral-100 rounded-xl border-none text-sm/[1.125rem] md:text-base text-neutral-800 appearance-none cursor-pointer">
                        <SelectValue placeholder="Select Weight Unit" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="kg">kg</SelectItem>
                        <SelectItem value="lb">lb</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="flex justify-center mt-6">
                <Button
                  onClick={calculateBMI}
                  variant="primary"
                  className="px-8"
                  disabled={
                    bmiFormData.heightUnit === "cm"
                      ? !bmiFormData.height || !bmiFormData.weight
                      : !bmiFormData.heightFeet ||
                        !bmiFormData.heightInches ||
                        !bmiFormData.weight
                  }
                >
                  Get BMI Results
                </Button>
              </div>
            </>
          )}

          {currentStep === "results" && (
            <>
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-neutral-1100">
                  Your BMI Details
                </h2>
                <p className="text-sm text-neutral-700 mt-2">
                  Review your information and see your results
                </p>
              </div>

              <div className="flex flex-col gap-4">
                {/* Age and Gender Row */}
                <div className="grid grid-cols-2 gap-4">
                  {/* Age Display */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <FaCalendarAlt className="w-4 h-4 text-primary-800" />
                      </div>
                      <BodyLarge className="text-neutral-1100 font-medium ">
                        Age
                      </BodyLarge>
                    </div>
                    <div className="px-4 py-3 bg-neutral-100 rounded-xl text-sm/[1.125rem] md:text-base text-neutral-800">
                      {bmiFormData.age || "--"}
                    </div>
                  </div>

                  {/* Gender Display */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <FaUser className="w-4 h-4 text-primary-800" />
                      </div>
                      <BodyLarge className="text-neutral-1100 font-medium ">
                        Gender
                      </BodyLarge>
                    </div>
                    <div className="px-4 py-3 bg-neutral-100 rounded-xl text-sm/[1.125rem] md:text-base text-neutral-800">
                      {bmiFormData.gender || "--"}
                    </div>
                  </div>
                </div>

                {/* Height Display */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <FaRuler className="w-4 h-4 text-primary-800" />
                    </div>
                    <BodyLarge className="text-neutral-1100 font-medium ">
                      Height
                    </BodyLarge>
                  </div>
                  <div className="flex gap-3">
                    <div className="flex-1 px-4 py-3 bg-neutral-100 rounded-xl text-sm/[1.125rem] md:text-base text-neutral-800">
                      {bmiFormData.heightUnit === "cm"
                        ? `${bmiFormData.height || "--"} cm`
                        : `${bmiFormData.heightFeet || "--"}' ${
                            bmiFormData.heightInches || "--"
                          }"`}
                    </div>
                  </div>
                </div>

                {/* Weight Display */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      <FaWeightHanging className="w-4 h-4 text-primary-800" />
                    </div>
                    <BodyLarge className="text-neutral-1100 font-medium ">
                      Weight
                    </BodyLarge>
                  </div>
                  <div className="flex gap-3">
                    <div className="flex-1 px-4 py-3 bg-neutral-100 rounded-xl text-sm/[1.125rem] md:text-base text-neutral-800">
                      {bmiFormData.weight || "--"} {bmiFormData.weightUnit}
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Right Section - Results */}
        <div className="w-1/2 flex flex-col">
          <div className="text-center mb-4">
            <HeadingMedium className="text-neutral-1100 font-semibold">
              Get Your BMI Results
            </HeadingMedium>
          </div>

          {/* BMI Circle */}
          <div className="flex justify-center mb-3">
            <div className="relative w-36 h-36">
              <svg className="w-full h-full" viewBox="0 0 100 100">
                {/* Background circle */}
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="#E3EAF3"
                  strokeWidth="8"
                />
                {/* Colored perimeter circle based on BMI category */}
                {bmiResult && currentStep === "results" ? (
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke={getBMICircleColorValue(bmiResult)}
                    strokeWidth="8"
                  />
                ) : (
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke="#E3EAF3"
                    strokeWidth="8"
                  />
                )}
              </svg>
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <span
                  className={`text-3xl font-bold ${
                    bmiResult && currentStep === "results"
                      ? getBMIColor(bmiResult)
                      : "text-neutral-400"
                  }`}
                >
                  {bmiResult && currentStep === "results" ? bmiResult : "--"}
                </span>
              </div>
            </div>
          </div>

          {/* BMI Categories Legend */}
          <div className="space-y-4">
            <div className="flex justify-center gap-20">
              {/* Left Column */}
              <div className="space-y-3 w-fit">
                {bmiCategories.slice(0, 2).map((category, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div
                      className={`w-5 h-5 ${category.color} rounded-full`}
                    ></div>
                    <BodyLarge className="text-neutral-1100 font-semibold">
                      {category.label}
                    </BodyLarge>
                  </div>
                ))}
              </div>
              {/* Right Column */}
              <div className="space-y-4">
                {bmiCategories.slice(2, 4).map((category, index) => (
                  <div key={index + 2} className="flex items-center gap-3">
                    <div
                      className={`w-5 h-5 ${category.color} rounded-full`}
                    ></div>
                    <BodyLarge className="text-neutral-1100 font-semibold">
                      {category.label}
                    </BodyLarge>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Healthy BMI Range */}
          <div className="text-center mt-3">
            <BodyLarge>
              <span className="text-neutral-1100 font-semibold">
                Healthy BMI Range -
              </span>{" "}
              <span className="text-neutral-800 font-medium">
                18.5kg/m² - 24.9 kg/m²
              </span>
            </BodyLarge>
          </div>

          {/* Get Another Report Button */}
          <div className="flex justify-center mt-6">
            <Button
              onClick={resetCalculator}
              variant="primary"
              className="px-8"
              disabled={currentStep !== "results"}
            >
              Get another Report
            </Button>
          </div>
        </div>
      </div>
    </SectionContainerMedium>
  );
};

export default Calculator;
