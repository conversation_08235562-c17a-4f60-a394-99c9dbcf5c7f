import React from "react";
import {
  BodyLarge,
  BodyMedium,
  HeadingXLarge,
} from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerSmall from "@/components/globals/SectionContainerSmall";
import PillBadge from "@/components/globals/PillBadge";

const WhatIsBMI = () => {
  return (
    <SectionContainerLarge id="what-is-bmi">
      <PillBadge pill="BMI Calculator" />
      <HeadingXLarge as="h2" className="text-center mt-3 mb-6">
        What is BMI (Body Mass Index)?
      </HeadingXLarge>

      <SectionContainerSmall className="!p-0 mb-0 md:mb-0">
        {/* Informational Text Block */}
        <div className="w-full max-w-4xl mx-auto mb-4 p-4 md:p-6 bg-white border border-primary-200 rounded-xl">
          <BodyMedium className="text-neutral-800 text-justify">
            A BMI calculator helps insurers assess your health risk. Higher or
            lower BMI can affect premium rates, eligibility, and medical
            underwriting. Maintaining a healthy BMI improves chances of getting
            better insurance terms, lower premiums, and reduces risk-based
            exclusions during policy approval.
          </BodyMedium>
        </div>

        {/* Feature Cards Section */}
        <div className="flex flex-col md:flex-row gap-4 justify-between items-stretch">
          {/* Scientific Formula Card */}
          <div className="flex-1 p-4 bg-white border border-primary-200 rounded-xl text-center">
            <BodyLarge className="font-bold text-neutral-1100 mb-3">
              Scientific Formula
            </BodyLarge>
            <BodyMedium className="text-neutral-800">
              Based on globally accepted medical standards
            </BodyMedium>
          </div>

          {/* Insurance Ready Card */}
          <div className="flex-1 p-4 bg-white border border-primary-200 rounded-xl text-center">
            <BodyLarge className="font-bold text-neutral-1100 mb-3">
              Insurance Ready
            </BodyLarge>
            <BodyMedium className="text-neutral-800">
              Directly applicable for health insurance applications
            </BodyMedium>
          </div>
        </div>
      </SectionContainerSmall>
    </SectionContainerLarge>
  );
};

export default WhatIsBMI;
