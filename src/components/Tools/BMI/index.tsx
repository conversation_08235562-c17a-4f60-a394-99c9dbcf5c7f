"use client";
import React, { useState } from "react";
import Hero from "@/components/Tools/BMI/components/Hero";
import PageNavigation from "@/components/globals/PageNavigation";
import Formula from "@/components/Tools/BMI/components/Formula";
import WhatIsBMI from "@/components/Tools/BMI/components/WhatIsBMI";
import HowToCalculate from "@/components/Tools/BMI/components/HowToCalculate";
import BMIChart from "@/components/Tools/BMI/components/Chart";
import BMIExample from "@/components/Tools/BMI/components/Example";
import Consumer from "@/components/Tools/BMI/components/Consumer";
import LeadForm from "@/components/globals/LeadForm";
import Usecase from "@/components/Tools/BMI/components/UseCase";
import Testimonial from "@/components/globals/Testimonial";
import { featuresComparisonData } from "@/components/Compare/data/featuresComparisonData";
import AccordianSection from "@/components/globals/AccordianSection";
import BlogSection from "@/components/globals/RelatedBlogs";
import GoToTopFloater from "@/components/globals/GoToTopFloater";
import SharePageFloater from "@/components/globals/SharePageFloater";
import CategoryCards from "@/components/globals/CategoryCards";
import InsurerPlan from "@/components/globals/InsurerPlan";
import type {
  BMIHowToData,
  BMIExampleData,
  BMIConsumerData,
  BMIFAQ,
} from "@/components/Tools/BMI/dto/dtos";
import OrufyFloater from "@/components/globals/OrufyFloater";

const BMI = ({
  allInsurerData,
  blogData,
  howToCalculate,
  exampleData,
  consumerData,
  faqs,
  testimonials,
}: {
  allInsurerData?: any[];
  blogData?: {
    heading: string;
    blogs: Array<{
      title: string;
      date: string;
      author: string;
      description: string;
      imageUrl: string;
      url: string;
    }>;
  };
  howToCalculate?: BMIHowToData;
  exampleData?: BMIExampleData;
  consumerData?: BMIConsumerData[];
  faqs?: BMIFAQ[];
  testimonials?: {
    id: string;
    name: string;
    content: string;
  }[];
}) => {
  const [activeTab, setActiveTab] = useState("what-is-bmi");
  const tabs = [
    { label: "What is BMI?", id: "what-is-bmi" },
    { label: "BMI Formula", id: "bmi-formula" },
    { label: "How to Calculate?", id: "how-to-calculate" },
    { label: "BMI Example", id: "bmi-example" },
    { label: "BMI Chart", id: "bmi-chart" },
    { label: "Get Personalized Advice", id: "get-personalized-advice" },
    {
      label: "Why our BMI Calculator?",
      id: "why-our-bmi-calculator",
    },
    { label: "FAQs", id: "faqs" },
  ];

  return (
    <>
      <Hero />
      <PageNavigation
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        tabs={tabs}
      />
      <WhatIsBMI />
      <Formula />
      <HowToCalculate
        cards={howToCalculate?.cards ?? []}
        title={howToCalculate?.title ?? "How to calculate Body Mass Index?"}
      />
      <BMIExample
        title={exampleData?.title ?? "BMI Calculation Example"}
        topCardsData={exampleData?.topCardsData ?? []}
        bmiResultData={exampleData?.bmiResultData}
        mobilePersonMetrics={exampleData?.mobilePersonMetrics ?? []}
        mobileBMIValue={exampleData?.mobileBMIValue}
        mobileBMIInterpretation={exampleData?.mobileBMIInterpretation}
      />
      {consumerData && consumerData.length > 0 && (
        <Consumer plans={consumerData?.[0]?.plans ?? []} />
      )}
      <BMIChart />
      <LeadForm
        id="get-personalized-advice"
        pill="BMI Calculator"
        title="Get Personalized Advice"
        description="Our insurance experts are here to help you make the right choice. Get personalized recommendations based on your specific needs and budget."
      />
      <Usecase />
      {testimonials && testimonials.length > 0 && (
        <Testimonial
          testimonials={testimonials ?? []}
          sectionHeaderProps={{
            pill: "BMI Calculator",
            heading: "Customer Testimonials",
            subheading:
              "Make an informed decision with our detailed side-by-side comparison of top health insurance policies. Compare coverage, benefits, and premiums to find the perfect plan for your needs.",
          }}
          pill="BMI Calculator"
        />
      )}
      <LeadForm
        pill="BMI Calculator"
        title="Get Personalized Advice"
        description="Our insurance experts are here to help you make the right choice. Get personalized recommendations based on your specific needs and budget."
      />
      <CategoryCards
        pill="BMI Calculator"
        heading="Features Comparison"
        subHeading="Compare the features of the top health insurance policies to find the perfect plan for your needs."
        categories={featuresComparisonData.plans}
        id="features-comparison"
      />
      {faqs && faqs.length > 0 && (
        <AccordianSection
          pill="BMI Calculator"
          heading="Frequently Asked Questions"
          subheading="Find answers to common questions about the BMI Calculator."
          faqs={faqs ?? []}
          id="faqs"
        />
      )}
      {allInsurerData && allInsurerData.length > 0 && (
        <InsurerPlan allInsurerData={allInsurerData} />
      )}
      {blogData && blogData.blogs.length > 0 && (
        <BlogSection blogData={blogData} />
      )}

      <SharePageFloater />
      <OrufyFloater />
      <GoToTopFloater />
    </>
  );
};

export default BMI;
