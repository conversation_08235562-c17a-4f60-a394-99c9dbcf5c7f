// Types for HowToCalculate
export type BMIHowToCard = {
  id: string | number;
  title: string;
  description: string;
  iconKey?: "phone" | "calculator" | "graph" | "check" | string;
};

export type BMIHowToData = {
  title: string;
  cards: BMIHowToCard[];
};

export function transformHowToCalculate(apiData: any): BMIHowToData {
  return {
    title: apiData?.title ?? "",
    cards: Array.isArray(apiData?.cards)
      ? apiData.cards.map((c: any, index: number) => ({
          id: c?.id ?? index,
          title: String(c?.title ?? ""),
          description: String(c?.description ?? ""),
          iconKey: c?.iconKey,
        }))
      : [],
  };
}

// Types for BMI Example
export type BMIExampleCardData = {
  title: string;
  content: string | string[];
};

export type BMIExamplePersonMetric = {
  label: string;
  value: string;
};

export type BMIExampleData = {
  title: string;
  topCardsData: BMIExampleCardData[];
  bmiResultData: BMIExampleCardData;
  mobilePersonMetrics: BMIExamplePersonMetric[];
  mobileBMIValue: string;
  mobileBMIInterpretation: string;
};

export function transformBMIExample(apiData: any): BMIExampleData {
  return {
    title: String(apiData?.title ?? ""),
    topCardsData: Array.isArray(apiData?.topCardsData)
      ? apiData.topCardsData.map((c: any) => ({
          title: String(c?.title ?? ""),
          content: Array.isArray(c?.content)
            ? c.content.map((v: any) => String(v ?? ""))
            : String(c?.content ?? ""),
        }))
      : [],
    bmiResultData: {
      title: String(apiData?.bmiResultData?.title ?? ""),
      content: String(apiData?.bmiResultData?.content ?? ""),
    },
    mobilePersonMetrics: Array.isArray(apiData?.mobilePersonMetrics)
      ? apiData.mobilePersonMetrics.map((m: any) => ({
          label: String(m?.label ?? ""),
          value: String(m?.value ?? ""),
        }))
      : [],
    mobileBMIValue: String(apiData?.mobileBMIValue ?? ""),
    mobileBMIInterpretation: String(apiData?.mobileBMIInterpretation ?? ""),
  };
}

// Types for Consumer (Men/Women/Children cards)
export type BMIPlanRangeRisk = {
  title: string;
  description: string;
};

export type BMIPlanRange = {
  title: string;
  range: string;
  description: string;
  risks: BMIPlanRangeRisk[];
  expanded?: boolean;
};

export type BMIPlanDto = {
  title: string;
  description: string;
  iconKey: "men" | "women" | "children" | string;
  iconBgColor: string;
  button: string;
  features: string[];
  bmiRanges: BMIPlanRange[];
};

type BMIBelowHealthyAboveRange = {
  id: string;
  title: string;
  subtitle: string;
  bmi_below_healthy_above_points: {
    id: string;
    point_title: string;
    point_content: string;
  }[];
};

export type BMIForMenWomenChildren = {
  id: string;
  title: string;
  content: string;
  bmi_below_healthy_above_ranges: BMIBelowHealthyAboveRange[];
};

// Types and transformer for FAQs
export type BMIFAQ = {
  id: string;
  question: string;
  answer: string;
};

export function transformBMIFAQs(apiData: any): BMIFAQ[] {
  return Array.isArray(apiData)
    ? apiData.map((f: any) => ({
        id: String(f?.id ?? ""),
        question: String(f?.question ?? ""),
        answer: String(f?.answer ?? ""),
      }))
    : [];
}

export const dummyHowToCalculate: BMIHowToData = {
  title: "How to calculate Body Mass Index?",
  cards: [
    {
      id: 1,
      title: "Check Weight",
      description: "Weigh yourself accurately",
      iconKey: "phone",
    },
    {
      id: 2,
      title: "Measure Height",
      description: "Note your exact height",
      iconKey: "calculator",
    },
    {
      id: 3,
      title: "Fill Details",
      description: "Input weight and height",
      iconKey: "graph",
    },
    {
      id: 4,
      title: "Get Result",
      description: "See which range you fall in",
      iconKey: "check",
    },
  ],
};

export const dummyBMIExampleData: BMIExampleData = {
  title: "BMI Calculation Example",
  topCardsData: [
    {
      title: "Person Details",
      content: ["Age: 28 years", "Gender: Male"],
    },
    {
      title: "Weight",
      content: "154 lbs (70 kg)",
    },
    {
      title: "Height",
      content: "5ft 10 Inches",
    },
  ],
  bmiResultData: {
    title: "BMI Result - 22.9",
    content:
      "This BMI value is in the “Normal” range (18.5 – 24.9) for adults. Your weight is balanced for your height and age. Keep it steady with a healthy diet, exercise, and regular checkups.",
  },
  mobilePersonMetrics: [
    { label: "Age", value: "28 years" },
    { label: "Weight", value: "154 lbs (70 kg)" },
    { label: "Height", value: "5ft 10 Inches (178 cm)" },
  ],
  mobileBMIValue: "23.1",
  mobileBMIInterpretation:
    "This BMI value is in the “Normal” range (18.5 – 24.9) for adults. Your weight is balanced for your height and age. Keep it steady with a healthy diet, exercise, and regular checkups.",
};

export type BMIConsumerData = {
  plans: Array<{
    title: string;
    description: string;
    iconKey: string;
    iconBgColor: string;
    button: string;
    features: string[];
    bmiRanges: Array<{
      title: string;
      range: string;
      description: string;
      risks: Array<{
        title: string;
        description: string;
      }>;
      expanded: boolean;
    }>;
  }>;
};
