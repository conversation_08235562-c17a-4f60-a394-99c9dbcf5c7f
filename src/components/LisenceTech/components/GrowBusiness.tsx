import Container from "@/components/globals/Container";
import { data } from "../data";
import Image from "next/image";

const GrowBusiness = () => {
  return (
    <div className=" mb-24 px-5 md:px-0">
      <Container>
        {/* Heading */}
        <div className="flex flex-col items-center justify-center">
          <h2 className="font-semibold text-2xl md:text-4xl text-gray900 mb-10 md:w-[50%] text-center">
            Help to grow business
          </h2>
        </div>

        {/* Text */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-10 items-start">
          {data.growBusiness.map((item, idx) => (
            <div
              className="flex flex-col items-center justify-center"
              key={idx}
            >
              {/* illustration */}
              <div className="w-16 h-16 mb-4">
                <Image src={item.icon} alt="" width={64} height={64} />
              </div>
              <h2 className="font-semibold text-gray900 text-xl mb-1">
                {item.title}
              </h2>
              <p className="text-base text-slateGrey text-justify">
                {item.description}
              </p>
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
};

export default GrowBusiness;
