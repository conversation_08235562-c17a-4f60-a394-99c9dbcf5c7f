import BookACallBtn from "@/components/globals/BookACall";
import Container from "@/components/globals/Container";
import Image from "next/image";
import { data } from "../data";

const WhyUs = () => {
  return (
    <div className="mb-24 px-5 md:px-0">
      <Container>
        {/* Heading */}
        <div className="flex flex-col items-center justify-center">
          <h2 className="font-semibold text-2xl md:text-4xl text-gray900 mb-8 w-[50%] text-center">
            Why choose us
          </h2>
          {/* <p className="text-base text-gray800 mb-8 text-center">
            Lorem ipsum dolor sit amet consectetur. Ipsum id purus consectetur
            lobortis bibendum.
          </p> */}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
          {/* Text */}
          <div>
            {data.whyUs.map((p, idx) => (
              <div className="flex mb-8" key={idx}>
                <div className=" mr-4">
                  <Image src={p.icon} alt="" width={48} height={48} />
                </div>
                <div>
                  <h2 className="font-semibold text-2xl text-gray900">
                    {p.title}
                  </h2>
                  <p className="text-base text-gray800">{p.description}</p>
                </div>
              </div>
            ))}

            <div className="flex items-center justify-center md:justify-start">
              <BookACallBtn className="py-3 px-6 md:px-8 bg-primary-1 text-ntrl-white rounded-lg" />
            </div>
          </div>
          {/* Illustration */}
          <div className="flex items-center justify-end">
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL}/licTechWhyUs.svg`}
              alt="hero"
              width={500}
              height={500}
            />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default WhyUs;
