import BookACallBtn from "@/components/globals/BookACall";
import Container from "@/components/globals/Container";
import Image from "next/image";
import { data } from "../data";

const Hero = () => {
  return (
    <div className=" mb-52 px-5 md:px-0">
      <Container>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-7 items-center">
          {/* Headings */}
          <div className="">
            <h1 className="text-2xl md:text-4xl font-semibold mb-6 text-center md:text-left">
              {data.hero.title}
            </h1>

            <p className="text-justify md:text-left text-base/[22px] text-slateGrey mb-10">
              {data.hero.subTitle}
            </p>

            <div className="flex items-center justify-center md:justify-start">
              <BookACallBtn className="py-3 px-6 md:px-8 bg-primary-1 text-ntrl-white rounded-lg" />
            </div>
          </div>

          {/* Illustration */}
          <div className="flex items-center justify-end">
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL}/licTechHero.svg`}
              alt="hero"
              width={500}
              height={500}
            />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default Hero;
