"use client";

import { useState, useMemo } from "react";
import Container from "@/components/globals/Container";
import SectionContainer from "@/components/globals/SectionContainer";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import SectionHeaderWithParse from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
import InsurerFilterButtons from "./components/InsurerFilterButtons";
import ComparisonsGrid from "./components/ComparisonsGrid";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { usePathname } from "next/navigation";
import PageTopBar from "@/components/globals/PageTopBar";
import { SiteHealthVariantStaticContent, ComparisonPair, InsurerData } from "@/components/Compare/Comparisons/types";

const generateComparisons = (
  data: SiteHealthVariantStaticContent[]
): ComparisonPair[] => {
  const comparisons: ComparisonPair[] = [];
  const products = data.map((item) => item.product_variant);

  for (let i = 0; i < products.length; i++) {
    for (let j = i + 1; j < products.length; j++) {
      const product1 = products[i];
      const product2 = products[j];

      if (
        !product1?.variant_slug ||
        !product2?.variant_slug ||
        !product1?.id ||
        !product2?.id
      ) {
        continue;
      }

      const url = `/compare-health-insurance-plans/${product1.product.insurer.slug}-${product1.variant_slug}-vs-${product2.product.insurer.slug}-${product2.variant_slug}/${product1.id}-${product2.id}`;

      comparisons.push({ product1, product2, url });
    }
  }

  return comparisons;
};

export const Comparisons = ({
  comparisonData,
  preSelectedInsurer,
  insurerName
}: {
  comparisonData: SiteHealthVariantStaticContent[];
  preSelectedInsurer?: string;
  insurerName?: string;
}) => {
  const breadcrumbPath = [
    "OneAssure",
    "Compare Health Insurance Plans",
    preSelectedInsurer ? `${preSelectedInsurer} Comparisons` : "All Comparisons",
  ];

  const fullUrl = usePathname();

  const [selectedInsurers, setSelectedInsurers] = useState<string[]>(
    preSelectedInsurer ? [preSelectedInsurer] : ["all"]
  );

  const allComparisons = useMemo(() => {
    return generateComparisons(comparisonData);
  }, [comparisonData]);

  const filteredComparisons = useMemo(() => {
    if (selectedInsurers.includes("all") || selectedInsurers.length === 0) {
      return allComparisons;
    }

    return allComparisons.filter(
      (comparison) =>
        selectedInsurers.includes(
          comparison.product1.product.insurer.slug
        ) ||
        selectedInsurers.includes(comparison.product2.product.insurer.slug)
    );
  }, [allComparisons, selectedInsurers]);

  const uniqueInsurers = useMemo(() => {
    const insurerMap = new Map();
    comparisonData.forEach((item) => {
      const insurer = item.product_variant.product.insurer;
      if (!insurerMap.has(insurer.slug)) {
        insurerMap.set(insurer.slug, insurer);
      }
    });
    return Array.from(insurerMap.values());
  }, [comparisonData]);

  return (
    <SectionContainer className="mt-6 mx-0">
      <PageTopBar breadcrumbPath={breadcrumbPath} fullUrl={fullUrl || ""} />
      <SectionContainerLarge className="mb-8 !px-0">
        <SectionHeaderWithParse
          pill="Health Insurance Comparisons"
          heading={insurerName ? `Compare ${insurerName} Plans With Other Insurers` : "Compare All Health Insurance Plans"}
          subheading="Explore comprehensive comparisons between different health insurance plans. Filter by insurer to find the perfect coverage for your needs."
          component="h1"
          className="!mb-8 !px-0"
        />

        <InsurerFilterButtons
          uniqueInsurers={uniqueInsurers}
          selectedInsurers={selectedInsurers}
          onInsurerSelect={setSelectedInsurers}
        />

        <ComparisonsGrid
          filteredComparisons={filteredComparisons}
          selectedInsurers={selectedInsurers}
          uniqueInsurers={uniqueInsurers}
        />
      </SectionContainerLarge>
    </SectionContainer>
  );
};
