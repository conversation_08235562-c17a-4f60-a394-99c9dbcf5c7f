export type ProductVariant = {
  id: string;
  variant_name: string;
  variant_slug: string;
  product: {
    insurer: {
      name: string;
      slug: string;
    };
  };
};

export type SiteHealthVariantStaticContent = {
  product_variant: ProductVariant;
};

export type ComparisonPair = {
  product1: ProductVariant;
  product2: ProductVariant;
  url: string;
};

export type InsurerData = {
  company_slug: string;
  company_name: string;
};
