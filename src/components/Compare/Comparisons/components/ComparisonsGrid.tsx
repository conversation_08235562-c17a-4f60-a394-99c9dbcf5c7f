
import React from "react";
import ComparisonCard from "./ComparisonCard";
import { ComparisonPair } from "@/components/Compare/Comparisons/types";
import { BodyMedium, HeadingMedium } from "@/components/UI/Typography";

type Insurer = {
  name: string;
  slug: string;
};

type ComparisonsGridProps = {
  filteredComparisons: ComparisonPair[];
  selectedInsurers: string[];
  uniqueInsurers: Insurer[];
}

const ComparisonsGrid: React.FC<ComparisonsGridProps> = ({
  filteredComparisons,
  selectedInsurers,
  uniqueInsurers,
}) => {
  const getFilterTitle = () => {
    if (selectedInsurers.includes("all") || selectedInsurers.length === 0) {
      return "All Comparisons";
    }
    if (selectedInsurers.length === 1) {
      const insurer = uniqueInsurers.find(i => i.slug === selectedInsurers[0]);
      return `${insurer?.name} Comparisons`;
    }
    return `Filtered Comparisons`;
  };

  return (
    <div id="all-comparisons" className="space-y-6">
      <div className="flex items-center justify-between">
        <HeadingMedium as="h2" className="text-xl font-semibold text-neutral-1100">
          {getFilterTitle()} ({filteredComparisons.length})
        </HeadingMedium>
      </div>

      {filteredComparisons.length === 0 ? (
        <div className="text-center py-12">
          <BodyMedium className="text-neutral-800 text-lg">No comparisons available for the selected filter.</BodyMedium>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredComparisons.map((comparison, index) => (
            <ComparisonCard
              key={`${comparison.product1.id}-${comparison.product2.id}`}
              comparison={comparison}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ComparisonsGrid;
