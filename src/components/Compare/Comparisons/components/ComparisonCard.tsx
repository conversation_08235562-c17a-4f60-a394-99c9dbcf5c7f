import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Card, CardContent } from "./Card";
import { BodyLarge, BodyMedium } from "@/components/UI/Typography";

type ProductVariant = {
  id: string;
  variant_name: string;
  variant_slug: string;
  product: {
    insurer: {
      name: string;
      slug: string;
    };
  };
};

type ComparisonPair = {
  product1: ProductVariant;
  product2: ProductVariant;
  url: string;
};

type ComparisonCardProps = {
  comparison: ComparisonPair;
}

const ComparisonCard: React.FC<ComparisonCardProps> = ({
  comparison,
}) => {
  const insurer1 = comparison.product1.product.insurer.name.endsWith("Health Insurance") ? comparison.product1.product.insurer.name.replace("Health Insurance", "") : comparison.product1.product.insurer.name;
  const insurer2 = comparison.product2.product.insurer.name.endsWith("Health Insurance") ? comparison.product2.product.insurer.name.replace("Health Insurance", "") : comparison.product2.product.insurer.name;

  return (
    <Link href={comparison.url}>
      <Card className="hover:shadow-lg transition-shadow cursor-pointer border-2 hover:border-primary-300 h-full">
        <CardContent className="p-4 h-full flex items-center justify-center">
          <div className="flex items-center gap-3 w-full">
            {/* Plan 1 */}
            <div className="flex-1 text-left">
              <div className="block md:flex items-center gap-2">
                <BodyLarge className="font-medium text-neutral-1100">
                  {insurer1}
                </BodyLarge>
                <BodyLarge className="text-neutral-900">
                  {comparison.product1.variant_name}
                </BodyLarge>
              </div>
            </div>

            {/* VS Divider */}
            <div className="bg-primary-100 text-primary-600 px-3 py-2 rounded-full text-sm font-semibold">
              VS
            </div>

            {/* Plan 2 */}
            <div className="flex-1 text-right">
              <div className="block md:flex items-center justify-end gap-2">
                <BodyLarge className="font-medium text-neutral-1100">
                  {insurer2}
                </BodyLarge>
                <BodyLarge className="text-neutral-900">
                  {comparison.product2.variant_name}
                </BodyLarge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default ComparisonCard;
