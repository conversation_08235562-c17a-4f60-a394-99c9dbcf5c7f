import React from "react";
import Link from "next/link";
import { Button } from "@/components/UI/Button";
import { HeadingMedium } from "@/components/UI/Typography";

type Insurer = {
  name: string;
  slug: string;
};

type InsurerFilterButtonsProps = {
  uniqueInsurers: Insurer[];
  selectedInsurers: string[];
  onInsurerSelect: (slugs: string[]) => void;
}

const InsurerFilterButtons: React.FC<InsurerFilterButtonsProps> = ({
  uniqueInsurers,
  selectedInsurers,
}) => {
  const isAllSelected = selectedInsurers.includes("all");

  return (
    <div id="filter-by-insurer" className="mb-8">
      <HeadingMedium as={"h2"} className="text-xl font-semibold mb-4 text-neutral-1100">Filter by Insurer</HeadingMedium>
      
      {/* Desktop Button Layout */}
      <div className="hidden md:flex flex-wrap gap-3">
        <Link href="/html-sitemaps/compare-health-insurance">
          <Button
            variant={isAllSelected ? "default" : "outline"}
            className="h-12 px-6 rounded-xl"
          >
            All Insurers
          </Button>
        </Link>
        {uniqueInsurers.map((insurer) => {
          const isSelected = selectedInsurers.includes(insurer.slug);
          return (
            <Link key={insurer.slug} href={`/html-sitemaps/compare-health-insurance/${insurer.slug}`}>
              <Button
                variant={isSelected ? "default" : "outline"}
                className="h-12 px-4 flex items-center gap-2 rounded-xl"
              >
                {insurer.name.endsWith("Health Insurance") ? insurer.name.replace("Health Insurance", "") : insurer.name}
              </Button>
            </Link>
          );
        })}
      </div>

      {/* Mobile Button Layout */}
      <div className="md:hidden grid grid-cols-2 gap-2">
        <Link href="/html-sitemaps/compare-health-insurance">
          <Button
            variant={isAllSelected ? "default" : "outline"}
            className="h-10 px-3 rounded-lg text-sm w-full"
          >
            All Insurers
          </Button>
        </Link>
        {uniqueInsurers.map((insurer) => {
          const isSelected = selectedInsurers.includes(insurer.slug);
          return (
            <Link key={insurer.slug} href={`/html-sitemaps/compare-health-insurance/${insurer.slug}`}>
              <Button
                variant={isSelected ? "default" : "outline"}
                className="h-10 px-2 rounded-lg text-sm w-full"
              >
                {insurer.name.endsWith("Health Insurance") ? insurer.name.replace("Health Insurance", "") : insurer.name}
              </Button>
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default InsurerFilterButtons;
