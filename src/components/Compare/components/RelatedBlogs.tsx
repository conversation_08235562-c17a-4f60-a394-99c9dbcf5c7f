import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import SectionContainer from "@/components/globals/SectionContainer";
import {
  BodyMedium,
  BodySmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import NavigationButton from "@/components/globals/NavigationButton";

type BlogCardProps = {
  title: string;
  date: string;
  author: string;
  description: string;
  imageUrl: string;
  url: string;
};

const BlogCard: React.FC<BlogCardProps> = ({
  title,
  date,
  author,
  description,
  imageUrl,
  url,
}) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-primary-200 overflow-hidden hover:shadow-md transition-shadow duration-300 w-full">
      <div className="h-36 relative">
        <Image src={imageUrl} alt={title} fill className="object-cover" />
      </div>
      <div className="h-[50%] flex flex-col gap-2 px-6 py-4 justify-between">
        <BodyMedium weight="medium" className="text-neutral-1100">
          {title}
        </BodyMedium>
        <BodySmall className="text-neutral-800">
          {date} | {author}
        </BodySmall>
        <Link
          href={url}
          className="inline-block text-center mt-2 text-xs font-normal bg-primary-800 hover:bg-primary-700 text-white border border-primary-200 px-4 py-3 rounded-base transition-colors duration-200"
        >
          Read More
        </Link>
      </div>
    </div>
  );
};

type RelatedBlogsProps = {
  blogData: {
    heading: string;
    blogs: BlogCardProps[];
  };
};

const RelatedBlogs: React.FC<RelatedBlogsProps> = ({ blogData }) => {
  const { heading, blogs } = blogData;
  const [currentPage, setCurrentPage] = useState(0);
  const blogsPerPage = 3;
  const totalPages = Math.ceil(blogs.length / blogsPerPage);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const handlePrevPage = () => {
    setCurrentPage((prev) => Math.max(0, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(totalPages - 1, prev + 1));
  };

  // Handle scroll events for mobile view
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const scrollLeft = container.scrollLeft;
      const cardWidth = container.scrollWidth / blogs.length;
      const currentIndex = Math.round(scrollLeft / cardWidth);
      setCurrentPage(currentIndex);
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [blogs.length]);

  const PageIndicator = ({
    isActive,
    onClick,
  }: {
    isActive: boolean;
    onClick: () => void;
  }) => (
    <button
      onClick={onClick}
      className={`w-2.5 h-2.5 rounded-full transition-all duration-200 ${
        isActive
          ? "bg-primary-800 scale-110"
          : "bg-gray-300 hover:bg-gray-400 hover:scale-105"
      }`}
    />
  );

  return (
    <SectionContainer
      className="w-full mb-6 md:mb-14 bg-white"
      id="related-blogs"
    >
      <HeadingXLarge
        as="h2"
        className="text-neutral-1100 text-center mb-4 md:mb-6"
      >
        Related Blogs
      </HeadingXLarge>

      {/* Desktop view */}
      <div className="relative hidden md:block">
        <div className="flex items-center">
          {blogs.length > blogsPerPage && (
            <div className="flex-shrink-0 mr-4">
              <NavigationButton
                onClick={handlePrevPage}
                disabled={currentPage === 0}
                direction="prev"
              />
            </div>
          )}

          <SectionContainerLarge className="flex-1 overflow-hidden !mb-0 md:!mb-0 !px-0 md:!px-0">
            <div
              className="flex transition-transform duration-500 ease-in-out"
              style={{
                transform: `translateX(-${currentPage * (100 / totalPages)}%)`,
                width: `${totalPages * 100}%`,
              }}
            >
              {Array.from({ length: totalPages }, (_, pageIndex) => (
                <div
                  key={pageIndex}
                  className="grid grid-cols-1 md:grid-cols-3 gap-6 flex-shrink-0"
                  style={{ width: `${100 / totalPages}%` }}
                >
                  {blogs
                    .slice(
                      pageIndex * blogsPerPage,
                      (pageIndex + 1) * blogsPerPage
                    )
                    .map((blog, index) => (
                      <BlogCard
                        key={pageIndex * blogsPerPage + index}
                        {...blog}
                      />
                    ))}
                </div>
              ))}
            </div>
          </SectionContainerLarge>

          {blogs.length > blogsPerPage && (
            <div className="flex-shrink-0 ml-4">
              <NavigationButton
                onClick={handleNextPage}
                disabled={currentPage === totalPages - 1}
                direction="next"
              />
            </div>
          )}
        </div>

        {/* Page Indicators */}
        {blogs.length > blogsPerPage && (
          <div className="flex justify-center items-center mt-6 gap-2">
            {Array.from({ length: totalPages }, (_, index) => (
              <PageIndicator
                key={index}
                isActive={currentPage === index}
                onClick={() => setCurrentPage(index)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Mobile view */}
      <div className="relative md:hidden">
        <div
          ref={scrollContainerRef}
          className="overflow-x-auto scroll-smooth no-scrollbar"
          style={{
            scrollbarWidth: "none",
            msOverflowStyle: "none",
          }}
        >
          <div
            className="flex gap-4 pb-4 items-stretch"
            style={{ width: `${blogs.length * 100}%` }}
          >
            {blogs.map((blog, index) => (
              <BlogCard key={index} {...blog} />
            ))}
          </div>
        </div>

        {/* Page Indicators for Mobile */}
        {blogs.length > 1 && (
          <div className="flex justify-center items-center mt-2 gap-2">
            {Array.from({ length: blogs.length }, (_, index) => (
              <PageIndicator
                key={index}
                isActive={currentPage === index}
                onClick={() => {
                  const container = scrollContainerRef.current;
                  if (container) {
                    const cardWidth = container.scrollWidth / blogs.length;
                    container.scrollTo({
                      left: index * cardWidth,
                      behavior: "smooth",
                    });
                  }
                }}
              />
            ))}
          </div>
        )}
      </div>
    </SectionContainer>
  );
};

export default RelatedBlogs;
