import SimpleCard from "@/components/globals/DSComponentsV0/SimpleCard";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import SectionHeaderWithParse from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
import { Grid } from "@/components/UI/Grid";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import { FaShieldAlt } from "react-icons/fa";
import { CompareIndexPageTopComparisons } from "@/components/Compare/type";
import CompareIndexTopComparisonCard from "./CompareIndexTopComparisonCard";

export default function CompareIndexTopComparisons({
  topComparisons,
}:{
  topComparisons: CompareIndexPageTopComparisons[];
}) {
  
  if (!topComparisons) return null;

  const topComparison = topComparisons[0];
  if (!topComparison) return null;

  const numPoints = topComparison.compare_index_page_top_comparison_cards.length;

  return (
    <SectionContainerMedium className="flex flex-col items-center !px-0" id="top-comparisons">
      <SectionHeaderWithParse
        heading={topComparison.title}
        subheading={topComparison.description}
        component="h2"
      />
       
      {/* Desktop Grid Layout */}
      <Grid cols={numPoints >= 4 ? 4 : numPoints as 1 | 2 | 3} className="hidden md:grid w-full">
        {topComparison.compare_index_page_top_comparison_cards.map((point) => (
          <CompareIndexTopComparisonCard key={point.id} topComparison={point} />
        ))}
      </Grid>

      {/* Mobile Carousel Layout */}
      <MobileCarousel totalSlides={numPoints} className="md:hidden">
        {topComparison.compare_index_page_top_comparison_cards.map((point) => (
          <MobileCarouselItem key={point.id} className="mt-0">
            <CompareIndexTopComparisonCard topComparison={point}/>
          </MobileCarouselItem>
        ))}
      </MobileCarousel>
    </SectionContainerMedium>
  );
}