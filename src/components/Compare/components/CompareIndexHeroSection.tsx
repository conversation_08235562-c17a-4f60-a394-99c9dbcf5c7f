import React from "react";
import PageTopBar from "@/components/globals/PageTopBar";
import SectionContainer from "@/components/globals/SectionContainer";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import { ComparisonData } from "@/components/Compare/Dtos/unifiedDataTransformer";
import CompareForm from "@/components/Compare/components/CompareForm";
import SectionHeader from "@/components/globals/SectionHeader";
import SectionHeaderWithParse from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
import { usePathname } from "next/navigation";


const CompareIndexHeroSection = ({
  staticContent,
  comparisonData,
}: {
  staticContent: {
    hero_title: string;
    hero_description: string;
    pill_content: string;
  };
  comparisonData: ComparisonData[];
}) => {
  const fullUrl = usePathname();
  const { hero_title, hero_description, pill_content } = staticContent;
  const breadcrumbPath = ["OneAssure", "Compare Health Insurance Plans"];
  return (
    <SectionContainer className="mt-6">
      <PageTopBar breadcrumbPath={breadcrumbPath} fullUrl={fullUrl || ""} />
      <SectionContainerMedium className="mb-0 md:mb-0 !p-0">
        <SectionHeaderWithParse
          pill={pill_content}
          heading={hero_title}
          subheading={hero_description}
          component="h1"
          className="!mb-4 md:!mb-9 !px-0"
        />
      <CompareForm comparisonData={comparisonData} />
      </SectionContainerMedium>
    </SectionContainer>
  );
};

export default CompareIndexHeroSection;
