import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { FiArrowRight } from "react-icons/fi";
import { Button } from "@/components/UI/Button";
import { BodyMedium, HeadingXLarge } from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";

type Plan = {
  name: string;
  logo: string;
  link: string;
  popular?: boolean;
};

type MostPopularPlansProps = {
  heading: string;
  plans: Plan[];
};

const PlanCard: React.FC<{ plan: Plan; index: number }> = ({ plan }) => (
  <Link href={plan.link} className="block">
    <div className="bg-white border border-primary-200 rounded-xl p-4 flex flex-col justify-between shadow-sm hover:shadow-md transition-shadow cursor-pointer group">
      <div className="flex items-center justify-between">
        <div className="flex flex-col items-start gap-y-2">
          <div className="flex items-center gap-1">
            <Image
              src={plan.logo}
              alt={plan.name}
              width={50}
              height={50}
              className="object-contain h-6"
            />
            <BodyMedium weight="medium" className="mx-4 text-neutral-1100">
              {plan.name}
            </BodyMedium>
          </div>
        </div>
        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-xs text-neutral-1100 group-hover:bg-primary-100 transition-colors">
          <FiArrowRight />
        </div>
      </div>
    </div>
  </Link>
);

const MostPopularPlans: React.FC<MostPopularPlansProps> = ({
  heading,
  plans,
}) => {
  const [showAllPlans, setShowAllPlans] = useState(false);

  // Separate ordering for mobile and desktop
  const getMobilePlans = () => {
    const popularPlans = plans.filter((plan) => plan.popular);
    const nonPopularPlans = plans.filter((plan) => !plan.popular);
    return [...popularPlans, ...nonPopularPlans];
  };

  const getDesktopPlans = () => {
    const popularPlans = plans.filter((plan) => plan.popular);
    const nonPopularPlans = plans.filter((plan) => !plan.popular);

    if (popularPlans.length === 0) return plans;

    // Insert popular plans in the middle
    const middleIndex = Math.floor(nonPopularPlans.length / 2);
    const beforeMiddle = nonPopularPlans.slice(0, middleIndex);
    const afterMiddle = nonPopularPlans.slice(middleIndex);

    return [...beforeMiddle, ...popularPlans, ...afterMiddle];
  };

  const mobilePlans = getMobilePlans();
  const desktopPlans = getDesktopPlans();

  const displayedMobilePlans = showAllPlans
    ? mobilePlans
    : mobilePlans.slice(0, 5);
  const hasMorePlans = plans.length > 4;

  const toggleShowAllPlans = () => setShowAllPlans((prev) => !prev);

  return (
    <SectionContainerLarge
      className="w-full flex flex-col items-center"
      id="explore-other-plans"
    >
      <HeadingXLarge
        as="h2"
        className="text-neutral-1100 text-center mb-4 md:mb-6"
      >
        Explore Our Most Popular Plans
      </HeadingXLarge>

      {/* Desktop view - popular plans in middle */}
      <div className="hidden md:grid w-full grid-cols-3 gap-y-6 gap-x-6 px-2 md:px-0">
        {desktopPlans.map((plan, index) => (
          <PlanCard key={`desktop-${index}`} plan={plan} index={index} />
        ))}
      </div>

      {/* Mobile view - popular plans first */}
      <div className="lg:hidden w-full">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {displayedMobilePlans.map((plan, index) => (
            <PlanCard key={`mobile-${index}`} plan={plan} index={index} />
          ))}
        </div>

        {/* Toggle button for mobile */}
        {hasMorePlans && (
          <div className="flex justify-center mt-4">
            <Button
              onClick={toggleShowAllPlans}
              variant="primary"
              className="flex items-center"
            >
              <span className="text-sm font-normal">
                {showAllPlans ? "Show Less" : "See More Plans"}
              </span>
            </Button>
          </div>
        )}
      </div>
    </SectionContainerLarge>
  );
};

export default MostPopularPlans;
