import SectionContainerSmall from "@/components/globals/SectionContainerSmall";
import {
  BodyLarge,
  HeadingMedium,
  HeadingXLarge,
} from "@/components/UI/Typography";
import Image from "next/image";
import React from "react";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";

export type QuickDecisionGuideCard = {
  slug: string;
  variant_slug: string;
  temp_slug?: string;
  logo: string;
  productName: string;
  title: string;
  titleColor: string;
  points: string[];
  bgColor: string;
  borderColor: string;
};

export type QuickDecisionGuideProps = {
  data: {
    heading: string;
    cards: QuickDecisionGuideCard[];
  };
};

const QuickDecisionGuideCardComponent: React.FC<{
  card: QuickDecisionGuideCard;
}> = ({ card }) => (
  <div className="flex-1 bg-white rounded-2xl p-4 md:px-6 md:py-5 gap-4 shadow-md flex flex-col items-start justify-start border-[1px] border-primary-200 transition-all">
    <div className="flex items-center w-full gap-4 rounded-t-2xl">
      <div className="relative w-12 h-6 bg-white">
        <Image
          src={card.logo}
          alt={card.productName}
          fill
          className="object-contain"
        />
      </div>
      <div>
        <HeadingMedium weight="semibold" className="text-neutral-1100">
          {card.title}
        </HeadingMedium>
      </div>
    </div>
    {card.points?.length == 0 ? (
      <BodyLarge className="text-neutral-800">Not available</BodyLarge>
    ) : (
      <ul className="flex flex-col items-start gap-2">
        {card.points.map((point, i) => (
          <li key={i} className="flex items-start gap-2">
            <div className="flex-shrink-0 w-4 h-4 flex items-center justify-center md:pt-2">
              <IoMdCheckmarkCircleOutline className=" text-secondary-400" />
            </div>
            <BodyLarge className="text-neutral-800">{point}</BodyLarge>
          </li>
        ))}
      </ul>
    )}
  </div>
);

const QuickDecisionGuide: React.FC<QuickDecisionGuideProps> = ({ data }) => {
  return (
    <SectionContainerSmall
      className="flex flex-col gap-4 md:gap-6 items-center rounded-xl"
      id="quick-decision-guide"
    >
      <HeadingXLarge as="h2" className="text-center text-neutral-1100">
        Quick Decision Guide
      </HeadingXLarge>
      <div className="flex flex-col md:flex-row gap-4 md:gap-8 w-full justify-center">
        {data.cards.map((card, idx) => (
          <QuickDecisionGuideCardComponent key={idx} card={card} />
        ))}
      </div>
    </SectionContainerSmall>
  );
};

export default QuickDecisionGuide;
