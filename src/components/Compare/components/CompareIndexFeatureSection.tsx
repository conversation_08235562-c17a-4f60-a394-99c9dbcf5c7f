import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import { Grid } from "@/components/UI/Grid";
import { CompareIndexPageFeaturesToConsider } from "@/components/Compare/type";
import SectionContainerSmall from "@/components/globals/SectionContainerSmall";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import PointCard from "@/components/globals/DSComponentsV0/PointCard";
import {
  IoMdCheckmarkCircleOutline,
  IoMdCloseCircleOutline,
} from "react-icons/io";
import SectionHeaderWithParse from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";

export default function CompareIndexFeatureSection({
  features,
  pill_content,
}: {
  features: CompareIndexPageFeaturesToConsider[];
  pill_content?: string;
}) {
  if (!features) return null;

  const feature = features[0];
  if (!feature) return null;

  return (
    <SectionContainerMedium className="flex flex-col items-center !mb-0 !px-0" id="features_to_consider">

      <SectionHeaderWithParse
        pill={pill_content}
        heading={feature.title}
        subheading={feature.description}
        component="h2"
      />

      <SectionContainerSmall className="!px-0">
        <Grid cols={2} className="hidden md:grid" gap={8}>
          {feature.compare_index_page_features_to_consider_points.map(
            (point) => (
              <PointCard
                key={point.id}
                title={point.title}
                points={point.points}
                pointIcon={
                  point.type === "benefits" ? (
                    <IoMdCheckmarkCircleOutline className="text-secondary-400 flex-shrink-0 mt-1" size={16} />
                  ) : (
                    <IoMdCloseCircleOutline className="text-red-400 flex-shrink-0 mt-1" size={16} />
                  )
                }
                className="text-center"
                as="h3"
              />
            )
          )}
        </Grid>

        <MobileCarousel
          totalSlides={
            feature.compare_index_page_features_to_consider_points.length
          }
          className="md:hidden"
        >
          {feature.compare_index_page_features_to_consider_points.map(
            (point) => (
              <MobileCarouselItem key={point.id}>
                <PointCard
                  key={point.id}
                  title={point.title}
                  points={point.points}
                  pointIcon={
                    point.type === "benefits" ? (
                      <IoMdCheckmarkCircleOutline className="text-secondary-400 flex-shrink-0 mt-1" size={14} />
                    ) : (
                      <IoMdCloseCircleOutline className="text-red-400 flex-shrink-0 mt-1" size={14} />
                    )
                  }
									className="px-2"
                />
              </MobileCarouselItem>
            )
          )}
        </MobileCarousel>
      </SectionContainerSmall>
    </SectionContainerMedium>
  );
}
