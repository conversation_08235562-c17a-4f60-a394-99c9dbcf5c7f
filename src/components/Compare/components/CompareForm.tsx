import { BodyMedium, BodySmall } from "@/components/UI/Typography";
import { ComparisonData, ProductVariant } from "../Dtos/unifiedDataTransformer";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/UI/Select";
import { useState } from "react";
import SectionContainerXSmall from "@/components/globals/SectionContainerXSmall";
import { Button } from "@/components/UI/Button";

export default function CompareForm({
  comparisonData,
}: {
  comparisonData: ComparisonData[];
}) {
  const [compareFormData, setCompareFormData] = useState<{
    variantOne: ProductVariant | undefined;
    variantTwo: ProductVariant | undefined;
  }>({
    variantOne: {
      id: "",
      variant_name: "",
      variant_slug: "",
      temp_slug: "",
      product: {
        insurer: {
          name: "",
          slug: "",
        },
      },
    },
    variantTwo: {
      id: "",
      variant_name: "",
      variant_slug: "",
      temp_slug: "",
      product: {
        insurer: {
          name: "",
          slug: "",
        },
      },
    },
  });

  const handleClick = () => {
    if (
      !compareFormData?.variantOne?.variant_name ||
      !compareFormData?.variantTwo?.variant_name ||
      compareFormData.variantOne.id === compareFormData.variantTwo.id
    ) {
      return;
    }
    const url = `/compare-health-insurance-plans/${compareFormData.variantOne.product.insurer.slug}-${compareFormData.variantOne.variant_slug}-vs-${compareFormData.variantTwo.product.insurer.slug}-${compareFormData.variantTwo.variant_slug}/${compareFormData.variantOne.id}-${compareFormData.variantTwo.id}`;
    window.location.href = url;
  };

  return (
    <SectionContainerXSmall className="!p-0">
      <div className="w-full">
        {/* Combined responsive design */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-center gap-4 md:gap-0 mt-4 md:mt-0">
          <div className="md:pl-10 md:pr-1.5 md:py-1.5 flex flex-col md:flex-row md:items-center md:justify-between md:bg-primary-50 md:border md:border-primary-300 md:rounded-full w-full gap-2 md:gap-0">
            
            {/* First Select */}
            <div className="md:w-[45%] md:overflow-hidden md:pr-5 w-full">
              <div className="md:bg-transparent bg-primary-50 border md:border-none border-primary-300 rounded-full md:rounded-none py-3 md:py-0 text-center md:text-left flex items-center justify-center md:justify-start">
                <Select
                  value={compareFormData?.variantOne?.variant_name}
                  onValueChange={(value) => {
                    setCompareFormData({
                      ...compareFormData,
                      variantOne: comparisonData.find(
                        (data) => data.product_variant.variant_name === value
                      )?.product_variant,
                    });
                  }}
                >
                  <SelectTrigger className="bg-transparent border-none shadow-none text-neutral-dark-1100 font-medium !px-0 !py-0 !w-auto md:!w-full text-sm/[1.125rem] md:text-[0.875rem]/[1.25rem]">
                    <SelectValue placeholder="Select a plan to compare">
                      {compareFormData?.variantOne?.variant_name}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {comparisonData.map((data) => (
                      <SelectItem
                        key={data.product_variant.id}
                        value={data.product_variant.variant_name}
                      >
                        {data.product_variant.variant_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* VS Text */}
            <BodyMedium className="text-neutral-dark-1100 font-medium md:font-semibold md:mx-5 text-center">
              vs
            </BodyMedium>

            {/* Second Select */}
            <div className="md:w-[45%] md:overflow-hidden md:pl-5 w-full">
              <div className="md:bg-transparent bg-primary-50 border md:border-none border-primary-300 rounded-full md:rounded-none py-3 md:py-0 text-center md:text-left flex items-center justify-center md:justify-start">
                <Select
                  value={compareFormData?.variantTwo?.variant_name}
                  onValueChange={(value) => {
                    setCompareFormData({
                      ...compareFormData,
                      variantTwo: comparisonData.find(
                        (data) => data.product_variant.variant_name === value
                      )?.product_variant,
                    });
                  }}
                >
                  <SelectTrigger className="bg-transparent border-none shadow-none text-neutral-dark-1100 font-medium !px-0 !py-0 !w-auto md:!w-full text-sm/[1.125rem] md:text-[0.875rem]/[1.25rem]">
                    <SelectValue placeholder="Select a plan to compare">
                      {compareFormData?.variantTwo?.variant_name}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {comparisonData.map((data) => (
                      <SelectItem
                        key={data.product_variant.id}
                        value={data.product_variant.variant_name}
                      >
                        {data.product_variant.variant_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Compare Button */}
            <div className="md:ml-20 w-full md:w-auto mt-2 md:mt-0">
              <Button
                className="bg-primary-800 rounded-full px-5 md:!py-2 py-3 w-full md:w-auto flex items-center justify-center disabled:opacity-100 disabled:text-neutral-300"
                disabled={
                  !compareFormData?.variantOne?.variant_name ||
                  !compareFormData?.variantTwo?.variant_name ||
                  compareFormData.variantOne.id === compareFormData.variantTwo.id
                }
                onClick={handleClick}
              >
                <BodyMedium className="font-semibold leading-none">
                  Compare
                </BodyMedium>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </SectionContainerXSmall>
  );
}
