"use client";
import SectionHeader from "@/components/globals/SectionHeader";
import Image from "next/image";
import React from "react";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import { IoTrendingUpSharp } from "react-icons/io5";
import { Button } from "@/components/UI/Button";
import {
  BodyLarge,
  BodySmall,
  HeadingMedium,
} from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { orufyHandler } from "@/utils/orufyHandler";

export type Plan = {
  icon: string;
  title: string;
  button: string;
  features: string[];
  buttonColor: string;
  cardBg: string;
  cardBorder: string;
  mostPopular?: boolean;
};

type FeaturesComparisonSectionProps = {
  pill: string;
  heading: string;
  subHeading: string;
  plans: Plan[];
};

const FeaturesComparisonSection: React.FC<FeaturesComparisonSectionProps> = ({
  pill,
  heading,
  subHeading,
  plans,
}) => {
  function handleClick() {
    const pathname = window.location.pathname;
    const utm_medium = sessionStorage.getItem("utm_medium");
    const utm_source = sessionStorage.getItem("utm_source");
    const utm_campaign = sessionStorage.getItem("utm_campaign");
    const utm_content = sessionStorage.getItem("utm_content");
    const utm_term = sessionStorage.getItem("utm_term");

    const orufyLink = `${process.env.NEXT_PUBLIC_ONE_ASSURE_ORUFY_LINK}?utm_source=${utm_source}&utm_medium=${utm_medium}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&utm_term=${utm_term}`;
    orufyHandler(orufyLink);
  }
  return (
    <SectionContainerLarge
      className="w-full flex flex-col items-center"
      id="features-comparison"
    >
      <SectionHeader
        pill={pill}
        heading={heading}
        subheading={subHeading}
        component="h2"
      />
      <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-4 items-center mt-3 md:mt-2">
        {plans.map((plan, idx) => (
          <div
            key={idx}
            className={`relative flex flex-col justify-center items-center rounded-2xl w-full p-4 md:px-5 transition-all duration-300 text-center ${
              plan.mostPopular
                ? "scale-y-108 border-2 border-primary-300 shadow-lg md:pt-8 md:pb-4 order-first md:order-none"
                : "md:py-4 shadow-md border border-primary-200"
            }`}
          >
            {plan.mostPopular && (
              <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-secondary-100 px-4 rounded-full font-medium z-10 border-[1px] border-secondary-400">
                <BodySmall className="text-secondary-400 flex items-center gap-2">
                  <IoTrendingUpSharp className="text-secondary-400" /> Most
                  Popular
                </BodySmall>
              </div>
            )}
            <div
              className={`flex flex-col items-center ${
                plan.mostPopular ? "gap-3 md:gap-4" : "gap-3"
              } w-full`}
            >
              <div className="flex justify-center items-center">
                <Image
                  src={plan.icon}
                  alt={plan.title}
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full"
                />
              </div>

              <HeadingMedium
                weight="semibold"
                className="text-neutral-1100 text-center"
              >
                {plan.title}
              </HeadingMedium>

              <ul className="space-y-2 w-full">
                {plan.features.map((feature, i) => (
                  <li key={i} className="flex text-left">
                    <IoMdCheckmarkCircleOutline className="text-green-main-200 mr-2 flex-shrink-0" />
                    <BodyLarge className="text-neutral-800">
                      {feature}
                    </BodyLarge>
                  </li>
                ))}
              </ul>
            </div>
            <Button
              variant="primary"
              className={"mt-4 md:mt-6"}
              onClick={() => {
                handleClick();
              }}
            >
              <span className="text-white text-sm">{plan.button}</span>
            </Button>
          </div>
        ))}
      </div>
    </SectionContainerLarge>
  );
};

export default FeaturesComparisonSection;
