import React from "react";
import WhatOurExpertsHelpYouWithCardComponent from "@/components/Compare/components/WhatOurExpertHelpYouWithCardComponent";
import SectionContainer from "@/components/globals/SectionContainer";
import { HeadingXLarge } from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";

export type WhatOurExpertsHelpYouWithCard = {
  title: string;
  titleClass: string;
  bgClass: string;
  points: { icon: React.ReactNode; text: string }[];
};

export type WhatOurExpertsHelpYouWithProps = {
  heading: string;
  subheading: string;
  cards: WhatOurExpertsHelpYouWithCard[];
};

const WhatOurExpertsHelpYouWith: React.FC<WhatOurExpertsHelpYouWithProps> = ({
  heading,
  subheading,
  cards,
}) => {
  return (
    <SectionContainer
      className="w-full flex flex-col items-center gap-4 md:gap-6"
      id="expert-reviews"
    >
      <HeadingXLarge as="h2" className="text-neutral-1100 text-center">
        {heading}
      </HeadingXLarge>
      <SectionContainerLarge className="grid grid-cols-1 md:grid-cols-3 gap-y-4 md:gap-x-4 !mb-0 md:!mb-0 !px-0 md:!px-0">
        {cards.map((card) => (
          <WhatOurExpertsHelpYouWithCardComponent
            key={card.title}
            card={card}
          />
        ))}
      </SectionContainerLarge>
    </SectionContainer>
  );
};

export default WhatOurExpertsHelpYouWith;
