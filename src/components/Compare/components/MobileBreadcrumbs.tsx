import Breadcrumb from "@/components/globals/Breadcrumb";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";

type data = {
  title: string;
  subtitle: string;
  slug: string;
  variant_slug: string;
};

const MobileBreadcrumbs = ({ data }: { data: data[] }) => {
  const breadcrumbPath = [
    "OneAssure",
    "Health Insurance",
    "Compare Health Insurance Plans",
    `${data[0].title} vs ${data[1].title}`,
  ];

  return (
    <SectionContainerMedium className="w-full md:hidden mb-6">
      <Breadcrumb path={breadcrumbPath} fullUrl={""} />
    </SectionContainerMedium>
  );
};

export default MobileBreadcrumbs;
