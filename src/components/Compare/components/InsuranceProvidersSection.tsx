import Link from "next/link";
import React, { useState } from "react";
import { FiArrowRight } from "react-icons/fi";
import Image from "next/image";
import { Button } from "@/components/UI/Button";
import { BodyMedium, HeadingXLarge } from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";

type InsuranceProvidersSectionProps = {
  allInsurerData: any[];
};

const PlanCard: React.FC<{ plan: any }> = ({ plan }) => (
  <Link
    scroll={false}
    href={`${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/${plan.company_slug}`}
    className="block"
  >
    <div className="bg-white border border-primary-200 rounded-xl p-4 flex flex-col justify-between shadow-sm hover:shadow-md transition-shadow cursor-pointer group">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="relative w-12 h-6">
            <Image
              src={plan.logo_url}
              alt={plan.company_name}
              fill
              className="object-contain"
            />
          </div>
          <BodyMedium weight="medium" className="mx-4 text-neutral-1100">
            {plan.company_name}
          </BodyMedium>
        </div>
        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-xs text-neutral-1100 group-hover:bg-primary-100 transition-colors">
          <FiArrowRight />
        </div>
      </div>
    </div>
  </Link>
);

const InsuranceProvidersSection: React.FC<InsuranceProvidersSectionProps> = ({
  allInsurerData,
}) => {
  const [showAllPlans, setShowAllPlans] = useState(false);

  const displayedPlans = showAllPlans
    ? allInsurerData
    : allInsurerData.slice(0, 6);
  const hasMorePlans = allInsurerData.length > 6;

  const toggleShowAllPlans = () => setShowAllPlans((prev) => !prev);

  return (
    <SectionContainerLarge
      className="w-full bg-white rounded-xl"
      id="health-insurance-providers-in-india"
    >
      <HeadingXLarge
        as="h2"
        className="text-neutral-1100 mb-4 md:mb-6 text-center"
      >
        Health Insurance Providers in India
      </HeadingXLarge>

      {/* Desktop view - show all plans */}
      <div className="hidden md:grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        {allInsurerData.map((item, index) => (
          <PlanCard key={`desktop-${index}`} plan={item} />
        ))}
      </div>

      {/* Mobile view - show limited plans with toggle */}
      <div className="md:hidden w-full">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {displayedPlans.map((item, index) => (
            <PlanCard key={`mobile-${index}`} plan={item} />
          ))}
        </div>

        {/* Toggle button for mobile */}
        {hasMorePlans && (
          <div className="flex justify-center mt-4">
            <Button
              onClick={toggleShowAllPlans}
              variant="primary"
              className="flex items-center"
            >
              <span className="text-sm font-normal">
                {showAllPlans ? "Show Less" : "See More Insurers"}
              </span>
            </Button>
          </div>
        )}
      </div>
    </SectionContainerLarge>
  );
};

export default InsuranceProvidersSection;
