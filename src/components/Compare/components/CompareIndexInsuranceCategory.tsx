import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import SectionHeaderWithParse from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
import { CompareIndexPageInsuranceCategory } from "@/components/Compare/type";
import CategoryCard from "@/components/globals/DSComponentsV0/CategoryCard";
import { Grid } from "@/components/UI/Grid";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";

export default function CompareIndexInsuranceCategory({
  insuranceCategories,
}: {
  insuranceCategories: CompareIndexPageInsuranceCategory[];
}) {
  if (!insuranceCategories) return null;

  const insuranceCategory = insuranceCategories[0];
  if (!insuranceCategory) return null;

  return (
    <SectionContainerMedium className="flex flex-col items-center !px-0">
      <SectionHeaderWithParse
        pill={insuranceCategory.pill_content}
        heading={insuranceCategory.title}
        subheading={insuranceCategory.description}
        component="h2"
      />

      <Grid cols={3} className="hidden md:grid mt-6">
        {insuranceCategory.compare_index_page_insurance_category_cards.map(
          (card) => (
            <CategoryCard
              key={card.id}
              title={card.title}
              features={card.points}
              button={card.button_text}
              icon={
                card.title.includes("Family") === true ? (
                  "https://cdn.oasr.in/oa-site/cms-uploads/media/Family_bb99bedf43.svg"
                ) : card.title.includes("Senior") === true ? (
                  "https://cdn.oasr.in/oa-site/cms-uploads/media/Mobile_5a0506064e.svg"
                ) : card.title.includes("Maternity") === true ? (
                  "https://cdn.oasr.in/oa-site/cms-uploads/media/Baby_16368eea11.svg"
                ) : null
              }
              mostPopular={card.title.includes("Family")}
            />
          )
        )}
      </Grid>

      <MobileCarousel
        totalSlides={
          insuranceCategory.compare_index_page_insurance_category_cards.length
        }
        className="md:hidden"
      >
        {insuranceCategory.compare_index_page_insurance_category_cards.map(
          (card) => (
            <MobileCarouselItem key={card.id} className="pt-2 md:pt-0">
              <CategoryCard
                title={card.title}
                features={card.points}
                button={card.button_text}
                icon={
                  card.title.includes("Family") === true ? (
                    "https://cdn.oasr.in/oa-site/cms-uploads/media/Family_bb99bedf43.svg"
                  ) : card.title.includes("Senior") === true ? (
                    "https://cdn.oasr.in/oa-site/cms-uploads/media/Mobile_5a0506064e.svg"
                  ) : card.title.includes("Maternity") === true ? (
                    "https://cdn.oasr.in/oa-site/cms-uploads/media/Baby_16368eea11.svg"
                  ) : null
                }
                mostPopular={card.title.includes("Family")}
                className="h-full"
              />
            </MobileCarouselItem>
          )
        )}
      </MobileCarousel>
    </SectionContainerMedium>
  );
}
