import { BodyLarge } from "@/components/UI/Typography";

const Separator = () => {
  return (
    <div className="flex items-center justify-center w-full">
      {/* Mobile design - hidden on larger screens */}
      <div className="flex items-center w-full px-7 md:hidden">
        {/* Left line */}
        <div className="h-0.5 bg-primary-200 w-full rounded-full"></div>

        {/* VS text */}
        <span className="text-neutral-1100 font-medium text-sm mx-3 h-8 w-8 flex items-center justify-center">
          VS
        </span>

        {/* Right line */}
        <div className="h-0.5 bg-primary-200 w-full rounded-full"></div>
      </div>

      {/* Desktop design - hidden on mobile, visible on md and up */}
      <div className="hidden md:flex items-center justify-center w-full">
        <div className="w-14 h-14 rounded-full bg-primary-100 flex items-center justify-center">
          <BodyLarge weight="medium" className="text-neutral-1100">
            VS
          </BodyLarge>
        </div>
      </div>
    </div>
  );
};

export default Separator;
