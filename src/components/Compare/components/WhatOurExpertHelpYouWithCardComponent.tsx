import { BodyLarge, HeadingSmall } from "@/components/UI/Typography";
import React from "react";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";

export interface WhatOurExpertsHelpYouWithCard {
  title: string;
  points: { icon: React.ReactNode; text: string }[];
}

interface WhatOurExpertsHelpYouWithCardComponentProps {
  card: WhatOurExpertsHelpYouWithCard;
}

const WhatOurExpertsHelpYouWithCardComponent: React.FC<
  WhatOurExpertsHelpYouWithCardComponentProps
> = ({ card }) => {
  return (
    <div className="rounded-xl shadow-md border-[1px] border-primary-200 p-4 md:p-6 flex flex-col">
      <HeadingSmall
        weight="semibold"
        className="text-neutral-1100 mb-3 md:mb-4 text-center"
      >
        {card.title}
      </HeadingSmall>

      <ul className="space-y-3">
        {card.points.map((point, i) => (
          <li key={i} className="flex text-left">
            <IoMdCheckmarkCircleOutline className="text-green-main-200 mr-2 flex-shrink-0" />
            <BodyLarge className="text-neutral-800">{point.text}</BodyLarge>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default WhatOurExpertsHelpYouWithCardComponent;
