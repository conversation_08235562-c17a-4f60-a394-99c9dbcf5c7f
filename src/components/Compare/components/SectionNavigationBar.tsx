import React, { useState, useEffect, useRef } from "react";
import SectionContainer from "@/components/globals/SectionContainer";

interface SectionNavigationBarProps {
  activeTab: string;
  setActiveTab: (tabId: string) => void;
}

const tabs = [
  { label: "Quick Decision", id: "quick-decision-guide" },
  { label: "Features Comparison", id: "comparison" },
  { label: "Get Expert Consultation", id: "expert-consultation" },
  { label: "Expert Reviews", id: "expert-reviews" },
  { label: "Category", id: "features-comparison" },
  { label: "FAQs", id: "faqs" },
];

const SectionNavigationBar: React.FC<SectionNavigationBarProps> = ({
  activeTab,
  setActiveTab,
}) => {
  const [highlightedTab, setHighlightedTab] = useState(activeTab);
  const manualScroll = useRef(false);
  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);

  const activeTabLabel =
    tabs.find((tab) => tab.id === activeTab)?.label || "Select Section";

  // Intersection Observer for scroll-based highlighting
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: "-20% 0px -20% 0px",
      threshold: 0.1,
    };
    const observer = new window.IntersectionObserver((entries) => {
      if (manualScroll.current) return;

      // Find the section that is most visible in the viewport
      let bestEntry: IntersectionObserverEntry | null = null;
      let bestRatio = 0;

      for (const entry of entries) {
        if (entry.isIntersecting && entry.intersectionRatio > bestRatio) {
          bestRatio = entry.intersectionRatio;
          bestEntry = entry;
        }
      }

      if (bestEntry && bestRatio > 0.1) {
        const sectionId = bestEntry.target.id;

        if (tabs.some((tab) => tab.id === sectionId)) {
          setHighlightedTab(sectionId);
        }
      }
    }, observerOptions);

    tabs.forEach((tab) => {
      const el = document.getElementById(tab.id);
      if (el) {
        observer.observe(el);
      }
    });

    return () => observer.disconnect();
  }, []);

  // When activeTab changes from parent, update highlightedTab
  useEffect(() => {
    if (activeTab) {
      setHighlightedTab(activeTab);
    }
  }, [activeTab]);

  // Handle tab click: scroll to section, highlight, and temporarily disable observer
  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    setHighlightedTab(tabId);
    manualScroll.current = true;
    const section = document.getElementById(tabId);
    if (section) {
      // Calculate dynamic offset based on actual navbar and navigation bar heights
      const navbar = document.querySelector("nav"); // Main navbar
      const navBarHeight = navbar ? navbar.offsetHeight : 80; // Default navbar height
      const sectionNavHeight = 60; // Height of this section navigation bar
      const totalOffset = navBarHeight + sectionNavHeight + 20; // Additional 20px for breathing room

      const elementTop = section.offsetTop - totalOffset;
      window.scrollTo({
        top: elementTop,
        behavior: "smooth",
      });
    }
    // Allow observer to resume after scroll
    if (scrollTimeout.current) clearTimeout(scrollTimeout.current);
    scrollTimeout.current = setTimeout(() => {
      manualScroll.current = false;
    }, 1500); // Reduced timeout for better responsiveness
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeout.current) clearTimeout(scrollTimeout.current);
    };
  }, []);

  return (
    <>
      {/* Desktop View */}
      <SectionContainer className="z-[30] sticky top-[5.5rem] lg:flex hidden border border-primary-300 rounded-full backdrop-blur-md bg-white/60 md:px-4 md:py-3 gap-3 justify-between shadow-md">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            type="button"
            onClick={() => handleTabClick(tab.id)}
            className={`px-3 py-1 text-sm tab-link transition-colors whitespace-nowrap
              ${
                highlightedTab === tab.id
                  ? "text-primary-800"
                  : "text-neutral-1100  hover:text-primary-700"
              }
            `}
            style={{
              fontWeight: highlightedTab === tab.id ? 600 : 400,
            }}
          >
            {tab.label}
          </button>
        ))}
      </SectionContainer>
    </>
  );
};

export default SectionNavigationBar;
