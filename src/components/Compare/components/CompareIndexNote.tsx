import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import { HeadingMedium } from "@/components/UI/Typography";
import { BodyLarge } from "@/components/UI/Typography";
import parse, { DOMNode, domToReact, Element } from "html-react-parser";

//custom html-renderer for this component
const htmlRenderer = (domNode: DOMNode) => {
  if (domNode.type === "tag" && domNode.attribs) {
    let attrs = domNode.attribs;
    if (attrs.style) {
      attrs.style = "";
    }

    switch (domNode.name) {
      case "p":
        // Check if p contains a span and extract its content
        if (domNode.children && domNode.children.length > 0) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            // Create a new p element with the span's content
            return (
              <BodyLarge as="p" className="text-neutral-1100 text-justify">
                {domToReact(spanChild.children as DOMNode[])}
              </BodyLarge>
            );
          }
        }
        return (
          <BodyLarge as="p" className="text-neutral-1100 text-justify">
            {domToReact(domNode.children as DOMNode[])}
          </BodyLarge>
        );
    }
    return domNode;
  }
};

const CompareIndexNote = ({
  note,
  heading,
}: {
  note: string;
  heading: string;
}) => {
  const parsedNote = parse(note, {
    replace: htmlRenderer,
  });
  return (
    <SectionContainerMedium className="flex flex-col items-center !px-0">
      <div className="bg-primary-100 p-4 md:px-6 md:py-5 border border-primary-300 rounded-xl mx-6 md:mx-0">
        {/* <HeadingMedium
            as="h3"
            className="text-neutral-1100 text-center font-semibold mb-2"
        >
            {heading}
        </HeadingMedium> */}
        <div className="flex flex-col gap-2.5 ">
          <BodyLarge
            as="p"
            className="text-neutral-900 text-justify font-bold"
          >
            {heading}
          </BodyLarge>
          <BodyLarge>{parsedNote}</BodyLarge>
        </div>
      </div>
    </SectionContainerMedium>
  );
};

export default CompareIndexNote;
