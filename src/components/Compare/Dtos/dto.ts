import { CompareIndexPageAPIResponse, CompareIndexPageData } from "../type";

export const transformData = (data: CompareIndexPageAPIResponse | null) : CompareIndexPageData | null => {

    if (!data) {
        return null;
    }

    const staticContent = {
        hero_title: data.hero_title,
        hero_description: data.hero_description,
        note: data.note,
        pill_content: data.pill_content,
        need_to_compare_title: data.need_to_compare_title,
        need_to_compare_description: data.need_to_compare_description
    }

    const sections = data.compare_index_page_sections.map((section) => {
        return {
            id: section.id,
            title: section.title,
            description: section.description,
            type: section.type,
            compare_index_page_section_points: section.compare_index_page_section_points.map((point) => {
                return {
                    id: point.id,
                    title: point.title,
                    description: point.description
                }
            })
        }
    })

    const faqSection = sections.find((section) => section.type === "faqs");
    const faqData = {
        id: faqSection?.id || "",
        title: faqSection?.title || "",
        description: faqSection?.description || "",
        pill: staticContent.pill_content,
        faqs: faqSection?.compare_index_page_section_points.map((point) => {
            return {
                id: point.id || "",
                question: point.title || "",
                answer: point.description || ""
            }
        }) || []
    }

    const featuresToConsider = data.compare_index_page_features_to_considers.map((feature) => {
        return {
            id: feature.id,
            title: feature.title,
            description: feature.description,
            compare_index_page_features_to_consider_points: feature.compare_index_page_features_to_consider_points.map((point) => {
                return {
                    id: point.id,
                    title: point.title,
                    points: point.points,
                    type: point.type
                }
            })
        }
    })

    const insuranceCategories = data.compare_index_page_insurance_categories.map((category) => {
        return {
            id: category.id,
            title: category.title,
            pill_content: category.pill_content,
            description: category.description,
            compare_index_page_insurance_category_cards: category.compare_index_page_insurance_category_cards.map((card) => {
                return {
                    id: card.id,
                    title: card.title,
                    points: card.points,
                    button_text: card.button_text
                }
            })
        }
    })

    const topComparisons = data.compare_index_page_top_comparisons.map((comparison) => {
        return {
            id: comparison.id,
            title: comparison.title,
            description: comparison.description,
            compare_index_page_top_comparison_cards: comparison.compare_index_page_top_comparison_cards.map((card) => {
                return {
                    id: card.id,
                    variant_one_id: card.variant_one_id,
                    variant_two_id: card.variant_two_id,
                    variant_one: card.variant_one,
                    variant_two: card.variant_two
                }
            })
        }
    })

    const pageNavigationSection = {
        activeTab: "top-comparisons",
        tabs: [
            { label: "Top Comparisons", id: "top-comparisons" },
            { label: "Assess Healthcare Need", id: "assess_healthcare_need" },
            { label: "Features To Consider", id: "features_to_consider" },
            { label: "Read Inclusion Exclusion", id: "read_inclusion_exclusion" },
            { label: "Policy Conditions", id: "policy_conditions" },
            { label: "What Experts Help You With", id: "what_experts_help_you_with" },
            { label: "FAQs", id: "faqs" },
        ]
    }

    const seo = data.compare_index_page_seo;

    return {
        staticContent,
        sections,
        featuresToConsider,
        insuranceCategories,
        topComparisons,
        pageNavigationSection,
        seo,
        faqData
    }
}