import React from "react";
import { formatPlanVariantName } from "@/utils/planVariantFormatter";

type ApiResponse = {
  health_product_variants?: Array<{
    id?: string;
    variant_name?: string;
    variant_slug?: string;
    temp_slug?: string;
    product?: {
      name?: string;
      insurer?: {
        name?: string;
        logo_url?: string;
        claim_settlement_ratio?: number;
        network_hospital_count?: number;
        network_hospital_url?: string;
        slug?: string;
        temp_slug?: string;
      };
      policy_brochure_url?: string;
      policy_wording_url?: string;
    };
    health_variant_static_content?: {
      best_for?: string[];
      decision_guide?: string[];
      specialty?: string;
      subtitle?: string;
      comparison_enabled?: boolean;
      product_popularity?: number;
      id?: string;
    };
    feature_values?: Array<{
      compare_feature?: {
        name?: string;
        sequence?: number;
        hint_text?: string;
      };
      value?: string;
      sub_value?: string;
      metadata?: {
        listedFeatures?: Array<{
          feature?: string;
          id?: number;
        }>;
      };
    }>;
  }>;
  site_comparison_why_choose_expert_consultation?: Array<{
    id?: string;
    logo_url?: string;
    title?: string;
    description?: string;
  }>;
  site_comparison_how_is_expert_consultation?: Array<{
    id?: string;
    title?: string;
    points?: string[];
  }>;
  site_comparison_health_faqs?: Array<{
    id?: string;
    question?: string;
    answer?: string;
  }>;
};

type HeroData = {
  pill: string;
  heading: string;
  subheading: string;
  cards: Array<{
    slug: string;
    insurer_temp_slug?: string;
    variant_slug: string;
    temp_slug?: string;
    logo: string;
    alt: string;
    title: string;
    subtitle: string;
    whatMakesItSpecial: string;
    bestFor: string[];
  }>;
};

type QuickDecisionGuideData = {
  heading: string;
  cards: Array<{
    slug: string;
    insurer_temp_slug?: string;
    variant_slug: string;
    temp_slug?: string;
    logo: string;
    productName: string;
    title: string;
    titleColor: string;
    points: string[];
    bgColor: string;
    borderColor: string;
  }>;
};

type WhyChooseExpertConsultationData = {
  heading: string;
  cards: Array<{
    icon: string;
    title: string;
    description: string;
  }>;
};

type WhatOurExpertsHelpYouWithData = {
  heading: string;
  subheading: string;
  cards: Array<{
    title: string;
    titleClass: string;
    bgClass: string;
    points: Array<{
      icon: React.ReactNode;
      text: string;
    }>;
  }>;
};

type FAQData = {
  pill: string;
  heading: string;
  subheading: string;
  faqs: Array<{
    question: string;
    answer: string;
  }>;
};

type UpdatedHealthProductVariant = {
  id?: string;
  variant_name?: string;
  variant_slug?: string;
  temp_slug?: string;
  slug?: string;
  insurer_temp_slug?: string;
  name?: string;
  subtitle?: string;
  product?: {
    name?: string;
    insurer?: {
      name?: string;
      logo_url?: string;
      claim_settlement_ratio?: number;
      network_hospital_count?: number;
      network_hospital_url?: string;
      slug?: string;
      temp_slug?: string;
    };
    policy_brochure_url?: string;
    policy_wording_url?: string;
  };
  health_variant_static_content?: {
    best_for?: string[];
    decision_guide?: string[];
    specialty?: string;
    subtitle?: string;
    comparison_enabled?: boolean;
    product_popularity?: number;
    id?: string;
  };
  feature_values?: Array<{
    compare_feature?: {
      name?: string;
      sequence?: number;
      hint_text?: string;
    };
    value?: string;
    sub_value?: string;
    metadata?: {
      listedFeatures?: Array<{
        feature?: string;
        id?: number;
      }>;
    };
  }>;
};

type ProductVariant = {
  id: string;
  variant_name: string;
  variant_slug: string;
  temp_slug?: string;
  product: {
    insurer: {
      name: string;
      slug: string;
      temp_slug?: string;
    };
  };
}

type ComparisonData = {
  product_variant: ProductVariant;
}

type TransformedData = {
  heroData: HeroData;
  quickDecisionGuideData: QuickDecisionGuideData;
  whyChooseExpertConsultationData: WhyChooseExpertConsultationData;
  whatOurExpertsHelpYouWithData: WhatOurExpertsHelpYouWithData;
  faqData: FAQData;
  updatedVariants: UpdatedHealthProductVariant[];
};

const unifiedDataTransformer = (data: ApiResponse): TransformedData => {
  if (
    !data?.health_product_variants ||
    data.health_product_variants.length < 2
  ) {
    throw new Error(
      "Invalid data: At least 2 health product variants are required"
    );
  }

  const variant1 = data.health_product_variants[0];
  const variant2 = data.health_product_variants[1];

  // Create a map of features for easy lookup
  const variant1Features = new Map();
  const variant2Features = new Map();

  variant1.feature_values?.forEach((feature) => {
    if (feature.compare_feature?.name) {
      variant1Features.set(feature.compare_feature.name, {
        sequence: feature.compare_feature.sequence || 0,
        value:
          feature.value || feature.metadata?.listedFeatures?.[0]?.feature || "",
        sub_value: feature.sub_value || "",
      });
    }
  });

  variant2.feature_values?.forEach((feature) => {
    if (feature.compare_feature?.name) {
      variant2Features.set(feature.compare_feature.name, {
        sequence: feature.compare_feature.sequence || 0,
        value:
          feature.value || feature.metadata?.listedFeatures?.[0]?.feature || "",
        sub_value: feature.sub_value || "",
      });
    }
  });

  const insurer1Name = variant1.product?.insurer?.name || "";
  const insurer2Name = variant2.product?.insurer?.name || "";

  // Combine features from both variants
  // Update the original variants with processed feature values
  const updatedVariants = data.health_product_variants.map((variant) => ({
    ...variant,
    slug: variant.product?.insurer?.slug || "",
    insurer_temp_slug: variant.product?.insurer?.temp_slug || "",
    feature_values: variant.feature_values?.map((feature) => ({
      ...feature,
      value:
        feature.value || feature.metadata?.listedFeatures?.[0]?.feature || "",
    })),
  }));

  // Transform Hero Data
  const heroData: HeroData = {
    pill: "Insurance Plans Comparison",
    heading: `${insurer1Name} ${variant1.variant_name} vs ${insurer2Name} ${variant2.variant_name}`,
    subheading:
      "Make an informed decision with our detailed side-by-side comparison of top health insurance policies. Compare coverage, benefits, and premiums to find the perfect plan for your needs.",
    cards: [
      {
        slug: variant1.product?.insurer?.slug || "",
        insurer_temp_slug: variant1.product?.insurer?.temp_slug || "",
        variant_slug: variant1.variant_slug || "",
        temp_slug: variant1.temp_slug || "",
        logo: variant1.product?.insurer?.logo_url || "",
        alt: variant1.product?.insurer?.name || "",
        title: variant1.variant_name || "",
        subtitle: variant1.health_variant_static_content?.subtitle || "",
        whatMakesItSpecial:
          variant1.health_variant_static_content?.specialty ||
          `${
            variant1.product?.name ||
            variant1.variant_name ||
            "Health Insurance Plan"
          } is designed for those who want comprehensive coverage without restrictions. It offers extensive coverage for modern treatments and innovative features.`,
        bestFor: variant1.health_variant_static_content?.best_for || [],
      },
      {
        slug: variant2.product?.insurer?.slug || "",
        insurer_temp_slug: variant2.product?.insurer?.temp_slug || "",
        variant_slug: variant2.variant_slug || "",
        temp_slug: variant2.temp_slug || "",
        logo: variant2.product?.insurer?.logo_url || "",
        alt: variant2.product?.insurer?.name || "",
        title: variant2.variant_name || "",
        subtitle: variant2.health_variant_static_content?.subtitle || "",
        whatMakesItSpecial:
          variant2.health_variant_static_content?.specialty ||
          `${
            variant2.product?.name ||
            variant2.variant_name ||
            "Health Insurance Plan"
          } focuses on providing essential health coverage at an affordable premium. It's designed for budget-conscious individuals who want reliable coverage.`,
        bestFor: variant2.health_variant_static_content?.best_for || [],
      },
    ],
  };

  // Transform Quick Decision Guide Data
  const quickDecisionGuideData: QuickDecisionGuideData = {
    heading: "Quick Decision Guide",
    cards: [
      {
        slug: variant1.product?.insurer?.slug || "",
        insurer_temp_slug: variant1.product?.insurer?.temp_slug || "",
        variant_slug: variant1.variant_slug || "",
        temp_slug: variant1.temp_slug || "",
        logo: variant1.product?.insurer?.logo_url || "",
        productName: variant1.variant_name || "",
        title: variant1.variant_name || "",
        titleColor: "text-blue-600",
        points: variant1.health_variant_static_content?.decision_guide || [],
        bgColor: "#fff",
        borderColor: "border-blue-200",
      },
      {
        slug: variant2.product?.insurer?.slug || "",
        insurer_temp_slug: variant2.product?.insurer?.temp_slug || "",
        variant_slug: variant2.variant_slug || "",
        temp_slug: variant2.temp_slug || "",
        logo: variant2.product?.insurer?.logo_url || "",
        productName: variant2.variant_name || "",
        title: variant2.variant_name || "",
        titleColor: "text-orange-600",
        points: variant2.health_variant_static_content?.decision_guide || [],
        bgColor: "#fff",
        borderColor: "border-orange-200",
      },
    ],
  };

  // Transform Why Choose Expert Consultation Data
  const whyChooseExpertConsultationData: WhyChooseExpertConsultationData = {
    heading: "Why Choose Our Expert Consultation?",
    cards:
      data?.site_comparison_why_choose_expert_consultation?.map((item) => {
        return {
          icon: item.logo_url || "",
          title: item.title || "Expert Consultation",
          description:
            item.description ||
            "Professional guidance for your insurance needs",
        };
      }) || [],
  };

  // Transform What Our Experts Help You With Data
  const whatOurExpertsHelpYouWithData: WhatOurExpertsHelpYouWithData = {
    heading: "What Our Experts Help You With",
    subheading: "",
    cards:
      data?.site_comparison_how_is_expert_consultation?.map((item) => ({
        title: item.title || "Expert Consultation",
        titleClass: "text-blue-500",
        bgClass: "bg-blue-50",
        points: (item.points || []).map((point: string) => ({
          icon: (
            <svg width="28" height="28" fill="none" viewBox="0 0 28 28">
              <circle
                cx="14"
                cy="14"
                r="11"
                stroke="#22c55e"
                strokeWidth="2.5"
                fill="none"
              />
              <path
                d="M9.5 14.5l3 3 7-7"
                stroke="#22c55e"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
          text: point,
        })),
      })) || [],
  };

  // Transform FAQ Data
  const faqData: FAQData = {
    pill: "Insurance Plans Comparison",
    heading: "Frequently Asked Questions",
    subheading:
      "Having questions? We've got answers. Explore our FAQs to find the information you need.",
    faqs:
      data?.site_comparison_health_faqs
        ?.map((faq) => ({
          question: faq.question || "",
          answer: faq.answer || "",
        }))
        .filter((faq) => faq.question && faq.answer) || [],
  };

  return {
    heroData,
    quickDecisionGuideData,
    whyChooseExpertConsultationData,
    whatOurExpertsHelpYouWithData,
    faqData,
    updatedVariants,
  };
};

export default unifiedDataTransformer;
export type {
  ApiResponse,
  HeroData,
  QuickDecisionGuideData,
  WhyChooseExpertConsultationData,
  WhatOurExpertsHelpYouWithData,
  FAQData,
  TransformedData,
  UpdatedHealthProductVariant,
  ProductVariant,
  ComparisonData,
};
