// Local storage for popular plans data to avoid multiple API calls
export interface PopularPlan {
  name: string;
  logo: string;
  url: string;
  popular: boolean;
}

// This data will be updated periodically by a script or manually
export const popularPlansData: PopularPlan[] = [
  {
    name: "ICICI Lombard Elevate",
    logo: "https://cdn.oasr.in/oa-site/cms-uploads/media/icici_lombard_logo_b73c659d45.jpeg",
    url: "https://www.oneassure.in/health-insurance/icici-lombard/icici-elevate",
    popular: true,
  },
  {
    name: "Tata AIG Medicare Premier",
    logo: "https://cdn.oasr.in/oa-site/cms-uploads/media/logo_min_ae1e5d67ba.png",
    url: "https://www.oneassure.in/health-insurance/tata-aig-health-insurance/medicare-premier",
    popular: true,
  },
  {
    name: "HDFC ERGO My Optima Secure",
    logo: "https://cdn.oasr.in/oa-site/cms-uploads/media/logo_hdfc_22e6758170.png",
    url: "https://www.oneassure.in/health-insurance/hdfc-ergo/my-optima-secure",
    popular: true,
  },
  {
    name: "Care Health Supreme",
    logo: "https://cdn.oasr.in/oa-site/cms-uploads/media/LOGO_care_health_insurance_2c0265102e.svg",
    url: "https://www.oneassure.in/health-insurance/care-health-insurance/care-supreme",
    popular: true,
  },
  {
    name: "Niva Bupa Reassure 2.0",
    logo: "https://cdn.oasr.in/oa-site/cms-uploads/media/Niva_health_insurance_logo_a32bd384cc.png",
    url: "https://www.oneassure.in/health-insurance/niva-bupa-health-insurance/reassure-20",
    popular: true,
  },
  {
    name: "Tata AIG Medicare Select Plan",
    logo: "https://cdn.oasr.in/oa-site/cms-uploads/media/logo_min_ae1e5d67ba.png",
    url: "https://www.oneassure.in/health-insurance/tata-aig-health-insurance/tata-aig-medicare-select-plan",
    popular: true,
  },
  {
    name: "Niva Bupa Aspire Titanium Plus",
    logo: "https://cdn.oasr.in/oa-site/cms-uploads/media/Niva_health_insurance_logo_a32bd384cc.png",
    url: "https://www.oneassure.in/health-insurance/niva-bupa-health-insurance/aspire-titanium-plus",
    popular: true,
  },
  {
    name: "Bajaj Allianz My Healthcare Plan",
    logo: "https://cdn.oasr.in/oa-site/cms-uploads/media/bajaj_logo_67de220632.png",
    url: "https://www.oneassure.in/health-insurance/bajaj-allianz-general-insurance-co-ltd/my-healthcare-plan",
    popular: true,
  },
  {
    name: "Star Health Women",
    logo: "https://cdn.oasr.in/oa-site/cms-uploads/media/star_logo_27881f1158.png",
    url: "https://www.oneassure.in/health-insurance/star-health-insurance/star-women",
    popular: true,
  },
  {
    name: "Bajaj Allianz Health Ensure",
    logo: "https://cdn.oasr.in/oa-site/cms-uploads/media/bajaj_logo_67de220632.png",
    url: "https://www.oneassure.in/health-insurance/bajaj-allianz-general-insurance-co-ltd/health-ensure",
    popular: true,
  },
  {
    name: "Niva Bupa Health Companion",
    logo: "https://cdn.oasr.in/oa-site/cms-uploads/media/Niva_health_insurance_logo_a32bd384cc.png",
    url: "https://www.oneassure.in/health-insurance/niva-bupa-health-insurance/niva-bupa-health-companion-variant-2022",
    popular: true,
  },
  {
    name: "Care Health Ultimate",
    logo: "https://cdn.oasr.in/oa-site/cms-uploads/media/LOGO_care_health_insurance_2c0265102e.svg",
    url: "https://www.oneassure.in/health-insurance/care-health-insurance/care-ultimate",
    popular: true,
  },
];

// Function to get popular plans data
export const getPopularPlansData = (): PopularPlan[] => {
  return popularPlansData;
}; 