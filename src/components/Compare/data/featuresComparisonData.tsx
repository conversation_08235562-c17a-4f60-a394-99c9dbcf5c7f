import React from "react";
import { Plan } from "../components/FeaturesComparisonSection";

export const featuresComparisonData: {
  plans: Plan[];
} = {
  plans: [
    {
      icon: "https://cdn.oasr.in/oa-site/cms-uploads/media/Baby_16368eea11.svg",
      title: "Senior Citizen Health Plan",
      button: "Book Free Consultation",
      features: [
        "Secure against age-related medical costs",
        "Tailored for seniors healthcare needs",
      ],
      buttonColor: "bg-blue-500 hover:bg-blue-600 text-white",
      cardBg: "bg-blue-50",
      cardBorder: "border-blue-200",
    },
    {
      icon: "https://cdn.oasr.in/oa-site/cms-uploads/media/Family_bb99bedf43.svg",
      title: "Family Health Plan",
      button: "Book Free Consultation",
      features: [
        "One policy covers the entire family",
        "High sum insured with cashless care",
        "Multiple coverage options based on your family needs",
      ],
      buttonColor: "bg-emerald-500 hover:bg-emerald-600 text-white",
      cardBg: "bg-emerald-50",
      cardBorder: "border-emerald-200",
      mostPopular: true,
    },
    {
      icon: "https://cdn.oasr.in/oa-site/cms-uploads/media/Mobile_5a0506064e.svg",
      title: "Maternity Health Plan",
      button: "Book Free Consultation",
      features: [
        "Covers delivery, newborn care, and maternity expenses",
        "Reduces financial stress of childbirth costs",
      ],
      buttonColor: "bg-blue-500 hover:bg-blue-600 text-white",
      cardBg: "bg-blue-50",
      cardBorder: "border-blue-200",
    },
  ],
};
