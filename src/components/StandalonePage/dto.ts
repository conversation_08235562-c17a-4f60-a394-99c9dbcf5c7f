import { StandAloneApiResponse } from "@/components/StandalonePage/types";
import { featuresComparisonData } from "@/components/Compare/data/featuresComparisonData";


const transformStandalonePageData = (pageData: StandAloneApiResponse) => {
  if (!pageData || !pageData.site_standalone_page || !(pageData.site_standalone_page.length>0)) {
    return null;
  }

  const standalonePageData = pageData.site_standalone_page[0];
  const testimonialsData = pageData.site_testimonials;

  const HeroSection = {
    pill: standalonePageData.pill_content,
    title: standalonePageData.hero_title,
    description: standalonePageData.hero_description,
    image: standalonePageData.hero_image_url,
    breadcrumbPath: [
      { name: "OneAssure", url: `${process.env.NEXT_PUBLIC_BASE_URL}/` },
      {
        name: standalonePageData.hero_title,
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${standalonePageData.slug}/sp/${standalonePageData.id}`,
      },
    ],
    stats: standalonePageData.standalone_hero_cards.map((card) => {
      return {
        id: card.id,
        title: card.description,
        value: card.title,
        suffix: "",
        prefix: "",
        icon: card.icon_url,
      };
    }),
  };
  const BenefitsSection = {
    pill: standalonePageData.standalone_benefits_section?.pill_content,
    title: standalonePageData.standalone_benefits_section?.section_title,
    description: standalonePageData.standalone_benefits_section?.section_description,
    points:
      standalonePageData.standalone_benefits_section?.standalone_benefits_section_points.map(
        (point) => {
          return {
            id: point.id,
            title: point.title,
            description: point.description,
            icon_url: point.icon_url || "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a0773-54bb-7c6e-ad9e-ce07e64f7569/Group 1000001551.svg",
          };
        }
      ),
  };
  const ClaimSettlementSection = {
    pill: standalonePageData.standalone_claim_settlement_section?.pill_content,
    title: standalonePageData.standalone_claim_settlement_section?.section_title,
    description:
      standalonePageData.standalone_claim_settlement_section?.section_description,
    settlements:
      standalonePageData.standalone_claim_settlement_section?.standalone_claim_settlement_types.map(
        (type) => {
          return {
            id: type.id,
            title: type.title,
            types: type.standalone_claim_settlement_steps.map((step) => {
              return {
                id: step.id,
                title: step.title,
                description: step.description,
              };
            }),
          };
        }
      ),
  };
  const DocumentsSection = {
    pill: standalonePageData.standalone_documents_section?.pill_content,
    title: standalonePageData.standalone_documents_section?.section_title,
    description: standalonePageData.standalone_documents_section?.section_description,
    points:
      standalonePageData.standalone_documents_section?.standalone_documents_section_points.map(
        (point) => {
          return {
            id: point.id,
            title: point.title,
            description: point.description,
            icon_url: point.icon_url || "",
          };
        }
      ),
  };
  const FAQSection = {
    pill: standalonePageData.standalone_faq_section?.pill_content,
    title: standalonePageData.standalone_faq_section?.section_title,
    description: standalonePageData.standalone_faq_section?.section_description,
    faqs: standalonePageData.standalone_faq_section?.standalone_faq_section_points.map(
      (faq) => {
        return {
          id: faq.id,
          question: faq.question,
          answer: faq.answer,
        };
      }
    ),
  };

  const inclusionSection= standalonePageData.standalone_inclusion_section?.standalone_inclusion_section_points.find(
    (point) => point.type === "inclusion"
  );
  const exclusionSection= standalonePageData.standalone_inclusion_section?.standalone_inclusion_section_points.find(
    (point) => point.type === "exclusion"
  );
  const InclusionSection = {
    pill: standalonePageData.standalone_inclusion_section?.pill_content,
    title: standalonePageData.standalone_inclusion_section?.section_title,
    description: standalonePageData.standalone_inclusion_section?.section_description,
    inclusions: {
      title: "Inclusions",
      points: inclusionSection?.points || [],
    },
    exclusions: {
      title: "Exclusions",
      points: exclusionSection?.points || [],
    },
  };
  const InsuranceCategorySection = {
    pill: standalonePageData.hero_title || "Insurance Company",
    heading: "Related Health Insurance Plans",
    featuresComparisonData: featuresComparisonData.plans,
  };
  const KeyFactorsSection = {
    pill: standalonePageData.standalone_key_factors_section?.pill_content,
    title: standalonePageData.standalone_key_factors_section?.section_title,
    description: standalonePageData.standalone_key_factors_section?.section_description,
    points:
      standalonePageData.standalone_key_factors_section?.standalone_key_factors_section_points.map(
        (point) => {
          return {
            id: point.id,
            title: point.title,
            description: point.description,
            icon: point.icon_url || "",
          };
        }
      ),
  };
  const PlansSection = {
    pill: standalonePageData.standalone_plans_section?.pill_content,
    title: standalonePageData.standalone_plans_section?.section_title,
    description: standalonePageData.standalone_plans_section?.section_description,
    plans:
      standalonePageData.standalone_plans_section?.standalone_plans_section_plans.map(
        (plan) => {
          return {
            logo_url: plan.health_product_variant.product.insurer.logo_url,
            plan_title: `${plan.health_product_variant.product.insurer.name} ${plan.health_product_variant.variant_name}`,
            redirect_url: `${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/${plan.health_product_variant.product.insurer.temp_slug}/${plan.health_product_variant.temp_slug}`,
          };
        }
      ),
  };
  const RenewalSection = {
    pill: standalonePageData.standalone_renewal_section?.pill_content,
    title: standalonePageData.standalone_renewal_section?.section_title,
    description: standalonePageData.standalone_renewal_section?.section_description,
    renewalSteps:
      standalonePageData.standalone_renewal_section?.standalone_renewal_types.map(
        (type) => {
          return {
            id: type.id,
            title: type.title,
            type: type.type,
            types: type.standalone_renewal_steps.map((step) => {
              return {
                id: step.id,
                title: step.title,
                description: step.description,
              };
            }),
          };
        }
      ),
  };
  const TaxAdvantageSection = {
    pill: standalonePageData.standalone_tax_advantage_section?.pill_content,
    title: standalonePageData.standalone_tax_advantage_section?.section_title,
    description: standalonePageData.standalone_tax_advantage_section?.section_description,
    points:
      standalonePageData.standalone_tax_advantage_section?.standalone_tax_advantage_section_points.map(
        (point) => {
          return {
            id: point.id,
            title: point.title,
            description: point.description,
            icon: point.icon_url || "",
          };
        }
      ),
  };
  const WhyPlansSection = {
    pill: standalonePageData.standalone_why_plans_section?.pill_content,
    title: standalonePageData.standalone_why_plans_section?.section_title,
    description: standalonePageData.standalone_why_plans_section?.section_description,
    points:
      standalonePageData.standalone_why_plans_section?.standalone_why_plans_section_points.map(
        (point) => {
          return {
            id: point.id,
            title: point.title,
            description: point.description,
            icon_url: point.icon_url || "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a0773-54bb-7c6e-ad9e-ce07e64f7569/Group 1000001551.svg",
          };
        }
      ),
  };
  const WhatToLookForSection = {
    pill: standalonePageData.standalone_what_to_look_for_section?.pill_content,
    title: standalonePageData.standalone_what_to_look_for_section?.section_title,
    description:
      standalonePageData.standalone_what_to_look_for_section?.section_description,
    points:
      standalonePageData.standalone_what_to_look_for_section?.standalone_what_to_look_for_section_points.map(
        (point) => {
          return {
            id: point.id,
            title: point.title,
            description: point.description,
            icon: point.icon_url || "",
          };
        }
      ),
  };
  const TestimonialSection = {
    pill: "Customer Testimonials",
    title: "Customer Testimonials",
    description: "Real experiences from our valued customers who have chosen our insurance coverage.",
    points:
      testimonialsData.map(
        (point) => {
          return {
            id: point.id,
            name: point.name,
            content: point.content,
          };
        }
      ),
  };

  const whatWeLike = standalonePageData.standalone_verdict_section?.standalone_verdict_section_pros_cons.find(
    (point) => point.type === "pro"
  );
  const AreasOfImprovement = standalonePageData.standalone_verdict_section?.standalone_verdict_section_pros_cons.find(
    (point) => point.type === "con"
  );
  const VerdictSection = {
    pill: standalonePageData.standalone_verdict_section?.pill_content,
    title: standalonePageData.standalone_verdict_section?.section_title,
    description: standalonePageData.standalone_verdict_section?.section_description,
    verdict: standalonePageData.standalone_verdict_section?.verdict,
    whatWeLike: {
      heading: whatWeLike?.title || "Pros",
      points: whatWeLike?.points || [],
    },
    AreasOfImprovement: {
      heading: AreasOfImprovement?.title || "Cons",
      points: AreasOfImprovement?.points || [],
    },
  };

  const PageNavigationSection = {
    activeTab: "expert-review",
    tabs: [
      { label: "Verdict", id: "expert-review" },
      { label: "Testimonials", id: "testimonials" },
      { label: "What's Included", id: "inclusions-and-exclusions" },
      { label: "Key Factors", id: "key-factors" },
      { label: "Claim Settlement Process", id: "claim-settlement" },
      { label: "Renewal Process", id: "renewal-process" },
      { label: "Insurance Category", id: "insurance-category" },
      { label: "FAQs", id: "faqs" },
    ],
  };

  return {
    id: standalonePageData.id,
    slug: standalonePageData.slug,
    heroSection: HeroSection,
    benefitsSection: BenefitsSection,
    claimSettlementSection: ClaimSettlementSection,
    documentsSection: DocumentsSection,
    faqSection: FAQSection,
    inclusionSection: InclusionSection,
    insuranceCategorySection: InsuranceCategorySection,
    keyFactorsSection: KeyFactorsSection,
    plansSection: PlansSection,
    renewalSection: RenewalSection,
    taxAdvantageSection: TaxAdvantageSection,
    whyPlansSection: WhyPlansSection,
    whatToLookForSection: WhatToLookForSection,
    testimonialSection: TestimonialSection,
    verdictSection: VerdictSection,
    pageNavigationSection: PageNavigationSection,
  };
};

export type StandalonePageData = ReturnType<typeof transformStandalonePageData>;

export default transformStandalonePageData;
