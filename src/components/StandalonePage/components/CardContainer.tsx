import SectionHeader from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
import SimpleCard from "@/components/globals/DSComponentsV0/SimpleCard";
import SectionContainer from "@/components/globals/SectionContainer";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import DesktopCarousel from "@/components/UI/DesktopCarousal";
import { Grid } from "@/components/UI/Grid";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import { HtmlParserOptions } from "@/utils/htmlParser";

type card_content = {
  icon_url?: string;
  title: string;
  description?: string;
};

const CardContainer = ({
  heading,
  subheading,
  pill,
  card_content,
  grid_cols,
  id,
  parserProps,
}: {
  heading: string;
  subheading?: string;
  pill?: string;
  card_content: card_content[];
  grid_cols?: 1 | 2 | 3 | 4 | 5 | 6;
  id?: string;
  parserProps?: HtmlParserOptions;
}) => {
  const cols = grid_cols ? grid_cols : 3;

  return (
    <SectionContainer className="!px-0" id={id}>
      <SectionHeader
        pill={pill}
        heading={heading}
        subheading={subheading}
        component="h2"
      />

      {/* <Grid cols={cols} gap={4} className="hidden md:grid">
        {card_content.map((card, index) => (
          <SimpleCard
            key={index}
            title={card.title}
            description={card.description}
						icon={card.icon_url}
            parserProps={parserProps}
          />
        ))}
      </Grid> */}

      <DesktopCarousel
        className="hidden md:block"
        totalSlides={card_content.length}
        itemsPerPage={grid_cols || 4}
      >
        {card_content.map((card, index) => (
          <SimpleCard
            key={index}
            title={card.title}
            description={card.description}
            icon={card.icon_url}
            parserProps={parserProps}
          />
        ))}
      </DesktopCarousel>

			<MobileCarousel totalSlides={card_content.length} className="md:hidden">
        {card_content.map((card, index) => (
          <MobileCarouselItem key={index}>
            <SimpleCard
              key={index}
              title={card.title}
              description={card.description}
              icon={card.icon_url}
              parserProps={parserProps}
            />
          </MobileCarouselItem>
        ))}
      </MobileCarousel>
    </SectionContainer>
  );
};

export default CardContainer;
