import PointCard from "@/components/globals/DSComponentsV0/PointCard";
import PointCardWithTitle from "@/components/globals/DSComponentsV0/PointCardWithTitle";
import SectionHeader from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerSmall from "@/components/globals/SectionContainerSmall";
import { Button } from "@/components/UI/Button";
import { Grid } from "@/components/UI/Grid";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import { useState } from "react";
import {
  IoMdCheckmarkCircleOutline,
  IoMdCloseCircleOutline,
} from "react-icons/io";

const InclusionExclusionSection = ({
  heading,
  subheading,
  pill,
  inclusions,
  exclusions,
  id,
}: {
  heading: string;
  subheading?: string;
  pill?: string;
  inclusions: {
    title: string;
    points: string[];
  };
  exclusions: {
    title: string;
    points: string[];
  };
  id?: string;
}) => {
  // show 7 inclusions and exclusions on mobile
  // when clicked on read more, show all inclusions and exclusions
  const [showAll, setShowAll] = useState(false);
  const [inclusionPoints, setInclusionPoints] = useState(inclusions.points.slice(0, 7));
  const [exclusionPoints, setExclusionPoints] = useState(exclusions.points.slice(0, 7));

  const handleReadMore = () => {
    
    setShowAll(!showAll);
    
    if (showAll) {
      setInclusionPoints(inclusions.points.slice(0, 7));
      setExclusionPoints(exclusions.points.slice(0, 7));
    } else {
      setInclusionPoints(inclusions.points);
      setExclusionPoints(exclusions.points);
    }
  };


  return (
    <SectionContainerLarge className="!px-0" id={id}>
      <SectionHeader
        pill={pill}
        heading={heading}
        subheading={subheading}
        component="h2"
      />

      <SectionContainerSmall className="!px-0">
        <Grid cols={2} className="hidden md:grid" gapX={8}>
          <PointCard
            title={inclusions.title}
            points={inclusions.points}
            pointIcon={
              <IoMdCheckmarkCircleOutline
                className="text-secondary-400 flex-shrink-0 mt-1"
                size={16}
              />
            }
          />
          <PointCard
            title={exclusions.title}
            points={exclusions.points}
            pointIcon={
              <IoMdCloseCircleOutline
                className="text-red-400 flex-shrink-0 mt-1"
                size={16}
              />
            }
          />
        </Grid>

				<MobileCarousel totalSlides={2} className="md:hidden">
          <MobileCarouselItem>
            <PointCard
            title={inclusions.title}
            points={inclusionPoints}
            pointIcon={
              <IoMdCheckmarkCircleOutline
                className="text-secondary-400 flex-shrink-0 mt-1"
                size={16}
              />
            }
          />
					</MobileCarouselItem>
          <MobileCarouselItem>
						<PointCard
            title={exclusions.title}
            points={exclusionPoints}
            pointIcon={
              <IoMdCloseCircleOutline
                className="text-red-400 flex-shrink-0 mt-1"
                size={16}
              />
            }
          />
					</MobileCarouselItem>
        </MobileCarousel>
        
        <div className="flex justify-center mt-4 md:hidden">
          <Button variant="primary" onClick={handleReadMore}>{showAll ? "Show Less" : "See More"}</Button>
        </div>
      </SectionContainerSmall>
    </SectionContainerLarge>
  );
};

export default InclusionExclusionSection;
