// import { AxiosResponse } from "axios";
// import { getRequest } from '@/scripts/requests';
import { useMutation, useQuery } from "@tanstack/react-query";
import { ExtractFnReturnType, MutationConfig } from "@/lib/react-query";
import { useSessionStorage } from "usehooks-ts";
export const createLeadAPI = async ({
  lead_name,
  lead_phone_number,
  ...restObj
}: Lead) => {
  const url = `${process.env.NEXT_PUBLIC_BROKER_URL}/leads/v1/`;

  let body = {
    lead_name,
    lead_phone_number,
    ...restObj,
  };

  const response = await fetch(url, {
    method: "POST", // Specify the HTTP method (default is GET)
    headers: {
      "Content-Type": "application/json", // Set content type for JSON data
    },
    body: JSON.stringify(body),
  });

  // Check for successful response
  if (!response.ok) {
    throw new Error(`API request failed with status: ${response.status}`);
  }

  // Parse the JSON response
  const data = await response.json();

  return data;
};

type Lead = {
  lead_name: string;
  lead_phone_number: string | number;
  lead_email?: string;
  lead_message?: string;
  lead_contact_type?: string | null;
  source?: string;
  utm_source?: string | null;
  utm_medium?: string | null;
  utm_campaign?: string | null;
  utm_content?: string | null;
  utm_term?: string | null;
  lead_point?: string | null;
};

export const useCreateLead = (
  config?: MutationConfig<typeof createLeadAPI>
) => {
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);

  return useMutation({
    mutationFn: (body: Lead) => {
      const { lead_name, lead_phone_number, lead_contact_type, ...restObj } = body;
      return createLeadAPI({
        lead_name,
        lead_phone_number,
        utm_source,
        utm_medium,
        utm_campaign,
        utm_content,
        utm_term,
        lead_contact_type,
        ...restObj,
      });
    },
  });
};
