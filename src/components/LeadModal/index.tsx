"use client";
import { But<PERSON>, Label, Modal, TextInput } from "flowbite-react";
import React, { FormEvent, useState } from "react";
import { useCreateLead } from "./api/createLead";
import { string } from "yup";
import { usePathname } from "next/navigation";

function LeadModal({
  open,
  handleModal,
}: {
  open: boolean;
  handleModal: (value: boolean) => void;
}) {
  const { mutateAsync, isPending, isSuccess } = useCreateLead();
  const [formData, setFormData] = useState<Record<string, string>>({});
  const pathname = usePathname();
  const handleInputChange = (key: string, value: string) => {
    setFormData((prev) => {
      return { ...prev, [key]: value };
    });
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (
      !formData.hasOwnProperty("lead_name") ||
      !formData.hasOwnProperty("lead_phone_number")
    ) {
      throw new Error("Missing required form data");
    }
    const { lead_name, lead_phone_number, ...restObj } = formData;

    const res = await mutateAsync({
      lead_name: formData!.lead_name,
      lead_phone_number: formData!.lead_phone_number,
      lead_point: pathname,
      ...restObj,
    });

    if (res.success) {
      handleModal(false);
    }
  };

  return (
    <Modal show={open} onClose={() => {}} position={"center"}>
      {/* <Modal.Header>Leads Form</Modal.Header> */}
      <Modal.Body>
        <form
          className="flex max-w-md flex-col gap-4 mx-auto"
          onSubmit={handleSubmit}
        >
          <div>
            <div className="mb-2 block">
              <Label htmlFor="full_name" value="Your Full Name" />
            </div>
            <TextInput
              id="full_name"
              type="text"
              placeholder="John Doe"
              onChange={(e) => handleInputChange("lead_name", e.target.value)}
              required
            />
          </div>
          <div>
            <div className="mb-2 block">
              <Label htmlFor="phone_number" value="Your Phone Number" />
            </div>
            <TextInput
              id="phone_number"
              placeholder="9123456789"
              type="text"
              onChange={(e) =>
                handleInputChange("lead_phone_number", e.target.value)
              }
              required
            />
          </div>
          <div>
            <div className="mb-2 block">
              <Label htmlFor="email" value="Your Email Address" />
            </div>
            <TextInput
              id="email"
              placeholder="<EMAIL>"
              type="email"
              onChange={(e) => handleInputChange("lead_email", e.target.value)}
            />
          </div>

          <Button className="bg-blue600 text-white" type="submit">
            Submit
          </Button>
        </form>
      </Modal.Body>
    </Modal>
  );
}

export default LeadModal;
