import { Logo } from "../globals/types";

type Attributes = {
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  hero: {
    id: number;
    title: string;
    description: string;
    insuranceType: {
      id: number;
      title: string;
      url: string;
      logo: {
        data: {
          id: number;
          attributes: {
            url: string;
          };
        };
      };
    }[];
  };
  policyVerticals: {
    id: number;
    insuranceTypeCard: {
      id: number;
      title: string;
      description: string;
      url: string;
      logo: {
        data: {
          id: number;
          attributes: {
            url: string;
          };
        };
      };
    }[];
  };
  ourOffering: {
    id: number;
    title: string;
    description: string;
    offers: {
      id: number;
      title: string;
      description: string;
      logo: {
        data: {
          id: number;
          attributes: {
            url: string;
          };
        };
      };
    }[];
  };
  talkToExpert: {
    id: number;
    title: string;
    subTitle: string;
  };
  faq: {
    id: number;
    title: string;
    subTitle: string;
    faqs: {
      id: number;
      question: string;
      ans: string;
    }[];
  };
  testimonials: {
    id: number;
    testimonial: {
      id: number;
      name: string;
      statement: string;
      backgroundColor: string;
      thumbnail: {
        data: {
          id: number;
          attributes: {
            url: string;
          };
        };
      };
    }[];
  };
  whyOneAssure: {
    id: number;
    title: string;
    subTitle: string;
    points: {
      id: number;
      number: string;
      title: string;
      subTitle: string;
    }[];
  };
  oneAssureInNews: {
    id: number;
    newsCard: {
      id: number;
      url: string;
      logo: {
        data: {
          id: number;
          attributes: {
            url: string;
          };
        };
      };
    }[];
  };
  products: {
    id: number;
    health_variants: {
      data: {
        id: number;
        attributes: {
          name: string;
          verdict: string | null;
          slug: string;
          company: {
            data: {
              id: number;
              attributes: {
                name: string;
                slug: string;
                category: string;
                logo: {
                  data: {
                    id: number;
                    attributes: {
                      url: string;
                    };
                  };
                };
              };
            };
          };
        };
      }[];
    };
    term_variants: {
      data: {
        id: number;
        attributes: {
          name: string;
          verdict: string | null;
          slug: string;
          company: {
            data: {
              id: number;
              attributes: {
                name: string;
                slug: string;
                category: string;
                logo: {
                  data: {
                    id: number;
                    attributes: {
                      url: string;
                    };
                  };
                };
              };
            };
          };
        };
      }[];
    };
  };
};

export type HomeDataObject = {
  id: number;
  attributes: Attributes;
  meta: Record<string, unknown>;
};

export type PartnerCompany = {
  attributes: {
    name: string;
    slug: string;
    category: string;
    logo: Logo;
    health_variants?: {
      data: {
        id: number;
        attributes: {
          name: string;
          slug: string;
        };
      }[];
    };
    term_variants?: {
      data: {
        id: number;
        attributes: {
          name: string;
          slug: string;
        };
      }[];
    };
  };
}[];
