"use client";
import Container from "@/components/globals/Container";
import Hero from "@/components/Home/components/Hero";
import TopProducts from "@/components/Home/components/Top_Products";
import PolicyVerticals from "@/components/Home/components/PolicyVerticals";
import TalkToExpert from "@/components/globals/TalkToOAExpert";
import PartnerCompanies from "@/components/Home/components/PartnerCompanies";
import News from "@/components/Home/components/News";
import Testimonials from "@/components/globals/Testimonials";
import ContactForm from "@/components/globals/ContactForm";
import Faq from "@/components/globals/Faq";
import { Suspense } from "react";
import { HomeDataObject, PartnerCompany } from "@/components/Home/types";
import OfferingBenefits from "@/components/globals/OfferingBenefits";
import WhyOneAssure from "@/components/Home/components/WhyOneAssure";
import FloatingContactButton from "@/components/Home/components/FloatingContactForm";

const Home = ({
  data,
  partners,
}: {
  data: HomeDataObject;
  partners: PartnerCompany;
}) => {
  return (
    <>
      <Container className="font-manrope">
        <Suspense>
          <Hero hero={data.attributes.hero} partners={partners} />
        </Suspense>

        <Suspense>
          <PartnerCompanies partners={partners} />
        </Suspense>

        <TopProducts
          variants={data.attributes.products.health_variants.data}
          title="Top Selling Health Insurance Products"
        />
        <TopProducts
          variants={data.attributes.products.term_variants.data}
          title="Top Selling Term Insurance Products"
        />

        <Suspense>
          <PolicyVerticals policyData={data.attributes.policyVerticals} />
        </Suspense>

        <WhyOneAssure whyOneAssure={data.attributes.whyOneAssure} />
        <OfferingBenefits
          title={data.attributes.ourOffering.title}
          description={data.attributes.ourOffering.description}
          offers={data.attributes.ourOffering.offers}
          className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4 justify-items-center md:px-10 px-7 pt-6 md:pb-10 pb-4 md:pt-0  md:mt-6 "
        />

        <TalkToExpert />

        <Faq faq={data.attributes.faq} />
        <News oneAssureInNews={data.attributes.oneAssureInNews} />
        <Testimonials testimonials={data.attributes.testimonials.testimonial} />
        <Suspense>
          <ContactForm />
        </Suspense>
        <FloatingContactButton />
      </Container>
    </>
  );
};

export default Home;
