import "@/app/globals.css";
import React from "react";
import Image from "next/image";
import { GoArrowRight } from "react-icons/go";
import ProductDropdown from "@/components/globals/InsuranceAllProducts/ProductDropdown";
import { PartnerCompany } from "../types";
import CustomLink from "@/components/globals/CustomLink";
import { useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { useSessionStorage } from "usehooks-ts";
interface InsuranceType {
  id: number;
  title: string;
  url: string;
  logo: {
    data: {
      id: number;
      attributes: {
        url: string;
      };
    };
  };
}

interface HeroProps {
  hero: {
    id: number;
    title: string;
    description: string;
    insuranceType: InsuranceType[];
  };
  partners: PartnerCompany;
}

const Hero: React.FC<HeroProps> = ({ hero, partners }) => {
  const searchParams = useSearchParams();

  const [utm_source, setUtmSource] = useSessionStorage("utm_source", "");
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", "");
  const [utm_campaign, setUtmCampaign] = useSessionStorage("utm_campaign", "");
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", "");
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", "");

  // Update session storage when UTM parameters are present in URL
  useEffect(() => {
    const urlUtmSource = searchParams.get("utm_source");
    const urlUtmMedium = searchParams.get("utm_medium");
    const urlUtmCampaign = searchParams.get("utm_campaign");
    const urlUtmContent = searchParams.get("utm_content");
    const urlUtmTerm = searchParams.get("utm_term");

    if (urlUtmSource) setUtmSource(urlUtmSource);
    if (urlUtmMedium) setUtmMedium(urlUtmMedium);
    if (urlUtmCampaign) setUtmCampaign(urlUtmCampaign);
    if (urlUtmContent) setUtmContent(urlUtmContent);
    if (urlUtmTerm) setUtmTerm(urlUtmTerm);
  }, [
    searchParams,
    setUtmSource,
    setUtmMedium,
    setUtmCampaign,
    setUtmContent,
    setUtmTerm,
  ]);
  const titleWords = hero.title.split(" ");
  const firstHalf = titleWords
    .slice(0, Math.ceil(titleWords.length / 2))
    .join(" ");
  const secondHalf = titleWords
    .slice(Math.ceil(titleWords.length / 2))
    .join(" ");

  // Split the description into words
  const descriptionWords = hero.description.split(" ");

  return (
    <div className="bg-ntrl-white mt-1 md:mt-10 mb-12">
      <div className="grid grid-cols-1 md:grid-cols-2">
        {/* Left Side */}
        <div className="md:text-[60px]/[70px] text-2xl text-ntrl-black-1 pt-6 md:pt-0 px-5 ">
          <h1 className="flex flex-col">
            <span className="font-bold text">{firstHalf}</span>

            <span className=" flex flex-col font-bold bg-gradient-to-r from-primary-green-2 to-primary-blue-3  bg-clip-text text-transparent md:w-2/3 w-full">
              {secondHalf}
            </span>
          </h1>
          <div className="md:pt-8 pt-4">
            <p className="md:text-lg md:w-2/3  font-medium w-full text-base">
              {descriptionWords.map((word: string, index: number) => (
                <span
                  key={index}
                  className={
                    index >= 1 && index <= 3
                      ? "bg-gradient-to-r from-primary-green-2 to-primary-blue-3 bg-clip-text text-transparent"
                      : ""
                  }
                >
                  {word}
                  {index < descriptionWords.length - 1 && " "}
                </span>
              ))}
            </p>
          </div>
        </div>

        <div className="md:mx-20  md:w-[85%] mt-5 md:mt-0">
          <div className="grid grid-cols-2  md:gap-10 gap-4 justify-between">
            {/* Container for the looped divs */}
            {hero.insuranceType.map((h: InsuranceType, index: number) => (
              <div key={index} className="">
                <div
                  className={`relative ${
                    index % 2 === 0 ? "bg-primary-green-2" : "bg-primary-blue-3"
                  } rounded-br-10xl border rounded-xl shadow-customblue4d opacity-80% justify-start p-5 md:p-7`}
                >
                  <p className="text-white text-xl md:text-3xl justify-start px-auto font-bold">
                    {h.title}
                  </p>
                  <div className="mt-4">
                    <Image
                      src={h.logo.data.attributes.url}
                      alt="logo"
                      height={72}
                      width={79}
                      className="md:h-20 md:w-20 h-14 w-14"
                    />
                  </div>
                  <div
                    className={`size-16 rounded-full bg-white absolute right-0 bottom-0 shadow-customblue4d flex justify-center items-center`}
                  >
                    <CustomLink
                      href={h.url}
                      utm={`utm_source=${utm_source}&utm_medium=${utm_medium}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&utm_term=${utm_term}`}
                    >
                      <GoArrowRight
                        className={`${
                          index % 2 === 0
                            ? "text-primary-green-2"
                            : "text-primary-blue-3"
                        } size-10 cursor-pointer bg-none hover:scale-110 transition-all duration-300`}
                      />
                    </CustomLink>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <ProductDropdown companyData={partners} />
        </div>
      </div>
    </div>
  );
};

export default Hero;
