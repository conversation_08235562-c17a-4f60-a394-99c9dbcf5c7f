"use client";
import { Dialog } from "@headlessui/react";
import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import Image from "next/image";
import CustomLink from "@/components/globals/CustomLink";

const GetQuoteModal = ({
  open,
  handleModal,
}: {
  open: boolean;
  handleModal: () => void;
}) => {
  const search = useSearchParams();
  const utm = search.get("utm_source");

  return (
    <Dialog
      open={open}
      onClose={handleModal}
      className="fixed inset-0 z-[100] overflow-y-auto flex items-center justify-center"
    >
      <div className="flex items-end justify-center min-h-screen pt-4 md:px-4 pb-20 text-center sm:block sm:p-0">
        <Dialog.Overlay className="fixed inset-0 transition-opacity bg-gray-500 opacity-75" />

        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>

        <Dialog.Panel
          className={`inline-block align-bottom bg-secondary-2 rounded-3xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:w-full  md:max-w-md relative p-6`}
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-headline"
        >
          <div className="grid grid-cols-2 gap-6">
            {/* Health Insurance */}
            <CustomLink href={"/health-insurance"} utm={utm}>
              <div className="px-6 py-[18px] border border-secondary-1 rounded-2xl cursor-pointer h-full">
                <Image
                  src={
                    "https://cdn.oasr.in/oa-site/cms-uploads/media/cardiology_f1ee877fc8.png"
                  }
                  width={32}
                  height={32}
                  style={{ objectFit: "cover" }}
                  alt="health"
                  className="mb-2"
                />
                <div className="font-generalSans font-medium text-[18px]/[24px]">
                  <h2>Health</h2>
                  <h2>Insurance</h2>
                </div>
              </div>
            </CustomLink>

            {/* term Insurance */}
            <CustomLink href={"/term-insurance"} utm={utm}>
              <div className="px-6 py-[18px] border border-secondary-1 rounded-2xl cursor-pointer h-full">
                <Image
                  src={
                    "https://cdn.oasr.in/oa-site/cms-uploads/media/account_child_invert_1_fa77f49ccd.png"
                  }
                  width={24}
                  height={24}
                  style={{ objectFit: "cover" }}
                  alt="term"
                  className="mb-2"
                />
                <div className="font-generalSans font-medium text-[18px]/[24px]">
                  <h2>Term</h2>
                  <h2>Insurance</h2>
                </div>
              </div>
            </CustomLink>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default GetQuoteModal;
