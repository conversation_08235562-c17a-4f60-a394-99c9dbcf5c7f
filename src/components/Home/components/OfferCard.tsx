"use client";

import Image from "next/image";
import parse, { attributesToProps } from "html-react-parser";
import { Element } from "html-react-parser";
import Link from "next/link";
import { ArrowUpRightIcon } from "@heroicons/react/24/solid";
import { Dispatch, SetStateAction } from "react";
import BookACallBtn from "@/components/globals/BookACall";
import BookACallMobileBtn from "@/components/globals/BookACallMobile";

const OfferCard = ({
  color,
  title,
  description,
  getQuoteUrl,
  thumbnail,
  setOpenModal,
}: {
  color: string;
  title: string;
  description: string;
  getQuoteUrl: string | null;
  thumbnail: string;
  setOpenModal: Dispatch<SetStateAction<boolean>>;
}) => {
  const lastBlankIndex = title.lastIndexOf(" ");
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "p":
          attrs.className =
            "text-ntrl-white text-[14px]/[22px] md:text-[16px]/[24px] font-normal mb-6";
          break;
      }
      return domNode;
    }
  };

  return (
    <div
      className={`rounded-xl bg-${color} grid grid-cols-1 md:grid-cols-2 items-center min-w-[335px] md:min-w-[816px] p-4 md:p-2 snap-start`}
    >
      <div className="md:py-12 md:pl-12 md:pr-9">
        {/* Heading */}
        <div className="mb-6 flex md:flex-col items-center md:items-start gap-2">
          <h2 className="text-ntrl-white font-generalSans text-[20px]/[28px] md:text-[40px]/[40px] font-medium">
            {title.substring(0, lastBlankIndex)}
          </h2>
          <h2 className="text-ntrl-white font-generalSans text-[20px]/[28px] md:text-[40px]/[40px] font-medium">
            {title.substring(lastBlankIndex + 1)}
          </h2>
        </div>

        {/* Description */}
        <div className="text-ntrl-white text-[14px]/[22px] md:text-[16px]/[24px] font-normal">
          {/* @ts-ignore */}
          {parse(description, { replace })}
        </div>

        {getQuoteUrl !== null ? (
          // Get Quote Btn
          <div>
            <Link href={getQuoteUrl}>
              <button
                className={`py-3 px-8 bg-ntrl-white rounded-full font-generalSans font-semibold text-[16px]/[24px] text-${color} hidden md:block`}
                id="get-quote"
              >
                Get a Quote
              </button>
            </Link>
          </div>
        ) : (
          // Book A Call Btn
          <div>
            {/* <button
              className={`py-3 px-8 bg-ntrl-white rounded-full font-generalSans font-semibold text-[16px]/[24px] text-${color} hidden md:block`}
              onClick={() => setOpenModal(true)}
            >
              Book a Call
            </button> */}
            <BookACallBtn className="py-3 px-6 md:px-8 bg-ntrl-white rounded-full font-generalSans font-semibold text-[16px]/[24px] text-${color} hidden md:block" />
          </div>
        )}
      </div>

      {/* Image here */}
      <div className="rounded-xl h-full bg-white min-h-[262px] relative">
        <Image
          src={thumbnail}
          fill={true}
          style={{ objectFit: "cover" }}
          alt={title}
          className="rounded-xl"
        />

        {getQuoteUrl !== null ? (
          // Get Quote Btn

          <div className="absolute bottom-5 right-5">
            <Link href={getQuoteUrl}>
              <button
                className={`p-3 bg-ntrl-white rounded-full font-generalSans font-semibold text-[16px]/[24px] text-${color} md:hidden`}
              >
                <ArrowUpRightIcon
                  className={`w-6 h-6 text-${color} font-semibold`}
                />
              </button>
            </Link>
          </div>
        ) : (
          // Book A Call Btn
          <div className="absolute bottom-5 right-5">
            <BookACallMobileBtn bgColor="bg-ntrl-white rounded-full font-generalSans font-semibold text-[16px]/[24px] text-${color} md:hidden" />
          </div>
        )}
      </div>
    </div>
  );
};

export default OfferCard;
