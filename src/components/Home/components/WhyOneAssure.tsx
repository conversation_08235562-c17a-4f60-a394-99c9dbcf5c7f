import React from "react";
import { HomeDataObject } from "../types";

interface WhyOneAssureProps {
  whyOneAssure: HomeDataObject["attributes"]["whyOneAssure"];
}

// Create the functional component
const WhyOneAssure: React.FC<WhyOneAssureProps> = ({ whyOneAssure }) => {
  return (
    <div className="md:mb-14 mb-5 md:px-10 px-5">
      {/* Heading */}
      <div className="text-center">
        <h2 className="md:text-2xl text-lg font-medium">
          {whyOneAssure.title}
        </h2>
        <p className="text-ntrl-black-1 font-light text-sm ">
          {whyOneAssure.subTitle}
        </p>
      </div>

      {/* Features List */}
      <div className="grid grid-cols-2 md:grid-cols-5 md:mt-9 mt-5 md:gap-x-14 gap-x-4 gap-y-4">
        {whyOneAssure.points.map((feature, index) => (
          <div
            key={feature.number}
            className={`flex flex-col justify-start ${
              index === whyOneAssure.points.length - 1
                ? "col-span-2 w-1/2 md:mx-0 md:col-span-1 md:w-full"
                : ""
            }`}
          >
            {/* Number inside a Circle */}
            <div className="w-[50px] h-[50px] flex items-center justify-center rounded-full border text-white bg-green1100 font-bold text-[20px]/[24px]">
              {feature.number}
            </div>

            {/* Title & Description */}

            {/* Title */}
            <h3 className="items-center text-ntrl-black-1 text-[14px]/[20px] md:my-4 my-2">
              {feature.title}
            </h3>
            {/* Description */}
            <p className=" text-ntrl-black-1 font-light text-sm ">
              {feature.subTitle}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WhyOneAssure;
