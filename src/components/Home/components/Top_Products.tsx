import React, { useState, useRef } from "react";
import Image from "next/image";
// import circles2 from "@/assets/circles2.png";
import PartnerCard from "./PartnerCard";
import { HomeDataObject } from "../types";

interface TopProductsProps {
  variants: HomeDataObject["attributes"]["products"]["health_variants"]["data"];
  className?: string;
  title?: string;
}

const TopProducts: React.FC<TopProductsProps> = ({
  variants,
  className,
  title,
}) => {
  const [activeSlide, setActiveSlide] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Extract all variants from health_variants and term_variants
  // const healthVariants = products?.health_variants?.data || [];
  // const termVariants = products?.term_variants?.data || [];
  // const allVariants = [...healthVariants, ...termVariants];

  // const itemsPerRow = 5;
  // const totalRows = Math.ceil(allVariants.length / itemsPerRow);
  // const visibleProducts = allVariants.slice(0, visibleRows * itemsPerRow);

  // const handleSeeMore = () => {
  //   setVisibleRows((prevRows) => Math.min(prevRows + 1, totalRows));
  // };

  // const handleSeeLess = () => {
  //   setVisibleRows(1);
  // };

  // Function to extract plain text from HTML (for description)
  const getPlainTextFromHTML = (html: string | null): string => {
    if (!html) return "";
    // Simple regex to remove HTML tags
    return html.replace(/<[^>]*>?/gm, "");
  };

  return (
    <div className="relative text-center md:mb-14 mb-5 md:w-full flex flex-col bg-gradient-to-r from-primary-blue-3 to-primary-green-2 md:rounded-5xl text-transparent opacity-80 overflow-hidden">
      <div className="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={570}
          height={570}
          className="hidden md:block"
        />
      </div>

      <div className="absolute bottom-24 left-0 transform -translate-x-1/2 translate-y-1/2">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={524}
          height={524}
        />
      </div>

      <h2 className="md:text-[30px]/[36px] text-[18px]/[22px] text-white md:font-semibold font-medium z-10 px-4 pt-6">
        {title}
      </h2>

      <div
        ref={scrollContainerRef}
        className={`md:grid md:grid-cols-5 flex md:gap-5 gap-4 md:px-8 py-4 z-10 px-4 overflow-x-scroll md:overflow-x-hidden scrollbar-hide mx-4 items-stretch`}
        onScroll={(e) => {
          const container = e.currentTarget;
          const scrollPosition = container.scrollLeft;
          const maxScroll = container.scrollWidth - container.clientWidth;

          // Calculate what percentage of the total scroll we've moved
          const scrollPercentage = scrollPosition / maxScroll;

          // Calculate which slide should be active based on the scroll percentage
          const newActiveSlide = Math.min(
            Math.round(scrollPercentage * (variants.length - 1)),
            variants.length - 1
          );

          setActiveSlide(newActiveSlide);
        }}
      >
        {variants.map((variant) => (
          <div
            key={variant.id}
            className="min-w-[50%] md:min-w-none snap-start"
          >
            <PartnerCard
              logoUrl={
                variant.attributes.company.data.attributes.logo.data.attributes
                  .url
              }
              name={variant.attributes.name}
              description={getPlainTextFromHTML(variant.attributes.verdict)}
              knowMore={`/${variant.attributes.company.data.attributes.category}/${variant.attributes.company.data.attributes.slug}/${variant.attributes.slug}`}
            />
          </div>
        ))}
      </div>

      {/* Dot indicators */}
      <div className="flex md:hidden justify-center gap-2 mt-1 mb-4">
        {variants.map((_, index) => (
          <div
            key={index}
            className={`w-2 h-2 rounded-full transition-colors duration-300 ${
              index === activeSlide ? "bg-white" : "bg-black/50"
            }`}
          />
        ))}
      </div>
    </div>
  );
};

export default TopProducts;
