import Container from "@/components/globals/Container";
import WhatYouGetOption from "./WhatYouGetOption";

const WhatYouGet = ({
  benefitsOffered,
}: {
  benefitsOffered: {
    id: number;
    benefit: {
      id: number;
      title: string;
      subtitle: string;
      icon: {
        data: {
          id: number;
          attributes: {
            url: string;
          };
        };
      };
    }[];
  };
}) => {
  return (
    <div className="md:py-20 py-10 bg-primary-1">
      <Container>
        <div className="mb-12">
          {/* Heading */}
          <div
            className="flex items-center justify-center font-generalSans 
         text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-white gap-2 mb-4 "
          >
            <h2 className="font-normal">What You Are</h2>
            <h2 className="font-medium">Going To Get?</h2>
          </div>

          <p className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-white font-normal">
            Make wise insurance decisions with OneAs<PERSON>. Receive 100% unbiased
            recommendations
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-[30px] items-start">
          {benefitsOffered.benefit.map((b, idx) => (
            <WhatYouGetOption
              key={idx}
              title={b.title}
              subtitle={b.subtitle}
              icon={b.icon.data.attributes.url}
            />
          ))}

          {/* <WhatYouGetOption />
          <WhatYouGetOption />
          <WhatYouGetOption /> */}
        </div>
      </Container>
    </div>
  );
};

export default WhatYouGet;
