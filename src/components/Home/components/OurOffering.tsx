"use client";

import React from "react";
import customer from "@/assets/customer.png";
import Image from "next/image";
import circles2 from "@/assets/circles2.png";
import { custom } from "zod";
const offeringData = [
  {
    title: "Support 24/7",
    description: "Claim Assistance, Anytime, Anywhere.",
  },
  {
    title: "Easy Purchase",
    description: "Simplifying Insurance, One Recommendation at a Time.",
  },
  {
    title: "Quick Claim",
    description: "Streamlined Claims, Hassle-Free Solutions.",
  },
  {
    title: "Easy Upgrade",
    description: "Get Recommendations on Renewals too.",
  },
];

const New_OurOffering = () => {
  return (
    <div className=" relative mt-20 bg-gradient-to-r from-blue1000 to-blue900 bg-200% animate-gradient rounded-3xl text-center h-[392px] w-full shadow-whyOneassure overflow-hidden">
      <div className="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2">
        <Image
          src={circles2}
          alt=""
          width={570} // Increased width
          height={570} // Increased height
          objectFit="cover"
        />
      </div>

      <div className="absolute bottom-36 left-12 transform -translate-x-1/2 translate-y-1/2">
        <Image
          src={circles2}
          alt=""
          width={570}
          height={570}
          objectFit="cover"
        />
      </div>

      <p className="pt-6 md:text-[36px]/[46px] text-white font-helvetica font-medium">
        What OneAssure Offers
      </p>
      <p className="md:text-[14px]/[20px] text-white font-helvetica font-light">
        Make wise insurance decisions with OneAssure. Receive 100% unbiased
        recommendations
      </p>

      <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-4 gap-4 h-[384px] justify-items-center">
        {offeringData.map((offer, index) => (
          <div
            key={index}
            className="rounded-2xl h-[240px] w-[208px] relative mt-9"
          >
            <div className="relative inset-0 rounded-2xl">
              <div className="absolute w-[115px] h-[115px] z-10 rounded-full bg-white shadow-md left-1/2 top-[30%] transform -translate-x-1/2 flex justify-center items-center">
                <Image
                  src={customer}
                  alt="image"
                  className="h-[55px] w-[55px]"
                />
              </div>
            </div>

            <div className="absolute bottom-0 left-0 right-0 text-center text-white font-helvetica">
              <div className="mb-2">
                <p className="text-[24px]/[30px] font-medium">{offer.title}</p>
              </div>
              <div>
                <p className="text-[14px]/[20px] mb-6 font-light">
                  {offer.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default New_OurOffering;
