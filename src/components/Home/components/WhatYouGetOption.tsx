"use client";

import Image from "next/image";

const WhatYouGetOption = ({
  title,
  subtitle,
  icon,
}: {
  title: string;
  subtitle: string;
  icon: string;
}) => {
  return (
    <div className="flex md:flex-col md:items-center md:justify-center">
      <div className="md:w-20 md:h-20 w-10 h-10 rounded-full flex items-center justify-center bg-ntrl-white mb-4">
        <div className="md:w-10 md:h-10 w-5 h-5 rounded-full flex items-center justify-center bg-ntrl-white relative">
          <Image
            src={icon}
            fill={true}
            style={{ objectFit: "cover" }}
            alt={title}
          />
        </div>
      </div>
      <div className=" ml-4 md:ml-0">
        <h3 className="md:text-[24px]/[30px] text-[20px]/[28px] text-ntrl-white font-medium font-generalSans mb-2 md:text-center">
          {title}
        </h3>
        <p className="text-[16px]/[24px] text-ntrl-white font-normal md:text-center">
          {subtitle}
        </p>
      </div>
    </div>
  );
};

export default WhatYouGetOption;
