import Image from "next/image";
import { useRouter } from "next/navigation";

interface PartnerCardProps {
  logoUrl?: string;
  name?: string;
  description?: string;
  knowMore?: string;
}

export default function PartnerCard({
  logoUrl,
  name,
  description,
  knowMore,
}: PartnerCardProps) {
  const router = useRouter();

  const handleCardClick = () => {
    if (knowMore) {
      router.push(knowMore);
    }
  };

  const handleKnowMoreClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click when clicking the button
    if (knowMore) {
      router.push(knowMore);
    }
  };

  return (
    <div
      className="rounded-2xl relative md:p-6 p-4 flex flex-col justify-between h-full md:mb-6 pt-4 cursor-pointer"
      onClick={handleCardClick}
    >
      {/* Glass Effect Background */}
      <div className="bg-cardBackground absolute inset-0 rounded-4xl opacity-20 border-[1px] border-white transition-shadow duration-300"></div>

      {/* Logo */}
      <div className="items-start text-start">
        <div className="relative size-[52px]  z-10">
          <Image
            src={logoUrl || ""}
            fill={true}
            style={{ objectFit: "contain" }}
            alt={name || ""}
          />
        </div>
        <h3 className="text-white font-bold md:text-[14px]/[20px] text-[14px]/[16px] mt-2">
          {name}
        </h3>
      </div>
      {/* Content */}

      <p className="text-ntrl-white text-sm line-clamp-5 md:mt-4 mt-2 text-left">
        {description}
      </p>

      {/* Know More Button */}
      <div className="hidden md:block">
        <div className="flex justify-end mt-4">
          <button
            onClick={handleKnowMoreClick}
            className="text-white font-light text-xs rounded-xl border border-white px-4 py-1 justify-end h-auto max-h-[24px] relative z-10"
          >
            Know More
          </button>
        </div>
      </div>
    </div>
  );
}
