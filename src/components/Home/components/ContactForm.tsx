import Container from "@/components/globals/Container";
import GetInTouch from "@/components/globals/GetInTouch";
import circles2 from "@/assets/circles2.png";
import Image from "next/image";
const ContactForm = () => {
  return (
    <div className="relative bg-gradient-to-tr from-blue1100 to-blue800 pt-10 mb-10 pb-8 rounded-3xl overflow-hidden">
      <div className="absolute top-10 right-10 transform translate-x-1/2 -translate-y-1/2">
        <Image
          src={circles2}
          alt="Decorative circles"
          width={524}
          height={524}
          objectFit="cover"
        />
      </div>
      <div className="absolute left-16 bottom-40 transform -translate-x-1/2 translate-y-1/2">
        <Image
          src={circles2}
          alt="Decorative circles"
          width={524}
          height={524}
          objectFit="cover"
        />
      </div>

      <div className="grid grid-cols-[40%_60%]">
        <div className="justify-center font-helvetica text-[50px]/[70px] text-ntrl-white mb-2 flex pt-16">
          <h2 className="font-medium text-[45px]/[60px] w-2/3">
            Get In Touch with Our Insurance Experts
          </h2>
        </div>
        <div>
          <GetInTouch />
        </div>
      </div>
    </div>
  );
};

export default ContactForm;
