"use client";

import Container from "@/components/globals/Container";
import Image from "next/image";

const News = ({
  oneAssureInNews,
}: {
  oneAssureInNews: {
    id: number;
    newsCard: {
      id: number;
      url: string;
      logo: {
        data: {
          id: number;
          attributes: {
            url: string;
          };
        };
      };
    }[];
  };
}) => {
  return (
    <div className="md:mb-14 mb-5 ">
      <div className="">
        {/* Heading */}
        <div className="flex items-center justify-center  text-ntrl-black gap-2 mb-5">
          <h2 className="md:text-3xl text-lg font-medium">
            OneAssure In The News
          </h2>
        </div>
      </div>

      {/* News links */}
      <div className="grid grid-cols-2 md:grid-cols-5 md:gap-8 gap-4 md:mx-0 mx-5">
        {oneAssureInNews.newsCard.map((n, idx) => (
          <a
            key={idx}
            href={n.url}
            className="md:h-32 h-20 aspect-video relative transition-transform duration-300 hover:-translate-y-2"
          >
            <Image
              src={n.logo.data.attributes.url}
              fill={true}
              style={{ objectFit: "contain" }}
              alt={n.url}
              className=""
            />
          </a>
        ))}
      </div>
    </div>
  );
};

export default News;
