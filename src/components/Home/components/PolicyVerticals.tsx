import "@/app/globals.css";
import React, { useRef } from "react";
import { GoArrowRight } from "react-icons/go";
import Image from "next/image";
import { HomeDataObject } from "../types";
import CustomLink from "@/components/globals/CustomLink";
import { useSearchParams } from "next/navigation";
import BookACallBtn from "@/components/globals/BookACall";
interface PolicyVerticalsProps {
  policyData: HomeDataObject["attributes"]["policyVerticals"];
}

const PolicyVerticals: React.FC<PolicyVerticalsProps> = ({ policyData }) => {
  // Define the colors to cycle through
  const backgroundColors = [
    { 1: "bg-secondary-blue-1", 2: "text-secondary-blue-1" },
    { 1: "bg-secondary-green-1", 2: "text-secondary-green-1" },
    { 1: "bg-secondary-green-2", 2: "text-secondary-green-2" },
  ];
  const search = useSearchParams();
  const utm = search.get("utm_source");
  const bookACallBtnRef = useRef<HTMLButtonElement>(null);

  const handleTriggerButtonClick = () => {
    bookACallBtnRef.current?.click();
    // console.log("bookACallBtnRef", bookACallBtnRef.current);
  };

  return (
    <div className="md:mb-14 mb-5 bg-white rounded-lg">
      <div className="text-center">
        <p className="text-lg md:text-3xl text-ntrl-black-1 font-medium">
          Looking for a Different Insurance Policy?
        </p>
        <p className="text-sm font-light">
          We also assist with the Insurances mentioned below
        </p>
      </div>
      <div className={`grid md:grid-cols-3 grid-cols-2 gap-4 md:mt-9 mt-5`}>
        {/* <div className="flex flex-wrap gap-4"> */}
        {policyData.insuranceTypeCard.map((policy, index) => {
          // Get background color by cycling through the colors array
          const backgroundColor =
            backgroundColors[index % backgroundColors.length];
          return (
            <div
              key={index}
              className={`relative ${
                backgroundColor["1"]
              } md:rounded-br-12xl rounded-br-10xl border rounded-4xl  border-none shadow-customblue4d md:p-0 p-5 ${
                index === policyData.insuranceTypeCard.length - 1
                  ? "col-span-2 w-1/2 mx-auto md:mx-0 md:col-span-1 md:w-full"
                  : ""
              }`}
            >
              <p className="text-white md:pt-8 md:text-3xl text-xl justify-center md:p-5 w-1/2 md:w-full  mb-4 md:mb-0 font-bold">
                {policy.title}
              </p>
              <div className="flex text-left md:pl-0 md:mb-14 mb-0 ">
                <p className="hidden md:block text-white font-light text-sm w-2/3 text-left px-6">
                  {policy.description}
                </p>
                <Image
                  src={policy.logo.data.attributes.url}
                  alt="Policy graphic"
                  width={90}
                  height={90}
                  className="md:h-[90px] md:w-[90px] h-[56px] w-[56px]"
                />
                <div
                  className={`size-16 rounded-full bg-white absolute right-0 bottom-0 shadow-customblue4d flex justify-center items-center`}
                  onClick={handleTriggerButtonClick}
                >
                  <div className="w-full h-full flex items-center justify-center cursor-pointer">
                    <GoArrowRight
                      className={`${backgroundColor["2"]} size-10 transition-all duration-200 hover:scale-110`}
                    />
                  </div>
                </div>
                <BookACallBtn className="hidden" ref={bookACallBtnRef} />
              </div>
            </div>
            // </div>
          );
        })}
        {/* </div> */}
      </div>
    </div>
  );
};

export default PolicyVerticals;
