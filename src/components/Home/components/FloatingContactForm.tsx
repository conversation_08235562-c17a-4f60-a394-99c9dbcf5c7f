"use client";
import React, { useEffect, useState } from "react";
import BookACallBtn from "@/components/globals/BookACall";
import "animate.css";

const FloatingContactButton = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const mainContentHeight =
        document.querySelector(".font-manrope")?.getBoundingClientRect()
          .bottom || 0;
      const viewportHeight = window.innerHeight;

      // Show button if we're within the main content area
      if (mainContentHeight > viewportHeight) {
        setIsVisible(true);
      } else {
        // Hide when reached footer
        setIsVisible(false);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [lastScrollY]);

  return (
    <>
      {isVisible && (
        <div
          className={`fixed bottom-6 right-6 z-30 animate__animated md:hidden ${
            isVisible ? "animate__bounceIn" : "animate__bounceOut"
          }`}
        >
          <div className="bg-white px-3 py-2 rounded-full shadow-[0_0_10px_rgba(0,0,0,0.1)] hover:shadow-[0_0_15px_rgba(0,0,0,0.15)] transition-all duration-300 border border-ntrl-grey-3/20">
            <BookACallBtn
              className="text-ntrl-black-1 hover:text-black flex items-center gap-2 font-medium text-base"
              icon={true}
              label="Talk to an Expert"
              rightIcon={true}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default FloatingContactButton;
