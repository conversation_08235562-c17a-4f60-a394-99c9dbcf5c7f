"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import CustomLink from "@/components/globals/CustomLink";
import { Logo } from "@/components/HealthInsurance/types";

const PartnerCompanies = ({
  partners,
}: {
  partners: {
    attributes: { name: string; slug: string; category: string; logo: Logo };
  }[];
}) => {
  const search = useSearchParams();
  const utm = search.get("utm_source");
  const [showAll, setShowAll] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const visiblePartners = showAll
    ? partners
    : partners.slice(0, isMobile ? 8 : 12);

  return (
    <div className="mx-auto md:mb-14 mb-5 overflow-hidden">
      <div className="flex flex-col items-center justify-center mb-2">
        <h2 className="font-medium text-center text-ntrl-black-1 text-2xl md:text-3xl">
          Worried You Will Pick the Wrong Insurer?
        </h2>
        <p className="text-center text-gray-500 text-sm">
          Explore trusted insurance providers with OneAssure.
        </p>
      </div>

      <div className="bg-gray-50/50 p-3 md:p-6 rounded-3xl">
        <div className="grid md:grid-cols-6 grid-cols-2 md:gap-4 gap-2 md:gap-x-11 md:gap-y-8">
          {visiblePartners.map((partner, idx) => (
            <div className="p-1 md:p-2" key={idx}>
              {/* <CustomLink
                href={`/${partner.attributes.category}/${partner.attributes.slug}`}
                utm={utm}
                style="block"
              >
                <> */}
              <div className="relative w-full aspect-[3/2] md:aspect-[4/3] bg-white rounded-xl md:rounded-2xl border border-gray-100 shadow-[0_4px_12px_rgba(0,0,0,0.08)] md:shadow-[0_8px_24px_rgba(0,0,0,0.12)] hover:shadow-[0_8px_24px_rgba(0,0,0,0.16)] hover:border-primary-blue-3 transition-all duration-200 cursor-pointer overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center">
                  <CustomLink
                    style="relative w-[85%] h-[85%]"
                    href={`/${partner.attributes.category}/${partner.attributes.slug}`}
                    utm={utm}
                  >
                    <Image
                      src={partner.attributes.logo.data.attributes.url}
                      fill
                      style={{ objectFit: "contain" }}
                      alt={partner.attributes.name}
                      className="hover:scale-105 transition-all duration-200"
                    />
                  </CustomLink>
                </div>
              </div>
              {/* </>
              </CustomLink> */}
            </div>
          ))}
        </div>
      </div>

      {partners.length > 12 && (
        <div className="flex justify-center mt-2">
          <button
            onClick={() => setShowAll(!showAll)}
            className="inline-flex items-center gap-2 px-6 py-2 text-primary-blue-3 font-medium rounded-full border border-primary-blue-3 hover:border-primary-blue-4 hover:text-primary-blue-4 transition-colors duration-200"
          >
            {showAll ? "Show Less" : "Show More"}
            <svg
              className={`w-4 h-4 transition-transform duration-200 ${
                showAll ? "rotate-180" : ""
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

export default PartnerCompanies;
