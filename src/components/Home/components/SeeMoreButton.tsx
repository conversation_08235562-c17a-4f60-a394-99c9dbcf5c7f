import { CiSquareMinus, CiSquarePlus } from "react-icons/ci";
interface SeeMoreButtonProps {
  onClick: () => void;
  children: string;
}

export default function SeeMoreButton({
  onClick,
  children,
}: SeeMoreButtonProps) {
  return (
    <button
      onClick={onClick}
      className="bg-ntrl-black-2 text-white flex items-center px-3 gap-2 text-sm font-medium transition-transform active:scale-95 overflow-hidden rounded-b-xl"
    >
      <div className="p-1 flex items-center text-[14px][20px] justify-center ">
        {children === "See More" ? (
          <CiSquarePlus className="md:size-8 size-5" />
        ) : (
          <CiSquareMinus className="md:size-8 size-5" />
        )}
      </div>
      <span>{children}</span>
    </button>
  );
}
