import Image from "next/image";

const FeaturesCard = ({
  title,
  desc,
  icon,
}: {
  title: string;
  desc: string;
  icon: string;
}) => {
  return (
    <div className="px-8 pt-6 pb-4 shadow-shadow100 rounded-2xl min-w-[300px] md:w-[400px]  max-h-[500px]">
      <div className=" w-16 h-16  mb-1 flex items-center justify-center">
        <Image src={icon} alt="features" width={60} height={60} />
      </div>
      <h2 className="font-semibold text-2xl text-gray900 mb-1">{title}</h2>
      <p className="text-gray800 mb-6">{desc}</p>
    </div>
  );
};

export default FeaturesCard;
