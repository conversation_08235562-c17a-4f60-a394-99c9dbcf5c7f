import Container from "@/components/globals/Container";
import FeaturesCard from "./FeaturesCard";
import { data } from "../data";

const CorporateFeatures = () => {
  const features = data.features;
  return (
    <div className="mb-24">
      <Container>
        <div className="flex flex-col items-center justify-center">
          <h2 className="font-semibold text-4xl text-gray900 mb-8 md:w-[50%] text-center">
            Corporate features
          </h2>
          {/* <p className="text-base text-gray800 mb-8">
            Lorem ipsum dolor sit amet consectetur. Ipsum id purus consectetur
            lobortis bibendum.
          </p> */}
        </div>

        <div className="flex md:grid md:grid-cols-3 md:items-stretch gap-6 overflow-x-auto px-5 md:px-2 py-5">
          {features.map((feat, idx) => (
            <FeaturesCard
              key={idx}
              title={feat.title}
              desc={feat.description}
              icon={feat.icon}
            />
          ))}
        </div>
      </Container>
    </div>
  );
};

export default CorporateFeatures;
