import Container from "@/components/globals/Container";
import KY<PERSON><PERSON> from "./KYICard";
import { data } from "../data";

const KnowYourInsurance = () => {
  const otherFeat = data.otherFeat;
  return (
    <div className="mb-24">
      <Container>
        <div className="flex flex-col items-center justify-center">
          <h2 className="font-semibold text-4xl text-gray900 mb-8 md:w-[50%] text-center">
            Other Features
          </h2>
          {/* <p className="text-base text-gray800 mb-8">
            Lorem ipsum dolor sit amet consectetur. Ipsum id purus consectetur
            lobortis bibendum.
          </p> */}
        </div>

        <div className="flex md:items-stretch md:justify-center gap-6 overflow-x-auto md:overflow-hidden px-5 py-5">
          {otherFeat.map((feat, idx) => (
            <KYICard
              key={idx}
              title={feat.title}
              icon={feat.icon}
              desc={feat.desc}
            />
          ))}
        </div>
      </Container>
    </div>
  );
};

export default KnowYourInsurance;
