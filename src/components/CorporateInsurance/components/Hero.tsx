import Container from "@/components/globals/Container";
import Image from "next/image";
import BookACallBtn from "@/components/globals/BookACall";
import { Dispatch, SetStateAction } from "react";

const Hero = ({
  setOpenModal,
}: {
  setOpenModal: Dispatch<SetStateAction<boolean>>;
}) => {
  return (
    <div className=" mb-52 px-5 md:px-0">
      <Container>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-7 items-center">
          {/* Headings */}
          <div className="">
            <h1 className="md:text-4xl text-2xl font-semibold mb-6">
              Your Tailored Corporate Insurance Solutions
            </h1>

            <p className=" text-base/[22px] text-slateGrey mb-10 text-justify md:text-left">
              Invest in the future stability and protection of your business
              with OneAssure`s. Take comfort in the knowledge that your company
              is protected from unforeseen risks and liabilities, freeing you up
              to concentrate on fostering success and expansion.
            </p>

            <div className="flex items-center justify-center md:justify-start mb-8 md:mb-0">
              <BookACallBtn className="py-3 px-6 md:px-8 bg-primary-1 text-ntrl-white rounded-lg" />
            </div>
          </div>

          {/* Illustration */}
          <div className="flex items-center justify-end">
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL}/corporateHero.svg`}
              alt="corporate-insurance-hero"
              width={500}
              height={500}
            />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default Hero;
