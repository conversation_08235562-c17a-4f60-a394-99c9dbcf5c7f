"use client";

import Hero from "./components/Hero";
import TeamHealthCare from "./components/TeamHealthCare";
import CorporateFeatures from "./components/CorporateFeatures";
import OtherFeatures from "./components/OtherFeatures";
import { useState } from "react";
import Modal from "../globals/Modal";
import { Suspense } from "react";

const CorporateInsurance = () => {
  const [openModal, setOpenModal] = useState(false);
  return (
    <>
      <Hero setOpenModal={setOpenModal} />
      {/* <MarqueeSection /> */}
      <CorporateFeatures />
      <OtherFeatures />
      <TeamHealthCare setOpenModal={setOpenModal} />
      <Suspense>
        <Modal
          open={openModal}
          handleModal={() => setOpenModal(false)}
          msg="Corporate insurance assistance needed. Thank you!"
          utmSrc="BLP"
        />
      </Suspense>
    </>
  );
};

export default CorporateInsurance;
