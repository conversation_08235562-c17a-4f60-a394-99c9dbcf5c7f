import { useMutation } from "@tanstack/react-query";

export type Product = {
  product_id: string;
  product_name: string;
  variant_id: string;
};

export type Insurer = {
  insurer_name: string;
  product_count: number;
  products: Product[];
};

export type CatalogResponse = {
  payload: {
    data: Insurer[];
    message: string;
    status: string;
    total_insurers: number;
    total_products: number;
  };
  metadata: any;
};

// Function to fetch catalog
const getCatalog = async (): Promise<CatalogResponse> => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_GATEWAY_URL}/v1/site/catalog`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || "Failed to fetch catalog. Please try again."
    );
  }

  const responseData = await response.json();
  return responseData;
};

// Hook for fetching catalog
export const useGetCatalog = () => {
  return useMutation<CatalogResponse, Error, void>({
    mutationFn: getCatalog,
  });
};
