import { useMutation } from "@tanstack/react-query";

type InitiateVerificationRequest = {
  phone_number: string;
  scopes: string[];
};

type InitiateVerificationResponse = {
  payload: {
    otpToken: string;
  };
  metadata: any;
};

type VerifyOTPRequest = {
  phone_number: string;
  otp_token: string;
  otp_code: string;
};

type VerifyOTPResponse = {
  payload?: {
    accessToken: string;
  };
  metadata?: any;
  type?: string;
  title?: string;
  detail?: string;
  instance?: string;
};

// Function to initiate phone verification
const initiateVerification = async (
  data: InitiateVerificationRequest
): Promise<InitiateVerificationResponse> => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_GATEWAY_URL}/v1/site/initiate`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || "Failed to initiate verification. Please try again."
    );
  }

  const responseData = await response.json();
  return responseData;
};

// Function to verify OTP
const verifyOTP = async (data: {
  phone_number: string;
  otp: string;
  otp_token: string;
}): Promise<VerifyOTPResponse> => {
  const requestData: VerifyOTPRequest = {
    phone_number: data.phone_number,
    otp_token: data.otp_token,
    otp_code: data.otp,
  };

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_GATEWAY_URL}/v1/site/verify`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
    }
  );

  const responseData = await response.json();

  if (!response.ok) {
    throw new Error(
      responseData.detail || "Failed to verify OTP. Please try again."
    );
  }

  return responseData;
};

// Hook for initiating verification
export const useInitiateVerification = () => {
  return useMutation<
    InitiateVerificationResponse,
    Error,
    InitiateVerificationRequest
  >({
    mutationFn: initiateVerification,
  });
};

// Hook for verifying OTP
export const useVerifyOTP = () => {
  return useMutation<
    VerifyOTPResponse,
    Error,
    {
      phone_number: string;
      otp: string;
      otp_token: string;
    }
  >({
    mutationFn: verifyOTP,
  });
};
