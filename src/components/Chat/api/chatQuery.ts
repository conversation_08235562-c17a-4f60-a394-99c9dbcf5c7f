import { useMutation } from "@tanstack/react-query";

export type ChatQueryRequest = {
  query: string;
  session_id?: string | null;
  variant_id?: string;
  agent_type?: string;
  policy_id?: string;
};

export type ChatQueryResponse = {
  payload: {
    agent_type: string;
    processing_time: number;
    response: string;
    session_id: string;
    status: string;
  };
  metadata: any;
};

// Function to get the token from sessionStorage
const getAuthToken = (): string | null => {
  if (typeof window !== "undefined") {
    return sessionStorage.getItem("accessToken");
  }
  return null;
};

// Function to send a chat query
const sendChatQuery = async (
  data: ChatQueryRequest
): Promise<ChatQueryResponse> => {
  const requestData = {
    ...data,
    agent_type: data.agent_type || "function",
  };

  // Get the auth token from sessionStorage
  const token = getAuthToken();

  // Prepare headers with Authorization if token exists
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers["Authorization"] = `Bearer ${token}`;
  }

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_GATEWAY_URL}/v1/site/query`,
    {
      method: "POST",
      headers,
      body: JSON.stringify(requestData),
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || "Failed to get a response. Please try again."
    );
  }

  const result = await response.json();
  return result;
};

// Hook for sending chat queries
export const useSendChatQuery = () => {
  return useMutation<ChatQueryResponse, Error, ChatQueryRequest>({
    mutationFn: sendChatQuery,
  });
};
