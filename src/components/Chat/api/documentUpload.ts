import { useMutation } from "@tanstack/react-query";

type UploadLinkResponse = {
  payload: {
    presigned_url: string;
    document_key: string;
  };
  metadata: any;
};

// Function to get presigned upload URL
const getUploadLink = async (): Promise<UploadLinkResponse> => {
  // Get the access token from sessionStorage if available
  const accessToken =
    typeof window !== "undefined"
      ? sessionStorage.getItem("accessToken")
      : null;

  // Prepare headers with authorization if token exists
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };

  if (accessToken) {
    headers["Authorization"] = `Bearer ${accessToken}`;
  }

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_GATEWAY_URL}/v1/site/upload-link`,
    {
      method: "GET",
      headers,
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || "Failed to get upload link. Please try again."
    );
  }

  return await response.json();
};

// Function to upload document using presigned URL
const uploadDocument = async ({
  presignedUrl,
  file,
}: {
  presignedUrl: string;
  file: File;
}): Promise<void> => {
  const response = await fetch(presignedUrl, {
    method: "PUT",
    body: file,
    headers: {
      "Content-Type": file.type,
    },
  });

  if (!response.ok) {
    throw new Error("Failed to upload document. Please try again.");
  }

  return;
};

// Hook for getting upload link
export const useGetUploadLink = () => {
  return useMutation<UploadLinkResponse, Error, void>({
    mutationFn: getUploadLink,
  });
};

// Hook for uploading document
export const useUploadDocument = () => {
  return useMutation<void, Error, { presignedUrl: string; file: File }>({
    mutationFn: uploadDocument,
  });
};
