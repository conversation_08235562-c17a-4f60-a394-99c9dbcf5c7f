import { useMutation } from "@tanstack/react-query";

export type Policy = {
  customer_id: string;
  expiry_date: string;
  insurer_name: string;
  insurer_policy_number: string;
  issuance_date: string;
  phone: string;
  policy_id: string;
  premium_amount: string;
  product_name: string;
  proposer_name: string;
  sum_insured: string;
  variant_id: string;
};

export type GetPoliciesResponse = {
  payload: {
    policies: Policy[];
  };
  metadata: any;
};

// Function to fetch user policies - now uses GET request with accessToken
const getPolicies = async (): Promise<GetPoliciesResponse> => {
  // Get the access token from sessionStorage if available
  const accessToken =
    typeof window !== "undefined"
      ? sessionStorage.getItem("accessToken")
      : null;

  // Prepare headers with authorization if token exists
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };

  if (!accessToken) {
    throw new Error("Access token is required to fetch policies");
  }

  headers["Authorization"] = `Bearer ${accessToken}`;

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_GATEWAY_URL}/v1/site/get-policies`,
    {
      method: "GET",
      headers,
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || "Failed to fetch policies. Please try again."
    );
  }

  return await response.json();
};

// Hook for fetching policies - updated to not require phone number
export const useGetPolicies = () => {
  return useMutation<GetPoliciesResponse, Error>({
    mutationFn: getPolicies,
  });
};
