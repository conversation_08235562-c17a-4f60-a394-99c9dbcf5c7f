import { useMutation } from "@tanstack/react-query";

interface LeadGenerationPayload {
  insurer_name: string;
  product_name: string;
  session_id: string;
}

interface LeadGenerationResponse {
  success: boolean;
  message?: string;
}

interface LeadGenerationRequest {
  payload: LeadGenerationPayload;
  accessToken: string;
}

const generateLead = async ({
  payload,
  accessToken,
}: LeadGenerationRequest): Promise<LeadGenerationResponse> => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_GATEWAY_URL}/v1/site/chat-lead`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify(payload),
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `Failed to generate lead: ${response.status}`
    );
  }

  return await response.json();
};

export const useGenerateLead = () => {
  return useMutation({
    mutationFn: generateLead,
    onError: (error) => {
      console.error("Lead generation failed:", error);
    },
  });
};

export type {
  LeadGenerationPayload,
  LeadGenerationResponse,
  LeadGenerationRequest,
};
