import { useMutation } from "@tanstack/react-query";
import axios from "axios";

export type PolicyWorkflowResponse = {
  payload: {
    workflow_id: string;
    run_id: string;
    status: string;
    metadata_id: string;
    policy_id: string;
  };
  metadata: any;
};

export type PolicyDetails = {
  insurer_details: {
    enterprise_logo: string;
    insurer_id: string;
    insurer_name: string;
    lead_id: string;
    policy_number: string;
    product_id: string;
    product_name: string;
    status: string;
    variant_id: string;
    variant_name: string;
  };
  proposer_details: {
    address: string;
    dob: string;
    email: string;
    name: string;
    phone: string;
    pincode: string;
  };
  policy_details: {
    expiry_date: string;
    gross_premium: string;
    gst: string;
    issuance_date: string;
    policy_source: string;
    policy_type: string;
    sum_insured: string;
    tenure: number;
    total_premium: string;
  };
  bank_details: {
    bank_account_number: string;
    bank_name: string;
    branch_name: string;
    ifsc_code: string;
  };
  insured_members: {
    members: any[];
  };
  nominee_details: {
    nominees: any[];
  };
  ped_details: {
    members_with_ped: any[];
  };
  addons: {
    addons: string[];
  };
  enhub_metadata: {
    enhub_data: {
      addons: string[];
      address: string;
      dob: string;
      email: string;
      expiry_date: string;
      insured_members: Array<{
        dob: string;
        gender: string;
        id_proofs: any[];
        name: string;
        peds_list: any[];
        relationship_with_proposer: string;
      }>;
      insurer_name: string;
      insurer_policy_number: string;
      issuance_date: string;
      name: string;
      nominee_details: Array<{
        name: string;
        relationship_with_proposer: string;
      }>;
      phone: string;
      pincode: string;
      product_name: string;
      sum_insured: string;
      total_premium: string;
      variant_name: string;
    };
  };
};

export type PolicyPollResponse = {
  payload: {
    policy: PolicyDetails;
  };
  metadata: any;
};

// Function to trigger policy workflow
const triggerPolicyWorkflow = async (data: {
  file_uuid: string;
}): Promise<PolicyWorkflowResponse> => {
  // Get the access token from sessionStorage if available
  const accessToken =
    typeof window !== "undefined"
      ? sessionStorage.getItem("accessToken")
      : null;

  // Prepare headers with authorization if token exists
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };

  if (accessToken) {
    headers["Authorization"] = `Bearer ${accessToken}`;
  }

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_GATEWAY_URL}/v1/site/trigger-policy-workflow`,
    {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message ||
        "Failed to trigger policy workflow. Please try again."
    );
  }

  const result = await response.json();
  return result;
};

// Function to poll policy workflow
const pollWorkflow = async (data: {
  policy_id: string;
}): Promise<PolicyPollResponse> => {
  // Get the access token from sessionStorage if available
  const accessToken =
    typeof window !== "undefined"
      ? sessionStorage.getItem("accessToken")
      : null;

  // Prepare headers with authorization if token exists
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };

  if (accessToken) {
    headers["Authorization"] = `Bearer ${accessToken}`;
  }

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_GATEWAY_URL}/v1/site/poll-workflow`,
    {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || "Failed to poll workflow. Please try again."
    );
  }

  return await response.json();
};

// Hook for triggering policy workflow
export const useTriggerPolicyWorkflow = () => {
  return useMutation<PolicyWorkflowResponse, Error, { file_uuid: string }>({
    mutationFn: triggerPolicyWorkflow,
  });
};

// Hook for polling workflow
export const usePollWorkflow = () => {
  return useMutation<PolicyPollResponse, Error, { policy_id: string }>({
    mutationFn: pollWorkflow,
  });
};

export async function linkPolicyToCustomer({
  policy_id,
  accessToken,
}: {
  policy_id: string;
  accessToken: string;
}) {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_GATEWAY_URL}/v1/site/link`,
    { policy_id },
    { headers: { Authorization: `Bearer ${accessToken}` } }
  );
  return response.data;
}

export function useLinkPolicyToCustomer() {
  return useMutation({ mutationFn: linkPolicyToCustomer });
}
