import React, { useState } from "react";
import { IoShieldCheckmark } from "react-icons/io5";
import { IoClose } from "react-icons/io5";
import { MdLockClock } from "react-icons/md";

type VerificationRequiredProps = {
  onVerifyPhone?: (phoneNumber?: string) => void;
};

export const VerificationRequired = ({
  onVerifyPhone,
}: VerificationRequiredProps) => {
  const [isExpanded, setIsExpanded] = useState(true);

  const handleStartVerification = () => {
    if (onVerifyPhone) onVerifyPhone();
  };

  const handleDismiss = () => {
    setIsExpanded(false);
  };

  if (!isExpanded) {
    return (
      <div className="bg-blue-50 rounded-xl p-3 flex items-center justify-between mb-1 animate-fadeIn">
        <div className="flex items-center gap-2">
          <IoShieldCheckmark className="text-blue-600" size={18} />
          <span className="text-xs text-gray-700">Verification required</span>
        </div>
        <button
          onClick={() => setIsExpanded(true)}
          className="text-blue-600 text-xs font-medium hover:text-blue-700 transition-colors focus:outline-none focus:ring-1 focus:ring-blue-300 rounded px-2 py-1"
        >
          Continue
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white border border-blue-200 rounded-xl overflow-hidden shadow-sm transition-all duration-300 ease-in-out animate-fadeIn max-w-sm">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 py-3 px-4 flex items-center justify-between border-b border-blue-100">
        <div className="flex items-center gap-2">
          <IoShieldCheckmark className="text-blue-600" size={20} />
          <h3 className="font-medium text-gray-800">Verification Required</h3>
        </div>
        <button
          onClick={handleDismiss}
          className="text-gray-500 hover:text-gray-700 transition-colors p-1 rounded-full hover:bg-gray-100"
          aria-label="Minimize verification dialog"
        >
          <IoClose size={18} />
        </button>
      </div>

      {/* Content */}
      <div className="p-4">
        <p className="text-xs text-gray-700 mb-4">
          You&apos;ve reached the limit of 5 free messages. Verify your phone
          number to:
        </p>

        <ul className="mb-4 space-y-2">
          <li className="flex items-start gap-2 text-xs text-gray-700">
            <span className="text-green-500 font-bold mt-0.5">✓</span>
            <span>Continue your conversation without interruption</span>
          </li>
          <li className="flex items-start gap-2 text-xs text-gray-700">
            <span className="text-green-500 font-bold mt-0.5">✓</span>
            <span>Get personalized insurance recommendations</span>
          </li>
          <li className="flex items-start gap-2 text-xs text-gray-700">
            <span className="text-green-500 font-bold mt-0.5">✓</span>
            <span>Access to expert insurance advisors when needed</span>
          </li>
        </ul>

        <div className="flex flex-col gap-3">
          <button
            onClick={handleStartVerification}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium py-2.5 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-300 flex items-center justify-center"
          >
            <IoShieldCheckmark className="mr-2" size={18} />
            Verify My Phone Number
          </button>

          <button
            onClick={handleDismiss}
            className="w-full bg-white hover:bg-gray-50 text-gray-600 text-xs font-medium py-2.5 px-4 rounded-lg border border-gray-200 transition-colors focus:outline-none focus:ring-1 focus:ring-gray-300 flex items-center justify-center"
          >
            <MdLockClock className="mr-2" size={18} />
            Try Later
          </button>
        </div>
      </div>
    </div>
  );
};
