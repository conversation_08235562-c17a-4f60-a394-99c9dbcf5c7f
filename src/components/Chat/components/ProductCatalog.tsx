import React, { useState, useEffect } from "react";
import {
  useGetCatalog,
  type Insurer,
  type Product,
} from "@/components/Chat/api/catalogFetch";

export const ProductCatalog = ({
  onProductSelected,
  disabled = false,
}: {
  onProductSelected: (product: Product & { insurer_name: string }) => void;
  disabled?: boolean;
}) => {
  const [insurers, setInsurers] = useState<Insurer[]>([]);
  const [selectedInsurerIndex, setSelectedInsurerIndex] = useState<number>(-1);
  const [selectedProductIndex, setSelectedProductIndex] = useState<number>(-1);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const getCatalogMutation = useGetCatalog();

  // Fetch catalog on mount
  useEffect(() => {
    const fetchCatalog = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const result = await getCatalogMutation.mutateAsync();
        setInsurers(result.payload.data || []);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Failed to fetch insurance products. Please try again."
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchCatalog();
  }, []);

  const handleInsurerChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (disabled) return;
    const index = parseInt(e.target.value);
    setSelectedInsurerIndex(index);
    setSelectedProductIndex(-1); // Reset product selection
  };

  const handleProductChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (disabled) return;
    const index = parseInt(e.target.value);
    setSelectedProductIndex(index);
  };

  const handleSubmit = () => {
    if (disabled || !canSubmit) return;

    if (selectedInsurerIndex >= 0 && selectedProductIndex >= 0) {
      const selectedInsurer = insurers[selectedInsurerIndex];
      const selectedProduct = selectedInsurer.products[selectedProductIndex];

      onProductSelected({
        ...selectedProduct,
        insurer_name: selectedInsurer.insurer_name,
      });
    }
  };

  const selectedInsurer =
    selectedInsurerIndex >= 0 ? insurers[selectedInsurerIndex] : null;
  const canSubmit = selectedInsurerIndex >= 0 && selectedProductIndex >= 0;

  if (isLoading) {
    return (
      <div
        className="w-full bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-4"
        role="region"
        aria-label="Insurance product selection"
      >
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <h3 className="text-xs font-medium text-gray-800">
            Select Insurance Product
          </h3>
          <p className="text-xs text-gray-500 mt-1">
            Loading available insurance products...
          </p>
        </div>
        <div className="p-4">
          <div className="animate-pulse space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
              </div>
              <div>
                <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
              </div>
            </div>
            <div className="h-10 bg-gray-200 rounded mt-4"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="w-full bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-4"
        role="region"
        aria-label="Insurance product selection"
      >
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <h3 className="font-medium text-gray-800">
            Select Insurance Product
          </h3>
          <p className="text-xs text-gray-500 mt-1">
            Unable to load insurance products
          </p>
        </div>
        <div className="p-4">
          <div className="text-center py-4">
            <p className="text-red-500 font-medium mb-3">{error}</p>
            <div className="space-y-2">
              <button
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg text-xs hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
              <p className="text-xs text-gray-500">
                Or{" "}
                <a
                  href={process.env.NEXT_PUBLIC_ONE_ASSURE_ORUFY_LINK}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  book a call with our team
                </a>{" "}
                for personalized assistance
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`w-full bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-4 ${
        disabled ? "opacity-60" : ""
      }`}
      role="region"
      aria-label="Insurance product selection"
    >
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <h3 className="text-xs font-medium text-gray-800">
          Select Insurance Product
        </h3>
        <p className="text-xs text-gray-500 mt-1">
          {disabled
            ? "Product selection completed"
            : `Choose your insurance provider and product from ${insurers.length} available insurers`}
        </p>
      </div>

      <div className="p-4">
        {/* Two Column Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          {/* Left Column - Insurance Provider */}
          <div>
            <label
              htmlFor="insurer-select"
              className="block text-xs font-medium text-gray-700 mb-2"
            >
              Insurance Provider
            </label>
            <select
              id="insurer-select"
              value={selectedInsurerIndex}
              onChange={handleInsurerChange}
              disabled={disabled}
              className={`w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs bg-white ${
                disabled ? "cursor-not-allowed bg-gray-50 text-gray-500" : ""
              }`}
            >
              <option value={-1}>Select provider...</option>
              {insurers.map((insurer, index) => (
                <option key={index} value={index}>
                  {insurer.insurer_name} ({insurer.product_count})
                </option>
              ))}
            </select>
          </div>

          {/* Right Column - Insurance Product */}
          <div>
            <label
              htmlFor="product-select"
              className="block text-xs font-medium text-gray-700 mb-2"
            >
              Insurance Product
            </label>
            <select
              id="product-select"
              value={selectedProductIndex}
              onChange={handleProductChange}
              disabled={disabled || !selectedInsurer}
              className={`w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs bg-white disabled:bg-gray-50 disabled:cursor-not-allowed ${
                disabled ? "text-gray-500" : ""
              }`}
            >
              <option value={-1}>
                {selectedInsurer
                  ? "Select product..."
                  : "Select provider first"}
              </option>
              {selectedInsurer &&
                selectedInsurer.products.map((product, index) => (
                  <option key={index} value={index}>
                    {product.product_name}
                  </option>
                ))}
            </select>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            onClick={handleSubmit}
            disabled={disabled || !canSubmit}
            className={`px-6 py-3 rounded-xl text-xs font-medium flex items-center justify-center gap-2 
              ${
                !disabled && canSubmit
                  ? "bg-[#1c85c4] hover:bg-[#166bb0] text-white focus:ring-2 focus:ring-blue-300 focus:outline-none"
                  : "bg-gray-200 text-gray-500 cursor-not-allowed"
              } transition-colors`}
            aria-label={
              disabled
                ? "Product selection completed"
                : canSubmit
                ? "Continue with selected product"
                : "Please select provider and product"
            }
          >
            {disabled ? (
              <>
                Selection Complete
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </>
            ) : canSubmit ? (
              <>
                Continue with Product
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </>
            ) : (
              "Select Provider & Product"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};
