import { useState, useRef, useEffect } from "react";
import { FaFileUpload, FaFilePdf, FaCheckCircle } from "react-icons/fa";
import {
  useGetUploadLink,
  useUploadDocument,
} from "@/components/Chat/api/documentUpload";
import {
  useTriggerPolicyWorkflow,
  usePollWorkflow,
  PolicyDetails,
} from "@/components/Chat/api/policyWorkflow";
import { formatCurrency } from "@/utils/formatters";

export const PolicyUpload = ({
  onUploadComplete,
}: {
  onUploadComplete: (
    file: File,
    documentKey?: string,
    policyDetails?: PolicyDetails,
    error?: string
  ) => void;
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [documentKey, setDocumentKey] = useState<string | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [policyId, setPolicyId] = useState<string | null>(null);
  const [policyDetails, setPolicyDetails] = useState<PolicyDetails | null>(
    null
  );
  const [hasParsingError, setHasParsingError] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);

  // Get API mutation hooks
  const getUploadLinkMutation = useGetUploadLink();
  const uploadDocumentMutation = useUploadDocument();
  const triggerWorkflowMutation = useTriggerPolicyWorkflow();
  const pollWorkflowMutation = usePollWorkflow();

  // Clean up polling on unmount
  useEffect(() => {
    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (isCompleted) return;
    if (e.target.files && e.target.files[0]) {
      validateAndSetFile(e.target.files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    if (isCompleted) return;
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    if (isCompleted) return;
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    if (isCompleted) return;
    e.preventDefault();
    setIsDragging(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      validateAndSetFile(e.dataTransfer.files[0]);
    }
  };

  const validateAndSetFile = (file: File) => {
    if (isCompleted) return;
    setErrorMessage("");
    // Check file type
    if (file.type !== "application/pdf") {
      setErrorMessage("Only PDF files are allowed");
      return;
    }

    // Check file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      setErrorMessage("File size should be less than 5MB");
      return;
    }

    setFile(file);
  };

  const startPolling = (policyId: string) => {
    setIsProcessing(true);
    let progressIncrement = 0;

    // Clear any existing polling
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
    }

    pollingRef.current = setInterval(async () => {
      try {
        progressIncrement++;
        // Gradually increase progress but cap at 95% until completion
        setProcessingProgress(Math.min(95, progressIncrement * 5));

        const result = await pollWorkflowMutation.mutateAsync({
          policy_id: policyId,
        });

        // Check the response status to determine next action
        // 200: Success - process completed
        if (result.payload) {
          // Set policy details
          setPolicyDetails(result.payload.policy);

          // Save policy ID and variant ID to sessionStorage
          const variantId = result.payload.policy.insurer_details?.variant_id;
          const policyIdToStore = policyId;
          if (policyIdToStore) {
            sessionStorage.setItem("policyId", policyIdToStore);
          }
          if (variantId) {
            sessionStorage.setItem("variant_id", variantId);
          }

          if (pollingRef.current) {
            clearInterval(pollingRef.current);
          }

          setProcessingProgress(100);
          setTimeout(() => {
            setIsProcessing(false);
            setIsCompleted(true);

            // Notify parent with complete data
            if (file) {
              onUploadComplete(
                file,
                documentKey || undefined,
                result.payload.policy
              );
            }
          }, 500);
        }
        // If we reach here with no payload, it means 202 - continue polling
      } catch (error: any) {
        console.error("Polling error:", error);

        // Check if it's a 500 error (processing failed)
        if (error?.response?.status === 500 || error?.status === 500) {
          if (pollingRef.current) {
            clearInterval(pollingRef.current);
          }
          setIsProcessing(false);
          setHasParsingError(true);
          setIsCompleted(true);

          // Notify parent with error
          if (file) {
            onUploadComplete(
              file,
              documentKey || undefined,
              undefined // No policy details indicates parsing error
            );
          }
        }
        // For 202 or other non-500 errors, continue polling
        // No need to stop polling - let it continue
      }
    }, 2000); // Poll every 2 seconds
  };

  const handleSubmit = async () => {
    if (!file || isUploading || isCompleted) return;

    // Simulate upload progress
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // 1. Get presigned URL
      const uploadLinkResponse = await getUploadLinkMutation.mutateAsync();
      const { presigned_url, document_key } = uploadLinkResponse.payload;

      // Store document key for later use
      setDocumentKey(document_key);

      // 2. Simulate upload progress (for UX purposes)
      const interval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(interval);
            return 90; // Hold at 90% until actual upload completes
          }
          return prev + 10;
        });
      }, 200);

      // 3. Upload the file
      await uploadDocumentMutation.mutateAsync({
        presignedUrl: presigned_url,
        file,
      });

      // 4. Complete progress
      clearInterval(interval);
      setUploadProgress(100);

      // 5. Trigger the policy workflow with document key as file_uuid
      try {
        const workflowResponse = await triggerWorkflowMutation.mutateAsync({
          file_uuid: document_key,
        });

        // 6. Start polling for results if we have a policy_id
        if (workflowResponse.payload?.policy_id) {
          setPolicyId(workflowResponse.payload.policy_id);
          startPolling(workflowResponse.payload.policy_id);
        } else {
          // If no policy_id, just complete the process
          setIsCompleted(true);
          if (file) {
            onUploadComplete(file, document_key);
          }
        }
      } catch (workflowError) {
        console.error("Workflow trigger error:", workflowError);
        // Complete anyway even if workflow fails
        setIsCompleted(true);
        if (file) {
          onUploadComplete(file, document_key);
        }
      }
    } catch (error) {
      setIsUploading(false);
      if (error instanceof Error) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage("Failed to upload file. Please try again.");
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isCompleted) return;
    // Allow opening file selector with keyboard
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      fileInputRef.current?.click();
    }
  };

  // Helper function to safely format currency values
  const formatPolicyAmount = (value: string | undefined | null): string => {
    if (!value) return "Not available";
    const amount = parseFloat(value);
    return isNaN(amount) ? "Not available" : formatCurrency(amount);
  };

  return (
    <div
      className="w-full bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-4"
      role="region"
      aria-label="Upload insurance policy"
    >
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <h3 className="font-medium text-gray-800">
          {isCompleted
            ? hasParsingError
              ? "Policy Document Uploaded"
              : "Policy Document Uploaded"
            : isProcessing
            ? "Analyzing Policy Document"
            : "Upload Insurance Policy"}
        </h3>
        <p className="text-xs text-gray-500 mt-1">
          {isCompleted
            ? hasParsingError
              ? "Upload completed - analysis encountered an issue"
              : "Your policy has been uploaded and analyzed"
            : isProcessing
            ? "Please wait while we extract information from your policy"
            : "Please upload your insurance policy document for AI analysis"}
        </p>
      </div>

      <div className="p-4">
        {/* Drag and drop area */}
        <div
          className={`border-2 ${
            isCompleted
              ? hasParsingError
                ? "border-slate-200 bg-slate-50"
                : "border-green-100 bg-green-50"
              : "border-dashed"
          } rounded-lg p-6 text-center 
            ${
              !isCompleted && !isProcessing && "cursor-pointer"
            } transition-colors ${
            isDragging
              ? "border-blue-500 bg-blue-50"
              : isCompleted
              ? hasParsingError
                ? "border-slate-200"
                : "border-green-100"
              : "border-gray-300 hover:border-blue-400"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() =>
            !isCompleted && !isProcessing && fileInputRef.current?.click()
          }
          tabIndex={isCompleted || isProcessing ? -1 : 0}
          role={isCompleted ? "status" : "button"}
          aria-label={
            isCompleted
              ? "Policy document uploaded successfully"
              : isProcessing
              ? "Policy document is being analyzed"
              : "Drag and drop or click to select a PDF file"
          }
          onKeyDown={handleKeyDown}
        >
          {isCompleted ? (
            <div className="flex flex-col items-center">
              <div
                className={`w-16 h-16 rounded-full ${
                  hasParsingError ? "bg-slate-100" : "bg-green-100"
                } flex items-center justify-center mb-3`}
              >
                {hasParsingError ? (
                  <svg
                    className="text-slate-500 text-4xl w-8 h-8"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                ) : (
                  <FaCheckCircle className="text-green-500 text-4xl" />
                )}
              </div>
              <p className="text-base font-medium text-gray-700">
                {hasParsingError ? "Upload Complete" : "Analysis Complete"}
              </p>
              {file && (
                <>
                  <p className="text-xs text-gray-600 mt-2">{file.name}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </>
              )}
              {hasParsingError && (
                <div className="mt-3 text-left w-full max-w-md border border-slate-200 rounded p-3 bg-slate-50">
                  <p className="text-xs font-medium text-slate-700">
                    Unable to analyze document
                  </p>
                  <p className="text-xs text-slate-600 mt-1">
                    {`We couldn't extract policy details from your document. This
                    can happen with certain file formats or document quality.
                    Our support team can help analyze it manually.`}
                  </p>
                </div>
              )}
              {policyDetails && !hasParsingError && (
                <div className="mt-3 text-left w-full max-w-md border border-green-200 rounded p-3 bg-green-50">
                  <p className="font-medium text-gray-700">Policy Details:</p>
                  <div className="text-xs space-y-1 mt-2">
                    <p>Insurer: {policyDetails.insurer_details.insurer_name}</p>
                    <p>Policy: {policyDetails.insurer_details.product_name}</p>
                    {policyDetails.policy_details.sum_insured && (
                      <p>
                        Sum Insured:{" "}
                        {formatPolicyAmount(
                          policyDetails.policy_details.sum_insured
                        )}
                      </p>
                    )}
                    {policyDetails.policy_details.total_premium && (
                      <p>
                        Premium:{" "}
                        {formatPolicyAmount(
                          policyDetails.policy_details.total_premium
                        )}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : file && !isProcessing ? (
            <div className="flex flex-col items-center">
              <FaFilePdf className="text-red-500 text-3xl mb-2" />
              <p className="text-xs font-medium text-gray-700">{file.name}</p>
              <p className="text-xs text-gray-500 mt-1">
                {(file.size / 1024 / 1024).toFixed(2)} MB
              </p>
              {!isUploading && (
                <div className="mt-3 text-blue-500 text-xs">
                  <button
                    className="underline hover:text-blue-700"
                    onClick={(e) => {
                      e.stopPropagation();
                      setFile(null);
                      if (fileInputRef.current) fileInputRef.current.value = "";
                    }}
                    aria-label="Remove selected file"
                  >
                    Remove file
                  </button>
                </div>
              )}
            </div>
          ) : isProcessing ? (
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center animate-pulse mb-3">
                <svg
                  className="w-8 h-8 text-blue-600 animate-spin"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              </div>
              <p className="text-base font-medium text-gray-700">
                Analyzing Your Policy
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Extracting policy details, please wait...
              </p>
              {file && (
                <p className="text-xs text-gray-600 mt-2">{file.name}</p>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center">
              <FaFileUpload className="text-gray-400 text-3xl mb-2" />
              <p className="text-xs font-medium text-gray-700">
                Drop your file here, or click to browse
              </p>
              <p className="text-xs text-gray-500 mt-1">PDF only (max. 5MB)</p>
            </div>
          )}
          <input
            type="file"
            id="policy-file"
            ref={fileInputRef}
            accept=".pdf"
            onChange={handleFileChange}
            className="hidden"
            aria-hidden="true"
            disabled={isCompleted || isProcessing}
          />
        </div>

        {/* Error message */}
        {errorMessage && (
          <p className="text-xs text-red-500 mt-2" role="alert">
            {errorMessage}
          </p>
        )}

        {/* Upload progress */}
        {isUploading && !isCompleted && !isProcessing && (
          <div className="mt-4">
            <div className="flex justify-between mb-1">
              <span className="text-xs font-medium text-gray-700">
                Uploading...
              </span>
              <span className="text-xs font-medium text-gray-700">
                {uploadProgress}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                style={{ width: `${uploadProgress}%` }}
                role="progressbar"
                aria-valuenow={uploadProgress}
                aria-valuemin={0}
                aria-valuemax={100}
              ></div>
            </div>
          </div>
        )}

        {/* Processing progress */}
        {isProcessing && (
          <div className="mt-4">
            <div className="flex justify-between mb-1">
              <span className="text-xs font-medium text-gray-700">
                Analyzing document...
              </span>
              <span className="text-xs font-medium text-gray-700">
                {processingProgress}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-green-600 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                style={{ width: `${processingProgress}%` }}
                role="progressbar"
                aria-valuenow={processingProgress}
                aria-valuemin={0}
                aria-valuemax={100}
              ></div>
            </div>
          </div>
        )}

        {/* Submit button - only show if not completed or processing */}
        {!isCompleted && !isProcessing && (
          <div className="mt-4 flex justify-end">
            <button
              onClick={handleSubmit}
              disabled={!file || isUploading}
              className={`px-6 py-3 rounded-xl text-xs font-medium flex items-center justify-center gap-2 
                ${
                  file && !isUploading
                    ? "bg-[#1c85c4] hover:bg-[#166bb0] text-white focus:ring-2 focus:ring-blue-300 focus:outline-none"
                    : "bg-gray-200 text-gray-500 cursor-not-allowed"
                } transition-colors`}
              aria-label={
                isUploading ? "Uploading" : "Upload and analyze policy"
              }
            >
              {isUploading ? (
                <>
                  <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                  <span>Uploading...</span>
                </>
              ) : (
                "Upload & Analyze"
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
