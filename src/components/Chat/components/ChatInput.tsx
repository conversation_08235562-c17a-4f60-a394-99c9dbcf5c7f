import { OnboardingState } from "@/components/Chat/types/types";
import { useState, useRef } from "react";

// Paper plane icon component
const SendIcon = () => (
  <svg
    viewBox="0 0 24 24"
    width="20"
    height="20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    aria-hidden="true"
    focusable="false"
  >
    <path
      d="M22 2L11 13"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22 2L15 22L11 13L2 9L22 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// Chat input component
export const ChatInput = ({
  input,
  setInput,
  onboarding,
  handleSend,
  isDisabled,
}: {
  input: string;
  setInput: (input: string) => void;
  onboarding: OnboardingState;
  handleSend: (e?: React.FormEvent) => void;
  isDisabled?: boolean;
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  // Note: Removed auto-focus to prevent keyboard popup issues on mobile devices
  // Users can tap the input when they want to type

  // Handle input validation based on onboarding state
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Apply input validation based on the current step
    if (
      onboarding === "existing-phone" ||
      onboarding === "new-customer-phone"
    ) {
      // For phone number input, only allow digits up to 10 characters
      const sanitized = value.replace(/\D/g, "").slice(0, 10);
      setInput(sanitized);
    } else if (
      onboarding === "existing-otp" ||
      onboarding === "new-customer-otp"
    ) {
      // For OTP input, only allow digits up to 6 characters
      const sanitized = value.replace(/\D/g, "").slice(0, 6);
      setInput(sanitized);
    } else {
      // For regular chat input, allow any text
      setInput(value);
    }
  };

  const getPlaceholder = () => {
    switch (onboarding) {
      case "existing-phone":
      case "new-customer-phone":
        return "Enter your 10-digit phone number...";
      case "existing-otp":
      case "new-customer-otp":
        return "Enter 6-digit verification code...";
      case "new-upload":
        return "Enter your details or questions...";
      case "upload-error":
        return "Policy analysis failed. Please try uploading again...";
      case "policy-suggestions":
        return "Select a suggestion above or type your own question...";
      case "done":
        return isDisabled
          ? "Getting you a response..."
          : "Type your message...";
      default:
        return "Complete selection to continue...";
    }
  };

  const getInputType = () => {
    switch (onboarding) {
      case "existing-phone":
      case "new-customer-phone":
        return "tel";
      case "existing-otp":
      case "new-customer-otp":
        return "tel";
      default:
        return "text";
    }
  };

  const getInputMode = () => {
    switch (onboarding) {
      case "existing-phone":
      case "new-customer-phone":
      case "existing-otp":
      case "new-customer-otp":
        return "numeric";
      default:
        return "text";
    }
  };

  const getAutoComplete = () => {
    switch (onboarding) {
      case "existing-phone":
      case "new-customer-phone":
        return "tel";
      case "existing-otp":
      case "new-customer-otp":
        return "one-time-code";
      default:
        return "off";
    }
  };

  const getAriaLabel = () => {
    switch (onboarding) {
      case "existing-phone":
      case "new-customer-phone":
        return "Enter your phone number";
      case "existing-otp":
      case "new-customer-otp":
        return "Enter verification code";
      case "new-upload":
        return "Enter your details";
      case "upload-error":
        return "Input disabled due to policy analysis error";
      case "policy-suggestions":
        return "Select a suggestion or type your question";
      case "done":
        return isDisabled ? "Verification required" : "Type your message";
      default:
        return "Chat input";
    }
  };

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        if (
          !input.trim() ||
          onboarding === "select" ||
          onboarding === "upload-error" ||
          isDisabled
        )
          return;
        handleSend(e);
      }}
      className="p-4 border-t border-gray-200 flex items-center bg-white"
      aria-label="Chat message form"
    >
      <div
        className={`flex-1 relative ${
          isFocused ? "ring-2 ring-[#1c85c4] rounded-xl" : ""
        } ${isDisabled || onboarding === "upload-error" ? "opacity-60" : ""}`}
      >
        <input
          ref={inputRef}
          type={getInputType()}
          inputMode={getInputMode()}
          autoComplete={getAutoComplete()}
          className={`w-full rounded-xl bg-gray-100 text-gray-900 px-5 py-3 text-xs border-none focus:outline-none transition placeholder-gray-400 pr-12 ${
            isDisabled || onboarding === "upload-error"
              ? "cursor-not-allowed bg-gray-50"
              : ""
          }`}
          placeholder={getPlaceholder()}
          value={input}
          onChange={handleInputChange}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey) handleSend(e);
          }}
          aria-label={getAriaLabel()}
          disabled={
            onboarding === "select" ||
            onboarding === "upload-error" ||
            isDisabled
          }
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          readOnly={isDisabled}
        />
        <button
          type="submit"
          className={`absolute right-3 top-1/2 transform -translate-y-1/2 w-8 h-8 flex items-center justify-center rounded-full
            ${
              !input.trim() ||
              onboarding === "select" ||
              onboarding === "upload-error" ||
              isDisabled
                ? "text-gray-300 cursor-not-allowed"
                : "text-[#1c85c4] hover:text-white hover:bg-[#1c85c4] focus:bg-[#1c85c4] focus:text-white focus:outline-none focus:ring-2 focus:ring-blue-300"
            }
            transition-all duration-200 ease-in-out
          `}
          disabled={
            !input.trim() ||
            onboarding === "select" ||
            onboarding === "upload-error" ||
            isDisabled
          }
          aria-label="Send message"
        >
          <SendIcon />
        </button>
      </div>
    </form>
  );
};
