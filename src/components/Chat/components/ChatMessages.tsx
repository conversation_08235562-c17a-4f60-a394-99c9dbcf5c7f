import React, { useEffect } from "react";
import { MessageType } from "@/components/Chat/types/types";
import { Message } from "@/components/Chat/components/Message";
import { Policy } from "@/components/Chat/api/policyFetch";
import { PolicyDetails } from "@/components/Chat/api/policyWorkflow";
import { Product } from "@/components/Chat/api/catalogFetch";

const ChatMessagesComponent = ({
  messages,
  chatAreaRef,
  onSelect,
  onUploadComplete,
  onPolicySelected,
  onVerifyPhone,
  onPolicyDocumentChoice,
  onProductSelected,
  onSuggestionClick,
  isProductSelected = false,
  isSuggestionSelected = false,
  suggestionsContext = "policy",
  isSuggestionsDisabled = false,
  customerSelected = false,
  productSelectionCompleted = false,
  policyDocumentChoiceMade = false,
  policySelectionCompleted = false,
}: {
  messages: MessageType[];
  chatAreaRef: React.RefObject<HTMLDivElement>;
  onSelect: (type: "existing" | "new") => void;
  onUploadComplete?: (
    file: File,
    documentKey?: string,
    policyDetails?: PolicyDetails
  ) => void;
  onPolicySelected?: (policy: Policy) => void;
  onVerifyPhone?: (phoneNumber?: string) => void;
  onPolicyDocumentChoice?: (hasDocument: boolean) => void;
  onProductSelected?: (product: Product & { insurer_name: string }) => void;
  onSuggestionClick?: (suggestion: string) => void;
  isProductSelected?: boolean;
  isSuggestionSelected?: boolean;
  suggestionsContext?: "policy" | "product";
  isSuggestionsDisabled?: boolean;
  customerSelected?: boolean;
  productSelectionCompleted?: boolean;
  policyDocumentChoiceMade?: boolean;
  policySelectionCompleted?: boolean;
}) => {
  // Scroll to bottom when messages change
  useEffect(() => {
    if (chatAreaRef.current) {
      // Use requestAnimationFrame to ensure DOM has updated before scrolling
      requestAnimationFrame(() => {
        if (chatAreaRef.current) {
          // Always scroll to the bottom when new messages arrive
          chatAreaRef.current.scrollTop = chatAreaRef.current.scrollHeight;
        }
      });
    }
  }, [messages, chatAreaRef]);

  return (
    <div
      ref={chatAreaRef}
      className="flex-1 overflow-y-auto p-6 space-y-6 pr-8 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 min-h-0"
      style={{
        scrollBehavior: "smooth",
        overscrollBehavior: "contain",
        position: "relative",
        height: "100%",
      }}
      aria-live="polite"
      aria-label="Chat message history"
    >
      {messages.map((msg, i) => (
        <Message
          key={i}
          msg={msg}
          onSelect={onSelect}
          onUploadComplete={onUploadComplete}
          onPolicySelected={onPolicySelected}
          onVerifyPhone={onVerifyPhone}
          onPolicyDocumentChoice={onPolicyDocumentChoice}
          onProductSelected={onProductSelected}
          onSuggestionClick={onSuggestionClick}
          isProductSelected={isProductSelected}
          isSuggestionSelected={isSuggestionSelected}
          suggestionsContext={suggestionsContext}
          isSuggestionsDisabled={isSuggestionsDisabled}
          customerSelected={customerSelected}
          productSelectionCompleted={productSelectionCompleted}
          policyDocumentChoiceMade={policyDocumentChoiceMade}
          policySelectionCompleted={policySelectionCompleted}
        />
      ))}
      <div className="h-px" />{" "}
      {/* Extra space at the end to ensure good scrolling */}
    </div>
  );
};

export { ChatMessagesComponent as ChatMessages };
