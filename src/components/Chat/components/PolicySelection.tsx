import { useState, useEffect, useRef, useCallback } from "react";
import { Policy, useGetPolicies } from "@/components/Chat/api/policyFetch";
import { BsCheckCircleFill } from "react-icons/bs";
import { FaCalendarAlt, FaRupeeSign, FaShieldAlt } from "react-icons/fa";
import { formatCurrency } from "@/utils/formatters";

type PolicySelectionProps = {
  phoneNumber: string;
  onPolicySelected: (policy: Policy) => void;
  disabled?: boolean;
};

export const PolicySelection = ({
  phoneNumber,
  onPolicySelected,
  disabled = false,
}: PolicySelectionProps) => {
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [selectedPolicyId, setSelectedPolicyId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const fetchedRef = useRef(false);

  const getPoliciesMutation = useGetPolicies();

  // Fetch policies once on mount
  useEffect(() => {
    // Skip if already fetched or no phone number
    if (fetchedRef.current || !phoneNumber) return;

    const fetchPolicies = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Mark as fetched immediately to prevent double calls
        fetchedRef.current = true;

        const result = await getPoliciesMutation.mutateAsync();

        setPolicies(result.payload.policies || []);
      } catch (err) {
        fetchedRef.current = false;
        setError(
          err instanceof Error
            ? err.message
            : "Failed to fetch policies. Please try again."
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchPolicies();
  }, [phoneNumber]); // Remove getPoliciesMutation from deps array

  // Handle policy selection
  const handleSelectPolicy = useCallback(
    (policy: Policy) => {
      if (hasSubmitted || disabled) return;

      setSelectedPolicyId(policy.policy_id);
      setHasSubmitted(true);
      onPolicySelected(policy);
    },
    [hasSubmitted, disabled, onPolicySelected]
  );

  const handleRetry = () => {
    fetchedRef.current = false;
    setError(null);
    setIsLoading(true);

    const fetchPolicies = async () => {
      try {
        const result = await getPoliciesMutation.mutateAsync();
        setPolicies(result.payload.policies || []);
        setError(null);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Failed to fetch policies. Please try again."
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchPolicies();
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat("en-IN", {
        day: "numeric",
        month: "short",
        year: "numeric",
      }).format(date);
    } catch (e) {
      return dateString;
    }
  };

  if (isLoading) {
    return (
      <div className="w-full bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-4">
        <div className="p-4">
          <div className="animate-pulse space-y-4">
            <div className="h-5 bg-gray-200 rounded w-1/3"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-4">
        <div className="p-4">
          <div className="text-center py-4">
            <p className="text-red-500 font-medium">{error}</p>
            <button
              onClick={handleRetry}
              className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg text-xs hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (policies.length === 0) {
    return (
      <div className="w-full max-w-[400px] bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-4">
        <div className="p-4">
          <div className="text-center py-6">
            <p className="text-gray-700 font-medium">
              No policies found for this phone number.
            </p>
            <p className="text-gray-500 text-xs mt-2">
              We couldn&apos;t find any policies linked to this phone number in
              our system.
            </p>
            <button
              onClick={() =>
                onPolicySelected({
                  policy_id: "reset_flow",
                  reset_to_selection: true,
                } as any)
              }
              className="mt-4 px-4 py-2 bg-[#1c85c4] text-white rounded-lg text-xs hover:bg-blue-700 transition-colors"
              aria-label="Go back to customer selection"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`w-full bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-4 ${
        disabled ? "opacity-60" : ""
      }`}
      role="region"
      aria-label="Select your policy"
    >
      <div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xs font-semibold text-gray-800 flex items-center gap-1.5">
              <FaShieldAlt className="text-blue-600 text-xs" />
              {disabled ? "Policy Selected" : "Select Your Policy"}
            </h3>
            <p className="text-xs text-gray-600 mt-0.5">
              {disabled
                ? "You have successfully selected your policy"
                : `We found ${policies.length} ${
                    policies.length === 1 ? "policy" : "policies"
                  } associated with your phone number`}
            </p>
          </div>
          {disabled && (
            <div className="flex items-center text-green-600">
              <BsCheckCircleFill className="text-sm" />
            </div>
          )}
        </div>
      </div>

      <div className="p-3">
        <div className="space-y-2">
          {policies.map((policy) => (
            <div
              key={policy.policy_id}
              className={`relative border rounded-lg p-3 transition-all duration-200 ${
                disabled
                  ? "cursor-not-allowed"
                  : "cursor-pointer hover:border-blue-400 hover:shadow-md"
              } ${
                selectedPolicyId === policy.policy_id
                  ? "border-blue-500 bg-blue-50 shadow-md"
                  : "border-gray-200 hover:bg-gray-50"
              } ${
                disabled && selectedPolicyId !== policy.policy_id
                  ? "opacity-40"
                  : ""
              }`}
              onClick={() => handleSelectPolicy(policy)}
              role="button"
              tabIndex={hasSubmitted || disabled ? -1 : 0}
              aria-pressed={selectedPolicyId === policy.policy_id}
              aria-disabled={hasSubmitted || disabled}
              onKeyDown={(e) => {
                if (
                  !hasSubmitted &&
                  !disabled &&
                  (e.key === "Enter" || e.key === " ")
                ) {
                  e.preventDefault();
                  handleSelectPolicy(policy);
                }
              }}
            >
              {/* Selection indicator */}
              {selectedPolicyId === policy.policy_id && (
                <div className="absolute top-2 right-2">
                  <BsCheckCircleFill className="text-blue-500 text-sm" />
                </div>
              )}

              <div className="flex-1 pr-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 text-xs leading-tight">
                      {policy.product_name}
                    </h4>
                    <p className="text-xs text-gray-500 mt-0.5">
                      {policy.insurer_name}
                    </p>
                  </div>
                </div>

                {/* Key Details in 2 rows */}
                <div className="space-y-1.5">
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center text-gray-600">
                      <FaShieldAlt
                        className="text-blue-600 mr-1.5"
                        style={{ fontSize: "10px" }}
                      />
                      <span className="font-medium">
                        {formatCurrency(parseFloat(policy.sum_insured))}
                      </span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <FaCalendarAlt
                        className="text-orange-600 mr-1.5"
                        style={{ fontSize: "10px" }}
                      />
                      <span>{formatDate(policy.expiry_date)}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center text-gray-600">
                      <FaRupeeSign
                        className="text-green-600 mr-1.5"
                        style={{ fontSize: "10px" }}
                      />
                      <span>
                        {formatCurrency(parseFloat(policy.premium_amount))}
                      </span>
                    </div>
                    <div className="text-xs text-gray-500 truncate max-w-[120px]">
                      #{policy.insurer_policy_number}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {disabled && (
          <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center text-green-700">
              <BsCheckCircleFill className="mr-1.5 text-green-600 text-xs" />
              <span className="text-xs font-medium">
                Policy selection completed successfully
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
