import { ReactNode } from "react";

type MessageType =
  | { type: "ai"; text: string; component?: ReactNode }
  | { type: "ai-markdown"; text: string; component?: ReactNode }
  | { type: "user"; text: string }
  | { type: "select-customer" }
  | { type: "loading" }
  | { type: "policy-upload" }
  | { type: "policy-selection"; phone: string }
  | { type: "verification-required" }
  | { type: "policy-document-choice" }
  | { type: "product-catalog" }
  | { type: "policy-suggestions" };

type OnboardingState =
  | "select"
  | "existing-phone"
  | "existing-otp"
  | "existing-policy"
  | "new-upload"
  | "done"
  | "new-customer-phone"
  | "new-customer-otp"
  | "upload-error"
  | "new-policy-choice"
  | "new-catalog"
  | "policy-suggestions";

export type { MessageType, OnboardingState };
