"use client";

import Container from "../globals/Container";

const TermsAndConditions = () => {
  return (
    <div>
      <Container>
        <div className="px-5 md:px-0">
          <h1 className=" text-[28px] mb-8 text-gray900">Terms & Conditions</h1>

          <div className="mb-24">
            <h2 className=" text-2xl font-semibold text-gray900 mb-4">
              Grievance Redressal
            </h2>
            <p className=" text-base text-gray800 mb-6">
              Prost Insurance Brokers Private Limited (hereinafter referred to
              as the “Company”), is concerned about the complaint/grievance of
              the users and/or clients.
            </p>
            <p className=" text-base text-gray800 mb-6">
              This grievance redressal aims at minimizing the instances of
              customer complaints and grievances through proper service delivery
              and review mechanism to ensure prompt redressal of customer
              complaints and grievances. The review mechanism is aimed at
              helping in the identification of shortcomings in service delivery,
              if any, and compliance with the stipulations of IRDAI Regulations.
            </p>
            <p className=" text-base text-gray800 mb-6">
              To ensure that the company’s grievance redressal mechanism is more
              meaningful and effective, a system/ process has been designed.
              Such a system would ensure that the redressal provided is just and
              fair and is permissible within the given framework of the rules
              and regulations. This grievance redressal would be made available
              at all regional offices/branch offices of the company. The
              concerned employees in the respective branch offices shall be made
              aware of the complaints/grievance handling process.
            </p>
            <h2 className=" text-2xl font-semibold text-gray900 mb-4">
              Process
            </h2>
            <p className=" text-base text-gray800 mb-6">
              The process followed for addressing queries shall be:
            </p>

            <p className=" text-base text-gray800 mb-6">
              1. For any grievance, please contact us with the details by
              sending an email to the Grievance team, on ‘<EMAIL>’
              or to our Principal Officer Mr. Tarun <NAME_EMAIL>.
            </p>

            <p className=" text-base text-gray800 mb-6">
              2. You can also write to Prost Insurance Broking Services Pvt Ltd.
              3rd floor, 91springboard, MG Road, Gopala Krishna Complex 45/3,
              Residency Road, Mahatma Gandhi Rd, Bengaluru, Karnataka 560025.
              Phone no: +916364334343
            </p>
            <p className=" text-base text-gray800 mb-6">
              3. The grievance will be acknowledged within 2 working days of its
              receipt
            </p>
            <p className=" text-base text-gray800 mb-6">
              4. The grievance will be acknowledged within 2 working days of its
              receipt
            </p>
            <p className=" text-base text-gray800 mb-6">
              5. The company shall exercise all efforts to address it, including
              intimation to the respective Insurance company for a quick
              redressal.
            </p>
            <p className=" text-base text-gray800 mb-6">
              6. If the decision/resolution provided by the Principal Officer is
              not acceptable, the grievance may be further escalated as per the
              process specified by the Insurance Regulatory Development
              Authority of India (IRDAI) in the below mentioned link:
              https://www.policyholder.gov.in/report.aspx#
            </p>
            <p className=" text-base text-gray800 mb-6">
              In case of no reply from the Complainant, within 2 weeks from the
              date, any clarification was provided, the Company shall treat the
              Complaint as Closed.
            </p>
          </div>

          <div className="mb-24">
            <h2 className=" text-2xl font-semibold text-gray900 mb-4">
              Licence Information
            </h2>

            <div className="flex items-center">
              <a
                href={`${process.env.NEXT_PUBLIC_S3_URL}/license-info.pdf`}
                target="_blank"
              >
                <span className="text-base text-blue600 cursor-pointer mr-1 hover:underline">
                  Click here
                </span>
              </a>
              <span className="text-base text-gray800">
                to get license information
              </span>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default TermsAndConditions;
