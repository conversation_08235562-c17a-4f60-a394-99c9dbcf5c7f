"use client";

import Container from "../globals/Container";
import { data } from "./data";
import { useState } from "react";
import Link from "next/link";
import BookACallBtn from "../globals/BookACall";

const HealthInsuranceGuidePage = () => {
  const [activeOpt, setActiveOpt] = useState(0);
  return (
    <div className=" mb-16  px-5 xl:px-0">
      <Container>
        <h1 className="font-semibold text-gray900 text-4xl text-center mt-40 mb-20">
          Health Insurance - Complete Guide
        </h1>
        <div className="flex overflow-x-auto gap-6 md:hidden mb-5">
          {data.map((opt, idx) => (
            <div
              key={idx}
              className={`px-5 py-1 bg-gray200 rounded-full text-slateGrey font-semibold text-lg cursor-pointer flex items-center justify-center whitespace-nowrap ${
                activeOpt === idx ? "bg-blue200" : ""
              }`}
              onClick={() => setActiveOpt(idx)}
            >
              {opt.title}
            </div>
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="hidden md:block border-r-2 border-gray200 overflow-y-auto max-h-[500px]">
            {data.map((opt, idx) => (
              <div
                key={idx}
                className={`px-5 py-3 w-[90%] rounded-lg ${
                  activeOpt === idx ? "bg-gray200" : ""
                }`}
              >
                <h2
                  className="text-slateGrey font-semibold text-lg cursor-pointer"
                  onClick={() => setActiveOpt(idx)}
                >
                  {opt.title}
                </h2>
              </div>
            ))}
          </div>
          <div className="col-span-2 flex flex-col  justify-between ">
            <div>
              <h2 className="text-blue600 font-semibold text-3xl mb-8">
                {data[activeOpt].title}
              </h2>
              <p className="text-slateGrey text-xl">{data[activeOpt].desc}</p>
            </div>

            {/* Book a call banner */}
            <div className="shadow-shadow100 bg-blue100 rounded-2xl mt-20 py-4 px-4 flex items-center justify-center md:justify-between flex-col md:flex-row">
              <div>
                <h2 className="font-semibold text-2xl text-gray900 mb-2">
                  Want to know more
                </h2>
              </div>
              <div>{/* <BookACallBtn /> */}</div>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default HealthInsuranceGuidePage;
