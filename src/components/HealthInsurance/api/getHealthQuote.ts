"use server";

import {
  QuickQuoteResponse,
  UserFormResponse,
} from "@/components/QuickQuote/types";
import { number } from "yup";
import { z } from "zod";

export const getHealthQuote = async (prevState: any, formData: FormData) => {
  const adultCount = formData.get("adultCount");
  const childCount = formData.get("childCount");

  const formResponseSchema = z.object({
    adults: z.record(
      z.string(),
      z
        .number({ coerce: true, required_error: "Age cannot be empty!" })
        .min(18, { message: "Age cannot be less than 18 years!" })
        .max(100, { message: "Age cannot be greater than 100 years!" })
    ),
    children: z.record(
      z.string(),
      z
        .number({ coerce: true })
        .min(1, { message: "Age cannot be less than 1!" })
        .max(25, { message: "Age cannot be greater than 25 years!" })
    ),
    pincode: z
      .string({ required_error: "Pincode cannot be empty!" })
      .regex(/^[1-9][0-9]{5}$/, { message: "Invalid pincode!" })
      .refine(
        async (val) => {
          const url = `${process.env.NEXT_PUBLIC_BROKER_URL}/sales/validate_pincode/v1/?pincode=${val}`;
          const response = await fetch(url);

          if (
            (response?.status >= 400 && response.status <= 499) ||
            (response && response.status >= 500 && response.status <= 599)
          ) {
            return false;
          }

          return true;
        },
        { message: "Invalid pincode!" }
      ),
    sumInsured: z.coerce.number({ required_error: "Choose sumInsured!" }),
  });

  let formResponse = {
    adults: {},
    children: {},
    pincode: undefined,
    sumInsured: "",
  };

  let i = 1;

  // @ts-ignore
  while (i <= adultCount) {
    // @ts-ignore
    formResponse.adults[`adult${i}`] =
      formData.get(`adult${i}`) === "" ? 0 : formData.get(`adult${i}`);
    i++;
  }

  // @ts-ignore
  if (childCount && childCount > 0) {
    i = 1;
    // @ts-ignore
    while (i <= childCount) {
      // @ts-ignore
      formResponse.children[`child${i}`] = formData.get(`child${i}`);
      i++;
    }
  }

  if (formData.get("pincode") !== "")
    // @ts-ignore
    formResponse.pincode = formData.get("pincode");

  // @ts-ignore
  formResponse.sumInsured = formData.get("sumInsured");

  const parsedObj = await formResponseSchema.safeParseAsync(formResponse);

  if (parsedObj.success) {
    const user_response = {
      pincode: formResponse.pincode,
      proposer_gender: "male",
      members: {},
    };

    Object.keys(formResponse.adults).map((a, idx) => {
      if (idx === 0) {
        // @ts-ignore
        user_response.members["self"] = formResponse.adults[a];
      } else {
        // @ts-ignore
        user_response.members["spouse"] = formResponse.adults[a];
      }
    });

    Object.keys(formResponse.children).map((c, idx) => {
      // @ts-ignore
      user_response.members[`son${idx + 1}`] = formResponse.children[c];
    });

    // console.log("User response : ", user_response);

    const url = `${
      process.env.NEXT_PUBLIC_BROKER_URL
    }/quick_recommendations/v1/?user_response=${JSON.stringify(
      user_response
    )}&recommendation_tags=health-recommendations&sum_insured=${
      formResponse.sumInsured
    }`;

    // console.log("URL >>>>> ", url);
    const response = await fetch(url, {
      method: "GET", // Specify the HTTP method (default is GET)
      headers: {
        "Content-Type": "application/json", // Set content type for JSON data
        token: `${process.env.NEXT_PUBLIC_USER_TOKEN}`,
      },
    });
    let data;

    // console.log("Response >>>>> ", response);
    if (response.ok) {
      data = await response.json();
    } else {
      return {
        isSuccess: false,
        errors: { api: "Something went wrong!" },
        data: {},
      };
    }

    return {
      isSuccess: true,
      errors: {},
      data: data,
    };
  } else {
    // console.log("Error : ", JSON.stringify(parsedObj.error.format()));
    return {
      isSuccess: false,
      errors: parsedObj.error.format(),
      data: {},
    };
  }
};
