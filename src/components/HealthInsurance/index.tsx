import { HealthInsuranceLPDataObject } from "./types";
import BookACallBtn from "../globals/BookACall";
import InsuranceDesc from "../globals/InsuranceAllProducts/InsuranceDesc";
import BenefitsOfHI from "./components/BenefitsOfHI";
import HowToBuyInsuranceFromOA from "../globals/HowToBuyInsuranceFromOA";
import Checklist from "./components/Checklist";
import TopHealthPlans from "./components/TopHealthPlans";
import DocsRequired from "../globals/DocsRequired";

import Testimonials from "../globals/Testimonials";
import Hero from "./components/Hero";
import Container from "../globals/Container";
import PartnerCompanies from "../Home/components/PartnerCompanies";
import WhyBuyHI from "./components/WhyBuyHI";
import HowToChooseHI from "../globals/HIinIndia";
import ContactForm from "../globals/ContactForm";
import TalkToExpert from "../Home/components/TalkToExpert";
import { Suspense } from "react";
const HealthInsurance = ({
  data,
  otherCompanies,
}: {
  data: HealthInsuranceLPDataObject;
  otherCompanies: any;
}) => {
  return (
    <Container>
      <div className="font-manrope">
        <Hero
          data={data.attributes.hero}
          category="health"
          companyData={otherCompanies}
        />

        <InsuranceDesc
          title={data.attributes.whatIsHI.title}
          description={data.attributes.whatIsHI.description}
        />
        <Suspense>
          <PartnerCompanies partners={otherCompanies} />
        </Suspense>
        <WhyBuyHI data={data.attributes.whyBuyHI} />
        <BenefitsOfHI data={data.attributes.benefitsOfHI} />
        <Checklist data={data.attributes.HIChecklist} />
        <HowToChooseHI data={data.attributes.howToChoose} />
        <HowToBuyInsuranceFromOA data={data.attributes.howToBuy} />

        <TopHealthPlans data={data.attributes.HITopPlans} />
        {/* <Eligibility data={data.attributes.eligibility} /> */}
        <DocsRequired data={data.attributes.requiredDocs} />
        <TalkToExpert />
        <Testimonials testimonials={data.attributes.testimonials.testimonial} />
        <Suspense>
          <ContactForm />
        </Suspense>
        {/* <div className=" bg-green-1 inline-block fixed bottom-10 right-5 md:bottom-20 md:right-0 md:transform md:translate-x-12 md:-rotate-90 md:rounded-t-2xl md:rounded-b-none rounded-full z-30">
          <BookACallBtn className="py-3 px-6 md:px-8 transparent text-ntrl-white" />
        </div> */}
      </div>
    </Container>
  );
};

export default HealthInsurance;
