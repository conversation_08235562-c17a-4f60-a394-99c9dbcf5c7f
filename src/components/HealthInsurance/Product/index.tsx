"use client";
import Hero from "../../globals/Hero";
import { Suspense, useEffect } from "react";
import { HealthVariantData } from "../types";
import SideMenu from "@/components/globals/SideMenu";
import HighlightedFeatures from "./components/HighlightedFeatures";
import Verdict from "@/components/globals/Products/Verdict";
import About from "@/components/globals/Products/About";
import Features from "@/components/globals/Products/Features";
import Exclusions from "@/components/globals/Products/Exclusions";
import AddOns from "@/components/globals/Products/AddOns";
import Plans from "@/components/Company/components/Plans";
import NetworkHospitals from "./components/NetworkHospitals";
import WhyOneAssure from "@/components/globals/Products/WhyOneAssure";
import BookACall from "@/components/globals/Products/BookACall";
import Faqs from "@/components/globals/Products/Faq";
import { RelatedBlogs } from "@/components/globals/Products/RelatedBlogs";
import Container from "@/components/globals/Container";
import Statistics from "./components/Statistics";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import { useSessionStorage } from "usehooks-ts";
import Breadcrumb from "@/components/globals/Breadcrumb";

export const HealthProductRoot: React.FC<{
  product: HealthVariantData;
  category?: string;
}> = (props) => {
  const { product, category = "health" } = props;
  const menuItems = [
    ...(product.highlightedFeatures.length > 0
      ? [{ title: "Highlighted Feature", id: "highlightedFeature" }]
      : []),
    ...(product.verdict ? [{ title: "Our Verdict", id: "verdict" }] : []),
    ...(product.aboutThePlan
      ? [{ title: "About the Plan", id: "aboutThePlan" }]
      : []),
    ...(product.features.length > 0
      ? [{ title: "Features", id: "features" }]
      : []),
    ...(product.exclusions.length > 0
      ? [{ title: "Exclusions", id: "exclusions" }]
      : []),
    ...(product.addOns.length > 0 ? [{ title: "Add-Ons", id: "addOns" }] : []),
    ...(product.variants.length > 0
      ? [{ title: "Related Variants", id: "variants" }]
      : []),
    ...(product.company?.data?.attributes?.legecy
      ? [{ title: "About the Company", id: "aboutTheCompany" }]
      : []),
    ...(product.faqs.length > 0 ? [{ title: "FAQs", id: "faqs" }] : []),
    ...(product.blogs.data.length > 0
      ? [{ title: "Related Blogs", id: "relatedBlogs" }]
      : []),
  ];

  const pathname = usePathname();
  const router = useRouter();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);
  useEffect(() => {
    if (typeof window !== "undefined") {
      router.replace(
        `${pathname}?${utm_source !== null ? `utm_source=${utm_source}` : ""}${
          utm_medium !== null ? `&utm_medium=${utm_medium}` : ""
        }${utm_campaign !== null ? `&utm_campaign=${utm_campaign}` : ""}${
          utm_content !== null ? `&utm_content=${utm_content}` : ""
        }${utm_term !== null ? `&utm_term=${utm_term}` : ""}`
      );
    }
  }, []);

  const transformedData = product.variants.map((variant: any, idx: number) => {
    const variantSlug = variant.relatedVariant.data.attributes.slug;
    const variantName = variant.relatedVariant.data.attributes.name;
    const companyLogo =
      variant.relatedVariant.data.attributes.company.data.attributes.logo.data
        .attributes.url;
    const companyName =
      variant.relatedVariant.data.attributes.company.data.attributes.name;
    const companySlug =
      variant.relatedVariant.data.attributes.company.data.attributes.slug;
    return {
      id: idx,
      attributes: {
        name: variantName,
        slug: variantSlug,
        logo: companyLogo,
        companyName: companyName,
        companySlug: companySlug,
      },
    };
  });

  // console.log("Rating", product.rating);
  return (
    <div className="px-5 lg:px-[100px] bg-ntrl-white md:bg-soft-grey font-manrope pb-5 tracking-tightest border-t-blue-5 md:border-0 border-[.5px]">
      <Container>
        <div className="pt-3 pb-1 md:pt-5 md:pb-3">
          <Suspense>
            <Breadcrumb
              path={[
                "home",
                "health-insurance",
                product.company.data.attributes.slug,
                product.slug,
              ]}
            />
          </Suspense>
        </div>
        <div className="grid grid-cols-12 gap-5">
          <SideMenu
            menuItems={menuItems}
            classsName={menuItems.length < 3 ? "lg:hidden" : ""}
          />
          <div
            className={`col-span-12 ${
              menuItems.length < 3 ? "lg:col-span-12" : "lg:col-span-10"
            }`}
          >
            <Suspense>
              <Hero
                name={product.name}
                hero={product.hero}
                company={product.company.data.attributes}
                category={category}
                rating={product?.rating}
              />
            </Suspense>
            {product.highlightedFeatures.length > 0 && (
              <HighlightedFeatures
                features={product.highlightedFeatures}
                docs={product.policyDocs}
              />
            )}
            {product.verdict && <Verdict oneAssureVerdict={product.verdict} />}
            {product.aboutThePlan && (
              <About
                id="aboutThePlan"
                title="About the Plan"
                about={product.aboutThePlan}
              />
            )}
            {product.rating && <Statistics rating={product.rating} />}
            {product.features.length > 0 && (
              <Features features={product.features} />
            )}
            {product.exclusions.length > 0 && (
              <Exclusions exclusions={product.exclusions} />
            )}
            {product.addOns.length > 0 && <AddOns addOns={product.addOns} />}
            {product.variants.length > 0 && (
              <Plans
                title="Related Variants"
                category={category}
                variants={transformedData}
              />
            )}
            {product.hero?.networkHospitals.networkHospitalsURL &&
              product.whyOneAssure && (
                <div className="grid gap-x-8 grid-cols-5">
                  <NetworkHospitals
                    classname="col-span-5 lg:col-span-3"
                    url={product.hero.networkHospitals.networkHospitalsURL!}
                  />
                  <WhyOneAssure
                    className="col-span-5 lg:col-span-2"
                    data={product.whyOneAssure}
                  />
                </div>
              )}
            {product.company?.data?.attributes?.legecy && (
              <About
                id="aboutTheCompany"
                title="About the Company"
                about={product.company.data.attributes.legecy}
              />
            )}
            <BookACall classname="md:py-10 p-5 md:px-[50px]" />
            {product.faqs.length > 0 && (
              <Faqs title="Frequently Asked Questions" faqs={product.faqs} />
            )}
            {product.blogs.data.length > 0 && (
              <RelatedBlogs blogs={product.blogs} />
            )}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default HealthProductRoot;
