"use client";
import { CheckBadgeIcon } from "@heroicons/react/24/solid";
import { FiInfo } from "react-icons/fi";
import { FaRegHospital } from "react-icons/fa6";

const Statistics = ({
  rating,
}: {
  rating:
    | {
        id: number;
        coverage: {
          id: number;
          title: string;
          maxScore: number;
          score: number;
        };
        claimSettlement: {
          id: number;
          title: string;
          maxScore: number;
          score: number;
        };
        hospitalNetwork: {
          id: number;
          title: string;
          maxScore: number;
          score: number;
        };
        coPayment: {
          id: number;
          title: string;
          maxScore: number;
          score: number;
        };
        waitingPeriods: {
          id: number;
          title: string;
          maxScore: number;
          score: number;
        };
        noClaimBonus: {
          id: number;
          title: string;
          maxScore: number;
          score: number;
        };
      }
    | undefined;
}) => {
  const calculateHeight = (value: number, maxValue: number) => {
    return `${(value / maxValue) * 144}px`;
  };

  return (
    <section
      className="mt-[10px] border-[0.5]px md:mt-5 scroll-m-28 bg-white rounded-3xl mb-5 p-5 md:p-10"
      id="stats"
    >
      <div className="text-[22px]/[28px] md:text-[32px]/[36px] text-ntrl-black gap-2 flex items-start  py-[10px]">
        <h2 className="font-medium">Product Stats</h2>
      </div>

      <div className="w-full bg-[white] rounded-3xl mx-auto mb:5">
        <div className="">
          <div className="space-y-4 p-2 pt-5">
            <div className="flex items-center justify-between">
              <h2 className="text-4/6 font-bold">Rating</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 md:gap-9">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-3/5">{rating?.coverage.title}</span>
                  {/* <span className="text-3/5">
                    {rating?.coverage.score}/{rating?.coverage.maxScore}
                  </span> */}
                </div>
                <div className="h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-full bg-gradient-to-r from-gradient-1-blue to-gradient-1-green rounded-full"
                    style={{
                      width: `${
                        (rating?.coverage.score! / rating?.coverage.maxScore!) *
                        100
                      }%`,
                    }}
                  ></div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-3/5">
                    {rating?.claimSettlement.title}
                  </span>
                  {/* <span className="text-3/5">
                    {rating?.claimSettlement.score}/
                    {rating?.claimSettlement.maxScore}
                  </span> */}
                </div>
                <div className="h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-full bg-gradient-to-r from-gradient-1-blue to-gradient-1-green rounded-full"
                    style={{
                      width: `${
                        (rating?.claimSettlement.score! /
                          rating?.claimSettlement.maxScore!) *
                        100
                      }%`,
                    }}
                  ></div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-3/5">
                    {rating?.hospitalNetwork.title}
                  </span>
                  {/* <span className="text-3/5">
                    {rating?.hospitalNetwork.score}/
                    {rating?.hospitalNetwork.maxScore}
                  </span> */}
                </div>
                <div className="h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-full bg-gradient-to-r from-gradient-1-blue to-gradient-1-green rounded-full"
                    style={{
                      width: `${
                        (rating?.hospitalNetwork.score! /
                          rating?.hospitalNetwork.maxScore!) *
                        100
                      }%`,
                    }}
                  ></div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-3/5">{rating?.coPayment.title}</span>
                  {/* <span className="text-3/5">
                    {rating?.coPayment.score}/{rating?.coPayment.maxScore}
                  </span> */}
                </div>
                <div className="h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-full bg-gradient-to-r from-gradient-1-blue to-gradient-1-green rounded-full"
                    style={{
                      width: `${
                        (rating?.coPayment.score! /
                          rating?.coPayment.maxScore!) *
                        100
                      }%`,
                    }}
                  ></div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-3/5">
                    {rating?.waitingPeriods.title}
                  </span>
                  {/* <span className="text-3/5">
                    {rating?.waitingPeriods.score}/
                    {rating?.waitingPeriods.maxScore}
                  </span> */}
                </div>
                <div className="h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-full bg-gradient-to-r from-gradient-1-blue to-gradient-1-green rounded-full"
                    style={{
                      width: `${
                        (rating?.waitingPeriods.score! /
                          rating?.waitingPeriods.maxScore!) *
                        100
                      }%`,
                    }}
                  ></div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-3/5">{rating?.noClaimBonus.title}</span>
                  {/* <span className="text-3/5">
                    {rating?.noClaimBonus.score}/{rating?.noClaimBonus.maxScore}
                  </span> */}
                </div>
                <div className="h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-full bg-gradient-to-r from-gradient-1-blue to-gradient-1-green rounded-full"
                    style={{
                      width: `${
                        (rating?.noClaimBonus.score! /
                          rating?.noClaimBonus.maxScore!) *
                        100
                      }%`,
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Statistics;
