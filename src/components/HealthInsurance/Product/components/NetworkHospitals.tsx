import Image from "next/image";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
const NetworkHospitals: React.FC<{
  url: string;
  classname?: string;
}> = (props) => {
  return (
    <section
      id="network-hospitals"
      className={`flex mt-5 p-5 scroll-m-28 bg-gradient-to-r from-gradient-1-blue to-gradient-1-green rounded-3xl ${props.classname}`}
    >
      <div className="bg-white/10 w-full p-3 md:p-5 rounded-2xl border-[0.5px] border-white text-white relative">
        <h3 className="text-[16px]/[20px] md:text-custom-32 font-bold text-nowrap">
          Looking for a Specific Hospital ?
        </h3>
        <div className="flex flex-row justify-between items-center mt-4 md:mt-5">
          <div className="w-4/5 md:w-1/2 text-[12px]/[18px] md:text-[18px]/[26px]">
            Explore Network Hospitals
            <button
              onClick={() => window.open(props.url, "_blank")}
              className="flex flex-row gap-2 md:gap-5 items-center font-regular cursor-pointer bg-white text-primary-2 px-5 py-2 mt-4 md:my-9 rounded-xl hover:text-white hover:bg-primary-2 hover:border-white duration-300"
            >
              Go to Site
              <ArrowRightIcon className="h-3 md:h-6" />
            </button>
          </div>
          <Image
            src={
              "https://cdn.oasr.in/oa-site/cms-uploads/media/Group_83916277df.png"
            }
            fill={false}
            height={100}
            width={100}
            style={{ objectFit: "fill" }}
            alt="alt"
            className="absolute -bottom-1 right-5 md:right-10 w-28 md:w-60"
          />
        </div>
      </div>
    </section>
  );
};

export default NetworkHospitals;
