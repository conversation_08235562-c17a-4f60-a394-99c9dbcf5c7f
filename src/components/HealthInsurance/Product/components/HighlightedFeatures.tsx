"use client";

import parse, { Element } from "html-react-parser";
import Image from "next/image";
import { ArrowDownIcon } from "@heroicons/react/24/outline";
import type {
  HighlightedFeatures,
  PolicyDocs,
} from "@/components/globals/types";
const HighlightedFeatures: React.FC<{
  features: HighlightedFeatures;
  docs: PolicyDocs;
}> = (props) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black1 text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className = "text-ntrl-black text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[14px]/[18px] md:text-[16px]/[26px] text-ntrl-black font-normal";
          break;

        case "a":
          attrs.className = "text-blue-5";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  const ScrollToFeatures = () => {
    if (typeof window === "undefined") return;

    const element = document.getElementById("features");
    if (!element) return;

    element.scrollIntoView({ behavior: "smooth" });

    const newUrl = `${window.location.pathname}#features`;
    window.history.replaceState(null, "", newUrl);
  };

  return (
    <section
      id="highlightedFeature"
      className="md:mt-5 md:px-[50px] md:py-7 scroll-m-28 bg-white rounded-3xl"
    >
      <div className="p-2.5 md:p-0 flex flex-col rounded-2xl border-[0.5px] md:border-0 border-blue-5">
        <div className="text-[22px]/[28px] md:text-custom-32 text-ntrl-black gap-2 flex items-start p-2">
          <h2 className="font-semibold">Highlighted Features</h2>
        </div>
        <div className="grid grid-row gap-y-4 gap-x-20 grid-cols-1 md:grid-cols-2 py-2">
          {props.features.map((feature, index) => (
            <div key={index} className="flex flex-col items-start">
              <div className="flex justify-center gap-4 items-center mb-2">
                <div className="w-10 h-10 relative">
                  <Image
                    src={feature.icon?.data.attributes.url || ""}
                    fill={true}
                    style={{ objectFit: "contain" }}
                    alt={feature.title}
                  />
                </div>
                {/* check the text size */}
                <h3 className="text-ntrl-black font-semibold text-[14px]/[18px] md:text-[16px]/[24px]">
                  {feature.title}
                </h3>
              </div>
              <div className="">
                {/* @ts-ignore */}
                {parse(feature.description, { replace })}
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="flex justify-end gap-5 md:gap-10 my-3 md:mb-2">
        {props.docs.map((doc, index) => (
          <div
            key={index}
            className="flex items-center gap-1 py-1 md:py-2 px-2 md:px-4 rounded-3xl border-[0.5px] border-blue-5 bg-white text-blue-5 cursor-pointer hover:bg-blue-5 hover:text-white transition-colors duration-300"
            onClick={() =>
              window.open(doc.document.data[0].attributes.url, "_blank")
            }
          >
            <p className="text-[14px]/[20px] md:text-[16px]/[20px]">
              {doc.label}
            </p>
            <ArrowDownIcon className="h-3 md:h-5" />
          </div>
        ))}
        <div
          className="hidden md:flex items-center gap-1 py-1 md:py-2 px-2 md:px-4 rounded-3xl border-[0.5px] border-blue-5 bg-white text-blue-5 cursor-pointer hover:bg-blue-5 hover:text-white transition-colors duration-300"
          onClick={ScrollToFeatures}
        >
          <p className="text-[10px]/[12px] md:text-[16px]/[20px]">
            Explore more features
          </p>
        </div>
      </div>
    </section>
  );
};

export default HighlightedFeatures;
