import Container from "@/components/globals/Container";
import { HITopPlans } from "../types";
import parse from "html-react-parser";
import { Element } from "html-react-parser";
import Image from "next/image";

const TopHealthPlans = ({ data }: { data: HITopPlans }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-blue400 text-3xl font-semibold my-5";
          break;

        case "h3":
          attrs.className = "text-blue400 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-blue400 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "font-normal text-sm md:text-lg text-ntrl-black mb-5";
          break;

        case "a":
          attrs.className = "text-primary-green-2 font-semibold my-0";
          break;

        case "table":
          attrs.className = "mx-auto p-5 text-left";
          break;

        case "tr":
          attrs.className =
            "border-b border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;
        case "td":
          attrs.className =
            "border-r border-ntrl-outline bg-ntrl-white p-5 text-sm md:text-lg text-left";
          break;

        case "figure":
          attrs.className =
            "overflow-x-auto min-w-[300px] border border-ntrl-outline";
          attrs.style = "border-radius: 40px;";
          break;
        case "strong":
          attrs.className = "text-secondary-green-3 text-sm md:text-lg";
          break;
      }
      return domNode;
    }
  };
  return (
    <div className="md:mb-14 mb-5">
      <Container>
        {/* Heading */}
        <div className="mb-5">
          <div className="flex items-center justify-center  md:text-[48px]/[58px]  text-ntrl-black gap-2">
            <h2 className="md:text-3xl text-lg font-medium text-ntrl-black-1 ">
              {data.title}
            </h2>
          </div>

          <p className="md:text-[14px]/[20px] text-[16px]/[24px] text-center text-ntrl-black-1"></p>
        </div>
        <div className="flex flex-col gap-4">
          {data.types.map((type, idx) => (
            <div
              className="grid grid-cols-1 md:grid-cols-[1fr_2fr] gap-4"
              key={idx}
            >
              <div className="mx-auto bg-white md:p-6">
                {/* Header Section */}
                <div className="flex items-start gap-4 md:gap-6">
                  {type.icon?.data?.attributes?.url && (
                    <div className="flex flex-shrink-0">
                      <Image
                        src={type.icon.data.attributes.url}
                        alt="Young Adults Icon"
                        width={100}
                        height={100}
                        className="size-10 md:size-16 "
                        style={{ objectFit: "contain" }}
                      />
                    </div>
                  )}
                  <div>
                    <h2 className="text-base md:text-lg font-semibold text-secondary-green-3 mb-3">
                      {type.title}
                    </h2>
                    {/* @ts-expect-error unknown type */}
                    {parse(type.description, { replace })}
                  </div>
                </div>
              </div>
              {/* @ts-expect-error unknown type */}
              {parse(type.table, { replace })}
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
};

export default TopHealthPlans;
