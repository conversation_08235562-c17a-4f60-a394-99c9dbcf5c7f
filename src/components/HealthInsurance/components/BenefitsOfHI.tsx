import { LPBenefitsOfHI } from "../types";
import parse, { Element } from "html-react-parser";

import Image from "next/image";
const BenefitsOfHI = ({ data }: { data: LPBenefitsOfHI }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }
      if (domNode.name === "strong") {
      }
      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className = "text-white text-[14px]/[22px]";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );
        case "strong":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[24px] text-ntrl-black font-semibold";
          break;

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  return (
    <div className="md:mb-14 -mx-5 md:-mx-0 mb-5">
      <div className="relative bg-gradient-to-r from-blue1000 to-blue900 md:rounded-5xl text-center w-full shadow-whyOneassure overflow-hidden p-6">
        <div className="absolute top-0 right-10 transform translate-x-1/2 -translate-y-1/2">
          <Image
            src={
              "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
            }
            alt=""
            width={524} // Increased width
            height={524} // Increased height
            objectFit="cover"
          />
        </div>

        <div className="absolute bottom-0  left-6 transform -translate-x-1/2 translate-y-1/2">
          <Image
            src={
              "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
            }
            alt=""
            width={524}
            height={524}
            objectFit="cover"
          />
        </div>

        <h2 className="text-lg md:text-3xl text-white font-medium">
          {data.title}
        </h2>
        <div className="md:text-[14px]/[20px] text-white font-light mb-4">
          {/* @ts-expect-error unknown type */}
          {parse(data.description, { replace })}
        </div>

        <div className="flex flex-wrap justify-center md:justify-between gap-4 mt-5 md:flex-nowrap">
          {data.sectionData.map((item, idx) => (
            <div
              key={item.id}
              className="max-w-[150px] md:max-w-none flex flex-col items-center gap-2 flex-1"
            >
              <div className="size-20 bg-white rounded-full justify-center items-center flex shadow-lg">
                <Image
                  src={item.icon?.data?.attributes.url || ""}
                  alt={item.title}
                  width={100}
                  height={100}
                  className="size-10"
                  objectFit="fit"
                />
              </div>
              <h3 className="text-md text-white font-medium mb-2 ">
                {item.title}
              </h3>
              {/* @ts-expect-error unknown type */}
              {parse(item.description, { replace })}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BenefitsOfHI;
