"use client";

import { useFormik } from "formik";
import * as Yup from "yup";
import { Dispatch, SetStateAction, useState } from "react";
import Otp from "@/components/MarketingLandingPage/components/Otp";
import { useOtp } from "@/components/MarketingLandingPage/api/postOtp";

const LeadForm = ({
  setFormPart,
  formPart,
  setShowRecommendation,
  setFormIsOpen,
}: {
  setFormPart: Dispatch<SetStateAction<number>>;
  formPart: number;
  setShowRecommendation: Dispatch<SetStateAction<boolean>>;
  setFormIsOpen: Dispatch<SetStateAction<boolean>>;
}) => {
  const { mutate: mutateOtp, isPending: otpIsPending, error } = useOtp();
  const [showOtp, setShowOtp] = useState(false);
  const [formValues, setFormValues] = useState({});

  const handleState = () => {
    // console.log("inside state handler");
    setFormPart(1);
    setShowRecommendation(true);
    setFormIsOpen(false);
    setShowOtp(false);
  };

  const LeadSchema = Yup.object().shape({
    lead_name: Yup.string()
      .matches(/^[A-Za-z ]*$/, "Please enter valid name")
      .required("Required"),
    lead_email: Yup.string().email("Invalid email").required("Required"),
    lead_phone_number: Yup.string()
      .length(10, "Invalid phone number")
      .matches(
        /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/,
        "Phone number is not valid"
      )
      .required("Required"),
  });

  const formik = useFormik({
    initialValues: {
      lead_name: "",
      lead_email: "",
      lead_phone_number: "",
    },
    validationSchema: LeadSchema,
    onSubmit: (values, { resetForm }) => {
      const { lead_name, lead_phone_number, ...restObj } = values;
      setFormValues(values);
      const body = {
        phone_number: parseInt(lead_phone_number),
        otp_use_case: "landing-page-phone-number-verification",
      };

      // @ts-ignore
      mutateOtp(body, {
        onSuccess: (data) => {
          setShowOtp(true);
        },
      });
      resetForm();
      // mutate(
      //   {
      //     lead_name,
      //     lead_phone_number,
      //     source: "health-quick-quote",
      //     ...restObj,
      //   },
      //   {
      //     onSuccess: () => {
      //       resetForm();
      //       setFormPart(1);
      //       setShowRecommendation(true);
      //       setFormIsOpen(false);
      //     },
      //   }
      // );
    },
  });

  return (
    <>
      <div
        className={` w-[100%] mx-auto font-inter text-[12px] ${
          formPart === 2 && !showOtp ? "block" : "hidden"
        }`}
      >
        <form
          onSubmit={formik.handleSubmit}
          className="text-[16px]/[24px] text-ntrl-grey1"
        >
          <div className="grid grid-cols-2 gap-6">
            <div className="mb-6">
              <input
                type="text"
                name="lead_name"
                id="lead_name"
                onChange={formik.handleChange}
                value={formik.values.lead_name}
                className=" w-full py-3 px-4 rounded-xl border border-none bg-gray1300 font-normal"
                placeholder="Full name"
              />
              {formik.touched.lead_name && formik.errors.lead_name && (
                <p className=" text-sm text-red600">
                  {formik.errors.lead_name}
                </p>
              )}
            </div>
            <div className="mb-6">
              <input
                type="text"
                name="lead_email"
                id="lead_email"
                onChange={formik.handleChange}
                value={formik.values.lead_email}
                className="w-full py-3 px-4 rounded-xl border border-none bg-gray1300 font-normal"
                placeholder="Email"
              />
              {formik.touched.lead_email && formik.errors.lead_email && (
                <p className=" text-sm text-red600">
                  {formik.errors.lead_email}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-6">
            <div className="mb-6">
              <input
                type="text"
                name="lead_phone_number"
                id="lead_phone_number"
                onChange={formik.handleChange}
                value={formik.values.lead_phone_number}
                className="w-full py-3 px-4 rounded-xl border border-none bg-gray1300 font-normal"
                placeholder="Mobile number"
              />
              {formik.touched.lead_phone_number &&
                formik.errors.lead_phone_number && (
                  <p className=" text-sm text-red600">
                    {formik.errors.lead_phone_number}
                  </p>
                )}
            </div>
          </div>

          <div className="flex items-center justify-center">
            <button
              className="py-3 px-20 bg-black rounded-full text-[16px]/[24px] text-ntrl-white font-semibold border-2 cursor-pointer"
              type="submit"
              id="lead-submit"
            >
              Submit
            </button>
          </div>
        </form>
      </div>
      <div
        className={`md:w-[40%] w-[100%] mx-auto ${
          showOtp ? "block" : "hidden"
        }`}
      >
        <Otp
          formValues={formValues}
          source="website-health-quote"
          handleState={handleState}
        />
      </div>
    </>
  );
};

export default LeadForm;
