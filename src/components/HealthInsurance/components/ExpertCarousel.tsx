"use client";

import useEmblaCarousel from "embla-carousel-react";
import {
  PrevButton,
  NextButton,
  usePrevNextButtons,
} from "@/components/globals/TestimonialCarouselControls";
import { LPOurExperts } from "../types";
import Image from "next/image";
import parse, { attributesToProps } from "html-react-parser";
import { Element } from "html-react-parser";

// @ts-ignore
const ExpertCarousel = ({ data }) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    slidesToScroll: "auto",
  });
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black md:mb-4";
          break;

        case "h3":
          attrs.className = "text-primary-1 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-primary-1 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;

        case "table":
          attrs.className =
            "mx-auto font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5 drop-shadow-lg";
          break;

        case "tr":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;
        case "td":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "iframe":
          attrs.className =
            "my-20 mx-auto rounded-xl md:min-w-[1000px] md:min-h-[500px] shadow-md max-w-[350px]";
      }
      return domNode;
    }
  };

  const slides = data.experts;

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);
  return (
    <section className="mx-auto w-[50%] ml-10 hidden md:block">
      <div className="overflow-hidden" ref={emblaRef}>
        <div
          className="flex touch-pan-y touch-pinch-zoom -ml-[1rem]"
          style={{ backfaceVisibility: "hidden" }}
        >
          {/* @ts-ignore */}
          {data.map((expert, idx) => (
            <div
              className={`flex-shrink-0 flex-grow-0 basis-[40%] min-w-0 pl-[1rem]`}
              key={idx}
            >
              <div className="rounded-lg p-5 border border-ntrl-grey1 bg-ntrl-white h-[400px]">
                <div className="w-10 h-10 rounded-full bg-primary-1 flex items-center justify-center mb-6">
                  <Image
                    src={expert.icon.data.attributes.url}
                    width={35}
                    height={35}
                    alt="health-experts"
                  />
                </div>

                <h2 className="font-generalSans font-medium text-ntrl-black text-[24px]/[30px] mb-6">
                  {expert.title}
                </h2>

                {/* @ts-ignore */}
                {parse(expert.description, { replace })}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="flex items-end justify-end mt-12 ">
        <div className="flex items-center justify-center gap-5">
          <PrevButton onClick={onPrevButtonClick} disabled={prevBtnDisabled} />
          <NextButton onClick={onNextButtonClick} disabled={nextBtnDisabled} />
        </div>
      </div>
    </section>
  );
};

export default ExpertCarousel;
