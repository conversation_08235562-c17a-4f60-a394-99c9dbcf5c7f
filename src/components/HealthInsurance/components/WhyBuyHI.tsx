import { LPWhyBuyHI } from "../types";
import parse from "html-react-parser";
import { Element } from "html-react-parser";
import Image from "next/image";

const WhyBuyHI = ({ data }: { data: LPWhyBuyHI }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }
      if (domNode.name === "strong") {
      }
      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[24px] text-ntrl-black font-light";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );
        case "strong":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[24px] text-ntrl-black font-semibold";
          break;

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };

  return (
    <div className="md:mb-14 mb-5">
      {/* Heading */}
      <div className="mb-5">
        <div className="flex items-center justify-center text-ntrl-black-1 gap-2 mb-4 ">
          <h2 className="font-medium text-center md:text-3xl text-lg">
            Why You Need a Health Insurance?
          </h2>
        </div>

        <p className="md:text-[18px]/[28px] text-base text-center text-ntrl-grey-5">
          {`Given the unpredictable nature of health issues, medical insurance is a vital investment. It safeguards financial stability and health security. Here's how:`}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-[30px]">
        {data.hIImporantce.map((h, idx) => (
          <div className="bg-ntrl-white rounded-2xl" key={idx}>
            <h3 className="text-lg md:text-3xl font-medium text-ntrl-black-1 mb-10 text-center">
              {h.title}
            </h3>

            {/* Points */}
            {h.hi_importances.data[0].attributes.hIImporantceReasons.map(
              (reason, idx) => (
                <>
                  <div className="mb-3 flex items-center gap-4" key={idx}>
                    <div className="w-10 h-10 rounded-full flex items-center justify-center">
                      <Image
                        src={reason.iconUrl.data.attributes.url}
                        width={50}
                        height={50}
                        alt={reason.title}
                        className="rounded-xl"
                      />
                    </div>

                    <div className="md:text-[16px]/[24px] text-base text-ntrl-grey1 w-[90%]">
                      <h4 className="md:text-[18px]/[28px] text-sm text-ntrl-black-1 font-bold">
                        {reason.title}
                      </h4>

                      {/* @ts-ignore */}
                      {parse(reason.description, { replace })}
                    </div>
                  </div>
                </>
              )
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default WhyBuyHI;
