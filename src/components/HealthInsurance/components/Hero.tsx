"use client";

import "@/app/globals.css";
import React, { Suspense, useEffect } from "react";
import parse, { Element } from "html-react-parser";
import QuoteForm from "./QuoteForm";
import ProductDropdown from "../../globals/InsuranceAllProducts/ProductDropdown";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import { useSessionStorage } from "usehooks-ts";
interface HeroProps {
  data: {
    title: string;
    subTitleRichText: string;
  };
  category?: string;
  companyData?: any;
}

type InsuranceType = "health" | "term";

const Hero: React.FC<HeroProps> = ({ data, category, companyData }) => {
  const router = useRouter();
  const pathname = usePathname();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      router.replace(
        `${pathname}?${utm_source !== null ? `utm_source=${utm_source}` : ""}${
          utm_medium !== null ? `&utm_medium=${utm_medium}` : ""
        }${utm_campaign !== null ? `&utm_campaign=${utm_campaign}` : ""}${
          utm_content !== null ? `&utm_content=${utm_content}` : ""
        }${utm_term !== null ? `&utm_term=${utm_term}` : ""}`
      );
    }
  }, []);

  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      // if (attrs.style) {
      //   attrs.style = "";
      // }
      if (domNode.name === "strong") {
      }
      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[24px] text-ntrl-black font-light";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );
        case "strong":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[24px] text-ntrl-black font-semibold";
          break;

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  return (
    <div className="mt-4 md:mb-14 mb-5">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:pt-8 pt-4">
        {/* Left Side */}
        <div className="">
          <h1 className="text-ntrl-black-1 font-bold text-[24px]/[36px] text-start mb-3 md:text-[60px]/[70px] md:w-[60%]">
            {data.title.split(" ").map((word, index, array) => {
              const isLastTwoWords = index >= array.length - 2;
              return (
                <span
                  key={index}
                  className={isLastTwoWords ? "text-secondary-green-3" : ""}
                >
                  {word}{" "}
                </span>
              );
            })}
          </h1>
          <div className="text-black100 font-medium text-[12px]/[16px] md:text-[18px]/[24px] text-start mb-2 md:w-[60%]">
            {/* @ts-expect-error unknown type */}
            {parse(data.subTitleRichText, { replace })}
          </div>
          <Suspense>
            <ProductDropdown
              companyData={companyData}
              category={category as InsuranceType}
            />
          </Suspense>
        </div>

        {/* Right Side */}
        <QuoteForm />
      </div>
    </div>
  );
};

export default Hero;
