// @ts-nocheck

import { useFormState, useFormStatus } from "react-dom";
import { useState, useEffect } from "react";
import { PlusIcon, MinusIcon } from "@heroicons/react/24/solid";
import { getHealthQuote } from "../api/getHealthQuote";

const FormButton = ({
  setShowRecommendation,
}: {
  setShowRecommendation: Dispatch<SetStateAction<{}>>;
}) => {
  const { pending } = useFormStatus();
  return (
    <div className="flex items-center justify-center">
      <button
        className="bg-ntrl-black-1 text-ntrl-white py-3 px-12 rounded-full text-[16px]/[24px] font-generalSans font-semibold disabled:opacity-50 disabled:cursor-wait"
        disabled={pending ? true : false}
        onClick={() => setShowRecommendation(false)}
        type="submit"
      >
        Continue
      </button>
    </div>
  );
};

const CustomCounterInput = ({
  callBack,
  initialState,
  maxCount,
  name,
}: {
  callBack: (counter: number) => void;
  initialState: number;
  maxCount: number;
  name: string;
}) => {
  const [counter, setCounter] = useState(initialState);

  useEffect(() => {
    callBack(counter);
  }, [counter]);

  return (
    <div className="flex items-center">
      <button
        type="button"
        className="py-1 px-2 border border-ntrl-grey2 bg-ntrl-white rounded-l-full flex items-center justify-center cursor-pointer  text-ntrl-black disabled:cursor-not-allowed disabled:text-ntrl-grey1"
        disabled={counter === initialState}
        onClick={() => {
          if (counter > initialState) {
            setCounter(counter - 1);
          }
        }}
      >
        <MinusIcon className="w-6 h-6 font-semibold" />
      </button>
      <input
        type="number"
        name={name}
        value={counter}
        onChange={() => {}}
        className="py-1 px-2 text-center border border-ntrl-grey2 text-[18px]/[28px] font-normal max-w-[30px] max-h-[33px] bg-ntrl-grey-2"
        // disabled
      />
      <button
        type="button"
        className="py-1 px-2 border border-ntrl-grey2  bg-ntrl-white rounded-r-full text-[18px]/[28px] flex items-center justify-center cursor-pointer  text-ntrl-black disabled:cursor-not-allowed disabled:text-ntrl-grey1"
        disabled={counter === maxCount}
        onClick={() => {
          if (counter < maxCount) setCounter(counter + 1);
        }}
      >
        <PlusIcon className="w-6 h-6 font-semibold" />
      </button>
    </div>
  );
};

const QuickQuoteForm = ({
  formPart,
  setFormPart,
  setQuickQuoteRecommendation,
  setShowRecommendation,
}: {
  formPart: number;
  setFormPart: Dispatch<SetStateAction<number>>;
  setQuickQuoteRecommendation: Dispatch<SetStateAction<{}>>;
  setShowRecommendation: Dispatch<SetStateAction<{}>>;
}) => {
  // Local Form States
  const [adults, setAdults] = useState(1);
  const [children, setChildren] = useState(0);

  // Form Initial State
  const initialState = {
    isSuccess: true,
    data: {},
    errors: {},
  };

  // Sum Insured values
  const coverageValues = [
    { label: "5 Lakhs", value: "500000" },
    { label: "7.5 Lakhs", value: "750000" },
    { label: "10 Lakhs", value: "1000000" },
    { label: "15 Lakhs", value: "1500000" },
    { label: "20 Lakhs", value: "2000000" },
    { label: "25 Lakhs", value: "2500000" },
    { label: "50 Lakhs", value: "5000000" },
    { label: "75 Lakhs", value: "7500000" },
    { label: "1 Crore", value: "10000000" },
    { label: " 2 Crore", value: "20000000" },
  ];

  // Form State from hook
  const [state, getHealthQuoteAction] = useFormState(
    getHealthQuote,
    initialState
  );

  useEffect(() => {
    // console.log("State inside useEffect : ", state.data);
    if (state.isSuccess) {
      if (Object.keys(state.data).length > 0) {
        setFormPart(2);
        setQuickQuoteRecommendation(state.data);
      }
    } else {
      setShowRecommendation(true);
    }
  }, [state]);

  return (
    <form
      action={getHealthQuoteAction}
      className={`${formPart === 1 ? "block" : "hidden"}`}
    >
      {state.errors?.api && (
        <div className="mb-2 mt-[-1.5rem]">
          <p className="text-red-2 text-[16px]/[28px] font-semibold">
            {state.errors.api}
          </p>
        </div>
      )}
      {/* Adult and Child age inputs */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-10">
        {/* Adults */}
        <div>
          {/* Adult counter */}
          <div className="flex items-center mb-1 md:mb-5">
            <div className="mr-6">
              <label className="text-[18px]/[28px] text-ntrl-black font-normal">
                Select Adults
              </label>
              <label className="text-[14px]/[22px] text-ntrl-grey1 font-normal block">
                Aged 18+
              </label>
            </div>
            <CustomCounterInput
              initialState={1}
              maxCount={2}
              callBack={(counter) => {
                setAdults(counter);
              }}
              name="adultCount"
            />
          </div>

          {/* Age input for each adult */}
          <label className="text-[18px]/[28px] text-ntrl-black font-normal">{`Enter age`}</label>
          <div className="grid grid-cols-2 gap-5 mt-1 md:mt-3">
            {new Array(adults).fill(0).map((a, idx) => (
              <div key={idx}>
                <input
                  type="number"
                  name={`adult${idx + 1}`}
                  placeholder={`Adult ${idx + 1}`}
                  className={`rounded-[8px] w-full border border-ntrl-grey-5
                            ${
                              state.errors?.adults &&
                              Object.keys(state.errors.adults).includes(
                                `adult${idx + 1}`
                              )
                                ? "bg-red-3"
                                : ""
                            }`}
                />
                <p className="text-xs mt-1 text-red-1">
                  {state.errors?.adults &&
                  state.errors?.adults[`adult${idx + 1}`]
                    ? state.errors?.adults[`adult${idx + 1}`]._errors[0]
                    : ""}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Children */}
        <div>
          {/* Child counter */}
          <div className="flex items-center mb-1 md:mb-5">
            <div className="mr-6">
              <label className="text-[18px]/[28px] text-ntrl-black font-normal">
                Select Child
              </label>
              <label className="text-[14px]/[22px] text-ntrl-grey1 font-normal block">
                Aged less than 25
              </label>
            </div>
            <CustomCounterInput
              initialState={0}
              maxCount={4}
              callBack={(counter) => {
                setChildren(counter);
              }}
              name="childCount"
            />
          </div>

          {/* Age input for each child */}
          {children > 0 && (
            <label className="text-[18px]/[28px] text-ntrl-black font-normal">{`Enter age`}</label>
          )}

          <div className="grid grid-cols-2 gap-5 mt-1 md:mt-3 w-[80%] md:w-full">
            {new Array(children).fill(0).map((a, idx) => (
              <div key={idx} className="">
                <input
                  type="number"
                  name={`child${idx + 1}`}
                  placeholder={`Child ${idx + 1}`}
                  className={`rounded-[6px] w-full border border-ntrl-grey-5 ${
                    state.errors?.children &&
                    Object.keys(state.errors.children).includes(
                      `child${idx + 1}`
                    )
                      ? "bg-red-3"
                      : ""
                  }`}
                />
                <p className="text-xs mt-1 text-red-1">
                  {state.errors?.children &&
                  state.errors?.children[`child${idx + 1}`]
                    ? state.errors?.children[`child${idx + 1}`]._errors[0]
                    : ""}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Divider */}
      {/* <div className="border border-ntrl-grey1 my-8 opacity-50"></div> */}

      {/* Pincode and Sum Insured */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3 md:gap-10 mb-3 md:mb-8">
        <div>
          <label className="text-[18px]/[28px] text-ntrl-black font-normal block mb-3">
            Pincode
          </label>
          <input
            className={`rounded-[6px] w-full bg-ntrl-grey-2 border border-ntrl-grey-5 ${
              state.errors?.pincode ? "bg-red-3" : "bg-ntrl-white"
            }`}
            type="text"
            name="pincode"
            placeholder="Enter postal code"
          />
          <p className="text-xs mt-1 text-red-1">
            {state.errors?.pincode ? state.errors?.pincode._errors[0] : ""}
          </p>
        </div>
        <div>
          <label className="text-[18px]/[28px] text-ntrl-black font-normal block mb-3">
            Your coverage amount
          </label>
          <select
            name="sumInsured"
            className={`rounded-[6px] bg-ntrl-grey-2 w-full border border-ntrl-grey-5 ${
              state.errors?.sumInsured ? "bg-red-3" : ""
            }`}
            placeholder="Select your coverage"
          >
            <option value={undefined}>Select your coverage</option>
            {coverageValues.map((cover, idx) => (
              <option key={idx} value={cover.value}>
                {cover.label}
              </option>
            ))}
          </select>
          <p className="text-xs mt-1 text-red-1">
            {state.errors?.sumInsured
              ? state.errors?.sumInsured._errors[0]
              : ""}
          </p>
        </div>
      </div>
      <div className="flex justify-center mt-5 md:mt-0">
        <FormButton setShowRecommendation={setShowRecommendation} />
      </div>
    </form>
  );
};

export default QuickQuoteForm;
