import Container from "@/components/globals/Container";

const HIAtGlance = ({ data }: { data: any }) => {
  return (
    // <div className="bg-primary-3 md:py-20 py-10 mb-10 md:mb-20">
    //   <Container>

    //     <div className="mb-12">
    //       <div className="flex items-center justify-center font-generalSans md:text-[48px]/[58px] text-[24px]/[32px] text-ntrl-black gap-2 mb-4 ">
    //         <h2 className="font-normal">Health Insurance </h2>
    //         <h2 className="font-medium">At A Glance</h2>
    //       </div>

    //     </div>

    //     <div>
    //       {data.overviewFeat.map((feat, idx) => (
    //         <div className="flex items-center gap-[30px] mb-4" key={idx}>
    //           <h3 className="text-ntrl-grey2 text-9xl font-semibold italic">
    //             {idx + 1}
    //           </h3>
    //           <div
    //             className={`p-4 bg-ntrl-white rounded-xl w-full text-xl font-semibold text-ntrl-grey1 ${
    //               idx === 0 ? "ml-5" : "ml-0"
    //             }`}
    //           >
    //             {feat.description}
    //           </div>
    //         </div>
    //       ))}
    //     </div>
    //   </Container>
    // </div>
    <></>
  );
};

export default HIAtGlance;
