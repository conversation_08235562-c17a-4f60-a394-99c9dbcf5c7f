"use client";
import { LPHIChecklist } from "../types";
import parse from "html-react-parser";
import { Element } from "html-react-parser";
import { FaCheck } from "react-icons/fa";

const Checklist = ({ data }: { data: LPHIChecklist }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "p":
          attrs.className =
            "mb-6 text-black font-light mt-3 text-[12px]/[18px]";
          break;

        case "a":
          attrs.className = "text-primary-2 no-underline";
          break;
      }
      return domNode;
    }
  };

  return (
    <div className="md:mb-14 mb-5 font-helvetica text-ntrl-black-1 md:px-14 px-0">
      {/* Heading */}
      <div className="text-center">
        {/* title comes here  */}
        <h2 className="text-lg md:text-3xl font-medium ">
          {"Health Insurance Checklist"}
        </h2>
        {/* subTitle comes here  */}
        <p className="text-gray-500 text-[12px]/[18px] md:text-[14px]/[20px]">
          {"Buy Health Insurance with Oneassure in 4 Simple Steps"}
        </p>
      </div>

      {/* Features List */}
      <div className="grid grid-cols-2 md:grid-cols-6 mt-5 gap-y-4 gap-x-10">
        {data.sectionData.map((feat, idx) => (
          <div key={idx} className="">
            {/* Number inside a Circle */}
            <div className="w-[40px] h-[40px] flex items-center justify-center rounded-full border-4 text-white border-green1100 bg-white font-bold text-[20px]/[24px]">
              <FaCheck className="size-6 text-green1300 font-bold" />
            </div>

            {/* Title & Description */}

            {/* Title */}
            <h3 className="items-center text-black font-semibold text-[14px]/[20px] mt-2">
              {feat.title}
            </h3>
            {/* Description */}
            <>
              {/* @ts-ignore */}
              {parse(feat.description, { replace })}
            </>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Checklist;
