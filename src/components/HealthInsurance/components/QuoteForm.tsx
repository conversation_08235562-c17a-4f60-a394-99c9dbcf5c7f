// @ts-nocheck

"use client";

import Container from "@/components/globals/Container";
import QuickQuoteForm from "./QuickQuoteForm";
import LeadForm from "./LeadForm";
import QuickQuoteCard from "./QuickQuoteCard";
import { useEffect, useState } from "react";
import { Suspense } from "react";
import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/solid";

const QuoteForm = () => {
  const [formPart, setFormPart] = useState(1);
  const [quickQuoteRecommendation, setQuickQuoteRecommendation] =
    useState(undefined);
  const [showRecommendation, setShowRecommendation] = useState(false);

  const [formIsOpen, setFormIsOpen] = useState(true);
  const [isDropdownOpen, setIsDropdownOpen] = useState(true);

  return (
    <section id="quote-form ">
      <div className=" border-2 border-teal-400 rounded-2xl p-[1px]">
        <div className="rounded-3xl p-[30px] bg-ntrl-white">
          {/* Heading */}
          <div className={`${formIsOpen ? "" : "mb-0"}`}>
            <div className="flex items-center justify-between">
              <h2 className="font-semibold md:text-3xl text-2xl text-ntrl-black flex items-center gap-2">
                Get a Quote
              </h2>

              {formIsOpen ? (
                <ChevronUpIcon
                  className="w-6 h-6 text-ntrl-black cursor-pointer"
                  onClick={() => setFormIsOpen(!formIsOpen)}
                />
              ) : (
                <ChevronDownIcon
                  className="w-6 h-6 text-ntrl-black cursor-pointer"
                  onClick={() => setFormIsOpen(!formIsOpen)}
                />
              )}
            </div>
            <div className={`${formIsOpen ? "block" : "hidden"} mt-4`}>
              <QuickQuoteForm
                formPart={formPart}
                setFormPart={setFormPart}
                setQuickQuoteRecommendation={setQuickQuoteRecommendation}
                setShowRecommendation={setShowRecommendation}
              />
              <Suspense>
                <LeadForm
                  setFormPart={setFormPart}
                  formPart={formPart}
                  setShowRecommendation={setShowRecommendation}
                  setFormIsOpen={setFormIsOpen}
                />
              </Suspense>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-ntrl-white rounded-3xl">
        {/* Recommendations */}
        {quickQuoteRecommendation && (
          <div
            className={`${
              showRecommendation
                ? "block bg-ntrl-white border border-ntrl-black-1 rounded-3xl mt-2 max-h-[400px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 p-4"
                : "hidden"
            }`}
          >
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="w-full flex items-center justify-between text-ntrl-black mb-4"
            >
              <h2 className="font-semibold text-xl">Recommended Policies</h2>
              {isDropdownOpen ? (
                <ChevronUpIcon className="h-6 w-6 text-gray-500" />
              ) : (
                <ChevronDownIcon className="h-6 w-6 text-gray-500" />
              )}
            </button>

            <div className={`${isDropdownOpen ? "block" : "hidden"}`}>
              <div className="grid grid-cols-1 md:grid-cols-1 gap-[30px] max-h-[600px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {quickQuoteRecommendation.data.variant_recommendations[
                  "vanilla-health"
                ].map((recomm, idx) => (
                  <QuickQuoteCard
                    key={idx}
                    name={recomm.variant_name}
                    logo={recomm.icon}
                    networkHospitals={recomm.network_hospitals}
                    claim={recomm.claims_ratio}
                    prem={recomm.selected_premium}
                    si={recomm.selected_sum_insured.sum_insured}
                  />
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default QuoteForm;
