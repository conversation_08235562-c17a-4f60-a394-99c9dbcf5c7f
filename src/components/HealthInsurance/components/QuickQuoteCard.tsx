import Image from "next/image";

const QuickQuoteCard = ({
  name,
  logo,
  networkHospitals,
  claim,
  prem,
  si,
}: {
  name: string;
  logo: string;
  networkHospitals: string;
  claim: string;
  prem: number;
  si: number;
}) => {
  return (
    <div className="border border-ntrl-grey1 rounded-xl w-full py-4">
      {/* Title and Logo Section */}
      <div className="px-6 flex flex-col md:flex-row items-start md:items-center md:gap-6">
        <div className="h-14 w-14 relative">
          <Image
            src={logo}
            fill={true}
            style={{ objectFit: "contain" }}
            alt={name}
          />
        </div>
        <h2 className="font-generalSans font-semibold text-[20px]/[28px] md:text-[16px]/[13px]">
          {name}
        </h2>
      </div>

      {/* Grid Section */}
      <div className="grid grid-cols-4 gap-4 px-6 border-ntrl-grey1 mt-5 md:mt-0">
        {/* Network Hospitals */}
        <div>
          <p className="text-[14px] text-ntrl-grey1">Network Hospitals</p>
          <p className="text-[16px] font-medium">{networkHospitals}</p>
        </div>

        {/* Claim Settlement */}
        <div>
          <p className="text-[14px] text-ntrl-grey1">Claim Settlement</p>
          <p className="text-[16px] font-medium">{claim}</p>
        </div>

        {/* Sum Insured */}
        <div>
          <p className="text-[14px] text-ntrl-grey1">Sum Insured</p>
          <p className="text-[16px] font-medium">₹{formatCurrency(si)}</p>
        </div>

        {/* Premium */}
        <div>
          <p className="text-[14px] text-ntrl-grey1">Premium</p>
          <p className="text-[16px] font-medium">₹{prem}</p>
          <p className="text-[12px] text-ntrl-grey1">Inclusive of all taxes</p>
        </div>
      </div>

      {/* View More Button */}
      {/* <div className="px-6 py-3 flex justify-end">
        <button className="px-4 py-1.5 text-sm border border-primary-2 text-primary-2 rounded-xl hover:bg-primary-2/5 ">
          View More
        </button>
      </div> */}
    </div>
  );
};

function formatCurrency(amount: number): string {
  const lakh = 100000;
  const crore = 100 * lakh;

  if (amount >= crore) {
    const crores = amount / crore;
    return `${crores}Cr`;
  } else if (amount >= lakh) {
    const lakhs = amount / lakh;
    return `${lakhs}L`;
  } else {
    return amount.toString(); // Return as is if less than a lakh
  }
}

export default QuickQuoteCard;
