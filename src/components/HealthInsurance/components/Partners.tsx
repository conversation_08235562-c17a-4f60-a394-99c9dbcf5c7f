"use client";

import { useState } from "react";
import Container from "@/components/globals/Container";
import Marquee from "react-fast-marquee";
import Image from "next/image";
import Link from "next/link";

const Partners = ({ compData }: { compData: any }) => {
  const [showReadMore, setShowReadMore] = useState(false);
  const [activePartner, setActivePartner] = useState<number | null>(null);
  // console.log(compData[14].attributes.logo.data.attributes.url);

  return (
    <div className="md:mb-20 my-10 md:my-0">
      <Container>
        {/* Heading */}
        <div className="mb-12">
          <div className="flex items-center justify-center font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black gap-2">
            <h2 className="font-normal">Our Trusted</h2>
            <h2 className="font-medium">Partners</h2>
          </div>
        </div>
      </Container>
      {/* Marquee section */}
      <div className="">
        <Marquee pauseOnHover={true} className="cursor-pointer" speed={75}>
          {/* @ts-ignore */}
          {compData.map((comp, idx) => (
            <Link key={idx} href={`/health-insurance/${comp.attributes.slug}`}>
              <div
                className="border border-ntrl-grey2 bg-primary-2 mr-3 md:mr-20 rounded-xl h-28 md:h-44"
                onMouseEnter={() => {
                  setShowReadMore(true);
                  setActivePartner(idx);
                }}
                onMouseLeave={() => {
                  setShowReadMore(false);
                  setActivePartner(null);
                }}
              >
                <div
                  className={`px-10 md:py-12 rounded-xl bg-ntrl-white flex items-center justify-center min-w-[223px] ${
                    showReadMore && idx === activePartner
                      ? "h-[50%] md:h-[70%]"
                      : "h-[100%]"
                  }`}
                >
                  <Image
                    src={comp.attributes.logo.data?.attributes.url}
                    // fill={true}
                    // style={{ objectFit: "contain" }}
                    width={87}
                    height={56}
                    alt={comp.name}
                  />
                </div>

                {showReadMore && idx === activePartner && (
                  <div className="rounded-b-xl text-ntrl-white px-10 py-[14px] flex items-center justify-center font-generalSans text-[16px/[24px] font-medium">
                    Read more
                  </div>
                )}
              </div>
            </Link>
          ))}
        </Marquee>
      </div>
    </div>
  );
};

export default Partners;
