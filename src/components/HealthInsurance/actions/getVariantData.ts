"use server";

export default async function getHealthVariantData<T>(
  variant_slug: string
): Promise<T> {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/health-variants?filters[slug][$eq]=${variant_slug}&populate[company][populate][fields][0]=name&populate[company][populate][fields][1]=slug&populate[company][populate][logo][fields][0]=url&populate[faqs][fields][0]=question&populate[faqs][fields][1]=ans&populate[whyOneAssure][fields][0]=title&populate[whyOneAssure][fields][1]=description&populate[features][fields][0]=title&populate[features][fields][1]=description&populate[features][populate][listedFeatures][fields][0]=feature&populate[blogs][populate][fields][0]=Title&populate[blogs][populate][fields][1]=slug&populate[blogs][populate][fields][2]=subtitle&populate[blogs][populate][category][fields][0]=slug&populate[blogs][populate][Thumbnail][fields][0]=url&populate[exclusions][fields][0]=exclusion&populate[hero][fields][0]=title&populate[hero][populate][claimSettlementRatio][fields][0]=value&populate[hero][populate][claimSettlementRatio][fields][1]=description&populate[hero][populate][networkHospitals][fields][0]=value&populate[hero][populate][networkHospitals][fields][1]=description&populate[hero][populate][networkHospitals][fields][2]=networkHospitalsURL&populate[policyDocs][fields][0]=label&populate[policyDocs][populate][document][fields][0]=url&populate[highlightedFeatures][fields][0]=title&populate[highlightedFeatures][fields][1]=description&populate[highlightedFeatures][populate][icon][fields][0]=url&populate[addOns][fields][0]=title&populate[addOns][fields][1]=description&populate[variants][fields][0]=title&populate[variants][populate][relatedVariant][fields][0]=slug&populate[variants][populate][relatedVariant][fields][1]=name&populate[variants][populate][relatedVariant][populate][company][fields][0]=slug&populate[variants][populate][relatedVariant][populate][company][fields][1]=name&populate[variants][populate][relatedVariant][populate][company][populate][logo][fields][0]=url&populate[variants][populate][features][fields][0]=feature&populate[seo][fields][0]=metaTitle&populate[seo][fields][1]=metaDescription&populate[seo][fields][2]=keyword&populate[rating][populate][coverage][populate][fields][0]=title&populate[rating][populate][coverage][populate][fields][1]=score&populate[rating][populate][coverage][populate][fields][2]=maxScore&populate[rating][populate][claimSettlement][populate][fields][0]=title&populate[rating][populate][claimSettlement][populate][fields][1]=score&populate[rating][populate][claimSettlement][populate][fields][2]=maxScore&populate[rating][populate][hospitalNetwork][populate][fields][0]=title&populate[rating][populate][hospitalNetwork][populate][fields][1]=score&populate[rating][populate][hospitalNetwork][populate][fields][2]=maxScore&populate[rating][populate][coPayment][populate][fields][0]=title&populate[rating][populate][coPayment][populate][fields][1]=score&populate[rating][populate][coPayment][populate][fields][2]=maxScore&populate[rating][populate][waitingPeriods][populate][fields][0]=title&populate[rating][populate][waitingPeriods][populate][fields][1]=score&populate[rating][populate][waitingPeriods][populate][fields][2]=maxScore&populate[rating][populate][noClaimBonus][populate][fields][0]=title&populate[rating][populate][noClaimBonus][populate][fields][1]=score&populate[rating][populate][noClaimBonus][populate][fields][2]=maxScore`,
    { headers }
  );
  return res.json() as Promise<T>;
}
