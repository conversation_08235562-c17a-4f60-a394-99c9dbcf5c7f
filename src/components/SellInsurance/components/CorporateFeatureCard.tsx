import Image from "next/image";

const CorporateFeatureCard = ({
  icon,
  title,
  description,
}: {
  title: string;
  icon: string;
  description: string;
}) => {
  return (
    <div
      className="py-8 px-4 rounded-3xl shadow-shadow100 
    min-w-[300px] md:w-[400px]  max-h-[500px]"
    >
      {/* illustration */}
      <div className="w-16 h-16 mb-4">
        <Image src={icon} alt="" width={64} height={64} />
      </div>

      {/* Text */}
      <div>
        <h2 className="font-semibold text-gray900 text-xl mb-1">{title}</h2>
        <p className="text-base text-slateGrey">{description}</p>
      </div>
    </div>
  );
};

export default CorporateFeatureCard;
