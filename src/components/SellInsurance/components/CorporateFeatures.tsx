import Container from "@/components/globals/Container";
import CorporateFeatureCard from "./CorporateFeatureCard";
import { data } from "../data";

const CorporateFeatures = () => {
  const features = data.features;
  return (
    <div className=" mb-16">
      <Container>
        {/* Heading */}
        <div className="flex flex-col items-center justify-center">
          <h2 className="font-semibold text-2xl md:text-4xl text-gray900 mb-8 md:w-[50%] text-center">
            Corporate features
          </h2>
          {/* <p className="text-base text-gray800 mb-8">
            Lorem ipsum dolor sit amet consectetur. Ipsum id purus consectetur
            lobortis bibendum.
          </p> */}
        </div>

        {/* Cards */}
        <div className="flex md:grid md:grid-cols-3 md:items-stretch gap-6 overflow-x-auto px-5 py-5">
          {features.map((feat, idx) => (
            <CorporateFeatureCard
              key={idx}
              icon={feat.icon}
              title={feat.title}
              description={feat.description}
            />
          ))}
        </div>
      </Container>
    </div>
  );
};

export default CorporateFeatures;
