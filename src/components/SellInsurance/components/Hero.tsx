import BookACallBtn from "@/components/globals/BookACall";
import Container from "@/components/globals/Container";
import Image from "next/image";

const Hero = () => {
  return (
    <div className=" mb-24 px-5 md:px-0">
      <Container>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-7 items-center">
          {/* Headings */}
          <div className="mb-10">
            <h1 className=" text-2xl md:text-4xl font-semibold mb-6 text-center md:text-left">
              Entrust Your Business with OneAssure.
            </h1>

            <p className=" text-base/[22px] text-slateGrey mb-10 text-justify md:text-left">
              Offer insurance products seamlessly with OneAssure. Our API
              integrated platform provides comprehensive support and access to a
              wide range of options, empowering your business to grow its
              revenue streams.
            </p>

            <div className="flex items-center justify-center md:justify-start">
              <BookACallBtn className="py-3 px-6 md:px-8 bg-primary-1 text-ntrl-white rounded-lg" />
            </div>
          </div>

          {/* Illustration */}
          <div className="flex items-center justify-end">
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL}/sellInsuranceHero.svg`}
              alt="hero"
              width={500}
              height={500}
            />
          </div>
        </div>
      </Container>
    </div>
  );
};

export default Hero;
