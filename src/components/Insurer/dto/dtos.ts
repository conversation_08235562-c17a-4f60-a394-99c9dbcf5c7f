// Health Insurer API Response Types
import { ratingCalculator } from "@/components/Company/utility";

export type HealthInsurerClaimSettlementStep = {
  id: string;
  title: string;
  description?: string;
  sequence?: number;
};

export type HealthInsurerClaimSettlementType = {
  id: string;
  title: string;
  type: string; // e.g., "cashless claim", "reimbursement claim"
  health_insurer_claim_settlement_steps: HealthInsurerClaimSettlementStep[];
};

export type HealthInsurerClaimSettlement = {
  id: string;
  title: string;
  health_insurer_claim_settlement_types: HealthInsurerClaimSettlementType[];
};

export type HealthInsurerFAQ = {
  id: string;
  question: string;
  answer: string;
  sequence?: number;
};

export type HealthInsurerPolicyGuidePoint = {
  id: string;
  title: string;
  description?: string;
  sequence?: number;
}

export interface HealthInsurerPolicyGuide {
  id: string;
  title: string;
  health_insurer_policy_guide_points: HealthInsurerPolicyGuidePoint[];
};

export type HealthInsurerProsCons = {
  id: string;
  title: string;
  description: string;
  type: 'pros' | 'cons' | 'pro' | 'con';
};

export type SiteHealthInsurerRating = {
  id: string;
  type: string;
  max_value: number;
  min_value: number;
  max_rating: number;
  min_rating: number;
};

export type HealthInsurerRenewalTypeStep = {
  id: string;
  title: string;
  description?: string;
  sequence?: number;
};

export type HealthInsurerRenewalType = {
  id: string;
  title: string;
  type: string; // e.g., "online renewal", "offline renewal"
  health_insurer_renewal_type_steps: HealthInsurerRenewalTypeStep[];
};

export type HealthInsurerRenewalStep = {
  id: string;
  title: string;
  health_insurer_renewal_types: HealthInsurerRenewalType[];
};

export type HealthInsurerSEO = {
  id: string;
  meta_title: string;
  meta_description: string;
  meta_keyword: string;
  prevent_indexing: boolean;
  source: string;
};

export type HealthInsurerDocument = {
  id: string;
  title: string;
  required_documents: string[];
};

export type HealthInsurerStaticContent = {
  id: string;
  hero_title: string;
  hero_description: string;
  legacy: string;
  verdict: string;
  customer_support_email: string;
  customer_support_number: string;
  kyc_docs: string[];
  renewal_key_points: string[];
};

export type HealthInsurerStatistics = {
  id: string;
  type: string;
  company: number;
  industry: number;
  description: string;
};

export type HealthInsurerTestimonial = {
  id: string;
  name: string;
  content: string;
};

export type HealthInsurerNetworkHospitalDetail = {
  id: string;
  title: string;
  description?: string;
  url?: string;
  network_hospital_count?: string;
  cities_covered?: string;
  states_and_ut?: string;
};

export type HealthInsurerPlan = {
  id: string;
  name: string;
  title: string;
  description?: string;
  features?: string[];
  points?: string[];
  plan_type: 'top' | 'other';
  health_product_variant?: {
    id: string;
    variant_name: string;
    variant_slug: string;
    temp_slug?: string;
  };
};

export type HealthInsurerInsuranceType = {
  id: string;
  name?: string;
  title?: string;
  description?: string;
  button_text?: string;
};

export type HealthInsurerExpertReview = {
  id: string;
  title: string;
  content: string;
  author?: string;
  rating?: number;
  description?: string;
  what_we_like?: string[];
  improvement_areas?: string[];
};

// Main Health Insurer Interface
export type HealthInsurer = {
  id: string;
  name: string;
  slug: string;
  temp_slug: string;
  logo_url: string;
  preferred: boolean;
  claim_settlement_ratio: number;
  network_hospital_url: string;
  renewal_integration_type: 'online' | 'offline';
  renewal_integration_window: string;
  renewal_redirection_url: string;
  solvency: number;
  icr: number;
  growth: number;
  aum: number;
  
  // Arrays
  health_insurer_claim_settlements: HealthInsurerClaimSettlement[];
  health_insurer_expert_reviews: HealthInsurerExpertReview[];
  health_insurer_faqs: HealthInsurerFAQ[];
  health_insurer_insurance_types: HealthInsurerInsuranceType[];
  health_insurer_plans: HealthInsurerPlan[];
  health_insurer_policy_guides: HealthInsurerPolicyGuide[];
  health_insurer_pros_cons: HealthInsurerProsCons[];
  health_insurer_renewal_steps: HealthInsurerRenewalStep[];
  health_insurer_statistics: HealthInsurerStatistics[];
  health_insurer_testimonials: HealthInsurerTestimonial[];
  health_insurer_documents: HealthInsurerDocument[];
  // Single objects
  health_insurer_network_hospital_detail: HealthInsurerNetworkHospitalDetail | null;
  health_insurer_seo: HealthInsurerSEO;
  health_insurer_static_content: HealthInsurerStaticContent;
};

// Utility types for specific use cases
export type HealthInsurerPros = HealthInsurerProsCons & { type: 'pro' };
export type HealthInsurerCons = HealthInsurerProsCons & { type: 'con' };

// Type for FAQ with parsed HTML content
export type HealthInsurerFAQWithParsedContent = Omit<HealthInsurerFAQ, 'answer'> & {
  answer: string; // Raw HTML
  parsedAnswer?: string; // Parsed/cleaned text
};

// Type for renewal process
export type RenewalProcess = {
  steps: HealthInsurerRenewalStep[];
  integrationType: 'online' | 'offline';
  integrationWindow: string;
  redirectionUrl: string;
};

// Type for claim settlement process
export type ClaimSettlementProcess = {
  ratio: number;
  settlements: HealthInsurerClaimSettlement[];
};

// Type for company statistics
export type CompanyStatistics = {
  grossDirectPremium: {
    company: number;
    industry: number;
    description: string;
  };
  icr: {
    company: number;
    industry: number;
    description: string;
  };
  solvencyRatio: {
    company: number;
    industry: number;
    description: string;
  };
};

export type HealthInsurerApiResponse = {
  data: HealthInsurer;
  site_health_insurer_ratings: SiteHealthInsurerRating[];
  success: boolean;
};

// Import types for component interfaces
import { ProsConsData, ProsConsItem } from "../component/ProsConsCard";

function calculateRatingValue(ratings: SiteHealthInsurerRating | undefined, type: string, value: number): number {
  if(!ratings){
    return value;
  }

  if(!value || value===0){
    return 0;
  }
  
  const ratingConfig = ratings;
  const currentValue = value;
  
  
  return ratingCalculator({
    parameter: type.toUpperCase(),
    maxRating: ratingConfig.max_rating,
    minRating: ratingConfig.min_rating,
    paramMaxValue: ratingConfig.max_value,
    paramMinValue: ratingConfig.min_value,
    paramCurrentValue: currentValue,
  });
}

function calculateOverallScore(ratings: SiteHealthInsurerRating[], fallbackData?: { solvency: number; icr: number; growth: number; aum: number }): number {
  if (!ratings || ratings.length === 0) return 0;

  let totalScore = 0;
  ratings.map(rating => {
    const currentValue = rating.type === 'solvency' ? fallbackData?.solvency :
                        rating.type === 'icr' ? fallbackData?.icr :
                        rating.type === 'growth' ? fallbackData?.growth :
                        rating.type === 'aum' ? fallbackData?.aum : undefined;
    
    if (currentValue === undefined || currentValue === null || currentValue === 0) return;

    totalScore += ratingCalculator({
      parameter: rating.type.toUpperCase(),
      maxRating: rating.max_rating,
      minRating: rating.min_rating,
      paramMaxValue: rating.max_value,
      paramMinValue: rating.min_value,
      paramCurrentValue: currentValue,
    });
  });
  
  return totalScore;
}

  // Transformer function to convert API response to component-ready data
export const transformData = (apiResponse: HealthInsurerApiResponse) => {
  const data = apiResponse.data;
  const siteRatings = apiResponse.site_health_insurer_ratings;

  // Hero Section
  const heroSection = {
    name: data.name,
    description: data.health_insurer_static_content.hero_description || "",
    image: data.logo_url,
    stats:[
      {
        id:0,
        title: "Claim Settlement Ratio",
        value: data.claim_settlement_ratio,
        suffix: "%",
        prefix: "Claim Settlement Ratio",
      },
      {
        id:1,
        title: "Network Hospitals",
        value: parseInt(data.health_insurer_network_hospital_detail?.network_hospital_count?.replace(/[^\d]/g, '') || '0'),
        suffix: "+",
        prefix: "Network Hospitals",
      },
      {
        id:2,
        title: "Overall Rating",
        value: calculateOverallScore(siteRatings, { solvency: data.solvency, icr: data.icr, growth: data.growth, aum: data.aum }),
        suffix: "",
        prefix: "Overall Rating",
      }
    ],
    breadcrumbPath: [
      { name: "OneAssure", url: `${process.env.NEXT_PUBLIC_BASE_URL}/` },
      { name: "Health Insurance", url: `${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/` },
      { name: data.name, url: `${process.env.NEXT_PUBLIC_BASE_URL}/${data.slug}-health-insurance/hi/${data.id}` },
    ],
  };

  // Page Navigation Section
  const pageNavigationSection = {
    activeTab: "expert-review",
    tabs: [
      { label: "Expert Review", id: "expert-review" },
      { label: "Top Plans", id: "top-plans" },
      { label: "Testimonials", id: "testimonials" },
      { label: "Pros and Cons", id: "pros-and-cons" },
      { label: "Documents Required", id: "documents-required" },
      { label: "Claim Settlement Process", id: "claim-settlement" },
      { label: "Renewal Process", id: "renewal-process" },
      { label: "How to Buy", id: "how-to-buy" },
      { label: "FAQs", id: "faqs" },
    ],
  };

  // Expert Review Section
  const expertReviewSection = {
    pill: data.name || "Insurance Company",
    heading: data.health_insurer_expert_reviews[0]?.title || "Expert Review",
    subheading: data.health_insurer_expert_reviews[0]?.description || "",
    whatWeLike: {
      heading: "What We Like",
      points: data.health_insurer_expert_reviews[0]?.what_we_like || [],
    },
    AreasOfImprovement: {
      heading: "Areas of Improvement", 
      points: data.health_insurer_expert_reviews[0]?.improvement_areas || [],
    },
    verdict: data.health_insurer_static_content.verdict || "",
  };

  // Top Plans Section for CategoryCards
  const topPlansSection = {
    pill: data.name || "Insurance Company",
    heading: `Top ${data.name} Plans`,
    subHeading: "Discover our most popular and comprehensive health insurance plans designed to meet your healthcare needs.",
    categories: data.health_insurer_plans
      .filter(plan => plan.plan_type === 'top')
      .map((plan, index) => ({
        icon: data.logo_url,
        title: plan.title,
        button: "View Plan",
        features: plan.points || [],
        buttonColor: "primary",
        cardBg: "bg-white",
        cardBorder: "border-gray-200",
        mostPopular: index === 1,
        redirectUrl: process.env.NEXT_PUBLIC_BASE_URL + "/health-insurance/" + data.temp_slug + "/" + plan.health_product_variant?.temp_slug,
      })),
  };

  // Other Plans Section for CategoryCards  
  const otherPlansSection = {
    pill: data.name || "Insurance Company",
    heading: `Other ${data.name} Plans`,
    subHeading: "Explore additional health insurance options including super top-up and specialized coverage plans.",
    categories: data.health_insurer_plans
      .filter(plan => plan.plan_type === 'other')
      .map(plan => ({
        icon: data.logo_url,
        title: plan.title,
        button: "View Plan", 
        features: plan.points || [],
        buttonColor: "primary",
        cardBg: "bg-white",
        cardBorder: "border-gray-200",
        mostPopular: false,
        redirectUrl: process.env.NEXT_PUBLIC_BASE_URL + "/health-insurance/" + data.temp_slug + "/" + plan.health_product_variant?.temp_slug,
      })),
  };

  // About Section
  const aboutSection = {
    pill: data.name || "Insurance Company",
    heading: `About ${data.name}`,
    content: data.health_insurer_static_content.legacy || "",
  };

  // Rating Section
  const ratingSection = {
    pill: data.name || "Insurance Company",
    heading: `${data.name} Rating`,
    rating: calculateRatingValue(siteRatings.find(rating => rating.type === 'solvency'), 'solvency', data.solvency).toString(),
    overallRating: {
      // score: data.health_insurer_ratings[0]?.one_assure_rating || 0,
      score: calculateOverallScore(siteRatings, { solvency: data.solvency, icr: data.icr, growth: data.growth, aum: data.aum }),
      maxScore: siteRatings.reduce((max, rating) => max + rating.max_rating, 0),
      metrics: [
        { 
          label: "Solvency", 
          value: calculateRatingValue(siteRatings.find(rating => rating.type === 'solvency'), 'solvency', data.solvency), 
          total: siteRatings.find(rating => rating.type === 'solvency')?.max_rating || 4
        },
        { 
          label: "ICR", 
          value: calculateRatingValue(siteRatings.find(rating => rating.type === 'icr'), 'icr', data.icr), 
          total: siteRatings.find(rating => rating.type === 'icr')?.max_rating || 3
        },
        { 
          label: "Growth", 
          value: calculateRatingValue(siteRatings.find(rating => rating.type === 'growth'), 'growth', data.growth), 
          total: siteRatings.find(rating => rating.type === 'growth')?.max_rating || 3
        },
        { 
          label: "AUM", 
          value: calculateRatingValue(siteRatings.find(rating => rating.type === 'aum'), 'aum', data.aum), 
          total: siteRatings.find(rating => rating.type === 'aum')?.max_rating || 3
        }
      ],
    },
    performanceIndex: {
      companyLabel: data.name,
      industryLabel: "Industry Average",
      gdpCompany: {
        value: data.health_insurer_statistics.find(stat => stat.type === 'gross direct premium')?.company || 0,
        valueLabel: `${data.health_insurer_statistics.find(stat => stat.type === 'gross direct premium')?.company || 0}Cr`
      },
      gdpIndustry: {
        value: data.health_insurer_statistics.find(stat => stat.type === 'gross direct premium')?.industry || 0,
        valueLabel: `${data.health_insurer_statistics.find(stat => stat.type === 'gross direct premium')?.industry || 0}Cr`
      },
      gdpTotal: Math.max(
        data.health_insurer_statistics.find(stat => stat.type === 'gross direct premium')?.company || 0,
        data.health_insurer_statistics.find(stat => stat.type === 'gross direct premium')?.industry || 0
      ) * 1.2,
      icrCompany: {
        value: data.health_insurer_statistics.find(stat => stat.type === 'icr')?.company || 0,
        valueLabel: `${data.health_insurer_statistics.find(stat => stat.type === 'icr')?.company || 0}%`
      },
      icrIndustry: {
        value: data.health_insurer_statistics.find(stat => stat.type === 'icr')?.industry || 0,
        valueLabel: `${data.health_insurer_statistics.find(stat => stat.type === 'icr')?.industry || 0}%`
      },
    },
  };

  // Testimonials Section
  const testimonialsSection = {
    testimonials: data.health_insurer_testimonials.map(testimonial => ({
      id: testimonial.id,
      name: testimonial.name,
      content: testimonial.content,
    })),
    sectionHeaderProps: {
      id: "testimonials",
      pill: data.name || "Insurance Company",
      heading: "Customer Testimonials",
      subheading: "Real experiences from our valued customers who have chosen our insurance coverage.",
    },
  };

  // Pros and Cons Section - Transform to new structure
  const prosConsArray = data.health_insurer_pros_cons || [];
  const prosConsSection = {
    pill: data.name || "Insurance Company",
    heading: "Pros and Cons",
    data: {
      pros: prosConsArray
        .filter(item => item.type === 'pros' || item.type === 'pro')
        .map(item => ({
          id: item.id,
          title: item.title,
          description: item.description,
          type: 'pro' as const,
        })),
      cons: prosConsArray
        .filter(item => item.type === 'cons' || item.type === 'con')
        .map(item => ({
          id: item.id,
          title: item.title,
          description: item.description,
          type: 'con' as const,
        })),
    } as ProsConsData,
  };

  // Lead Form Sections
  const leadFormSection = {
    pill: data.name || "Insurance Company",
    title: "Get a free quote",
    description: "Get a free quote",
  };

  // Insurer Types Section
  const insurerTypesSection = {
    title: `${data.name} Types`,
    types:  data.health_insurer_insurance_types.map(type => {
      return {
        id: type.id,
        title: type.title || type.name || `Insurance Type ${type.id}`,
        description: type.description || `Comprehensive ${type.title || type.name || 'insurance'} coverage for your needs.`,
        button_text: type.button_text || "Get Quote",
      };
    })
  };

  // Documents Section
  const documentsSection = {
    title: "Documents Required",
    documents: data.health_insurer_documents || [],
    customerSupport: {
      email: data.health_insurer_static_content.customer_support_email || "",
      number: data.health_insurer_static_content.customer_support_number || "",
    },
  };

  // Claim Settlement Section
  const claimSettlementSection = {
    pill: data.name || "Insurance Company",
    heading: "Claim Settlement Process",
    subheading: "Quick and hassle-free claim settlement with dedicated support to ensure timely approvals and smooth hospital experiences",
    id: "claim-settlement",
    ratio: data.claim_settlement_ratio,
    settlements: data.health_insurer_claim_settlements.flatMap(settlement => 
      settlement.health_insurer_claim_settlement_types.map((type: HealthInsurerClaimSettlementType) => ({
        id: type.id,
        title: type.type,
        types: type.health_insurer_claim_settlement_steps
          .sort((a, b) => (a.sequence || 0) - (b.sequence || 0))
          .map((step: HealthInsurerClaimSettlementStep) => ({
            id: step.id,
            title: step.title,
            description: step.description || "",
          })),
      }))
    ),
  };

  // Renewal Section
  const renewalSection = {
    pill: data.name || "Insurance Company",
    heading: "Renewal Process",
    subheading: "Easily renew your policy online or offline to enjoy uninterrupted health coverage",
    id: "renewal-process",
    integrationType: data.renewal_integration_type,
    integrationWindow: data.renewal_integration_window,
    redirectionUrl: data.renewal_redirection_url,
    keyPoints: data.health_insurer_static_content.renewal_key_points || [],
    steps: data.health_insurer_renewal_steps.flatMap((step: HealthInsurerRenewalStep) => 
      step.health_insurer_renewal_types.map((type: HealthInsurerRenewalType) => ({
        id: type.id,
        title: type.type,
        types: type.health_insurer_renewal_type_steps
          .sort((a, b) => (a.sequence || 0) - (b.sequence || 0))
          .map((renewalStep: HealthInsurerRenewalTypeStep) => ({
            id: renewalStep.id,
            title: renewalStep.title,
            description: renewalStep.description || "",
          })),
      }))
    ),
  };

  // Network Hospital Section
  const networkHospitalSection = {
    title: data.health_insurer_network_hospital_detail?.title || `${data.name} Network Hospitals`,
    description: data.health_insurer_network_hospital_detail?.description || "",
    url: data.health_insurer_network_hospital_detail?.url || data.network_hospital_url,
    cards: [
      {
        id: 1,
        iconKey: "hospital",
        title: data.health_insurer_network_hospital_detail?.network_hospital_count || "10,000+",
        description: "Network hospitals across the country for cashless treatment",
      },
      {
        id: 2,
        iconKey: "location",
        title: data.health_insurer_network_hospital_detail?.cities_covered || "100+",
        description: "Cities covered nationwide for easy access to quality healthcare",
      },
      {
        id: 3,
        iconKey: "map",
        title: data.health_insurer_network_hospital_detail?.states_and_ut || "All India",
        description: "States and union territories ensuring comprehensive coverage",
      },
    ],
  };

  // Features Comparison Section
  const featuresComparisonSection = {
    pill: data.name || "Insurance Company",
    heading: "Features Comparison",
    subHeading: "Compare the features of the top health insurance policies to find the perfect plan for your needs.",
  };

  // How to Buy Section for FAQs Component
  const howToBuySection = {
    pill: data.name || "Insurance Company",
    heading: `How to Buy ${data.name}?`,
    subheading: `Follow these simple steps to purchase your ${data.name} health insurance policy and secure comprehensive coverage for you and your family.`,
    faqs: data.health_insurer_policy_guides.flatMap(guide => 
      guide.health_insurer_policy_guide_points
        .sort((a, b) => (a.sequence || 0) - (b.sequence || 0))
        .map(point => ({
          id: point.id,
          question: point.title,
          answer: point.description || "",
        }))
    ),
    id: "how-to-buy",
  };

  // Things to Remember Section
  const thingsRememberSection = {
    title: "Things to Remember",
    points: data.health_insurer_static_content.renewal_key_points || [],
  };

  // FAQs Section
  const faqsSection = {
    pill: data.name || "Insurance Company",
    heading: "Frequently Asked Questions",
    subheading: "Get answers to common questions about our insurance policies and services.",
    faqs: data.health_insurer_faqs
      .sort((a, b) => (a.sequence || 0) - (b.sequence || 0))
      .map(faq => ({
        id: faq.id,
        question: faq.question,
        answer: faq.answer,
      })),
    id: "faqs",
  };

  // Policy Guide Section
  const policyGuideSection = {
    guides: data.health_insurer_policy_guides.map(guide => ({
      id: guide.id,
      title: guide.title,
      points: guide.health_insurer_policy_guide_points
        .sort((a, b) => (a.sequence || 0) - (b.sequence || 0))
        .map(point => ({
          id: point.id,
          title: point.title,
          description: point.description || "",
        })),
    })),
  };

  // Plans Section
  const plansSection = {
    plans: data.health_insurer_plans.map(plan => ({
      id: plan.id,
      name: plan.name,
      description: plan.description || "",
      features: plan.features || [],
    })),
  };

  // Statistics Section
  const statisticsSection = {
    statistics: data.health_insurer_statistics.map(stat => ({
      id: stat.id,
      type: stat.type,
      company: stat.company,
      industry: stat.industry,
      description: stat.description,
    })),
  };

  // SEO Section
  const seoSection = {
    metaTitle: data.health_insurer_seo.meta_title,
    metaDescription: data.health_insurer_seo.meta_description,
    metaKeyword: data.health_insurer_seo.meta_keyword,
    preventIndexing: data.health_insurer_seo.prevent_indexing,
    source: data.health_insurer_seo.source,
  };

  return {
    // Basic Info
    id: data.id,
    name: data.name,
    slug: data.slug,
    logoUrl: data.logo_url,
    preferred: data.preferred,

    // Sections
    heroSection,
    pageNavigationSection,
    expertReviewSection,
    topPlansSection,
    otherPlansSection,
    aboutSection,
    ratingSection,
    testimonialsSection,
    prosConsSection,
    leadFormSection,
    insurerTypesSection,
    documentsSection,
    claimSettlementSection,
    renewalSection,
    networkHospitalSection,
    featuresComparisonSection,
    howToBuySection,
    thingsRememberSection,
    faqsSection,
    policyGuideSection,
    plansSection,
    statisticsSection,
    seoSection,

    // Raw data for backward compatibility
    health_insurer_expert_reviews: data.health_insurer_expert_reviews,
    health_insurer_pros_cons: data.health_insurer_pros_cons,
    health_insurer_testimonials: data.health_insurer_testimonials,
    health_insurer_faqs: data.health_insurer_faqs,
    legacy: data.health_insurer_static_content.legacy,
    logo_url: data.logo_url,
  };
};
export default transformData;
