"use client";

import { useState } from "react";
import Hero from "@/components/Insurer/component/Hero";
import Documents from "@/components/Insurer/component/Documents";
import NetworkHospital from "@/components/Insurer/component/NetworkHospital";
import AccordianSection from "@/components/globals/AccordianSection";
import ClaimTypes from "@/components/Insurer/component/ClaimTypes";
import RenewalTypes from "@/components/Insurer/component/RenewalTypes";
import ExpertReview from "@/components/Insurer/component/ExpertReview";
import PageNavigation from "@/components/globals/PageNavigation";
import GoToTopFloater from "@/components/globals/GoToTopFloater";
import SharePageFloater from "@/components/globals/SharePageFloater";
import CategoryCards from "@/components/globals/CategoryCards";
import Testimonial from "@/components/globals/Testimonial";
import { featuresComparisonData } from "@/components/Compare/data/featuresComparisonData";
import LeadForm from "@/components/globals/LeadForm";
import {
  transformData,
  HealthInsurerApiResponse,
} from "@/components/Insurer/plans/dto";
import RelatedBlogs from "@/components/globals/RelatedBlogs";
import InsurerPlan from "@/components/globals/InsurerPlan";
import OrufyFloater from "@/components/globals/OrufyFloater";
import WhyChooseUs from "@/components/Insurer/component/WhyChooseUs";
import PlanListing from "@/components/Insurer/component/PlanListing";
import PlansSection from "@/components/globals/DSComponentsV0/PlansSection";

const Plans = ({
  data,
  allInsurerData,
  blogData,
}: {
  data: ReturnType<typeof transformData>;
  allInsurerData?: any[];
  blogData?: {
    heading: string;
    blogs: Array<{
      title: string;
      date: string;
      author: string;
      description: string;
      imageUrl: string;
      url: string;
    }>;
  };
}) => {
  const [activeTab, setActiveTab] = useState("expert-review");

  // Transform the raw API data to component-ready format
  const transformedData = data;

  return (
    <div>
      <Hero
        name={transformedData.heroSection.name}
        description={transformedData.heroSection.description}
        image={transformedData.heroSection.image}
        slug={transformedData.slug}
        stats={transformedData.heroSection.stats}
        breadcrumbPath={transformedData.heroSection.breadcrumbPath}
      />
      <PageNavigation
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        tabs={transformedData.pageNavigationSection.tabs}
      />
      <ExpertReview
        pill={transformedData.expertReviewSection.pill}
        heading={transformedData.expertReviewSection.heading}
        subheading={transformedData.expertReviewSection.subheading}
        whatWeLike={transformedData.expertReviewSection.whatWeLike}
        AreasOfImprovement={
          transformedData.expertReviewSection.AreasOfImprovement
        }
        verdict={transformedData.expertReviewSection.verdict}
      />
      {/* {transformedData.planListingSection.plan_listing.length > 0 && (
        <PlanListing planListingSection={transformedData.planListingSection} />
      )} */}
      <PlanListing planListingSection={transformedData.allPlans} />
      {/* <PlansSection
        id={transformedData.allPlans.id}
        heading={transformedData.allPlans.title}
        pill={transformedData.allPlans.pill}
        plans={transformedData.allPlans.plans}
      /> */}

      <Testimonial
        testimonials={transformedData.testimonialsSection.testimonials}
        sectionHeaderProps={
          transformedData.testimonialsSection.sectionHeaderProps
        }
        pill={transformedData.testimonialsSection.sectionHeaderProps.pill}
      />
      <WhyChooseUs whyChooseUs={transformedData.whyChooseUsSection} />
      <Documents
        title={transformedData.documentsSection.title}
        documents={transformedData.documentsSection.documents}
        customerSupport={transformedData.documentsSection.customerSupport}
      />
      {/* Cashless & Reimbursement Claims */}
      <ClaimTypes
        pill={transformedData.claimSettlementSection.pill}
        heading={transformedData.claimSettlementSection.heading}
        subheading={transformedData.claimSettlementSection.subheading}
        claimSettlements={transformedData.claimSettlementSection.settlements}
      />
      {/* Renewal Process */}
      <RenewalTypes
        pill={transformedData.renewalSection.pill}
        heading={transformedData.renewalSection.heading}
        subheading={transformedData.renewalSection.subheading}
        renewalSteps={transformedData.renewalSection.steps}
      />
      {/* Network Hospital */}
      <NetworkHospital
        title={transformedData.networkHospitalSection.title}
        cards={transformedData.networkHospitalSection.cards}
      />

      {/* Related Insurer Plans */}
      <CategoryCards
        pill={transformedData.featuresComparisonSection.pill}
        heading={transformedData.featuresComparisonSection.heading}
        subHeading={transformedData.featuresComparisonSection.subHeading}
        categories={featuresComparisonData.plans}
      />

      <LeadForm
        pill={transformedData.leadFormSection.pill}
        title="Need help finding the right health plan?"
        description="Our experts offer personalised recommendations tailored to your age, family size, and health profile, ensuring you select the ideal coverage"
      />
      {/* FAQs */}
      <AccordianSection
        pill={transformedData.faqsSection.pill}
        heading={transformedData.faqsSection.heading}
        subheading={transformedData.faqsSection.subheading}
        faqs={transformedData.faqsSection.faqs}
        id="faqs"
        sectionTitle="FAQs"
      />
      {/* Insurer Plans */}
      {allInsurerData && allInsurerData.length > 0 && (
        <InsurerPlan allInsurerData={allInsurerData} />
      )}
      {/* Related Blogs */}
      {blogData && blogData.blogs.length > 0 && (
        <RelatedBlogs blogData={blogData} />
      )}
      <GoToTopFloater />
      <OrufyFloater />
      <SharePageFloater />
    </div>
  );
};

export default Plans;
