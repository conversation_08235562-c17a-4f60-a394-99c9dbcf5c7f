// Health Insurer API Response Types
import { ratingCalculator } from "@/components/Company/utility";

export type HealthInsurerClaimSettlementStep = {
  id: string;
  title: string;
  description?: string;
  sequence?: number;
};

export type HealthInsurerClaimSettlementType = {
  id: string;
  title: string;
  type: string; // e.g., "cashless claim", "reimbursement claim"
  health_insurer_claim_settlement_steps: HealthInsurerClaimSettlementStep[];
};

export type HealthInsurerClaimSettlement = {
  id: string;
  title: string;
  health_insurer_claim_settlement_types: HealthInsurerClaimSettlementType[];
};

export type HealthInsurerFAQ = {
  id: string;
  question: string;
  answer: string;
  sequence?: number;
};

export type SiteHealthInsurerRating = {
  id: string;
  type: string;
  max_value: number;
  min_value: number;
  max_rating: number;
  min_rating: number;
};

export type HealthInsurerRenewalTypeStep = {
  id: string;
  title: string;
  description?: string;
  sequence?: number;
};

export type HealthInsurerRenewalType = {
  id: string;
  title: string;
  type: string; // e.g., "online renewal", "offline renewal"
  health_insurer_renewal_type_steps: HealthInsurerRenewalTypeStep[];
};

export type HealthInsurerRenewalStep = {
  id: string;
  title: string;
  health_insurer_renewal_types: HealthInsurerRenewalType[];
};

export type HealthInsurerSEO = {
  id: string;
  meta_title: string;
  meta_description: string;
  meta_keyword: string;
  prevent_indexing: boolean;
  source: string;
  canonical: string;
};

export type HealthInsurerDocument = {
  id: string;
  title: string;
  required_documents: string[];
};

export type HealthInsurerStaticContent = {
  id: string;
  hero_title: string;
  hero_description: string;
  legacy: string;
  verdict: string;
  customer_support_email: string;
  customer_support_number: string;
  kyc_docs: string[];
  renewal_key_points: string[];
};

export type HealthInsurerTestimonial = {
  id: string;
  name: string;
  content: string;
};

export type HealthInsurerNetworkHospitalDetail = {
  id: string;
  title: string;
  description?: string;
  url?: string;
  network_hospital_count?: string;
  cities_covered?: string;
  states_and_ut?: string;
};

export type HealthInsurerExpertReview = {
  id: string;
  title: string;
  content: string;
  author?: string;
  rating?: number;
  description?: string;
  what_we_like?: string[];
  improvement_areas?: string[];
};

export type HealthInsurerWhyChooseUsCard = {
  id: string;
  title: string;
  description: string;
  icon_url: string;
};

export type HealthInsurerWhyChooseUs = {
  id: string;
  title: string;
  site_health_insurer_why_choose_us_cards: HealthInsurerWhyChooseUsCard[];
};

export type HealthProductVariant = {
  id: string;
  variant_name: string;
  variant_slug: string;
  temp_slug?: string;
};

export type HealthInsurerPlanListingCard = {
  id: string;
  health_variant_id: string;
  health_product_variant: HealthProductVariant;
};

export type HealthInsurerPlanListing = {
  id: string;
  title: string;
  site_health_insurer_plan_listing_cards: HealthInsurerPlanListingCard[];
};

// Main Health Insurer Interface
export type HealthInsurer = {
  id: string;
  name: string;
  slug: string;
  temp_slug: string;
  logo_url: string;
  preferred: boolean;
  claim_settlement_ratio: number;
  network_hospital_url: string;
  renewal_integration_type: 'online' | 'offline';
  renewal_integration_window: string;
  renewal_redirection_url: string;
  solvency: number;
  icr: number;
  growth: number;
  aum: number;
  
  // Arrays
  health_insurer_claim_settlements: HealthInsurerClaimSettlement[];
  health_insurer_expert_reviews: HealthInsurerExpertReview[];
  health_insurer_faqs: HealthInsurerFAQ[];
  health_insurer_renewal_steps: HealthInsurerRenewalStep[];
  health_insurer_testimonials: HealthInsurerTestimonial[];
  health_insurer_documents: HealthInsurerDocument[];
  health_insurer_why_choose_us: HealthInsurerWhyChooseUs[];
  health_insurer_plan_listing: HealthInsurerPlanListing[];
  products: {
    product_variants: HealthProductVariant[];
  }[];

  // Single objects
  health_insurer_network_hospital_detail: HealthInsurerNetworkHospitalDetail | null;
  site_health_insurer_plan_listing_seo: HealthInsurerSEO;
  health_insurer_static_content: HealthInsurerStaticContent;
};

// Type for FAQ with parsed HTML content
export type HealthInsurerFAQWithParsedContent = Omit<HealthInsurerFAQ, 'answer'> & {
  answer: string; // Raw HTML
  parsedAnswer?: string; // Parsed/cleaned text
};

// Type for renewal process
export type RenewalProcess = {
  steps: HealthInsurerRenewalStep[];
  integrationType: 'online' | 'offline';
  integrationWindow: string;
  redirectionUrl: string;
};

// Type for claim settlement process
export type ClaimSettlementProcess = {
  ratio: number;
  settlements: HealthInsurerClaimSettlement[];
};

// Type for company statistics
export type CompanyStatistics = {
  grossDirectPremium: {
    company: number;
    industry: number;
    description: string;
  };
  icr: {
    company: number;
    industry: number;
    description: string;
  };
  solvencyRatio: {
    company: number;
    industry: number;
    description: string;
  };
};

export type HealthInsurerApiResponse = {
  data: HealthInsurer;
  site_health_insurer_ratings: SiteHealthInsurerRating[];
  success: boolean;
};

function calculateOverallScore(ratings: SiteHealthInsurerRating[], fallbackData?: { solvency: number; icr: number; growth: number; aum: number }): number {
  if (!ratings || ratings.length === 0) return 0;

  let totalScore = 0;
  ratings.map(rating => {
    const currentValue = rating.type === 'solvency' ? fallbackData?.solvency :
                        rating.type === 'icr' ? fallbackData?.icr :
                        rating.type === 'growth' ? fallbackData?.growth :
                        rating.type === 'aum' ? fallbackData?.aum : undefined;
    
    if (currentValue === undefined || currentValue === null || currentValue === 0) return;

    totalScore += ratingCalculator({
      parameter: rating.type.toUpperCase(),
      maxRating: rating.max_rating,
      minRating: rating.min_rating,
      paramMaxValue: rating.max_value,
      paramMinValue: rating.min_value,
      paramCurrentValue: currentValue,
    });
  });
  
  return totalScore;
}

  // Transformer function to convert API response to component-ready data
export const transformData = (apiResponse: HealthInsurerApiResponse) => {
  const data = apiResponse.data;
  const siteRatings = apiResponse.site_health_insurer_ratings;

  // Hero Section
  const heroSection = {
    name: data.name + " Plans",
    description: data.health_insurer_static_content.hero_description || "",
    image: data.logo_url,
    stats:[
      {
        id:0,
        title: "Claim Settlement Ratio",
        value: data.claim_settlement_ratio,
        suffix: "%",
        prefix: "Claim Settlement Ratio",
      },
      {
        id:1,
        title: "Network Hospitals",
        value: parseInt(data.health_insurer_network_hospital_detail?.network_hospital_count?.replace(/[^\d]/g, '') || '0'),
        suffix: "+",
        prefix: "Network Hospitals",
      },
      {
        id:2,
        title: "Overall Rating",
        value: calculateOverallScore(siteRatings, { solvency: data.solvency, icr: data.icr, growth: data.growth, aum: data.aum }),
        suffix: "",
        prefix: "Overall Rating",
      }
    ],
    breadcrumbPath: [
      { name: "OneAssure", url: `${process.env.NEXT_PUBLIC_BASE_URL}/` },
      { name: "Health Insurance", url: `${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/` },
      { name: data.name, url: `${process.env.NEXT_PUBLIC_BASE_URL}/${data.slug}-health-insurance/hi/${data.id}` },
      { name: "Health Insurance Plans", url: `${process.env.NEXT_PUBLIC_BASE_URL}/${data.slug}-health-insurance-plans/hl/${data.id}` },
    ],
  };

  // Page Navigation Section
  const pageNavigationSection = {
    activeTab: "expert-review",
    tabs: [
      { label: "Expert Review", id: "expert-review" },
      { label: "Testimonials", id: "testimonials" },
      { label: "Documents Required", id: "documents-required" },
      { label: "Claim Settlement Process", id: "claim-settlement" },
      { label: "Renewal Process", id: "renewal-process" },
      { label: "FAQs", id: "faqs" },
    ],
  };

  // Expert Review Section
  const expertReviewSection = {
    pill: data.name || "Insurance Company",
    heading: data.health_insurer_expert_reviews[0]?.title || "Expert Review",
    subheading: data.health_insurer_expert_reviews[0]?.description || "",
    whatWeLike: {
      heading: "What We Like",
      points: data.health_insurer_expert_reviews[0]?.what_we_like || [],
    },
    AreasOfImprovement: {
      heading: "Areas of Improvement", 
      points: data.health_insurer_expert_reviews[0]?.improvement_areas || [],
    },
    verdict: data.health_insurer_static_content.verdict || "",
  };

  // Testimonials Section
  const testimonialsSection = {
    testimonials: data.health_insurer_testimonials.map(testimonial => ({
      id: testimonial.id,
      name: testimonial.name,
      content: testimonial.content,
    })),
    sectionHeaderProps: {
      id: "testimonials",
      pill: data.name || "Insurance Company",
      heading: "Customer Testimonials",
      subheading: "Real experiences from our valued customers who have chosen our insurance coverage.",
    },
  };

  // Lead Form Sections
  const leadFormSection = {
    pill: data.name || "Insurance Company",
    title: "Get a free quote",
    description: "Get a free quote",
  };

  // Documents Section
  const documentsSection = {
    title: "Documents Required",
    documents: data.health_insurer_documents || [],
    customerSupport: {
      email: data.health_insurer_static_content.customer_support_email || "",
      number: data.health_insurer_static_content.customer_support_number || "",
    },
  };

  // Claim Settlement Section
  const claimSettlementSection = {
    pill: data.name || "Insurance Company",
    heading: "Claim Settlement Process",
    subheading: "Quick and hassle-free claim settlement with dedicated support to ensure timely approvals and smooth hospital experiences",
    id: "claim-settlement",
    ratio: data.claim_settlement_ratio,
    settlements: data.health_insurer_claim_settlements.flatMap(settlement => 
      settlement.health_insurer_claim_settlement_types.map((type: HealthInsurerClaimSettlementType) => ({
        id: type.id,
        title: type.type,
        types: type.health_insurer_claim_settlement_steps
          .sort((a, b) => (a.sequence || 0) - (b.sequence || 0))
          .map((step: HealthInsurerClaimSettlementStep) => ({
            id: step.id,
            title: step.title,
            description: step.description || "",
          })),
      }))
    ),
  };

  // Renewal Section
  const renewalSection = {
    pill: data.name || "Insurance Company",
    heading: "Renewal Process",
    subheading: "Easily renew your policy online or offline to enjoy uninterrupted health coverage",
    id: "renewal-process",
    integrationType: data.renewal_integration_type,
    integrationWindow: data.renewal_integration_window,
    redirectionUrl: data.renewal_redirection_url,
    keyPoints: data.health_insurer_static_content.renewal_key_points || [],
    steps: data.health_insurer_renewal_steps.flatMap((step: HealthInsurerRenewalStep) => 
      step.health_insurer_renewal_types.map((type: HealthInsurerRenewalType) => ({
        id: type.id,
        title: type.type,
        types: type.health_insurer_renewal_type_steps
          .sort((a, b) => (a.sequence || 0) - (b.sequence || 0))
          .map((renewalStep: HealthInsurerRenewalTypeStep) => ({
            id: renewalStep.id,
            title: renewalStep.title,
            description: renewalStep.description || "",
          })),
      }))
    ),
  };

  // Network Hospital Section
  const networkHospitalSection = {
    title: data.health_insurer_network_hospital_detail?.title || `${data.name} Network Hospitals`,
    description: data.health_insurer_network_hospital_detail?.description || "",
    url: data.health_insurer_network_hospital_detail?.url || data.network_hospital_url,
    cards: [
      {
        id: 1,
        iconKey: "hospital",
        title: data.health_insurer_network_hospital_detail?.network_hospital_count || "10,000+",
        description: "Network hospitals across the country for cashless treatment",
      },
      {
        id: 2,
        iconKey: "location",
        title: data.health_insurer_network_hospital_detail?.cities_covered || "100+",
        description: "Cities covered nationwide for easy access to quality healthcare",
      },
      {
        id: 3,
        iconKey: "map",
        title: data.health_insurer_network_hospital_detail?.states_and_ut || "All India",
        description: "States and union territories ensuring comprehensive coverage",
      },
    ],
  };

  // Features Comparison Section
  const featuresComparisonSection = {
    pill: data.name || "Insurance Company",
    heading: "Features Comparison",
    subHeading: "Compare the features of the top health insurance policies to find the perfect plan for your needs.",
  };


  // FAQs Section
  const faqsSection = {
    pill: data.name || "Insurance Company",
    heading: "Frequently Asked Questions",
    subheading: "Get answers to common questions about our insurance policies and services.",
    faqs: data.health_insurer_faqs
      .sort((a, b) => (a.sequence || 0) - (b.sequence || 0))
      .map(faq => ({
        id: faq.id,
        question: faq.question,
        answer: faq.answer,
      })),
    id: "faqs",
  };

  const whyChooseUsSection = data.health_insurer_why_choose_us.map(whyChooseUs => ({
    id: whyChooseUs.id,
    title: whyChooseUs.title,
    cards: whyChooseUs.site_health_insurer_why_choose_us_cards.map(card => ({
      id: card.id,
      title: card.title,
      description: card.description,
      iconUrl: card.icon_url,
    })),
  }));

  const planListingSection = {
    id: data.id,
    insurerName: data.name,
    insurerSlug: data.temp_slug,
    logoUrl: data.logo_url,
    plan_listing: data.health_insurer_plan_listing.map(planListing => ({
      id: planListing.id,
      title: planListing.title,
      cards: planListing.site_health_insurer_plan_listing_cards.map(card => ({
        id: card.id,
        healthVariantId: card.health_variant_id,
        healthProductVariant: card.health_product_variant,
      })),
    })),
  }

  // SEO Section
  const seoSection = {
    metaTitle: data.site_health_insurer_plan_listing_seo?.meta_title || "",
    metaDescription: data.site_health_insurer_plan_listing_seo?.meta_description || "",
    metaKeyword: data.site_health_insurer_plan_listing_seo?.meta_keyword || "",
    preventIndexing: data.site_health_insurer_plan_listing_seo?.prevent_indexing || false,
    source: data.site_health_insurer_plan_listing_seo?.source || "",
    canonical: data.site_health_insurer_plan_listing_seo?.canonical || "",
  };

  const allVariants: {
    id: string;
    healthVariantId: string;
    healthProductVariant: HealthProductVariant;
  }[] = [];

  data.products.forEach(product => {
    product.product_variants.forEach(variant => {
      if(variant.temp_slug) {
        allVariants.push({
          id: variant.id,
          healthVariantId: variant.id,
          healthProductVariant: variant,
        });
      }
    });
  });

  // Transform variants for PlansSection component
  const plansSectionPlans = allVariants.map(variant => ({
    logo_url: data.logo_url,
    plan_title: variant.healthProductVariant.variant_name || `Plan ${variant.id}`,
    redirect_url: `/health-insurance/${data.temp_slug}/${variant.healthProductVariant.temp_slug}` || "#",
  }));

  const allPlans = {
    id: data.id,
    title: `${data.name} Health Insurance Plans`,
    pill: "Insurance Plans",
    plans: plansSectionPlans,
    // Legacy structure for backward compatibility
    insurerName: data.name,
    insurerSlug: data.temp_slug,
    logoUrl: data.logo_url,
    plan_listing: [{
      id:"all-plans",
      title: "All Plans",
      cards: allVariants,
    }]
  }

  return {
    // Basic Info
    id: data.id,
    name: data.name,
    slug: data.slug,
    logoUrl: data.logo_url,
    preferred: data.preferred,

    // Sections
    heroSection,
    pageNavigationSection,
    expertReviewSection,
    testimonialsSection,
    leadFormSection,
    documentsSection,
    claimSettlementSection,
    renewalSection,
    networkHospitalSection,
    featuresComparisonSection,
    faqsSection,
    whyChooseUsSection,
    planListingSection,
    seoSection,
    allPlans,

    // Raw data for backward compatibility
    health_insurer_expert_reviews: data.health_insurer_expert_reviews,
    health_insurer_testimonials: data.health_insurer_testimonials,
    health_insurer_faqs: data.health_insurer_faqs,
    legacy: data.health_insurer_static_content.legacy,
    logo_url: data.logo_url,
  };
};

export type HealthInsurerPlanDataTransformed = ReturnType<typeof transformData>;

export default transformData;
