"use client";

import { useState } from "react";
import Hero from "@/components/Insurer/component/Hero";
import About from "@/components/Insurer/component/About";
import Rating from "@/components/Insurer/component/Rating";
import InsurerTypes from "@/components/Insurer/component/InsurerTypes";
import Documents from "@/components/Insurer/component/Documents";
import NetworkHospital from "@/components/Insurer/component/NetworkHospital";
import ProsCons from "@/components/Insurer/component/ProsCons";
import AccordianSection from "@/components/globals/AccordianSection";
import ClaimTypes from "@/components/Insurer/component/ClaimTypes";
import RenewalTypes from "@/components/Insurer/component/RenewalTypes";
import ExpertReview from "@/components/Insurer/component/ExpertReview";
import PageNavigation from "@/components/globals/PageNavigation";
import GoToTopFloater from "@/components/globals/GoToTopFloater";
import SharePageFloater from "@/components/globals/SharePageFloater";
import CategoryCards from "@/components/globals/CategoryCards";
import Testimonial from "@/components/globals/Testimonial";
import { featuresComparisonData } from "@/components/Compare/data/featuresComparisonData";
import LeadForm from "@/components/globals/LeadForm";
import ThingsRemember from "@/components/Insurer/component/ThingsRemember";
import {
  transformData,
  HealthInsurerApiResponse,
} from "@/components/Insurer/dto/dtos";
import RelatedBlogs from "@/components/globals/RelatedBlogs";
import InsurerPlan from "@/components/globals/InsurerPlan";
import OrufyFloater from "@/components/globals/OrufyFloater";

const Insurer = ({
  data,
  allInsurerData,
  blogData,
}: {
  data: HealthInsurerApiResponse;
  allInsurerData?: any[];
  blogData?: {
    heading: string;
    blogs: Array<{
      title: string;
      date: string;
      author: string;
      description: string;
      imageUrl: string;
      url: string;
    }>;
  };
}) => {
  const [activeTab, setActiveTab] = useState("expert-review");

  // Transform the raw API data to component-ready format
  const transformedData = transformData(data);

  return (
    <div>
      <Hero
        name={transformedData.heroSection.name}
        description={transformedData.heroSection.description}
        image={transformedData.heroSection.image}
        slug={data.data.slug}
        stats={transformedData.heroSection.stats}
        breadcrumbPath={transformedData.heroSection.breadcrumbPath}
      />
      <PageNavigation
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        tabs={transformedData.pageNavigationSection.tabs}
      />
      <ExpertReview
        pill={transformedData.expertReviewSection.pill}
        heading={transformedData.expertReviewSection.heading}
        subheading={transformedData.expertReviewSection.subheading}
        whatWeLike={transformedData.expertReviewSection.whatWeLike}
        AreasOfImprovement={
          transformedData.expertReviewSection.AreasOfImprovement
        }
        verdict={transformedData.expertReviewSection.verdict}
      />
      {/* Top Insurer Plans */}
      <CategoryCards
        pill={transformedData.topPlansSection.pill}
        heading={transformedData.topPlansSection.heading}
        subHeading={transformedData.topPlansSection.subHeading}
        categories={transformedData.topPlansSection.categories}
        id="top-plans"
      />
      {/* Super Topup / Other Insurer Plans */}
      <CategoryCards
        pill={transformedData.otherPlansSection.pill}
        heading={transformedData.otherPlansSection.heading}
        subHeading={transformedData.otherPlansSection.subHeading}
        categories={transformedData.otherPlansSection.categories}
        id="other-plans"
      />
      <About
        about={transformedData.aboutSection.content || ""}
        heading={transformedData.aboutSection.heading}
        pill={transformedData.aboutSection.pill}
      />
      <Rating
        rating={transformedData.ratingSection.rating}
        heading={transformedData.ratingSection.heading}
        overallRating={transformedData.ratingSection.overallRating}
        performanceIndex={transformedData.ratingSection.performanceIndex}
        pill={transformedData.ratingSection.pill}
      />
      <Testimonial
        testimonials={transformedData.testimonialsSection.testimonials}
        sectionHeaderProps={
          transformedData.testimonialsSection.sectionHeaderProps
        }
        pill={transformedData.testimonialsSection.sectionHeaderProps.pill}
      />
      <ProsCons
        pill={transformedData.prosConsSection.pill}
        heading={transformedData.prosConsSection.heading}
        data={transformedData.prosConsSection.data}
      />
      <LeadForm
        pill={transformedData.leadFormSection.pill}
        title="Need help finding the right health plan?"
        description="Our experts offer personalised recommendations tailored to your age, family size, and health profile, ensuring you select the ideal coverage"
      />
      <InsurerTypes
        title={transformedData.insurerTypesSection.title}
        types={transformedData.insurerTypesSection.types}
      />
      <Documents
        title={transformedData.documentsSection.title}
        documents={transformedData.documentsSection.documents}
        customerSupport={transformedData.documentsSection.customerSupport}
      />
      {/* Cashless & Reimbursement Claims */}
      <ClaimTypes
        pill={transformedData.claimSettlementSection.pill}
        heading={transformedData.claimSettlementSection.heading}
        subheading={transformedData.claimSettlementSection.subheading}
        claimSettlements={transformedData.claimSettlementSection.settlements}
      />
      {/* Renewal Process */}
      <RenewalTypes
        pill={transformedData.renewalSection.pill}
        heading={transformedData.renewalSection.heading}
        subheading={transformedData.renewalSection.subheading}
        renewalSteps={transformedData.renewalSection.steps}
      />

      {/* Network Hospital */}
      <NetworkHospital
        title={transformedData.networkHospitalSection.title}
        cards={transformedData.networkHospitalSection.cards}
      />

      {/* Related Insurer Plans */}
      <CategoryCards
        pill={transformedData.featuresComparisonSection.pill}
        heading={transformedData.featuresComparisonSection.heading}
        subHeading={transformedData.featuresComparisonSection.subHeading}
        categories={featuresComparisonData.plans}
      />
      {/* How To Buy */}
      <AccordianSection
        pill={transformedData.howToBuySection.pill}
        heading={transformedData.howToBuySection.heading}
        subheading={transformedData.howToBuySection.subheading}
        faqs={transformedData.howToBuySection.faqs}
        id="how-to-buy"
        sectionTitle="Guide"
      />
      {/* Things To Remember */}
      <ThingsRemember
        title={transformedData.thingsRememberSection.title}
        points={transformedData.thingsRememberSection.points}
      />
      <LeadForm
        pill={transformedData.leadFormSection.pill}
        title="Still Confused? Get Expert Guidance"
        description="Our insurance experts can help you compare plans and find the best coverage for your needs"
      />
      {/* FAQs */}
      <AccordianSection
        pill={transformedData.faqsSection.pill}
        heading={transformedData.faqsSection.heading}
        subheading={transformedData.faqsSection.subheading}
        faqs={transformedData.faqsSection.faqs}
        id="faqs"
        sectionTitle="FAQs"
      />
      {/* Insurer Plans */}
      {allInsurerData && allInsurerData.length > 0 && (
        <InsurerPlan allInsurerData={allInsurerData} />
      )}
      {/* Related Blogs */}
      {blogData && blogData.blogs.length > 0 && (
        <RelatedBlogs blogData={blogData} />
      )}
      <GoToTopFloater />
      <OrufyFloater />
      <SharePageFloater />
    </div>
  );
};

export default Insurer;
