import {
  BodyLarge,
  HeadingLarge,
  HeadingSmall,
} from "@/components/UI/Typography";
import { htmlParser } from "@/utils/htmlParser";
import React from "react";

type HeroStatsCardProps ={
  value: string | number;
  label: string;
  icon: React.ReactNode | string;
  suffix?: string;
  prefix?: string;
}

export const HeroStatsCard = ({
  value,
  label,
  icon,
  suffix = "",
  prefix = "",
}: HeroStatsCardProps) => {
  const formatValue = (val: string | number) => {
    if (typeof val === "number") {
      return val.toLocaleString();
    }
    return val;
  };

  return (
    <div className="flex items-center p-4 bg-white rounded-xl border border-primary-200 shadow-sm gap-3">
      {/* Icon */}
      {typeof icon === "string" ? (
        <img src={icon} alt={label} className="w-12 h-12 rounded-xl" />
      ) : (
        <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
          {icon}
        </div>
      )}

      <div className="flex flex-col">
        <HeadingLarge className="font-semibold text-neutral-1100">
          {prefix}
          {formatValue(value)}
          {suffix}
        </HeadingLarge>
        {htmlParser(label, {
          classNames: {
            p: "text-neutral-1100",
          },
        })}
      </div>
    </div>
  );
};

export default HeroStatsCard;
