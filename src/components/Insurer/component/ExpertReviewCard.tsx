import { BodyLarge, HeadingMedium } from "@/components/UI/Typography";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import Image from "next/image";

export type ExpertReviewCard = {
  logo: string;
  productName: string;
  title: string;
  points: string[];
};

const ExpertReviewCardComponent: React.FC<{
  card: ExpertReviewCard;
}> = ({ card }) => (
  <div className="flex-1 bg-white rounded-xl p-4 md:px-6 md:py-5 gap-4 shadow-md flex flex-col items-start justify-start border-[1px] border-primary-200 transition-all">
    <div className="flex items-center w-full gap-4 rounded-xl">
      <div className="relative w-12 h-6 bg-white">
        <Image
          src={card.logo}
          alt={card.productName}
          fill
          className="object-contain"
        />
      </div>
      <div>
        <HeadingMedium weight="semibold" className="text-neutral-1100">
          {card.title}
        </HeadingMedium>
      </div>
    </div>
    {card.points?.length == 0 ? (
      <BodyLarge className="text-neutral-800">Not available</BodyLarge>
    ) : (
      <ul className="flex flex-col items-start gap-2">
        {card.points.map((point, i) => (
          <li key={i} className="flex items-start gap-2">
            <div className="flex-shrink-0 w-4 h-4 flex items-center justify-center md:pt-2">
              <IoMdCheckmarkCircleOutline className=" text-secondary-400" />
            </div>
            <BodyLarge className="text-neutral-800">{point}</BodyLarge>
          </li>
        ))}
      </ul>
    )}
  </div>
);
