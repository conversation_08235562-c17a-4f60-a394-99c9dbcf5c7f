import React from "react";
import { BodyLarge, BodyMedium } from "@/components/UI/Typography";
import { cn } from "@/lib/utils";
import { FiTrendingUp } from "react-icons/fi";

type MetricProps = {
  label: string;
  valueLabel: string;
  value: number; // current value
  total: number; // max value to compute the filled width
  colorClass: string; // bar color
};

const ProgressRow: React.FC<MetricProps> = ({
  label,
  valueLabel,
  value,
  total,
  colorClass,
}) => {
  const percentage = Math.max(0, Math.min(100, (value / total) * 100));
  return (
    <div className="space-y-2">
      <div className="grid grid-cols-[1fr_auto] items-center">
        <BodyMedium className="text-neutral-1100 font-medium">
          {label}
        </BodyMedium>
        <BodyMedium className="text-neutral-1100 font-semibold tabular-nums">
          {valueLabel}
        </BodyMedium>
      </div>
      <div className="w-full h-3 rounded-full bg-neutral-200 overflow-hidden">
        <div
          className={cn("h-full rounded-full transition-all", colorClass)}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};

export type PerformanceIndexProps = {
  className?: string;
  title?: string;
  companyLabel?: string;
  industryLabel?: string;
  // Gross Direct Premium
  gdpCompany?: {
    value: number;
    valueLabel: string;
  };
  gdpIndustry?: {
    value: number;
    valueLabel: string;
  };
  gdpTotal?: number; // scale for GDP bars
  // ICR
  icrCompany?: {
    value: number; // percentage (0-100)
    valueLabel: string; // e.g., "87.86%"
  };
  icrIndustry?: {
    value: number; // percentage (0-100)
    valueLabel: string; // e.g., "83.49%"
  };
  // New: toggles for sections and legend
  showGdpSection?: boolean;
  showIcrSection?: boolean;
};

const PerformanceIndex: React.FC<PerformanceIndexProps> = ({
  className,
  title = "Performance Index",
  companyLabel = "Company",
  industryLabel = "Industry",
  gdpCompany = { value: 0, valueLabel: "0Cr" },
  gdpIndustry = { value: 0, valueLabel: "0Cr" },
  gdpTotal = 0, // visual scale; adjust via props if needed
  icrCompany = { value: 0, valueLabel: "0%" },
  icrIndustry = { value: 0, valueLabel: "0%" },
  showGdpSection = true,
  showIcrSection = true,
}) => {
  return (
    <div
      className={cn(
        "rounded-xl border border-neutral-200 shadow-sm bg-white p-6 md:p-6 h-full",
        className
      )}
    >
      {/* Header */}
      <div className="flex items-center gap-3">
        <span
          aria-hidden
          className="inline-flex items-center justify-center w-8 h-8 rounded-xl bg-secondary-100"
        >
          <FiTrendingUp className="w-5 h-5 text-secondary-600" />
        </span>
        <BodyLarge as="h3" className="text-neutral-1100 font-semibold">
          {title}
        </BodyLarge>
      </div>

      {/* Content */}
      <div
        className={cn(
          "mt-8 grid gap-10",
          showGdpSection && showIcrSection
            ? "grid-cols-1 md:grid-cols-2"
            : "grid-cols-1"
        )}
      >
        {showGdpSection && (
          <div className="space-y-5">
            <BodyMedium className="text-neutral-900" weight="semibold">
              Gross Direct Premium
            </BodyMedium>
            <ProgressRow
              label={companyLabel}
              valueLabel={gdpCompany.valueLabel}
              value={gdpCompany.value}
              total={gdpTotal}
              colorClass="bg-secondary-400"
            />
            <ProgressRow
              label={industryLabel}
              valueLabel={gdpIndustry.valueLabel}
              value={gdpIndustry.value}
              total={gdpTotal}
              colorClass="bg-primary-800"
            />
          </div>
        )}

        {showIcrSection && (
          <div className="space-y-5">
            <BodyMedium className="text-neutral-900 font-semibold">
              ICR%
            </BodyMedium>
            <ProgressRow
              label={companyLabel}
              valueLabel={icrCompany.valueLabel}
              value={icrCompany.value}
              total={100}
              colorClass="bg-secondary-400"
            />
            <ProgressRow
              label={industryLabel}
              valueLabel={icrIndustry.valueLabel}
              value={icrIndustry.value}
              total={100}
              colorClass="bg-primary-800"
            />
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="mt-8 md:mt-10 grid grid-cols-2 gap-6">
        <div className="flex items-center gap-3">
          <span className="flex-shrink-0 w-10 h-10 rounded-xl bg-secondary-400" />
          <BodyMedium className="text-neutral-1100 font-semibold">
            {companyLabel}
          </BodyMedium>
        </div>
        <div className="flex items-center gap-3">
          <span className="flex-shrink-0 w-10 h-10 rounded-xl bg-primary-800" />
          <BodyMedium className="text-neutral-1100 font-semibold">
            {industryLabel}
          </BodyMedium>
        </div>
      </div>
    </div>
  );
};

export default PerformanceIndex;
