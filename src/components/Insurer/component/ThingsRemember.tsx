import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { BodyLarge, BodyMedium } from "@/components/UI/Typography";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";

type ThingsRememberProps = {
  title?: string;
  points: string[];
};

const ThingsRemember = ({
  title = "Things to Remember",
  points,
}: ThingsRememberProps) => {
  return (
    <SectionContainerLarge>
      <div className="border border-primary-200 rounded-xl bg-primary-100 !p-4 md:px-6 md:py-5">
        <BodyLarge className="text-neutral-900 font-semibold mb-4">
          {title}
        </BodyLarge>
        <ul className="space-y-3">
          {points.map((point, index) => (
            <li key={index} className="flex items-start">
              <IoMdCheckmarkCircleOutline className="text-green-600 mr-3 mt-1 flex-shrink-0" />
              <BodyMedium className="text-neutral-900">{point}</BodyMedium>
            </li>
          ))}
        </ul>
      </div>
    </SectionContainerLarge>
  );
};

export default ThingsRemember;
