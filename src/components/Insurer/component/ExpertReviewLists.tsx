import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import {
  IoMdCheckmarkCircleOutline,
  IoMdCloseCircleOutline,
} from "react-icons/io";
import PointCard from "@/components/globals/DSComponentsV0/PointCard";

type ExpertReviewListsProps = {
  whatWeLike: {
    heading: string;
    points: string[];
  };
  areasOfImprovement: {
    heading: string;
    points: string[];
  };
}

const ExpertReviewLists: React.FC<ExpertReviewListsProps> = ({
  whatWeLike,
  areasOfImprovement,
}) => {

  return (
    <>
      {/* Desktop Layout */}
      <div className="hidden md:grid grid-cols-1 md:grid-cols-2 gap-8 w-full">
        <PointCard
          title={whatWeLike.heading}
          points={whatWeLike.points}
          pointIcon={
            <IoMdCheckmarkCircleOutline className="text-secondary-400 flex-shrink-0 mt-1" size={16} />
          }
          className="!py-5"
        />
        <PointCard
          title={areasOfImprovement.heading}
          points={areasOfImprovement.points}
          pointIcon={
            <IoMdCloseCircleOutline className="text-red-400 flex-shrink-0 mt-1" size={16} />
          }
          className="!py-5"
        />
      </div>

      {/* Mobile Carousel Layout */}
      <div className="block md:hidden w-full">
        <MobileCarousel totalSlides={2}>
          <MobileCarouselItem>
            <PointCard
              title={whatWeLike.heading}
              points={whatWeLike.points}
              pointIcon={
                <IoMdCheckmarkCircleOutline className="text-secondary-400 flex-shrink-0 mt-1" size={16} />
              }
            />
          </MobileCarouselItem>
          <MobileCarouselItem>
            <PointCard
              title={areasOfImprovement.heading}
              points={areasOfImprovement.points}
              pointIcon={
                <IoMdCloseCircleOutline className="text-red-400 flex-shrink-0 mt-1" size={16} />
              }
            />
          </MobileCarouselItem>
        </MobileCarousel>
      </div>
    </>
  );
};

export default ExpertReviewLists;
