import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SmallCard from "@/components/globals/SmallCard";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import { HeadingXLarge } from "@/components/UI/Typography";

type NetworkHospitalCard = {
  id: number;
  iconKey: string;
  title: string;
  description: string;
};

type NetworkHospitalProps = {
  title?: string;
  cards: NetworkHospitalCard[];
};

const NetworkHospital = ({
  title = "Network Hospital",
  cards,
}: NetworkHospitalProps) => {
  return (
    <SectionContainerLarge className="!px-0">
      <HeadingXLarge as="h2" className="text-center mb-4 md:mb-6 text-neutral-1100">
        {title}
      </HeadingXLarge>
      <div className="hidden md:grid md:grid-cols-3 gap-4">
        {cards.map((card) => {
          return <SmallCard key={card.id} card={card} />;
        })}
      </div>
      <div className="md:hidden">
        <MobileCarousel totalSlides={cards.length}>
          {cards.map((card) => (
            <MobileCarouselItem key={card.id}>
              <SmallCard card={card} />
            </MobileCarouselItem>
          ))}
        </MobileCarousel>
      </div>
    </SectionContainerLarge>
  );
};

export default NetworkHospital;
