import SectionHeader from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
import SimpleCard from "@/components/globals/DSComponentsV0/SimpleCard";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { Grid } from "@/components/UI/Grid";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";


type HealthInsurerWhyChooseUs = {
  id: string;
  title: string;
  cards: {
    id: string;
    title: string;
    description: string;
    iconUrl: string;
  }[];
};

const WhyChooseUs = ({ whyChooseUs }: { whyChooseUs: HealthInsurerWhyChooseUs[] }) => {
  return (
    <SectionContainerLarge className="!px-0">
        <SectionHeader
          heading={whyChooseUs[0].title}
          subheading=""
          component="h2"
        />
        <Grid cols={3} gap={6} className="hidden md:grid">
          {whyChooseUs[0].cards.map((card) => (
            <SimpleCard 
                key={card.id}
                title={card.title}
                description={card.description}
                icon={card.iconUrl}
            />
          ))}
        </Grid>
        <MobileCarousel totalSlides={whyChooseUs[0].cards.length} className="md:hidden">
          {whyChooseUs[0].cards.map((card) => (
            <MobileCarouselItem key={card.id} >
              <SimpleCard 
                title={card.title}
                description={card.description}
                icon={card.iconUrl}
              />
            </MobileCarouselItem>
          ))}
        </MobileCarousel>
    </SectionContainerLarge>
  );
};

export default WhyChooseUs;
