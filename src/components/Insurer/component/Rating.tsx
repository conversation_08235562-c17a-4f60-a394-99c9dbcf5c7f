import PillBadge from "@/components/globals/PillBadge";
import { HeadingXLarge } from "@/components/UI/Typography";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import OverallRating from "@/components/Insurer/component/OverallRating";
import PerformanceIndex from "@/components/Insurer/component/PerformanceIndex";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";

type RatingProps = {
  rating: string;
  heading: string;
  pill: string;
  overallRating?: {
    score: number;
    maxScore: number;
    metrics: Array<{
      label: string;
      value: number;
      total: number;
    }>;
  };
  performanceIndex?: {
    companyLabel?: string;
    industryLabel?: string;
    gdpCompany?: {
      value: number;
      valueLabel: string;
    };
    gdpIndustry?: {
      value: number;
      valueLabel: string;
    };
    gdpTotal?: number;
    icrCompany?: {
      value: number;
      valueLabel: string;
    };
    icrIndustry?: {
      value: number;
      valueLabel: string;
    };
  };
};

const Rating = ({
  rating,
  heading,
  overallRating,
  performanceIndex,
  pill,
}: RatingProps) => {
  const defaultPerformanceIndex = {
    companyLabel: "SBI General Insurance",
    industryLabel: "Industry Average",
    gdpCompany: { value: 1905.12, valueLabel: "1905.12Cr" },
    gdpIndustry: { value: 1905.12, valueLabel: "1905.12Cr" },
    gdpTotal: 2600,
    icrCompany: { value: 87.86, valueLabel: "87.86%" },
    icrIndustry: { value: 83.49, valueLabel: "83.49%" },
  };

  return (
    <SectionContainerMedium className="!px-0" id="rating">
      <PillBadge pill={pill} />
      <HeadingXLarge
        as="h2"
        className="text-neutral-1100 text-center mb-4 md:mb-6 font-semibold mt-2 md:mt-3"
      >
        {heading}
      </HeadingXLarge>

      {/* Rating Cards Grid */}
      {/* Desktop layout */}
      <div className="hidden md:flex gap-6">
        <div className="w-[35%]">
          <OverallRating
            score={overallRating?.score ?? 0}
            maxScore={overallRating?.maxScore ?? 0}
            metrics={overallRating?.metrics ?? []}
          />
        </div>
        <div className="w-[65%]">
          <PerformanceIndex
            companyLabel={
              performanceIndex?.companyLabel ??
              defaultPerformanceIndex.companyLabel
            }
            industryLabel={
              performanceIndex?.industryLabel ??
              defaultPerformanceIndex.industryLabel
            }
            gdpCompany={
              performanceIndex?.gdpCompany ?? defaultPerformanceIndex.gdpCompany
            }
            gdpIndustry={
              performanceIndex?.gdpIndustry ??
              defaultPerformanceIndex.gdpIndustry
            }
            gdpTotal={
              performanceIndex?.gdpTotal ?? defaultPerformanceIndex.gdpTotal
            }
            icrCompany={
              performanceIndex?.icrCompany ?? defaultPerformanceIndex.icrCompany
            }
            icrIndustry={
              performanceIndex?.icrIndustry ??
              defaultPerformanceIndex.icrIndustry
            }
          />
        </div>
      </div>

      {/* Mobile carousel */}
      <div className="md:hidden">
        <MobileCarousel totalSlides={3}>
          <MobileCarouselItem>
            <OverallRating
              score={overallRating?.score ?? 0}
              maxScore={overallRating?.maxScore ?? 0}
              metrics={overallRating?.metrics ?? []}
            />
          </MobileCarouselItem>
          <MobileCarouselItem>
            <PerformanceIndex
              title="Performance Index"
              companyLabel={
                performanceIndex?.companyLabel ??
                defaultPerformanceIndex.companyLabel
              }
              industryLabel={
                performanceIndex?.industryLabel ??
                defaultPerformanceIndex.industryLabel
              }
              gdpCompany={
                performanceIndex?.gdpCompany ??
                defaultPerformanceIndex.gdpCompany
              }
              gdpIndustry={
                performanceIndex?.gdpIndustry ??
                defaultPerformanceIndex.gdpIndustry
              }
              gdpTotal={
                performanceIndex?.gdpTotal ?? defaultPerformanceIndex.gdpTotal
              }
              showGdpSection
              showIcrSection={false}
            />
          </MobileCarouselItem>
          <MobileCarouselItem>
            <PerformanceIndex
              title="Performance Index"
              companyLabel={
                performanceIndex?.companyLabel ??
                defaultPerformanceIndex.companyLabel
              }
              industryLabel={
                performanceIndex?.industryLabel ??
                defaultPerformanceIndex.industryLabel
              }
              icrCompany={
                performanceIndex?.icrCompany ??
                defaultPerformanceIndex.icrCompany
              }
              icrIndustry={
                performanceIndex?.icrIndustry ??
                defaultPerformanceIndex.icrIndustry
              }
              showGdpSection={false}
              showIcrSection
            />
          </MobileCarouselItem>
        </MobileCarousel>
      </div>
    </SectionContainerMedium>
  );
};

export default Rating;
