import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { HeadingXLarge } from "@/components/UI/Typography";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import InsurerTypeCard from "@/components/Insurer/component/InsurerTypeCard";
import DesktopCarousel from "@/components/UI/DesktopCarousal";
import SectionContainer from "@/components/globals/SectionContainer";

type InsurerType = {
  id: string;
  title: string;
  description: string;
  button_text: string;
};

type InsurerTypesProps = {
  title?: string;
  types: InsurerType[];
};

const InsurerTypes = ({
  title = "Insurance Types",
  types,
}: InsurerTypesProps) => {
  if (!types.length) {
    return null;
  }

  return (
    <SectionContainer className="!px-0">
      <HeadingXLarge as="h2" className="text-center mb-4 md:mb-6 text-neutral-1100">
        {title}
      </HeadingXLarge>

      {/* Desktop Grid Layout */}

      <DesktopCarousel
        totalSlides={types.length}
        autoPlay={false}
        autoPlayInterval={5000}
        itemsPerPage={4}
        className="hidden md:block"
      >
          {types.map((type) => (
            <InsurerTypeCard key={type.id} type={type} />
          ))}
      </DesktopCarousel>

      {/* Mobile Carousel Layout */}
      <div className="md:hidden">
        <MobileCarousel totalSlides={types.length}>
          {types.map((type) => (
            <MobileCarouselItem key={type.id}>
              <InsurerTypeCard type={type} />
            </MobileCarouselItem>
          ))}
        </MobileCarousel>
      </div>
    </SectionContainer>
  );
};

export default InsurerTypes;
