"use client";

import { useState } from "react";
import AccordianSection from "@/components/globals/AccordianSection";
import { Button } from "@/components/UI/Button";

type ClaimType = {
  id: string;
  title: string;
  description?: string;
};

type ClaimSettlement = {
  id: string;
  title: string;
  types: ClaimType[];
};

const ClaimTypes = ({
  pill,
  heading,
  subheading,
  claimSettlements,
}: {
  pill: string;
  heading: string;
  subheading?: string ;
  claimSettlements: ClaimSettlement[];
}) => {
  // Find cashless and reimbursement claims
  const cashlessClaims = claimSettlements.find((settlement) =>
    settlement.title.toLowerCase().includes("cashless")
  );
  const reimbursementClaims = claimSettlements.find((settlement) =>
    settlement.title.toLowerCase().includes("reimbursement")
  );

  // Determine if we should show toggle (both types exist)
  const shouldShowToggle = cashlessClaims && reimbursementClaims;

  // Set initial active tab
  const [activeTab, setActiveTab] = useState<"cashless" | "reimbursement">(
    cashlessClaims ? "cashless" : "reimbursement"
  );

  // Get current active claims data
  const getCurrentClaimsData = () => {
    if (activeTab === "cashless" && cashlessClaims) {
      return cashlessClaims;
    }
    if (activeTab === "reimbursement" && reimbursementClaims) {
      return reimbursementClaims;
    }
    // Fallback to first available settlement
    return claimSettlements[0] || { id: "", title: "", types: [] };
  };

  const currentClaimsData = getCurrentClaimsData();

  // Convert claim types to FAQ format
  const faqsData = currentClaimsData.types.map((type) => ({
    question: type.title,
    answer: type.description || "Not available",
  }));

  // Create toggle component
  const toggleComponent = shouldShowToggle ? (
    <div className="flex w-full bg-gray-100 rounded-full p-1 border border-gray-200">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setActiveTab("cashless")}
        className={`px-6 py-2 w-1/2 rounded-full text-sm font-medium transition-all duration-200 border-0 shadow-none hover:scale-100 ${
          activeTab === "cashless"
            ? "!bg-primary-800 !text-white shadow-sm hover:!bg-primary-800"
            : "!bg-transparent !text-primary-800 hover:!bg-transparent"
        }`}
      >
        Cashless
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setActiveTab("reimbursement")}
        className={`px-6 py-2 w-1/2 rounded-full text-sm font-medium transition-all duration-200 border-0 shadow-none hover:scale-100 ${
          activeTab === "reimbursement"
            ? "!bg-primary-800 !text-white shadow-sm hover:!bg-primary-800"
            : "!bg-transparent !text-primary-800 hover:!bg-transparent"
        }`}
      >
        Reimbursement
      </Button>
    </div>
  ) : null;

  return (
    <AccordianSection
      pill={pill}
      heading={heading}
      subheading={subheading || ""}
      faqs={faqsData}
      id="claim-settlement"
      toggleComponent={toggleComponent}
      sectionTitle="Process"
    />
  );
};

export default ClaimTypes;
