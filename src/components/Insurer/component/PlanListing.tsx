import { useState } from "react";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionHeader from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
import { Grid } from "@/components/UI/Grid";
import PlanCard from "@/components/globals/DSComponentsV0/PlanCard";
import { HeadingLarge, HeadingSmall } from "@/components/UI/Typography";
import { Button } from "@/components/UI/Button";

type planListingSection = {
  id: string;
  insurerName: string;
  insurerSlug: string;
  logoUrl: string;
  plan_listing: {
    id: string;
    title: string;
    cards: {
      id: string;
      healthVariantId: string;
      healthProductVariant: {
        id: string;
        variant_name: string;
        variant_slug: string;
        temp_slug?: string | undefined;
      };
    }[];
  }[];
};

const PlanListing = ({
  planListingSection,
}: {
  planListingSection: planListingSection;
}) => {
  const [showAllPlans, setShowAllPlans] = useState(false);

  planListingSection.insurerName = planListingSection.insurerName.endsWith(
    "Health Insurance"
  )
    ? planListingSection.insurerName.replace("Health Insurance", "")
    : planListingSection.insurerName;
  return (
    <SectionContainerLarge className="">
      <SectionHeader
        pill={planListingSection.insurerName + "Health Insurance Plans"}
        heading={planListingSection.insurerName + "Health Insurance Plans"}
        subheading=""
        component="h2"
        className="md:!mb-12 !px-0"
      />

      {planListingSection.plan_listing.map((planListing) => {
        // Show more logic for mobile
        const displayedMobilePlans = showAllPlans ? planListing.cards : planListing.cards.slice(0, 5);
        const hasMorePlans = planListing.cards.length > 5;

        const toggleShowAllPlans = () => setShowAllPlans(prev => !prev);

        return (
          <div key={planListing.id}>
            <HeadingLarge className="text-center mb-6 text-neutral-1000" as="h3">
              {planListing.title}
            </HeadingLarge>

            {/* Desktop view - show all plans */}
            <div className="hidden md:block">
              <Grid cols={3} gap={6} mobileCols={1} gapY={4}>
                {planListing.cards.map((card) => (
                  <PlanCard
                    key={card.id}
                    plan={{
                      logo_url: planListingSection.logoUrl,
                      plan_title: card.healthProductVariant.variant_name,
                      redirect_url: `${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/${planListingSection.insurerSlug}/${card.healthProductVariant.temp_slug}`,
                    }}
                  />
                ))}
              </Grid>
            </div>

            {/* Mobile view - show limited plans with toggle */}
            <div className="md:hidden">
              <div className="grid grid-cols-1 gap-4">
                {displayedMobilePlans.map((card) => (
                  <PlanCard
                    key={card.id}
                    plan={{
                      logo_url: planListingSection.logoUrl,
                      plan_title: card.healthProductVariant.variant_name,
                      redirect_url: `${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/${planListingSection.insurerSlug}/${card.healthProductVariant.temp_slug}`,
                    }}
                  />
                ))}
              </div>

              {/* Toggle button for mobile */}
              {hasMorePlans && (
                <div className="flex justify-center mt-4">
                  <Button
                    onClick={toggleShowAllPlans}
                    variant="primary"
                    className="flex items-center"
                  >
                    <span className="text-sm font-normal">
                      {showAllPlans ? "Show Less" : "See More Plans"}
                    </span>
                  </Button>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </SectionContainerLarge>
  );
};

export default PlanListing;
