import {
  BodyLarge,
  BodyMedium,
  DisplaySmall,
} from "@/components/UI/Typography";
import React from "react";
import { FaStar } from "react-icons/fa";

type Metric = {
  label: string;
  value: number; // current points
  total: number; // max points
};

export type OverallRatingProps = {
  score: number;
  maxScore: number;
  metrics: Metric[]; // three rows expected but flexible
  className?: string;
};

const ProgressBar: React.FC<{ value: number; total: number }> = ({
  value,
  total,
}) => {
  const percentage = Math.max(0, Math.min(100, (value / total) * 100));
  return (
    <div className="w-full h-3 rounded-full bg-neutral-200 overflow-hidden">
      <div
        className="h-full bg-primary-800 rounded-full transition-all"
        style={{ width: `${percentage}%` }}
      />
    </div>
  );
};

const OverallRating: React.FC<OverallRatingProps> = ({
  score,
  maxScore,
  metrics,
  className,
}) => {
  return (
    <div
      className={`rounded-xl border border-neutral-200 shadow-sm bg-white p-6 md:p-6 ${
        className ?? ""
      }`}
    >
      {/* Header */}
      <div className="flex items-center justify-center gap-2">
        <span
          aria-hidden
          className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-tertiary-orange-400/20"
        >
          <FaStar className="w-4 h-4 text-tertiary-orange-400" />
        </span>
        <BodyLarge as="h3" weight="semibold" className="text-neutral-1100">
          Overall Rating
        </BodyLarge>
      </div>

      {/* Score */}
      <div className="mt-5 md:mt-7 text-center">
        <DisplaySmall as="h3" weight="semibold" className="text-primary-800">
          {score.toFixed(1)}
        </DisplaySmall>
        <BodyMedium className="mt-2 text-neutral-500">out of 10</BodyMedium>
      </div>

      {/* Metrics */}
      <div className="mt-8 space-y-6">
        {metrics.map((metric) => (
          metric.value !== 0 ? <div key={metric.label}>
          <div className="flex items-center justify-between">
            <BodyMedium className="text-neutral-800 font-semibold">
              {metric.label}
            </BodyMedium>
            <BodyMedium className="text-neutral-800 font-semibold tabular-nums">
              {metric.value}/{metric.total}
            </BodyMedium>
          </div>
          <div>
            <ProgressBar value={metric.value} total={metric.total} />
          </div>
        </div> : null          
        ))}
      </div>
    </div>
  );
};

export default OverallRating;
