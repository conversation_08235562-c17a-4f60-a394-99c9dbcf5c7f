import PillBadge from "@/components/globals/PillBadge";
import { HeadingXLarge } from "@/components/UI/Typography";
import SectionContainerSmall from "@/components/globals/SectionContainerSmall";
import ProsConsCard, {
  ProsConsData,
} from "@/components/Insurer/component/ProsConsCard";

const ProsCons = ({
  pill,
  heading,
  data,
}: {
  pill: string;
  heading: string;
  data?: ProsConsData;
}) => {
  return (
    <SectionContainerSmall id="pros-and-cons" className="!p-0">
      <PillBadge pill={pill} />
      <HeadingXLarge
        as="h2"
        className="text-neutral-1100 mb-4 md:mb-6 text-center font-semibold mt-2 md:mt-3 px-6 md:px-0"
      >
        {heading}
      </HeadingXLarge>
      <ProsConsCard data={data} />
    </SectionContainerSmall>
  );
};

export default ProsCons;
