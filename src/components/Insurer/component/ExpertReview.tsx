import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerSmall from "@/components/globals/SectionContainerSmall";
import PillBadge from "@/components/globals/PillBadge";
import { HeadingXLarge} from "@/components/UI/Typography";
import ExpertReviewLists from "@/components/Insurer/component/ExpertReviewLists";
import Verdict from "@/components/Insurer/component/Verdict";
import { htmlParser } from "@/utils/htmlParser";

const ExpertReview = ({
  pill,
  heading,
  subheading,
  whatWeLike,
  AreasOfImprovement,
  verdict,
}: {
  pill: string;
  heading: string;
  subheading?: string ;
  whatWeLike: {
    heading: string;
    points: string[];
  };
  AreasOfImprovement: {
    heading: string;
    points: string[];
  };
  verdict: string;
}) => {
  return (
    <SectionContainerLarge id="expert-review" className="!p-0">
      {/* Custom Section Header */}
      <div className="flex flex-col items-center gap-2 md:gap-3 mx-auto mb-6 px-6 md:px-0">
        <PillBadge pill={pill} />
        <HeadingXLarge className="text-center text-neutral-1100">
          {heading}
        </HeadingXLarge>
        {htmlParser(subheading || "", {
          classNames: {
            p: "text-neutral-1100 text-center",
          },
        })}
      </div>

      <SectionContainerSmall className="!mb-4 md:!mb-6 !p-0">
        <ExpertReviewLists
          whatWeLike={whatWeLike}
          areasOfImprovement={AreasOfImprovement}
        />
      </SectionContainerSmall>
       <Verdict verdict={verdict} heading="Final Verdict" />
    </SectionContainerLarge>
  );
};

export default ExpertReview;
