"use client";

import { useState } from "react";
import AccordianSection from "@/components/globals/AccordianSection";
import { Button } from "@/components/UI/Button";

type RenewalType = {
  id: string;
  title: string;
  description?: string;
};

type RenewalStep = {
  id: string;
  title: string;
  types: RenewalType[];
};

const RenewalTypes = ({
  pill,
  heading,
  subheading,
  renewalSteps,
}: {
  pill: string;
  heading: string;
  subheading: string;
  renewalSteps: RenewalStep[];
}) => {
  // Find online and offline renewal steps
  const onlineRenewal = renewalSteps.find((step) =>
    step.title.toLowerCase().includes("online")
  );
  const offlineRenewal = renewalSteps.find((step) =>
    step.title.toLowerCase().includes("offline")
  );

  // Determine if we should show toggle (both types exist)
  const shouldShowToggle = onlineRenewal && offlineRenewal;

  // Set initial active tab
  const [activeTab, setActiveTab] = useState<"online" | "offline">(
    onlineRenewal ? "online" : "offline"
  );

  // Get current active renewal data
  const getCurrentRenewalData = () => {
    if (activeTab === "online" && onlineRenewal) {
      return onlineRenewal;
    }
    if (activeTab === "offline" && offlineRenewal) {
      return offlineRenewal;
    }
    // Fallback to first available step
    return renewalSteps[0] || { id: "", title: "", types: [] };
  };

  const currentRenewalData = getCurrentRenewalData();

  // Convert renewal types to FAQ format
  const faqsData = currentRenewalData.types.map((type) => ({
    question: type.title,
    answer: type.description || "Not available",
  }));

  // Create toggle component
  const toggleComponent = shouldShowToggle ? (
    <div className="flex w-full bg-gray-100 rounded-full p-1 border border-gray-200">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setActiveTab("online")}
        className={`px-6 py-2 w-1/2 rounded-full text-sm font-medium transition-all duration-200 border-0 shadow-none hover:scale-100 ${
          activeTab === "online"
            ? "!bg-primary-800 !text-white shadow-sm hover:!bg-primary-800"
            : "!bg-transparent !text-primary-800 hover:!bg-transparent hover:!text-neutral-1100"
        }`}
      >
        Online Renewal
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setActiveTab("offline")}
        className={`px-6 py-2 w-1/2 rounded-full text-sm font-medium transition-all duration-200 border-0 shadow-none hover:scale-100 ${
          activeTab === "offline"
            ? "!bg-primary-800 !text-white shadow-sm hover:!bg-primary-800"
            : "!bg-transparent !text-primary-800 hover:!bg-transparent hover:!text-neutral-1100"
        }`}
      >
        Offline Renewal
      </Button>
    </div>
  ) : null;

  return (
    <AccordianSection
      pill={pill}
      heading={heading}
      subheading={subheading}
      faqs={faqsData}
      id="renewal-process"
      toggleComponent={toggleComponent}
      sectionTitle="Process"
    />
  );
};

export default RenewalTypes;
