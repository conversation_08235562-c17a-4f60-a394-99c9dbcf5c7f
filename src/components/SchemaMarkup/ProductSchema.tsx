"use client";
import JsonLd from "./JsonLd";

type ProductSchemaProps = {
  name: string;
  description: string;
  image: string;
  sku: string;
  brand: string;
  price: string;
  url: string;
  currency?: string;
  condition?: string;
  availability?: string;
};

export default function ProductSchema({
  name,
  description,
  image,
  sku,
  brand,
  price,
  url,
  currency = "INR",
  condition = "https://schema.org/NewCondition",
  availability = "https://schema.org/InStock",
}: ProductSchemaProps) {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Product",
    name,
    image: [image],
    description,
    sku,
    brand: {
      "@type": "Brand",
      name: brand,
    },
    offers: {
      "@type": "Offer",
      url,
      priceCurrency: currency,
      price,
      itemCondition: condition,
      availability,
    },
  };

  return <JsonLd data={schema} />;
}
