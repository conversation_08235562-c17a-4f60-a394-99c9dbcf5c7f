"use client";

import React from "react";
import { Metada<PERSON> } from "next";
import <PERSON>ript from "next/script";

interface JsonLdProps {
  data: any;
}

export default function JsonLd({ data }: JsonLdProps) {
  const jsonLd = data; // Don't force it into an array

  return (
    <script
      id="json-ld"
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  );
}

// Export a function to generate metadata
export function generateJsonLdMetadata(
  data: Record<string, any> | Record<string, any>[]
): Metadata {
  const jsonLd = data; // Don't force it into an array

  return {
    other: {
      "ld+json": JSON.stringify(jsonLd),
    },
  };
}
