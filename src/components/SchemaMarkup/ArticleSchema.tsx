"use client";
import JsonLd from "./JsonLd";

type ArticleSchemaProps = {
  title: string;
  description: string;
  image: string;
  author: string;
  datePublished: string;
  dateModified?: string;
  publisherName?: string;
  publisherLogo?: string;
  category: string;
};

export default function ArticleSchema({
  title,
  description,
  image,
  author,
  datePublished,
  dateModified,
  publisherName = "OneAssure",
  publisherLogo = "https://oneassure.com/logo.png",
  category,
}: ArticleSchemaProps) {
  const stripHtml = (html: string) => {
    if (!html) return "";
    // First decode HTML entities
    const decoded = html
      .replace(/&quot;/g, '"')
      .replace(/&amp;/g, "&")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, " ");

    // Then remove all HTML tags
    return (
      decoded
        .replace(/<[^>]*>/g, "")
        // Remove extra whitespace
        .replace(/\s+/g, " ")
        .trim()
    );
  };

  const schema = {
    "@context": "https://schema.org",
    "@type": "Article",
    headline: title,
    description: stripHtml(description || ""),
    image,
    author: {
      "@type": "Person",
      name: author,
    },
    publisher: {
      "@type": "Organization",
      name: publisherName,
      logo: {
        "@type": "ImageObject",
        url: publisherLogo,
      },
    },
    datePublished,
    dateModified: dateModified || datePublished,
    isPartOf: {
      "@type": "CreativeWorkSeries",
      name: category,
      description: `This is a blog series about understanding ${category} in India`,
    },
  };

  return <JsonLd data={schema} />;
}
