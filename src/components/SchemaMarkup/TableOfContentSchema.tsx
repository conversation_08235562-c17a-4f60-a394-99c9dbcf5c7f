"use client";
import JsonLd from "./JsonLd";

type ListItem = {
  position: number;
  name: string;
  url?: string;
  ["@type"]?: "ListItem";
};

export type ItemListData = {
  name?: string;
  itemListElement: ListItem[];
};

export default function TableOfContentSchema({ data }: { data: ItemListData }) {
  if (!data || !data.itemListElement || data.itemListElement.length === 0)
    return null;

  const stripHtml = (html: string) => html.replace(/<[^>]*>/g, "");

  const schema = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    name: data.name ? stripHtml(data.name) : undefined,
    itemListElement: data.itemListElement.map((item) => ({
      "@type": item["@type"] ?? "ListItem",
      position: item.position,
      name: stripHtml(item.name),
      url: item.url,
    })),
  };

  return <JsonLd data={schema} />;
}
