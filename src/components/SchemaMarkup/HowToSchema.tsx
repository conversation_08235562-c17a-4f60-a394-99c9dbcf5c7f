"use client";
import JsonLd from "./JsonLd";

type HowToTool = { name: string; ["@type"]?: "HowToTool" };

type HowToSupply = { name: string; ["@type"]?: "HowToSupply" };

type HowToStep = {
  position?: number;
  name: string;
  text?: string;
  url?: string;
  ["@type"]?: "HowToStep";
};

export type HowTo = {
  name: string;
  description?: string;
  url?: string;
  image?: string;
  totalTime?: string;
  tool?: HowToTool[];
  supply?: HowToSupply[];
  step: HowToStep[];
};

export default function HowToSchema({ data }: { data: HowTo }) {
  if (!data || !data.name || !data.step || data.step.length === 0) return null;

  const stripHtml = (html: string) => html.replace(/<[^>]*>/g, "");

  const schema = {
    "@context": "https://schema.org",
    "@type": "HowTo",
    name: data.name,
    description: data.description ? stripHtml(data.description) : undefined,
    url: data.url,
    image: data.image,
    totalTime: data.totalTime,
    tool: data.tool?.map((t) =>
      t["@type"] ? { ...t } : { "@type": "HowToTool", name: t.name }
    ),
    supply: data.supply?.map((s) =>
      s["@type"] ? { ...s } : { "@type": "HowToSupply", name: s.name }
    ),
    step: data.step.map((s, idx) => ({
      "@type": s["@type"] ?? "HowToStep",
      position: s.position ?? idx + 1,
      name: s.name,
      text: s.text ? stripHtml(s.text) : undefined,
      url: s.url,
    })),
  };

  return <JsonLd data={schema} />;
}
