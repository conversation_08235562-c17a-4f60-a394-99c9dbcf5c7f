import JsonLd from "@/components/SchemaMarkup/JsonLd";

export type PageSchemaProps = {
  name: string;
  url: string;
  headline: string;
  description: string;
  mainContentOfPage?: string;
  inLanguage?: string;
  mainEntity?: {
    name: string;
    type?: string;
  };
  publisher?: {
    name: string;
    logoUrl?: string;
  };
  image?: {
    contentUrl: string;
    width?: string;
    height?: string;
    description?: string;
  };
  datePublished?: string;
  dateModified?: string;
};

export const PageSchema = ({
  name,
  url,
  headline,
  description,
  mainContentOfPage = "All page content",
  inLanguage = "en",
  mainEntity,
  publisher,
  image,
  datePublished,
  dateModified,
}: PageSchemaProps) => {
  const data = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    name,
    url,
    headline,
    description,
    mainContentOfPage,
    inLanguage,
    ...(mainEntity && {
      mainEntity: {
        "@type": mainEntity.type || "Thing",
        name: mainEntity.name,
      },
    }),
    ...(publisher && {
      publisher: {
        "@type": "Organization",
        name: publisher.name,
        ...(publisher.logoUrl && {
          logo: {
            "@type": "ImageObject",
            url: publisher.logoUrl,
          },
        }),
      },
    }),
    ...(image && {
      image: {
        "@type": "ImageObject",
        contentUrl: image.contentUrl,
        ...(image.width && { width: image.width }),
        ...(image.height && { height: image.height }),
        ...(image.description && { description: image.description }),
      },
    }),
    ...(datePublished && { datePublished }),
    ...(dateModified && { dateModified }),
  };

  return <JsonLd data={data} />;
};
