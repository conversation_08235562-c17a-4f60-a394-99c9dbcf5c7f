"use client";
import JsonLd from "./JsonLd";

type Brand = { name: string; ["@type"]?: "Organization" | "Brand" };

type AggregateRating = {
  ratingValue: string | number;
  reviewCount?: string | number;
  bestRating?: string | number;
  worstRating?: string | number;
  ["@type"]?: "AggregateRating";
};

type ReviewRating = {
  ratingValue: string | number;
  bestRating?: string | number;
  worstRating?: string | number;
  ["@type"]?: "Rating";
};

type Review = {
  author?: { name: string; ["@type"]?: "Person" | "Organization" };
  datePublished?: string;
  reviewBody?: string;
  name?: string;
  reviewRating?: ReviewRating;
  ["@type"]?: "Review";
};

export type ProductRatingReview = {
  name: string;
  image?: string | string[];
  description?: string;
  url?: string;
  brand?: Brand;
  sku?: string;
  aggregateRating?: AggregateRating;
  review?: Review[];
};

export default function RatingAndReviewSchema({
  data,
}: {
  data: ProductRatingReview;
}) {
  if (!data || !data.name) return null;

  const stripHtml = (html: string) => html.replace(/<[^>]*>/g, "");

  const schema = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: data.name,
    image: data.image,
    description: data.description ? stripHtml(data.description) : undefined,
    url: data.url,
    brand: data.brand
      ? {
          "@type": data.brand["@type"] ?? "Organization",
          name: data.brand.name,
        }
      : undefined,
    sku: data.sku,
    aggregateRating: data.aggregateRating
      ? {
          "@type": data.aggregateRating["@type"] ?? "AggregateRating",
          ratingValue: data.aggregateRating.ratingValue,
          reviewCount: data.aggregateRating.reviewCount,
          bestRating: data.aggregateRating.bestRating,
          worstRating: data.aggregateRating.worstRating,
        }
      : undefined,
    review: data.review?.map((r) => ({
      "@type": r["@type"] ?? "Review",
      author: r.author
        ? { "@type": r.author["@type"] ?? "Person", name: r.author.name }
        : undefined,
      datePublished: r.datePublished,
      reviewBody: r.reviewBody ? stripHtml(r.reviewBody) : undefined,
      name: r.name,
      reviewRating: r.reviewRating
        ? {
            "@type": r.reviewRating["@type"] ?? "Rating",
            ratingValue: r.reviewRating.ratingValue,
            bestRating: r.reviewRating.bestRating,
            worstRating: r.reviewRating.worstRating,
          }
        : undefined,
    })),
  };

  return <JsonLd data={schema} />;
}
