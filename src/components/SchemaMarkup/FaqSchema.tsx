"use client";
import JsonLd from "./JsonLd";

type FAQ = {
  question: string;
  answer: string;
};

export default function FaqSchema({ faqs }: { faqs: FAQ[] }) {
  if (!faqs || faqs.length === 0) return null;

  const stripHtml = (html: string) => {
    return html.replace(/<[^>]*>/g, "");
  };

  const schema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqs.map((faq) => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: stripHtml(faq.answer),
      },
    })),
  };

  return <JsonLd data={schema} />;
}
