"use client";
import JsonLd from "./JsonLd";

type BreadcrumbItem = {
  name: string;
  item: string;
};

export default function BreadcrumbsSchema({
  breadcrumbs,
}: {
  breadcrumbs: BreadcrumbItem[];
}) {
  if (!breadcrumbs || breadcrumbs.length === 0) return null;

  const schema = {
    "@context": "https://schema.org/",
    "@type": "BreadcrumbList",
    itemListElement: breadcrumbs.map((breadcrumb, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: breadcrumb.name,
      item: breadcrumb.item,
    })),
  };

  return <JsonLd data={schema} />;
}
