"use client";

import JsonLd from "./JsonLd";

type InsuranceSchemaProps = {
  name: string;
  description: string;
  brand: string; // e.g., "Niva Bupa Health Insurance"
  price: string;
  url: string;
  sku: string;
};

export default function InsuranceSchema({
  name,
  description,
  brand,
  price,
  url,
  sku,
}: InsuranceSchemaProps) {
  const productSchema = {
    "@context": "https://schema.org",
    "@type": "Product",
    name,
    description,
    sku,
    brand: {
      "@type": "Organization",
      name: brand, // Insurance company like Niva Bupa
    },
    offers: {
      "@type": "Offer",
      priceCurrency: "INR",
      price,
      availability: "https://schema.org/InStock",
      url,
    },
  };

  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    name: `${name} - Insurance Broking`,
    serviceType: "Health Insurance",
    description: `Distributed by OneAssure Insurance Brokers Pvt. Ltd. as a licensed IRDAI broker for ${brand}.`,
    provider: {
      "@type": "InsuranceAgency",
      name: "OneAssure Insurance Brokers Pvt. Ltd.",
      url: "https://www.oneassure.in",
    },
    offers: {
      "@type": "Offer",
      priceCurrency: "INR",
      price,
      availability: "https://schema.org/InStock",
      url,
    },
  };

  return <JsonLd data={[productSchema, serviceSchema]} />;
}
