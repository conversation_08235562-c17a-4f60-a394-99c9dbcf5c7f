import { AxiosResponse } from "axios";
import { useMutation, useQuery } from "@tanstack/react-query";
import { postRequest } from "@/scripts/requests";
import { MutationConfig } from "@/lib/react-query";

export const exchangeToken = async (
  token: string = process.env.NEXT_PUBLIC_USER_TOKEN!
): Promise<AxiosResponse<{ data: { access_token: string } }>> => {
  const data = await postRequest(
    `${process.env.NEXT_PUBLIC_USER_API}/exchange-token/v1/`,
    { token: process.env.NEXT_PUBLIC_USER_TOKEN },
    { requireAuth: false }
  );
  return data;
};
type useExchangeTokenOptions = {
  config?: MutationConfig<typeof exchangeToken>;
};

export const useExchangeToken = (params: useExchangeTokenOptions = {}) => {
  return useMutation({
    // @ts-ignore
    mutationFn: (token: string) => exchangeToken(token),
    throwOnError: false,
    ...params.config,
    retry: false,
  });
};
