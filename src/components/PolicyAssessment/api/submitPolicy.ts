import { AxiosResponse } from "axios";
import { useMutation } from "@tanstack/react-query";

import { postRequest } from "@/scripts/requests";
import { MutationConfig } from "@/lib/react-query";

export const submitPolicy = async ({
  formData,
  token,
}: {
  formData: FormData;
  token: string;
}): Promise<AxiosResponse<{ data: any }>> => {
  return await postRequest(
    `${process.env.NEXT_PUBLIC_BROKER_URL}/policy/policy_assessment/v1/`,
    formData,
    {
      requireAuth: false,
      headers: { "Content-Type": "multipart/form-data", token: `${token}` },
    }
  );
};

type UseSubmitPolicyOptions = {
  config?: MutationConfig<typeof submitPolicy>;
};

export const useSubmitPolicy = (params: UseSubmitPolicyOptions = {}) => {
  return useMutation({
    // @ts-ignore
    mutationFn: (body: { formData: FormData; token: string }) =>
      submitPolicy(body),
    throwOnError: false,
    ...params.config,
    retry: false,
  });
};
