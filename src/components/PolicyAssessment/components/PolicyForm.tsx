"use client";
import { useF<PERSON><PERSON>, Formik, Form, Field, ErrorMessage } from "formik";
import { useEffect, useState } from "react";
import { useSubmitPolicy } from "../api/submitPolicy";
import { FaFileUpload } from "react-icons/fa";
import { toast } from "react-toastify";
import * as Yup from "yup";
import { useExchangeToken } from "../api/exchangeToken";
import { useRouter } from "next/navigation";
import Loader from "@/components/globals/Loader";
import SuccessPage from "./Success";

export const PolicyForm = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [formData, setFormData] = useState<{
    name: string;
    email: string;
    phone: string;
    file: File | null;
  }>({
    name: "",
    email: "",
    phone: "",
    file: null,
  });

  const { mutate: mutateSubmitPolicy } = useSubmitPolicy({
    config: {
      onSuccess: (data) => {
        toast.success("Policy submitted successfully!");
        setShowSuccess(true);
      },
      onError: (error: any) => {
        setIsLoading(false);
        toast.error("Failed to submit Policy");
      },
    },
  });
  const { mutate: mutateExchangeToken } = useExchangeToken({
    config: {
      onSuccess: (data) => {
        const formVals = new FormData();
        formVals.append("user_name", formData.name);
        formVals.append("user_email", formData.email);
        formVals.append("user_phone_number", formData.phone);
        if (formData.file) {
          formVals.append("file", formData.file);
        }
        //@ts-ignore
        mutateSubmitPolicy({
          formData: formVals,
          token: data.data.data.access_token,
        });
      },
      onError: (error: any) => {
        setIsLoading(false);
        toast.error(
          "Your session has timed out. Please reopen the form to proceed."
        );
      },
    },
  });

  const validationSchema = Yup.object({
    name: Yup.string()
      .required("Name is required")
      .matches(/^[a-zA-Z\s]+$/, "Name can only contain letters and spaces")
      .min(2, "Name must be at least 2 characters")
      .max(50, "Name cannot exceed 50 characters"),
    email: Yup.string()
      .email("Invalid email address")
      .required("Email is required"),
    phone: Yup.string()
      .matches(/^[6-9]\d{9}$/, "Please Enter a valid Phone Number")
      .required("Phone number is required"),
    file: Yup.mixed()
      .required("File is required")
      .test("fileType", "Only .pdf files are allowed", (value) => {
        return value && (value as File).type === "application/pdf";
      }),
  });

  const handleSubmit = (values: {
    name: string;
    email: string;
    phone: string;
    file: File | null;
  }) => {
    const updatedFormData = {
      name: values.name,
      email: values.email,
      phone: values.phone,
      file: values.file,
    };
    setFormData(updatedFormData);
    mutateExchangeToken();
  };

  return (
    <div className="flex items-center justify-center h-screen p-4 relative">
      {showSuccess ? (
        <SuccessPage />
      ) : (
        <div className="w-full max-w-2xl p-6 bg-white shadow-lg rounded-lg">
          <h1 className="text-2xl font-bold mb-6 text-center text-gray-800">
            Policy Assessment Form
          </h1>
          {isLoading && (
            <div className="absolute text-primary-2 inset-0 z-50 flex items-center justify-center bg-white ">
              <Loader />
            </div>
          )}

          <Formik
            initialValues={{
              name: "",
              email: "",
              phone: "",
              file: null,
            }}
            enableReinitialize
            validationSchema={validationSchema}
            onSubmit={(values, { resetForm }) => {
              if (values.file) {
                handleSubmit(values);
                setIsLoading(true);
                resetForm({
                  values: {
                    name: "",
                    email: "",
                    phone: "",
                    file: null,
                  },
                });
              }
            }}
          >
            {({ setFieldValue }) => (
              <Form className="space-y-4">
                {/* Phone Input */}
                <div>
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Name
                    <span className="text-red-500">*</span>
                  </label>
                  <Field
                    type="name"
                    id="name"
                    name="name"
                    placeholder="Enter Name"
                    className={`w-full mt-1 p-2 border border-black rounded-md shadow-sm `}
                  />
                  <ErrorMessage
                    name="name"
                    component="p"
                    className="mt-1 text-sm text-red-500"
                  />
                </div>
                {/* Email Input */}
                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Email
                    <span className="text-red-500">*</span>
                  </label>
                  <Field
                    type="email"
                    id="email"
                    name="email"
                    placeholder="Enter email"
                    className={`w-full mt-1 p-2 border rounded-md shadow-sm `}
                  />
                  <ErrorMessage
                    name="email"
                    component="p"
                    className="mt-1 text-sm text-red-500"
                  />
                </div>

                {/* Phone Input */}
                <div>
                  <label
                    htmlFor="phone"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Phone
                    <span className="text-red-500">*</span>
                  </label>
                  <Field
                    type="tel"
                    id="phone"
                    name="phone"
                    placeholder="Enter phone number"
                    className={`w-full mt-1 p-2 border rounded-md shadow-sm `}
                  />
                  <ErrorMessage
                    name="phone"
                    component="p"
                    className="mt-1 text-sm text-red-500"
                  />
                </div>

                {/* File Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Upload File
                    <span className="text-red-500">*</span>
                  </label>
                  <div className="relative mt-1">
                    <Field name="file">
                      {({ field, form }: any) => (
                        <label htmlFor="file" className="block cursor-pointer">
                          <div className="flex items-center">
                            <input
                              type="text"
                              readOnly
                              placeholder="No file selected"
                              value={form.values.file?.name || ""}
                              className="w-full mt-1 p-2 border rounded-md shadow-sm"
                              onClick={(e) => {
                                e.preventDefault();
                                document.getElementById("file")?.click();
                              }}
                            />
                            <div className="ml-2 inline-flex items-center justify-center p-2 text-white bg-primary-2 rounded-md hover:bg-blue-600 mt-1">
                              <FaFileUpload className="w-6 h-6" />
                            </div>
                            <input
                              type="file"
                              id="file"
                              accept=".pdf"
                              onChange={(event) => {
                                const file =
                                  event.currentTarget.files?.[0] || null;
                                form.setFieldValue("file", file);
                              }}
                              onClick={(e) => (e.currentTarget.value = "")}
                              className="hidden"
                            />
                          </div>
                        </label>
                      )}
                    </Field>
                  </div>
                  <ErrorMessage
                    name="file"
                    component="p"
                    className="mt-1 text-sm text-red-500"
                  />
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  className="w-full bg-primary-2 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-md"
                >
                  Submit
                </button>
              </Form>
            )}
          </Formik>
        </div>
      )}
    </div>
  );
};
