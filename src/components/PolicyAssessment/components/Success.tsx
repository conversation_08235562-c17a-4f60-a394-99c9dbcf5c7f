import { FaCheckCircle } from "react-icons/fa";
import Link from "next/link";
import BookACallBtn from "@/components/globals/BookACall";

const SuccessPage = () => {
  return (
    <div className="flex items-center justify-center h-screen  px-4">
      <div className="bg-white p-8 sm:p-10 rounded-lg shadow-xl text-center flex flex-col items-center justify-center w-full max-w-lg sm:max-w-xl">
        <FaCheckCircle className="text-green-500 text-5xl mb-6" />
        <h2 className="text-2xl font-bold text-green-700 mb-4">
          Policy Submitted Successfully!
        </h2>
        <p className="text-gray-700 text-base sm:text-lg mb-6">
          Ready to take the next step? Book a quick call now, and we’ll help
          assess your policy to ensure it’s just right for you.
        </p>
        <BookACallBtn
          className="bg-primary-2 rounded-lg text-white px-6 py-3 font-semibold text-lg hover:bg-primary-3 transition duration-300 ease-in-out mb-4"
          utm="website-policy-assessment-requested"
        />
        <Link href="/">
          <span className="text-primary-1 underline hover:text-primary-2 text-sm sm:text-base cursor-pointer transition duration-300 ease-in-out">
            Go Back
          </span>
        </Link>
      </div>
    </div>
  );
};

export default SuccessPage;
