"use client";

import React from "react";
import Image from "next/image";
import Book<PERSON>all from "@/components/globals/BookACall";

const BookCallBanner = () => {
  return (
    <div className="relative px-5 pt-6 md:pt-10 pb-5 flex flex-col gap-4 md:flex-row items-center bg-gradient-to-r from-gradient-3-green-dark to-gradient-3-green-light bg-200% animate-gradient rounded-xl text-center w-full shadow-whyOneassure font-medium overflow-hidden md:mb-14 mb-5">
      <div className="absolute bottom-0 left-0 md:left-28 transform -translate-x-1/2 translate-y-1/2 z-0">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={500}
          height={500}
          objectFit="cover"
        />
      </div>
      <div className="hidden md:block absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 z-0">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={570}
          height={570}
          objectFit="cover"
        />
      </div>
      {/* Left Side: Main Heading & Subheading */}
      <div className="flex-1 flex flex-col justify-center z-10 text-left md:pl-8">
        <p className="text-white font-medium text-2xl md:text-5xl md:leading-[60px] mb-2 md:mb-4">
          Get Claim Support Now
        </p>
        <p className="text-white text-base md:text-xl md:leading-[40px]">
          Our claim advisors are on standby to assist you.
        </p>
      </div>
      {/* Right Side: Secondary Text & Button */}
      <div className="flex flex-col items-center justify-center gap-4 z-10 md:pr-8 w-full md:w-auto mt-6 md:mt-0">
        <p className="text-white text-base md:text-2xl md:leading-[40px] font-medium text-center md:text-right">
          Start Your Claim Journey With One Click
        </p>
        <BookACall className="mt-2 md:mt-4 px-10 py-4 bg-white text-[#189CA7] text-2xl font-semibold rounded-full w-fit hover:bg-gray-100 shadow-lg transition-all duration-200" />
      </div>
    </div>
  );
};

export default BookCallBanner;
