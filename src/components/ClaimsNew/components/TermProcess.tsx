import React, { useState } from "react";
import ProcessStepCard from "./ProcessStepCard";
import MobileProcessStepCard from "./MobileProcessStepCard";

const TermProcess = (props: {
  title: string;
  steps: {
    icon: any;
    step: number;
    title: string;
    desc: string;
  }[];
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(true);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  return (
    <div className="max-w-[1240px] mx-auto p-4">
      {/* Mobile Header - Hidden on desktop */}
      <div className="text-center text-[22px] font-medium text-[#4CB6C1] mb-4 md:hidden">
        {props.title}
      </div>

      {/* Mobile Dropdown - Hidden on desktop */}
      <div
        className="rounded-xl bg-[#4CB6C1] text-white text-sm font-semibold flex items-center justify-between px-6 py-4 mb-8 md:hidden cursor-pointer transition-all duration-200 hover:bg-[#3da5b0]"
        onClick={toggleDropdown}
      >
        <span>Term Insurance Claim Settlement Process</span>
        <svg
          width="24"
          height="24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className={`transition-transform duration-300 ${
            isDropdownOpen ? "rotate-180" : ""
          }`}
        >
          <path
            d="M7 10l5 5 5-5"
            stroke="#fff"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>

      {/* Desktop Header - Hidden on mobile */}
      <div className="text-3xl leading-tight text-center text-[#2196C9] font-medium mb-8 hidden md:block">
        {props.title}
      </div>

      {/* Mobile Layout */}
      <div
        className={`flex flex-col gap-4 mt-8 md:hidden overflow-hidden transition-all duration-500 ease-in-out ${
          isDropdownOpen ? "max-h-[2000px] opacity-100" : "max-h-0 opacity-0"
        }`}
      >
        {props.steps.map((step: any) => (
          <MobileProcessStepCard
            key={step.step}
            icon={step.icon}
            step={step.step}
            title={step.title}
            description={step.desc}
          />
        ))}
      </div>

      {/* Desktop Layout */}
      <div className="hidden md:block">
        {props.steps.map((step: any) => (
          <ProcessStepCard
            key={step.step}
            icon={step.icon}
            step={step.step}
            title={step.title}
            description={step.desc}
          />
        ))}
      </div>
    </div>
  );
};

export default TermProcess;
