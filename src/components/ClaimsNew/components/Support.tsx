import React from "react";
import SupportCard from "./SupportCard";

const Support = (props: {
  title: string;
  supportItems: {
    icon: any;
    title: string;
    subtitle: string;
  }[];
  legalDisclaimer: string;
}) => {
  return (
    <section className="w-full flex flex-col items-center max-w-[1240px] mx-auto md:py-10 py-2 px-4 bg-white">
      <h2 className="text-4xl md:text-5xl font-semibold text-[#3DBAAB] text-center mb-14">
        {props.title}
      </h2>
      <div className="flex flex-col md:flex-row md:flex-wrap gap-8 w-full max-w-7xl justify-center">
        {props.supportItems.map((item: any, idx: number) => (
          <div key={idx} className="w-auto flex-shrink-0">
            <SupportCard {...item} />
          </div>
        ))}
      </div>
      <p className="text-center text-gray-500 mt-6 italic">
        {props.legalDisclaimer}
      </p>
    </section>
  );
};

export default Support;
