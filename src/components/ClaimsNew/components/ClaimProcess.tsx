import React from "react";
import HealthProcess from "./HealthProcess";
import TermProcess from "./TermProcess";

const ClaimProcess = (props: {
  title: string;
  healthTitle: string;
  termTitle: string;
  healthCashless: {
    step: number;
    title: string;
    desc: string;
    icon: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
  }[];
  healthReimbursement: {
    step: number;
    title: string;
    desc: string;
    icon: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
  }[];
  termProcess: {
    step: number;
    title: string;
    desc: string;
    icon: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
  }[];
}) => {
  return (
    <div className="mb-5 md:mb-10">
      <div className="text-3xl font-semibold text-center text-[#53B7CE]">
        {props.title}
      </div>
      <HealthProcess
        title={props.healthTitle}
        cashlessSteps={props.healthCashless}
        reimbursementSteps={props.healthReimbursement}
      />
      <TermProcess title={props.termTitle} steps={props.termProcess} />
    </div>
  );
};

export default ClaimProcess;
