import React, { useState } from "react";
import ProcessStepCard from "./ProcessStepCard";
import MobileProcessStepCard from "./MobileProcessStepCard";

const CLAIM_TYPES = [
  { label: "Cashless Claims", value: "cashless" },
  { label: "Reimbursement Claims", value: "reimbursement" },
];

const HealthProcess = (props: {
  title: string;
  cashlessSteps: {
    icon: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
    step: number;
    title: string;
    desc: string;
  }[];
  reimbursementSteps: {
    icon: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
    step: number;
    title: string;
    desc: string;
  }[];
}) => {
  // Desktop tab state
  const [claimType, setClaimType] = useState("cashless");
  // Mobile accordion state
  const [openMobileSection, setOpenMobileSection] = useState<
    null | "cashless" | "reimbursement"
  >("cashless");

  // Mobile dropdown header
  const renderMobileDropdownHeader = (
    label: string,
    section: "cashless" | "reimbursement"
  ) => (
    <div
      className="rounded-xl bg-[#4CB6C1] text-white text-sm font-semibold flex items-center justify-between px-6 py-4 mb-8 cursor-pointer transition-all duration-200 hover:bg-[#3da5b0] md:hidden"
      onClick={() =>
        setOpenMobileSection(openMobileSection === section ? null : section)
      }
    >
      <span>{label}</span>
      <svg
        width="24"
        height="24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={`transition-transform duration-300 ${
          openMobileSection === section ? "rotate-180" : ""
        }`}
      >
        <path
          d="M7 10l5 5 5-5"
          stroke="#fff"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );

  // Mobile dropdown content
  const renderMobileDropdownContent = (isOpen: boolean, steps: any[]) => (
    <div
      className={`flex flex-col gap-4 overflow-hidden transition-all duration-500 ease-in-out ${
        isOpen ? "max-h-[2000px] opacity-100 mb-8" : "max-h-0 opacity-0 mb-0"
      } md:hidden`}
    >
      {steps.map((step) => (
        <MobileProcessStepCard
          key={step.step}
          icon={step.icon}
          step={step.step}
          title={step.title}
          description={step.desc}
        />
      ))}
    </div>
  );

  return (
    <div className="max-w-[1240px] mx-auto p-4 md:py-8">
      <div className="text-3xl font-medium text-center text-[#53B7CE] mb-6">
        {props.title}
      </div>
      {/* Desktop/Laptop UI */}
      <div className="hidden md:block">
        <div className="flex justify-center mb-8">
          <div className="flex bg-gradient-to-r from-[#51A6DB] to-[#52B1A5] border border-[#B6E2F0] rounded-xl overflow-hidden w-[375px] md:w-[450px] p-1">
            {CLAIM_TYPES.map((type) => (
              <button
                key={type.value}
                className={`flex-1 py-2 text-xs md:text-base font-semibold rounded-xl transition-all duration-200 focus:outline-none z-10
                  ${
                    claimType === type.value
                      ? "bg-white text-[#2196C9] shadow-[0_2px_8px_0_rgba(33,150,201,0.10)] border border-[#B6E2F0]"
                      : "bg-transparent text-white border border-transparent"
                  }
                `}
                style={{
                  marginRight: type.value === "cashless" ? "4px" : 0,
                  marginLeft: type.value === "reimbursement" ? "4px" : 0,
                }}
                onClick={() => setClaimType(type.value)}
              >
                {type.label}
              </button>
            ))}
          </div>
        </div>
        <div className="space-y-6">
          {claimType === "cashless"
            ? props.cashlessSteps.map((step) => (
                <ProcessStepCard
                  key={step.step}
                  icon={step.icon}
                  step={step.step}
                  title={step.title}
                  description={step.desc}
                />
              ))
            : props.reimbursementSteps.map((step) => (
                <ProcessStepCard
                  key={step.step}
                  icon={step.icon}
                  step={step.step}
                  title={step.title}
                  description={step.desc}
                />
              ))}
        </div>
      </div>
      {/* Mobile UI (Accordion Dropdowns) */}
      <div className="md:hidden">
        {renderMobileDropdownHeader("Cashless Insurance Claims", "cashless")}
        {renderMobileDropdownContent(
          openMobileSection === "cashless",
          props.cashlessSteps
        )}
        {renderMobileDropdownHeader("Reimbursement Claims", "reimbursement")}
        {renderMobileDropdownContent(
          openMobileSection === "reimbursement",
          props.reimbursementSteps
        )}
      </div>
    </div>
  );
};

export default HealthProcess;
