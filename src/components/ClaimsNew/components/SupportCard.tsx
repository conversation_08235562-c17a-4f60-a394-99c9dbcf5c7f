import React from "react";

const SupportCard = ({
  icon,
  title,
  subtitle,
}: {
  icon: any;
  title: string;
  subtitle: string;
}) => (
  <div className="flex flex-col items-center text-center rounded-2xl px-8 py-8 bg-gradient-to-br from-[#3DBAAB] to-[#2889C8] shadow-lg w-full max-w-md mx-auto md:h-[250px]">
    <img
      src={icon.data.attributes.url}
      alt={title}
      width={70}
      height={70}
      className="mb-4"
    />
    <h3 className="text-white text-xl font-semibold mb-2">{title}</h3>
    <p className="text-white text-sm font-normal leading-relaxed">{subtitle}</p>
  </div>
);

export default SupportCard;
