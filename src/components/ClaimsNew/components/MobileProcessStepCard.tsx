import React from "react";

interface MobileProcessStepCardProps {
  icon: {
    data: {
      attributes: {
        url: string;
      };
    };
  };
  step: number;
  title: string;
  description: string;
}

const MobileProcessStepCard: React.FC<MobileProcessStepCardProps> = ({
  icon,
  step,
  title,
  description,
}) => (
  <div className="relative flex flex-col items-center bg-gradient-to-b from-[#F3FAFC] to-[#E6F4FA] rounded-2xl pt-14 pb-8 px-6 shadow-md mt-8">
    <div className="absolute -top-8 flex items-center justify-center w-[90px] h-[90px] rounded-full bg-gradient-to-tr from-[#c7e4f8] to-[#aafcf1] border-[8px] border-white">
      <img
        src={icon.data.attributes.url}
        alt={`Step ${step}`}
        className="w-12 h-12 object-contain"
      />
    </div>
    <div className="mt-4 text-[20px] font-semibold text-[#2196C9] text-center mb-2 leading-tight">
      <span className="text-[#2889C8]">Step {step}: </span>
      <span>{title}</span>
    </div>
    <div className="text-[#444] text-[15px] text-center leading-relaxed">
      {description}
    </div>
  </div>
);

export default MobileProcessStepCard;
