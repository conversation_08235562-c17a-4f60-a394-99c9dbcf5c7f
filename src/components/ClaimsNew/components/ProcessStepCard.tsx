import React from "react";

interface ProcessStepCardProps {
  icon: {
    data: {
      attributes: {
        url: string;
      };
    };
  };
  step: number;
  title: string;
  description: string;
}

const ProcessStepCard: React.FC<ProcessStepCardProps> = ({
  icon,
  step,
  title,
  description,
}) => (
  <div className="flex items-center mb-8 last:mb-0">
    <div className="w-16 h-16 flex items-center justify-center mr-6 mt-2">
      <img
        src={icon.data.attributes.url}
        alt={`Step ${step}`}
        className="w-15 h-15 object-contain"
      />
    </div>
    <div className="flex-1 border-2 border-[#7EC0E7] rounded-2xl px-8 py-6 bg-white shadow-sm">
      <div className="font-semibold text-[18px] leading-tight text-black mb-4">
        <span className="text-[#2196C9]">Step {step}: </span>
        <span>{title}</span>
      </div>
      <div className="font-normal text-[16px] text-[#444] leading-relaxed">
        {description}
      </div>
    </div>
  </div>
);

export default ProcessStepCard;
