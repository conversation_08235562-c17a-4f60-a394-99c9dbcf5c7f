import React from "react";
import parse from "html-react-parser";

const Hero = (props: { title: string; subtitle: string }) => {
  // Split the title at the comma
  const titleParts = props.title.split(",");

  return (
    <section className="w-full flex flex-col items-center justify-center md:pt-16 pt-8 pb-8 bg-white relative overflow-hidden">
      {/* Decorative background circle (optional, can be improved with SVG if needed) */}
      <div
        className="absolute left-0 top-0 w-[500px] h-[500px] rounded-full bg-blue-50 opacity-20 -z-10"
        style={{ filter: "blur(2px)" }}
      />
      <h1 className="text-5xl md:text-6xl font-bold text-center leading-tight mb-6">
        {titleParts[0]},
        {titleParts.length > 1 && (
          <span className="bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text text-transparent">
            {titleParts[1]}
          </span>
        )}
      </h1>
      <div className="text-lg md:text-xl text-center text-black font-normal max-w-3xl">
        {parse(props.subtitle)}
      </div>
    </section>
  );
};

export default Hero;
