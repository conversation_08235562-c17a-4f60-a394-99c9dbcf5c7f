"use client";
import Hero from "./components/Hero";
import Support from "./components/Support";
import Form from "../ContactUsNew/components/Form";
import Reviews from "./components/Reviews";
import BookCall from "./components/BookCall";
import ClaimProcess from "./components/ClaimProcess";

const heroProps = {
  title: "Get Expert Help with Health & Term Insurance",
  subtitle: "Talk to OneAssure Today",
};

const ClaimsNew = (props: any) => {
  return (
    <div>
      <div className="p-4 md:p-0">
        <Hero {...props.hero} />
      </div>
      <Support {...props.support} />
      <div className="max-w-[1240px] mx-auto p-4 md:p-0">
        <Form {...heroProps} />
        {/* <BookCall /> */}
      </div>
      <Reviews {...props.testimonials} />
      <ClaimProcess {...props.process} />
    </div>
  );
};

export default ClaimsNew;
