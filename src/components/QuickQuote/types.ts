// import { BaseEntity } from '@/types';

type BaseEntity = {
  success: boolean;
};

export type QuickQuoteResponse = {
  data: QuickQuoteData;
  message: string;
} & BaseEntity;

export type PlanTypes = "vanilla-health" | "super-top-up";
export type QuickQuoteData = {
  theme: Array<TypeTheme>;
  variant_recommendations: {
    [key in PlanTypes]: Array<PlanData>;
  };
};

export type TypeTheme = {
  tag: string;
  title: string;
  colors: Array<string>;
  icons: Array<{
    icon_url: string;
    icon_name: string;
  }>;
};

export type PlanData = {
  variant_name: string;
  company_name: string;
  icon: string;
  selected_sum_insured: {
    sum_insured: string;
    deductible?: string;
    tenure: string;
  };
  selected_premium: number | string;
  si_premium_options: Array<{
    sum_insured: string;
    deductible?: string;
    premium: number | string;
    tenure: string;
  }>;
  variant_id: string;
  total_rating: number;
  oneassure_recommended: boolean;
  network_hospitals: number;
};

export type UserFormResponse = {
  members: { [key: string]: string };
  pincode: string;
  proposer_gender: string;
};
