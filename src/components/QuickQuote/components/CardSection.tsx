import { RecommendationCard } from ".";
import { PlanData } from "../types";

interface Props {
  data: Array<PlanData>;
  activeTab: string;
  sumInsured: string;
}

export const CardSection = ({ data, activeTab, sumInsured }: Props) => {
  // const companies = [
  //   "Max Bupa Health Insurance",
  //   "Care Health Insurance Limited",
  //   "ICICI Lombard",
  //   "HDFC ERGO",
  //   "Niva Bupa Health Insurance",
  // ];
  return (
    <div className="flex flex-wrap gap-2 justify-center items-center">
      {data
        // .filter((item, index) => companies.includes(item.company_name))
        .toSorted((a, b) => b.total_rating - a.total_rating)
        // .filter((_, index) => index < 10)
        .map((item: PlanData, index: number) => {
          return (
            <RecommendationCard
              key={index}
              activeTab={activeTab}
              planData={item}
              sumInsured={sumInsured}
            />
          );
        })}
    </div>
  );
};
