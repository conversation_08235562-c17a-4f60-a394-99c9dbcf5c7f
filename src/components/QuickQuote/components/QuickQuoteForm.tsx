"use client";
import React, { useState } from "react";
import { quickQuoteInputsBuilder } from "../constants/quickQuoteInputs";
import { QuickQuoteInput } from ".";
import { UserFormResponse } from "../types";
import Container from "@/components/globals/Container";

interface Props {
  handleFormInputs: (answer: string, label: string) => void;
  quickQuoteAnswers: any;
  handleSubmitParams: (
    user_response: UserFormResponse,
    sumInsured: string
  ) => void;
}
export const QuickQuoteForm = ({
  handleFormInputs,
  quickQuoteAnswers,
  handleSubmitParams,
}: Props) => {
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const agesInputHandler = (label: string, index: number) => {
    let key = `${label.toLowerCase().includes("adults") ? "adult" : "son"}${
      index + 1
    }`;
    return key;
  };
  const handleQuickQuoteSubmit = async () => {
    setErrors(() => {
      return {};
    });
    let err: { [key: string]: string } = {};
    Object.keys(quickQuoteAnswers)
      .filter((key) => key.includes("adult"))
      .map((key: string, index: number) => {
        if (quickQuoteAnswers[key] < 18) {
          err["No. of Adults"] = "Adult age cannot be less than 18";
        } else if (quickQuoteAnswers[key] > 100) {
          err["No. of Adults"] = "Adult age cannot be greater than 100";
        }
      });
    Object.keys(quickQuoteAnswers)
      .filter((key) => key.includes("son"))
      .map((key: string, index: number) => {
        if (quickQuoteAnswers[key] <= 0) {
          err["No. of Children"] = "Child's age should at least be 1 year";
        } else if (quickQuoteAnswers[key] > 25) {
          err["No. of Children"] = "Child's age can't be greater than 25";
        }
      });
    const re = new RegExp("^[0-9][0-9]{5}$");
    if (!re.test(quickQuoteAnswers["Postal Code"])) {
      err["Postal Code"] = "Enter a valid Pincode";
    }

    const url = `${process.env.NEXT_PUBLIC_BROKER_URL}/sales/validate_pincode/v1/?pincode=${quickQuoteAnswers["Postal Code"]}`;
    const response = await fetch(url);

    if (
      (response?.status >= 400 && response.status <= 499) ||
      (response && response.status >= 500 && response.status <= 599)
    ) {
      err["Postal Code"] = "Enter a valid Pincode";
    }

    if (Object.keys(err).length === 0) {
      let user_response: UserFormResponse = {
        pincode: "",
        proposer_gender: "",
        members: {},
      };
      user_response["pincode"] = quickQuoteAnswers["Postal Code"];
      user_response["proposer_gender"] = "male";

      if (quickQuoteAnswers.hasOwnProperty("adult1")) {
        user_response["members"]["self"] = quickQuoteAnswers["adult1"];
      }
      if (quickQuoteAnswers.hasOwnProperty("adult2")) {
        user_response["members"]["spouse"] = quickQuoteAnswers["adult2"];
      }
      Object.keys(quickQuoteAnswers).map((tag: string) => {
        if (tag.includes("son")) {
          user_response["members"] = {
            ...user_response["members"],
            [tag]: quickQuoteAnswers[tag],
          };
        }
      });
      handleSubmitParams(
        user_response,
        quickQuoteAnswers["Your coverage Amount"]
      );
    } else {
      setErrors(() => err);
    }
  };

  // @ts-ignore
  const handleChange = (value, label) => {
    handleFormInputs(value, label);
    setErrors(() => {
      return {};
    });
  };
  return (
    <Container>
      <h2 className="font-bold text-gray900 text-[32px] mb-5 text-center ">
        Get a quote
      </h2>
      <form
        className="flex justify-between items-end gap-5 flex-wrap rounded-2xl shadow-100 p-3 md:px-4 md:py-6 border my-3 mx-5 lg:mx-0"
        onSubmit={(e) => {
          e.preventDefault();
          handleQuickQuoteSubmit();
        }}
      >
        {quickQuoteInputsBuilder.map((item: any, index: number) => {
          return (
            <>
              <QuickQuoteInput
                type={item.type}
                placeholder={item?.placeholder}
                label={item?.label}
                values={item?.values}
                handleFormInputs={handleChange}
                quickQuoteAnswers={quickQuoteAnswers}
                error={errors?.[item?.label]}
                key={index}
              />
              {quickQuoteAnswers?.[item?.label] && (
                <>
                  <div
                    className={`${
                      item?.label.toLowerCase().includes("adults") ||
                      (item?.label.toLowerCase().includes("children") &&
                        quickQuoteAnswers?.[item.label] > 0)
                        ? `flex-1`
                        : ""
                    }`}
                  >
                    {item?.label.toLowerCase().includes("adults") ||
                    item?.label.toLowerCase().includes("children") ? (
                      <>
                        {quickQuoteAnswers?.[item.label] > 0 && (
                          <label
                            className={`min-w-tabWidth flex-1 flex flex-col`}
                          >
                            <span
                              className={`text-xs w-full whitespace-nowrap font-medium text-gray500`}
                            >
                              {item?.label.toLowerCase().includes("adults")
                                ? "Enter adults age (in years)"
                                : "Enter children age (in years)"}
                            </span>
                            <div className="flex gap-[5px]">
                              {Array.from(
                                {
                                  length: parseInt(
                                    quickQuoteAnswers?.[item?.label]
                                  ),
                                },
                                (_, index) => index + 1
                              ).map((index) => {
                                return (
                                  <input
                                    //  className={
                                    //   errors[agesInputHandler(item.label, index)]
                                    //    ? styles.quickQuoteInputAgesError
                                    //    : styles.quickQuoteInputAges
                                    //  }
                                    className={
                                      errors[
                                        agesInputHandler(item.label, index)
                                      ]
                                        ? `max-w-[56px] p-2 bg-red200 border-red500 font-semibold text-center rounded-md`
                                        : `max-w-[56px] p-2 font-semibold valid:bg-gray200 text-center rounded-md border border-gray600`
                                    }
                                    onKeyDown={(e) => {
                                      if (
                                        !/\d/.test(e.key) &&
                                        e.key !== "Backspace" &&
                                        !/^Arrow(Left|Right|Up|Down)$/.test(
                                          e.key
                                        )
                                      ) {
                                        e.preventDefault();
                                      }
                                    }}
                                    value={
                                      quickQuoteAnswers?.[
                                        `${
                                          item?.label
                                            .toLowerCase()
                                            .includes("adults")
                                            ? "adult"
                                            : "son"
                                        }${index}`
                                      ]
                                    }
                                    onChange={(e) =>
                                      handleFormInputs(
                                        e.target.value,
                                        `${
                                          item?.label
                                            .toLowerCase()
                                            .includes("adults")
                                            ? "adult"
                                            : "son"
                                        }${index}`
                                      )
                                    }
                                    key={index}
                                    required={true}
                                  />
                                );
                              })}
                            </div>
                          </label>
                        )}
                      </>
                    ) : (
                      ""
                    )}
                  </div>
                </>
              )}
            </>
          );
        })}
        <input
          type="submit"
          className={`cursor-pointer max-w-[120px] flex-1 p-2 h-fit bg-blue600 font-semibold rounded-md shadow-lg text-gray200`}
          value="Done"
        />
      </form>
    </Container>
  );
};
