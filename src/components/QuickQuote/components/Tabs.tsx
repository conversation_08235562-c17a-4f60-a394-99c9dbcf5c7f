import React from "react";
import Image from "next/image";

interface Props {
  activeTab: string;
  handleChange: any;
  tabs: any;
}
export const Tabs = ({ activeTab, handleChange, tabs }: Props) => {
  return (
    <main className="md:w-[40%] m-auto mb-8">
      <section className="text-black text-xl font-semibold rounded-full shadow-shadow100 flex justify-evenly items-center py-4">
        {tabs.map((item: any, index: number) => {
          const showIcon = item.icons.filter(
            (iconItem: { icon_url: string; icon_name: string }) => {
              return activeTab === item.tag
                ? iconItem.icon_name === "white"
                : iconItem.icon_name === "grey";
            }
          )[0]["icon_url"];
          return (
            <section
              className={
                item.tag !== activeTab
                  ? "flex rounded-full py-1 px-2 justify-center items-center cursor-pointer  md:px-4 md:py-2"
                  : activeTab === "vanilla-health"
                  ? `flex bg-[#e3e3ff] rounded-full py-1 px-2 justify-center items-center cursor-pointer md:px-4 md:py-2`
                  : `flex bg-[#c6e8ff] rounded-full py-1 px-2 justify-center items-center cursor-pointer  md:px-4 md:py-2`
              }
              onClick={() => handleChange(item.tag)}
              key={index}
            >
              <div className="flex">
                {item.tag === "vanilla-health" ? (
                  <Image
                    src={showIcon}
                    alt=""
                    className=""
                    width={32}
                    height={32}
                  />
                ) : (
                  <Image
                    src={showIcon}
                    alt=""
                    className=""
                    width={32}
                    height={32}
                  />
                )}
              </div>
              <h4 className="text-sm md:text-lg">{item.title}</h4>
            </section>
          );
        })}
      </section>
    </main>
  );
};
