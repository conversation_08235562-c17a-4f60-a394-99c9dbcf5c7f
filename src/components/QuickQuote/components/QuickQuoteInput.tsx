import React from "react";

interface Props {
  type: string;
  placeholder?: string;
  label: string;
  values: Array<{ [key: string]: string }>;
  handleFormInputs: (ans: string, label: string) => void;
  quickQuoteAnswers: any;
  error: string;
}
export const QuickQuoteInput = ({
  type,
  placeholder,
  label,
  handleFormInputs,
  quickQuoteAnswers,
  values,
  error,
}: Props) => {
  if (type === "number") {
    return (
      <section className="flex-1">
        <label className="flex flex-col flex-1">
          <span className={`text-xs text-slateGrey flex-1`}>{label}</span>
          <input
            className={
              error
                ? `border p-3 rounded-md text-sm valid:bg-gray100 text-gray800 bg-red200 border-red500`
                : `border p-3 rounded-md text-sm text-gray800  valid:bg-gray200`
            }
            value={quickQuoteAnswers?.[label]}
            type={type}
            placeholder={placeholder}
            onChange={(e) => handleFormInputs(e.target.value, label)}
            required
          />
        </label>
        {error ? <span className="text-xs text-red500">{error}</span> : ""}
      </section>
    );
  } else {
    return (
      <section className="flex-1 max-w-xs">
        <label className="flex flex-col flex-1">
          <span className={`font-md text-xs text-slateGrey`}>{label}</span>
          <select
            className={
              error
                ? `border p-3 rounded-md text-sm flex-1 text-gray800 bg-red200 border-red500`
                : `border p-3 rounded-md text-sm flex-1 text-gray800 valid:bg-gray200`
            }
            required
            value={quickQuoteAnswers?.[label]}
            // @ts-ignore
            placeholder={placeholder}
            onChange={(e) => handleFormInputs(e.target.value, label)}
          >
            <option value="" disabled selected>
              {placeholder}
            </option>
            {values?.map((item) => (
              <option
                className={`font-semibold text-gray500`}
                value={item?.value}
                key={item.value}
              >
                {item?.title}
              </option>
            ))}
          </select>
        </label>
        {error ? (
          <span className="text-xs whitespace-nowrap text-red500">{error}</span>
        ) : (
          ""
        )}
      </section>
    );
  }
};
