// import { Space } from '@/components/Space';
import React from "react";

function LoadingState() {
  return (
    <div className="animate-pulse my-10 w-full mx-auto">
      {/* <div className="flex justify-between flex-col items-center"> */}
      <div className="h-20 w-2/4 bg-gray300 mx-auto mb-[40px]"></div>
      {/* <Space s={40} /> */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {new Array(6).fill(0).map((_, idx) => {
          return <div key={idx} className="h-[280px] bg-gray-300 mb-4"></div>;
        })}
      </div>
      {/* </div> */}
    </div>
  );
}

export default LoadingState;
