'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import { ChevronLeftIcon } from '@heroicons/react/24/solid';

interface Props {
 title: string;
}
export function TitleBar({ title }: Props) {
 const router = useRouter();

 const onClick = () => {
  router.back();
 };
 return (
  <div className="md:flex gap-2 my-6 lg:my-12">
   <div
    className="md:flex md:justify-center md:items-center cursor-pointer"
    onClick={onClick}
   >
    <ChevronLeftIcon className="text-primary600 w-6 h-6 lg:w-10 lg:h-10" />
   </div>
   <h1 className="text-xl md:text-3xl lg:text-4xl font-semibold">{title}</h1>
  </div>
 );
}
