"use client";
import {
  QuickQuoteForm,
  RecommendationSection,
  TitleBar,
} from "@/components/QuickQuote/components";
// import Container from '@components/globals/Container';
import EmptyState from "@/components/QuickQuote/components/EmptyState";
import { UserFormResponse } from "@/components/QuickQuote/types";
// import { Space } from '@/components/Space';
// import { quick_quote_data } from '@/temp/quick-quote-data';
import React, { useState } from "react";

const QuickQuote = () => {
  const [quickQuoteAnswers, setQuickQuoteAnswers] = useState({});
  const [submitParams, setSubmitParams] = useState<{
    user_response: UserFormResponse;
    sumInsured: string;
  }>();
  const handleSubmitParams = (
    user_response: UserFormResponse,
    sumInsured: string
  ) => {
    setSubmitParams(() => {
      return { user_response: user_response, sumInsured: sumInsured };
    });
  };
  const handleFormInputs = (answer: string, label: string) => {
    if (label.toLowerCase().includes("adults")) {
      let temp: { [key: string]: string | number } = {
        ...quickQuoteAnswers,
        [label]: answer,
      };
      let adultList = Object.keys(quickQuoteAnswers)
        .filter((item) => item.includes("adult"))
        .slice(parseInt(answer));
      adultList.forEach((element: string) => {
        delete temp[element];
      });
      setQuickQuoteAnswers({ ...temp });
    } else if (label.toLowerCase().includes("children")) {
      let temp: { [key: string]: string | number } = {
        ...quickQuoteAnswers,
        [label]: answer,
      };
      let childrenList = Object.keys(quickQuoteAnswers)
        .filter((item) => item.includes("son"))
        .slice(parseInt(answer));
      childrenList.forEach((element: string) => {
        delete temp[element];
      });
      setQuickQuoteAnswers({ ...temp });
    } else {
      setQuickQuoteAnswers({ ...quickQuoteAnswers, [label]: answer });
    }
  };
  return (
    <div>
      <TitleBar title={"Quick Quote"} />
      {/* <Space s={20} /> */}
      <QuickQuoteForm
        quickQuoteAnswers={quickQuoteAnswers}
        handleFormInputs={handleFormInputs}
        handleSubmitParams={handleSubmitParams}
      />
      {/* <Space s={20} /> */}
      {submitParams ? (
        <RecommendationSection
          quickQuoteAnswers={quickQuoteAnswers}
          submitParams={submitParams}
          mutateChanges={false}
          handleModal={() => {}}
        />
      ) : (
        <EmptyState />
      )}
    </div>
  );
};

export default QuickQuote;
