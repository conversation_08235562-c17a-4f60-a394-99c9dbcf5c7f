import React, { useEffect, useState } from "react";
import { Tabs, CardSection } from ".";
import { PlanTypes, QuickQuoteData, UserFormResponse } from "../types";
// import { Space } from '@/components/Space';
import { useGetQuickQuote } from "../api/getQuickQuote";
import LoadingState from "./LoadingState";
import LeadModal from "@/components/LeadModal";
import Container from "@/components/globals/Container";

interface Props {
  quickQuoteAnswers: any;
  submitParams: {
    user_response: UserFormResponse;
    sumInsured: string;
  };
  mutateChanges: boolean;
  handleModal: (value: boolean) => void;
}
export const RecommendationSection = ({
  quickQuoteAnswers,
  submitParams,
  handleModal,
  mutateChanges,
}: Props) => {
  const [activeTab, setActiveTab] = useState("vanilla-health");
  const [cover, setCover] = useState("");
  const handleChange = (value: string) => {
    setActiveTab((prev) => value);
  };

  const [leadModal, setLeadModal] = useState(mutateChanges);

  const { data: quick_quote_data, isLoading } = useGetQuickQuote(
    submitParams.user_response,
    "health-recommendations",
    submitParams.sumInsured,
    !!leadModal
  );

  // console.log(mutateChanges, "ppppiiiippp", leadModal);

  useEffect(() => {
    setLeadModal(true);
  }, [mutateChanges]);

  useEffect(() => {
    setCover(quickQuoteAnswers["Your coverage Amount"]);
  }, [quickQuoteAnswers]);

  if (isLoading) {
    return <LoadingState />;
  }
  if (!quick_quote_data) {
    return <LoadingState />;
  }

  return (
    <Container>
      <main className="m-auto my-6 md:my-10 md:mb-[100px] mx-5 lg:mx-0">
        <Tabs
          activeTab={activeTab}
          handleChange={handleChange}
          tabs={quick_quote_data?.data.theme}
        />
        <CardSection
          data={
            quick_quote_data?.data.variant_recommendations?.[
              activeTab as PlanTypes
            ]
          }
          activeTab={activeTab}
          sumInsured={cover}
        />
        {mutateChanges && <LeadModal open={true} handleModal={handleModal} />}
      </main>
    </Container>
  );
};
