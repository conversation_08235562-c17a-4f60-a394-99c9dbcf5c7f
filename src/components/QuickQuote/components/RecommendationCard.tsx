import Image from "next/image";
import { PlanData } from "../types";
import { StarIcon } from "@heroicons/react/24/solid";
// import { Space } from '@/components/Space';

interface Props {
  planData: PlanData;
  activeTab: string;
  sumInsured: string;
}
export const RecommendationCard = ({
  planData,
  activeTab,
  sumInsured,
}: Props) => {
  const shortenSI = (si: any) => {
    if (si >= 10000000) {
      return (si / 10000000).toFixed(si % 10000000 === 0 ? 0 : 1) + " Cr";
    } else if (si >= 100000) {
      return (si / 100000).toFixed(si % 100000 === 0 ? 0 : 1) + " L";
    } else {
      return si + " L";
    }
  };

  let premium;
  if (sumInsured === planData.selected_sum_insured.sum_insured) {
    premium = planData.selected_premium;
  } else {
    const filterData = planData.si_premium_options.filter(
      (item) => item.sum_insured === sumInsured
    );
    if (filterData.length > 0) {
      premium = filterData[0].premium;
    } else {
      return <></>;
    }
  }
  return (
    <div className="relative w-full m-auto rounded-2xl shadow-300 min-h-[250px] p-4 max-w-sm">
      <img
        src={
          activeTab == "vanilla-health"
            ? "https://d1wamg5uas0pit.cloudfront.net/quick-quote/purpleCurve.svg"
            : "https://d1wamg5uas0pit.cloudfront.net/quick-quote/Blue_curve.svg"
        }
        alt="curevSvg"
        className={"w-full absolute z-0 object-contain bottom-0 left-0"}
      />
      <div className="flex items-center justify-between">
        <div className=" p-1">
          <img src={planData.icon} alt="" className="w-12 rounded-md" />
        </div>
        <div className="flex gap-1 items-center justify-center">
          <StarIcon className="w-5 h-5 text-orange600" />
          <span className="text-primary500 text-sm font-normal">
            {planData.total_rating.toFixed(1)}
          </span>
        </div>
      </div>
      <div className="text-gray900 font-semibold text-base">
        {planData.variant_name}
      </div>
      <div className="flex gap-1 items-center text-slateGrey text-sm">
        <img
          src={`https://d1wamg5uas0pit.cloudfront.net/recommendation/network_hospital.svg`}
          alt=""
          className="w-4 h-4"
        />
        {planData.network_hospitals} Network Hospitals
      </div>
      {/* <Space s={30} /> */}
      <div className="flex z-10 relative justify-between items-center">
        <div className="flex flex-col">
          <span className="text-gray600 text-xs">Sum Insured</span>
          <span className="text-xl text-gray900 font-semibold">
            ₹{shortenSI(sumInsured)}
          </span>
        </div>
        {planData?.selected_sum_insured?.deductible ? (
          <div className="flex flex-col">
            <span className="text-gray600 text-xs">Deductible</span>
            <span className="text-xl text-gray900 font-semibold">
              ₹{shortenSI(planData?.selected_sum_insured?.deductible)}
            </span>
          </div>
        ) : (
          <></>
        )}
        <div className="flex flex-col text-end">
          <span className="text-gray600 text-xs">Premium</span>
          <span className="text-xl text-gray900 font-semibold">₹{premium}</span>
          <span className="text-gray600 text-xs">Inclusive of all taxes</span>
        </div>
      </div>
    </div>
  );
};
