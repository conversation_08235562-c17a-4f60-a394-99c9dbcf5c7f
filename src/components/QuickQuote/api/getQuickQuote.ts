// import { AxiosResponse } from "axios";
// import { getRequest } from '@/scripts/requests';
import { useQuery } from "@tanstack/react-query";
import { ExtractFnReturnType } from "@/lib/react-query";
import { QuickQuoteResponse, UserFormResponse } from "../types";

export const getQuickQuote = async (
  user_response: UserFormResponse,
  recommendation_tags: string,
  sum_insured: string
): Promise<QuickQuoteResponse> => {
  const url = `${
    process.env.NEXT_PUBLIC_BROKER_URL
  }/quick_recommendations/v1/?user_response=${JSON.stringify(
    user_response
  )}&recommendation_tags=${recommendation_tags}&sum_insured=${sum_insured}`;

  const response = await fetch(url, {
    method: "GET", // Specify the HTTP method (default is GET)
    headers: {
      "Content-Type": "application/json", // Set content type for JSON data
    },
  });

  // Check for successful response
  if (!response.ok) {
    throw new Error(`API request failed with status: ${response.status}`);
  }

  // Parse the JSON response
  const data = await response.json();

  return data as QuickQuoteResponse;
};

type QueryFnType = typeof getQuickQuote;

export const useGetQuickQuote = (
  user_response: UserFormResponse,
  recommendation_tags: string,
  sum_insured: string,
  enabled: boolean
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    enabled: enabled,
    queryKey: [
      "quick_quote",
      sum_insured,
      recommendation_tags,
      user_response.pincode,
      user_response.members?.self,
      user_response.members?.spouse,
      user_response.members?.son1,
      user_response.members?.son2,
      user_response.members?.son3,
      user_response.members?.son4,
    ],
    queryFn: () =>
      getQuickQuote(user_response, recommendation_tags, sum_insured),
  });
};
