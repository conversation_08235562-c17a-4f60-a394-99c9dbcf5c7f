import Hero from "./components/Hero";
import Goal from "./components/Goal";
import Impact from "./components/Impact";
import Journey from "./components/Journey";
import Founder from "./components/Founder";
import Investor from "./components/Investor";
import Team from "./components/Team";

export default function AboutNew({ data, team }: { data: any; team: any }) {
  return (
    <>
      <div className="bg-white">
        <div className="px-4">
          <Hero
            title={data.data.attributes.hero.title}
            subtitle={data.data.attributes.hero.subtitle}
          />
          <Goal
            title={data.data.attributes.goal.title}
            desc={data.data.attributes.goal.desc}
          />
          <Impact
            title={data.data.attributes.impact[0].title}
            cardData={data.data.attributes.impact[0].impactItems}
          />
          <Journey
            title={data.data.attributes.journey.title}
            mobile={data.data.attributes.journey.mobile.data.attributes.url}
            web={data.data.attributes.journey.web.data.attributes.url}
          />
        </div>
        <Founder
          title={data.data.attributes.founder.title}
          desc={data.data.attributes.founder.desc}
          name={data.data.attributes.founder.name}
          designation={data.data.attributes.founder.designation}
          company={data.data.attributes.founder.company}
          image={data.data.attributes.founder.image.data.attributes.url}
        />
        <div className="px-4">
          <Investor
            title={data.data.attributes.investors.title}
            subtitle={data.data.attributes.investors.subtitle}
            investors={data.data.attributes.investors.investorsItems}
          />
        </div>
        <Team
          title={data.data.attributes.team.title}
          subtitle={data.data.attributes.team.subtitle}
          team={team}
        />
      </div>
    </>
  );
}
