import React from "react";
import IconTiles from "@/components/globals/IconTiles";

const Impact = (props: { title: string; cardData: any }) => {
  return (
    <section className="w-full flex flex-col items-center py-5 max-w-[1240px] mx-auto">
      <h2 className="text-3xl md:text-4xl font-bold md:mb-12 mb-5 text-center bg-gradient-to-r from-[#2ca6c9] to-[#55b5aa] bg-clip-text text-transparent">
        {props.title}
      </h2>
      <IconTiles cardData={props.cardData} />
    </section>
  );
};

export default Impact;
