import React from "react";

const Investor = (props: {
  title: string;
  subtitle: string;
  investors: any;
}) => {
  // console.log("props.investors", props.investors);
  return (
    <div className="py-12 bg-white max-w-[1240px] mx-auto px-4">
      <h2 className="text-center text-[#2CB3C4] font-semibold text-3xl md:text-4xl leading-tight m-0 tracking-tight">
        {props.title}
      </h2>
      <p className="text-center text-base md:text-lg text-[#222] mt-4 mb-5 font-normal">
        {props.subtitle}
      </p>
      <div className="border-2 border-[#2CB3C4] rounded-3xl py-8 bg-white">
        <div className="grid grid-cols-3 md:grid-cols-3 gap-8 px-4">
          {props.investors.map((inv: any, idx: number) => (
            <div
              key={inv.name}
              className="flex flex-col items-center w-full relative group"
            >
              <img
                src={inv.image.data.attributes.url}
                alt={inv.name}
                className={`w-full h-[75px] object-contain rounded-lg${
                  idx === 0 ? "bg-[#0A2840]" : ""
                }`}
              />
              <div className="absolute -bottom-8 left-1/2 -translate-x-1/2 z-10 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none bg-[#222] text-white text-xs px-3 py-1 rounded shadow-lg whitespace-nowrap">
                {inv.name}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Investor;
