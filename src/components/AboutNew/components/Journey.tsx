import React from "react";

const Journey = (props: { title: string; mobile: string; web: string }) => {
  return (
    <div className="w-full pt-10 bg-white text-center max-w-[1240px] mx-auto">
      <h2 className="text-[#2ca6c9] text-4xl font-semibold mb-10 tracking-wide">
        {props.title}
      </h2>
      <div className="flex justify-center">
        <img
          src={props.web}
          alt="Journey timeline placeholder"
          className="hidden md:block h-44 w-auto"
        />
        <img
          src={props.mobile}
          alt="Journey timeline placeholder"
          className="block md:hidden h-auto w-auto"
        />
      </div>
    </div>
  );
};

export default Journey;
