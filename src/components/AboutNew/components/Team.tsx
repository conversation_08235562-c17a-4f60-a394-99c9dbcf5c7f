"use client";
import React, { useEffect, useRef, useState } from "react";
import parse from "html-react-parser";

const AUTO_SCROLL_INTERVAL = 8; // ~120fps
const AUTO_SCROLL_STEP = 2; // px per interval
const AUTO_SCROLL_PAUSE = 5; // ms to pause after user scroll (increased for better UX)

const Team = (props: { title: string; subtitle: string; team: any }) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isAutoScroll, setIsAutoScroll] = useState(true);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const pauseTimeout = useRef<NodeJS.Timeout | null>(null);
  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);

  // Duplicate team list for infinite scroll effect
  const teamList = [...props.team, ...props.team];

  useEffect(() => {
    const scrollContainer = scrollRef.current;
    if (!isAutoScroll || !scrollContainer || isUserScrolling) return;

    const singleListWidth = scrollContainer.scrollWidth / 2;

    const interval = setInterval(() => {
      if (scrollContainer.scrollLeft >= singleListWidth) {
        scrollContainer.scrollLeft = 0;
      } else {
        scrollContainer.scrollLeft += AUTO_SCROLL_STEP;
      }
    }, AUTO_SCROLL_INTERVAL);

    return () => clearInterval(interval);
  }, [isAutoScroll, isUserScrolling, props.team.length]);

  // Pause auto-scroll on user scroll
  const handleUserScroll = () => {
    if (scrollTimeout.current) clearTimeout(scrollTimeout.current);

    setIsUserScrolling(true);
    setIsAutoScroll(false);

    if (pauseTimeout.current) clearTimeout(pauseTimeout.current);
    pauseTimeout.current = setTimeout(() => {
      setIsAutoScroll(true);
      setIsUserScrolling(false);
    }, AUTO_SCROLL_PAUSE);

    // If user scrolls past the first list, reset to start
    const scrollContainer = scrollRef.current;
    if (scrollContainer) {
      const singleListWidth = scrollContainer.scrollWidth / 2;
      if (scrollContainer.scrollLeft >= singleListWidth) {
        scrollContainer.scrollLeft = 0;
      }
    }
  };

  // Pause on hover
  const handleMouseEnter = () => setIsAutoScroll(false);
  const handleMouseLeave = () => {
    if (!isUserScrolling) {
      setIsAutoScroll(true);
    }
  };

  // Handle touch events for mobile
  const handleTouchStart = () => {
    setIsAutoScroll(false);
    setIsUserScrolling(true);
  };

  const handleTouchEnd = () => {
    if (scrollTimeout.current) clearTimeout(scrollTimeout.current);
    scrollTimeout.current = setTimeout(() => {
      setIsUserScrolling(false);
      setIsAutoScroll(true);
    }, AUTO_SCROLL_PAUSE);
  };

  return (
    <div className="w-full flex flex-col items-center mb-16">
      <div className="px-2 md:px-0">
        <h2 className="text-4xl font-semibold text-[#38A6B0] mb-2 text-center">
          {props.title}
        </h2>
        <div className="text-lg font-medium text-center mt-2 mb-1">
          {parse(props.subtitle)}
        </div>
      </div>
      <div
        ref={scrollRef}
        className="flex flex-row gap-8 sm:gap-10 md:gap-14 overflow-x-auto w-full py-6 px-2 md:px-0 hide-scrollbar"
        style={{
          scrollBehavior: "auto",
          minWidth: 0,
          scrollbarWidth: "none", // Firefox
          msOverflowStyle: "none", // IE/Edge
        }}
        onScroll={handleUserScroll}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
      >
        {teamList.map((member: any, idx: number) => (
          <div
            key={idx}
            className="flex flex-col items-center min-w-[180px] md:min-w-[220px] lg:min-w-[250px] flex-shrink-0"
          >
            <div
              className={`w-[140px] h-[140px] md:w-[170px] md:h-[170px] lg:w-[200px] lg:h-[200px] rounded-full overflow-hidden border-4 border-white shadow-md flex items-center justify-center bg-gray-100`}
            >
              <img
                src={member.profile.image_original}
                alt={`Team member ${idx + 1}`}
                className="object-cover w-full h-full"
                loading="lazy"
              />
            </div>
            <div className="mt-5 text-center">
              <div className="text-xl md:text-2xl font-bold text-black">
                {member.real_name}
              </div>
              <div className="text-base md:text-lg font-medium text-[#6C47FF] mt-1">
                {member.profile.title}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Custom scrollbar styles for webkit browsers */}
      <style jsx>{`
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </div>
  );
};

export default Team;
