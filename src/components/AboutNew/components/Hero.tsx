import React from "react";
import parse from "html-react-parser";

const Hero = (props: { title: string; subtitle: string }) => {
  // Split title into words and separate first 5 words from the rest
  const words = props.title.split(" ");
  const firstFiveWords = words.slice(0, 5).join(" ");
  const remainingWords = words.slice(5).join(" ");

  return (
    <section className="w-full max-w-[1240px] mx-auto flex flex-col items-center justify-center pt-10 md:pt-16 pb-10 md:pb-16 bg-white relative overflow-hidden">
      <h1 className="text-3xl md:text-5xl font-bold text-center text-black mb-2">
        {firstFiveWords}
      </h1>
      {remainingWords && (
        <h1 className="text-3xl md:text-5xl font-bold text-center mb-4 bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text text-transparent">
          {remainingWords}
        </h1>
      )}
      <div className="text-base md:text-xl text-center text-black max-w-3xl mx-auto">
        {parse(props.subtitle)}
      </div>
      {/* Optional: Add subtle background circles/gradients for effect if needed */}
    </section>
  );
};

export default Hero;
