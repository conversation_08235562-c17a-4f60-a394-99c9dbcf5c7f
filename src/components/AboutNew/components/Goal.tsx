import React from "react";
import Image from "next/image";

const Goal = (props: { title: string; desc: string }) => {
  return (
    <div className="relative max-w-[1240px] mx-auto text-center md:mb-14 mb-5 md:w-full flex flex-col bg-gradient-to-r from-primary-blue-3 to-primary-green-2 rounded-xl md:rounded-2xl text-white opacity-80 overflow-hidden px-2 md:px-0">
      {/* Top right background SVG */}
      <div className="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 pointer-events-none select-none">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={570}
          height={570}
          className="hidden md:block"
        />
      </div>
      {/* Bottom left background SVG */}
      <div className="absolute bottom-24 left-0 transform -translate-x-1/2 translate-y-1/2 pointer-events-none select-none">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={524}
          height={524}
        />
      </div>
      <h2 className="md:text-3xl text-xl font-bold z-10 px-4 pt-10 pb-2">
        Our Goal and Vision
      </h2>
      <p className="md:text-xl text-base font-medium z-10 px-4 pb-10 max-w-5xl mx-auto">
        We Help Individuals Discover The Right Health And Term Insurance, With
        100% Unbiased Guidance And Full Transparency. We Simplify The Entire
        Insurance Journey—From Discovery To Claim—With Digital Ease And A Human
        Touch. We are Not Just Insurance Brokers—We are Insurance Educators,
        Advocates, And Partners.
      </p>
    </div>
  );
};

export default Goal;
