import React from "react";
import parse from "html-react-parser";

const Founder = (props: {
  title: string;
  desc: string;
  name: string;
  designation: string;
  company: string;
  image: string;
}) => {
  return (
    <div className="flex justify-center items-center min-h-screen bg-[#fafafa] mt-10 py-10">
      <div className="flex flex-col md:flex-row items-center">
        {/* Left: Founder Image Card */}
        <div className="relative rounded-xl w-full md:w-[400px] h-[300px] md:h-[450px] bg-transparent flex items-center justify-center">
          <img
            src={props.image}
            alt="Founder"
            className="object-contain h-full w-full"
          />
          {/* <div className="absolute bottom-8 left-0 w-full px-8">
            <div className="text-white text-center text-3xl font-medium drop-shadow-md">
              <PERSON><PERSON><PERSON>
            </div>
            <div className="text-white text-center text-xl font-normal drop-shadow-md">
              Founder, OneAssure
            </div>
          </div> */}
        </div>
        {/* Right: Founder Info Card */}
        <div className="bg-white rounded-2xl md:ml-[-40px] z-10 shadow-lg px-6 md:px-10 py-8 md:py-10 w-full md:w-[800px] flex flex-col justify-center mt-6 md:mt-0">
          <h2
            className="text-3xl md:text-4xl font-medium text-[#2ca6c9] text-center mb-4 md:mb-6"
            style={{ fontFamily: "Manrope, sans-serif" }}
          >
            {props.title}
          </h2>
          <div
            className="text-sm md:text-base text-gray-800 leading-relaxed"
            style={{ fontFamily: "Manrope, sans-serif" }}
          >
            {parse(props.desc)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Founder;
