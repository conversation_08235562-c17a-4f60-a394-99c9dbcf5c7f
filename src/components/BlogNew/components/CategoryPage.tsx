"use client";
import BlogCategoryPage from "@/components/BlogNew/components/blogCategoryPage";
import TalkToExpert from "@/components/Home/components/TalkToExpert";
import BlogCategory from "@/components/BlogNew/components/blogCategory";
import QuoteForm from "@/components/HealthInsurance/components/QuoteForm";
import { BlogPost } from "@/components/BlogNew/types";
import { useEffect } from "react";
import { useSessionStorage } from "usehooks-ts";
import { usePathname, useRouter } from "next/navigation";

const CategoryPage = ({
  data,
  category,
  title = "",
  currentCategory = "",
}: {
  data: BlogPost[];
  title?: string;
  category: any;
  currentCategory: string;
}) => {
  const pathname = usePathname();
  const router = useRouter();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);
  useEffect(() => {
    if (typeof window !== "undefined") {
      router.replace(
        `${pathname}?${utm_source !== null ? `utm_source=${utm_source}` : ""}${
          utm_medium !== null ? `&utm_medium=${utm_medium}` : ""
        }${utm_campaign !== null ? `&utm_campaign=${utm_campaign}` : ""}${
          utm_content !== null ? `&utm_content=${utm_content}` : ""
        }${utm_term !== null ? `&utm_term=${utm_term}` : ""}`
      );
    }
  }, []);
  return (
    <div className="flex flex-col max-w-[1240px] mx-auto mb-6 items-center">
      <BlogCategoryPage data={data} title={title} />
      {/* <BlogQuote /> */}
      <div className="mt-6 px-6 md:px-0 max-w-[1240px] mx-auto">
        <TalkToExpert />
      </div>
      <BlogCategory categories={category} />
      <div className="w-full px-6 md:px-0 mt-6">
        <QuoteForm />
      </div>
      {/* <div className="w-full mt-6"> */}
      {/* <BlogGIT /> */}
      {/* <Suspense>
          <ContactForm />
        </Suspense> */}
      {/* </div> */}
    </div>
  );
};

export default CategoryPage;
