// Reminder: Run `npm install styled-components @types/styled-components` if not already installed
import Link from "next/link";
import React from "react";
import Image from "next/image";
interface CategoryCardProps {
  icon: string;
  title: string;
  slug: string;
}

const CategoryCard: React.FC<CategoryCardProps> = ({ icon, title, slug }) => (
  <Link href={`/insurance/${slug}`}>
    <div className="flex items-center border border-gray-200 bg-white rounded-4xl shadow-md md:px-5 md:py-6 pl-2 pr-1 py-3 md:gap-3 gap-2 hover:mb-[0.5px] hover:shadow-lg transition-all duration-300 cursor-pointer">
      <div
        className={`flex items-center justify-center rounded-full p-3 md:w-16 md:h-16 w-12 h-12 relative`}
      >
        {/* Icon here */}
        <Image src={icon} alt={title} fill className="object-contain" />
      </div>
      <div className="md:text-base text-xs font-medium text-ntrl-black-1 leading-tight">
        {title}
      </div>
    </div>
  </Link>
);

export default CategoryCard;
