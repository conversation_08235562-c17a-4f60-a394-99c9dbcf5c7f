"use client";

import React from "react";
import BlogCard from "@/components/BlogNew/components/blogCard";
import { BlogPost } from "../types";

const BlogCategory = ({ data, title }: { data: BlogPost[]; title: string }) => {
  const [currentPage, setCurrentPage] = React.useState(1);
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [currentPage]);

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const itemsPerPage = isMobile ? 5 : 15;
  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = data.slice(startIndex, endIndex);

  const handlePrevious = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNext = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  return (
    <section className="flex flex-col max-w-[1240px] mx-auto bg-white pt-6">
      <div className="flex justify-center items-center mb-6 gap-4">
        <h2 className="md:text-4xl text-2xl font-semibold text-gray-900 text-center md:text-left">
          <span className="text-black">{title}</span>
        </h2>
      </div>
      <div className="flex justify-center w-full">
        <div
          className="grid grid-cols-1 md:grid-cols-3 items-stretch bg-none md:gap-12 gap-4 px-6 md:px-0 md:mt-4 w-full max-w-full"
          style={{ backfaceVisibility: "hidden" }}
        >
          {currentItems.map((blog, idx) => (
            <div key={idx} className="flex-shrink-0 w-full">
              <BlogCard
                imageUrl={blog.attributes.Thumbnail.data.attributes.url}
                tag={blog.attributes.category.data.attributes.name}
                title={blog.attributes.Title}
                date={blog.attributes.createdAt}
                author={blog.attributes.author.data.attributes.name}
                description={blog.attributes.subtitle}
                url={`/insurance/${blog.attributes.category.data.attributes.slug}/${blog.attributes.slug}`}
              />
            </div>
          ))}
          {data.length === 0 && (
            <div className="text-gray-500 text-lg mt-8">
              No blogs found for this category.
            </div>
          )}
        </div>
      </div>
      {data.length > 0 && (
        <div className="flex justify-center items-center gap-4 mt-8">
          <button
            onClick={handlePrevious}
            disabled={currentPage === 1}
            className={`px-4 py-2 rounded-full font-medium ${
              currentPage === 1
                ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                : "bg-[#4D9ED1] text-white hover:bg-[#4D9ED1]/90"
            }`}
          >
            Previous
          </button>
          <span className="text-gray-600">
            Page {currentPage} of {totalPages}
          </span>
          <button
            onClick={handleNext}
            disabled={currentPage === totalPages}
            className={`px-8 py-2 rounded-full font-medium ${
              currentPage === totalPages
                ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                : "bg-[#4D9ED1] text-white hover:bg-[#4D9ED1]/90"
            }`}
          >
            Next
          </button>
        </div>
      )}
    </section>
  );
};

export default BlogCategory;
