"use client";

import React, { useState } from "react";
import CategoryCard from "@/components/BlogNew/components/categoryCard";
import { FaClipboardList, FaVirus } from "react-icons/fa";
import { MdCompareArrows } from "react-icons/md";
import { HiOutlineDocumentText } from "react-icons/hi";

const categories = [
  {
    icon: <MdCompareArrows />,
    title: "Health Insurance Compare",
    bgColor: "bg-blue-100",
    iconColor: "text-blue-400",
  },
  {
    icon: <FaClipboardList />,
    title: "Health Insurance Checklist",
    bgColor: "bg-yellow-100",
    iconColor: "text-yellow-500",
  },
  {
    icon: <FaVirus />,
    title: "Health Insurance Disease",
    bgColor: "bg-red-100",
    iconColor: "text-red-400",
  },
  {
    icon: <HiOutlineDocumentText />,
    title: "Health Insurance Guides",
    bgColor: "bg-green-100",
    iconColor: "text-green-400",
  },
];

const BlogCategory = ({ categories }: { categories: any }) => {
  const [visibleCategories, setVisibleCategories] = useState(6);
  const initialCategories = 6;

  const handleViewMore = () => {
    setVisibleCategories((prev) => prev + 6);
  };

  const handleViewLess = () => {
    setVisibleCategories(initialCategories);
  };

  return (
    <section className="flex flex-col items-center px-6 md:px-0 max-w-[1240px] mx-auto bg-white">
      <h2 className="text-2xl font-medium text-center mb-4 md:mb-6">
        Blog Categories
      </h2>
      <div className="w-full md:border-none rounded-2xl md:h-auto">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-10 gap-y-5 md:gap-y-10 px-2 md:px-0">
          {categories
            .slice(0, visibleCategories)
            .map((cat: any, idx: number) => (
              <CategoryCard
                key={idx}
                icon={cat.attributes.icon.data.attributes.url}
                title={cat.attributes.name}
                slug={cat.attributes.slug}
              />
            ))}
          {/* Show all categories on desktop */}
          <div className="hidden md:contents">
            {categories
              .slice(visibleCategories)
              .map((cat: any, idx: number) => (
                <CategoryCard
                  key={idx + visibleCategories}
                  icon={cat.attributes.icon.data.attributes.url}
                  title={cat.attributes.name}
                  slug={cat.attributes.slug}
                />
              ))}
          </div>
        </div>
      </div>
      {categories.length > visibleCategories && (
        <div className="flex justify-center w-full mt-8 md:hidden">
          <button
            onClick={handleViewMore}
            className="flex items-center gap-2 bg-[#4D9ED1] text-white px-6 py-2 rounded-full font-medium shadow hover:bg-[#4D9ED1]/90 transition"
          >
            View More Categories <span className="text-sm">↓</span>
          </button>
        </div>
      )}
      {visibleCategories > initialCategories && (
        <div className="flex justify-center w-full mt-4 md:hidden">
          <button
            onClick={handleViewLess}
            className="flex items-center gap-2 bg-gray-200 text-gray-700 px-6 py-2 rounded-full font-medium shadow hover:bg-gray-300 transition"
          >
            View Less Categories <span className="text-sm">↑</span>
          </button>
        </div>
      )}
    </section>
  );
};

export default BlogCategory;
