"use client";
import React, { useState } from "react";

const purposes = [
  "Instagram",
  "Facebook",
  "Twitter",
  "LinkedIn",
  "Youtube",
  "Website",
  "Referral",
];

const BlogGIT = () => {
  const [form, setForm] = useState({
    name: "",
    phone: "",
    mail: "",
    purpose: "",
    message: "",
  });

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission logic here
    alert("Form submitted!");
  };

  return (
    <div className="px-2 md:px-0">
      <div className="flex flex-col md:flex-row items-center space-between w-full md:my-12 my-6 p-4 rounded-4xl border-2 border-teal-400 md:gap-8 bg-white shadow-sm">
        <h1 className="text-center w-full md:pl-6 md:w-[40%] text-2xl md:text-5xl/relaxed font-medium mb-5 md:mb-0 text-black">
          Get in Touch with Us
        </h1>
        <form
          onSubmit={handleSubmit}
          className="w-full md:px-6 md:pt-6 md:pb-2 mx-auto"
        >
          <div className="flex flex-col md:flex-row md:justify-between gap-3 mb-3 md:mb-5">
            <input
              name="name"
              value={form.name}
              onChange={handleChange}
              placeholder="Enter Name"
              className="w-full md:w-[49%] md:px-4 md:py-3 px-3 py-2 rounded-xl border border-teal-600 text-base md:text-xl font-normal text-black bg-white outline-none placeholder:text-gray-400 focus:border-teal-400"
              type="text"
              autoComplete="off"
            />
            <input
              name="phone"
              value={form.phone}
              onChange={handleChange}
              placeholder="Enter Phone No."
              className="w-full md:w-[49%] md:px-4 md:py-3 px-3 py-2 rounded-xl border border-teal-600 text-base md:text-xl font-normal text-black bg-white outline-none placeholder:text-gray-400 focus:border-teal-400"
              type="text"
              autoComplete="off"
            />
          </div>
          <div className="flex flex-col md:flex-row md:justify-between gap-3 mb-3 md:mb-5">
            <input
              name="mail"
              value={form.mail}
              onChange={handleChange}
              placeholder="Enter Mail"
              className="w-full md:w-[49%] md:px-4 md:py-3 px-3 py-2 rounded-xl border border-teal-600 text-base md:text-xl font-normal text-black bg-white outline-none placeholder:text-gray-400 focus:border-teal-400"
              type="email"
              autoComplete="off"
            />
            <select
              name="purpose"
              value={form.purpose}
              onChange={handleChange}
              className={`w-full md:w-[49%] md:px-4 md:py-3 px-3 py-2 rounded-xl border border-teal-600 text-base md:text-xl font-normal bg-white outline-none focus:border-teal-400 ${
                form.purpose ? "text-black" : "text-gray-400"
              }`}
              required
            >
              <option value="" disabled>
                How did you find us?
              </option>
              {purposes.map((p) => (
                <option key={p} value={p} className="text-black">
                  {p}
                </option>
              ))}
            </select>
          </div>
          <div className="mb-4">
            <textarea
              name="message"
              value={form.message}
              onChange={handleChange}
              placeholder="Message"
              className="w-full px-3 md:px-4 py-2 md:py-4 rounded-xl border border-teal-600 text-base md:text-xl font-normal text-black bg-white outline-none min-h-[12vh] resize-none placeholder:text-gray-400 focus:border-teal-400 font-sans"
            />
          </div>
          <div className="flex justify-center">
            <button
              type="submit"
              className="bg-black w-full md:w-auto text-white rounded-5xl px-10 md:px-16 py-2.5 md:py-3 text-base md:text-xl font-medium transition-colors duration-200 hover:bg-gray-900 focus:outline-none"
            >
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BlogGIT;
