"use client";

import useEmblaCarousel from "embla-carousel-react";
import { usePrevNextButtons } from "@/components/globals/TestimonialCarouselControls";
import { BPTestimonials } from "@/components/HealthInsurance/types";

const TestimonialCarousel = ({
  testimonials,
}: {
  testimonials: BPTestimonials[];
}) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    slidesToScroll: 1,
    containScroll: "trimSnaps",
    align: "start",
  });

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);
  return (
    <div className="flex flex-col relative items-center w-full mx-auto mt-10 mb-12 h-auto">
      <h2 className="text-3xl font-medium text-gray-900 text-center">
        What our Customers say
      </h2>
      <div className="flex w-full md:w-[1240px] items-center gap-0 md:gap-4">
        {/* Carousel Arrows */}
        <button
          className="hidden md:flex bg-white rounded-full shadow-lg p-2 border border-gray-200 hover:bg-gray-100 transition"
          onClick={onPrevButtonClick}
          disabled={prevBtnDisabled}
          aria-label="Previous"
        >
          <svg
            width="24"
            height="24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path d="M15 18l-6-6 6-6" />
          </svg>
        </button>

        <div
          className="overflow-x-scroll w-full md:w-[1240px] mx-0 md:mx-[1.5vw] md:pb-2 pb-0 scrollbar-hide md:pt-10 pt-5"
          ref={emblaRef}
        >
          <div
            className="flex items-stretch bg-none md:gap-10 gap-4 px-2 md:px-0 pb-5 md:pb-0"
            style={{ backfaceVisibility: "hidden" }}
          >
            {testimonials.map((testimony, index) => (
              <div
                className={`rounded-2xl w-full md:w-[22vw] flex-shrink-0 flex flex-col md:flex-row md:p-7 p-4 text-sm font-normal ${
                  index % 2 === 0 ? "bg-[#eaf0ff]" : "bg-[#eafaf6]"
                } text-ntrl-black-1 shadow-[0_8px_32px_0_rgba(44,62,80,0.08)] relative`}
                key={index}
              >
                {/* Card Content */}
                <div className="text-center md:my-4 mt-2 mb-4">
                  <h2 className="mb-3 text-lg font-medium text-ntrl-black-1">
                    {testimony.name}
                  </h2>
                  <p className="mb-2 font-light text-ntrl-black-1 text-center relative px-2 md:px-0 leading-relaxed text-sm">
                    <span
                      className="text-3xl text-black-1 align-top mr-1 font-serif"
                      style={{ lineHeight: "0.7" }}
                    >
                      {testimony.statement.trim()}
                    </span>
                    <span
                      className="text-3xl text-black-1 align-bottom ml-1 font-serif"
                      style={{ lineHeight: "0.7" }}
                    >
                      {""}
                    </span>
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <button
          className="hidden md:flex bg-white rounded-full shadow-lg p-2 border border-gray-200 hover:bg-gray-100 transition"
          onClick={onNextButtonClick}
          disabled={nextBtnDisabled}
          aria-label="Next"
        >
          <svg
            width="24"
            height="24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path d="M9 6l6 6-6 6" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default TestimonialCarousel;
