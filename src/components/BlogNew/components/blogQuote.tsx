"use client";

import React, { useState } from "react";
import { FaCaretDown, FaCaretUp } from "react-icons/fa";

const ageOptions = Array.from({ length: 83 }, (_, i) => 18 + i); // 18-100
const childAgeOptions = Array.from({ length: 25 }, (_, i) => 0 + i); // 0-24
const coverageOptions = {
  "₹5 Lakhs": "500000",
  "₹7.5 Lakhs": "750000",
  "₹10 Lakhs": "1000000",
  "₹15 Lakhs": "1500000",
  "₹20 Lakhs": "2000000",
  "₹25 Lakhs": "2500000",
  "₹50 Lakhs": "5000000",
  "₹75 Lakhs": "7500000",
  "₹1 Cr": "10000000",
  "₹2 Cr": "20000000",
};

const BlogQuote = () => {
  const [adults, setAdults] = useState(1);
  const [children, setChildren] = useState(0);
  const [adultAges, setAdultAges] = useState<(number | "")[]>(
    Array(1).fill("")
  );
  const [childAges, setChildAges] = useState<(number | "")[]>(
    Array(2).fill("")
  );
  const [coverage, setCoverage] = useState("");
  const [pin, setPin] = useState("");
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [mobileNumber, setMobileNumber] = useState("");
  const [step, setStep] = useState<
    "form" | "details" | "otp" | "recommendations"
  >("form");
  const [isAccordionOpen, setIsAccordionOpen] = useState(true);
  const [otp, setOtp] = useState("");

  // Handle dynamic age fields
  React.useEffect(() => {
    setAdultAges((prev) => {
      if (adults > prev.length)
        return [...prev, ...Array(adults - prev.length).fill("")];
      return prev.slice(0, adults);
    });
  }, [adults]);

  React.useEffect(() => {
    setChildAges((prev) => {
      if (children > prev.length)
        return [...prev, ...Array(children - prev.length).fill("")];
      return prev.slice(0, children);
    });
  }, [children]);

  const handleProceed = () => {
    // Validate first form fields as before
    const isFirstFormValid =
      adults > 0 &&
      adultAges.every((age) => age !== "") &&
      coverage !== "" &&
      pin !== "";
    if (isFirstFormValid) {
      setStep("details");
    }
  };

  const handleSecondFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setStep("otp");
  };

  const handleOtpSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Simulate OTP validation success
    setStep("recommendations");
    setIsAccordionOpen(false);
  };

  // Accordion toggle
  const handleAccordionToggle = () => {
    setIsAccordionOpen((prev) => !prev);
  };

  return (
    <div className="w-full md:mb-12 px-2 md:px-0 mt-6">
      {/* Accordion for Get a Quote */}
      <div className="border-2 w-full max-w-[1240px] mx-auto border-[#53B6AC] rounded-2xl px-4 md:px-8 py-4 md:my-5 bg-white flex flex-col">
        <div
          className="flex w-full justify-between items-center cursor-pointer"
          onClick={handleAccordionToggle}
        >
          <div className="text-2xl md:text-3xl items-center font-medium">
            Get a Quote
          </div>
          <span className="text-2xl">
            {isAccordionOpen ? (
              <FaCaretUp className="text-primary-blue-3" />
            ) : (
              <FaCaretDown className="text-primary-blue-3" />
            )}
          </span>
        </div>
        {isAccordionOpen && (
          <div className="w-full mt-4">
            {step === "form" && (
              <>
                <div className="flex flex-col md:flex-row w-full items-start gap-10 justify-between mt-3">
                  {/* Adults */}
                  <div className="flex flex-col w-full md:flex-1">
                    <div className="flex flex-row items-start gap-5">
                      <span className="text-base font-medium mb-1">
                        Select Adults
                      </span>
                      <span className="flex items-center gap-1 mb-1.5">
                        <button
                          className={`w-[22px] h-[22px] rounded border border-[#e0e0e0] bg-white text-sm cursor-pointer text-center leading-5 p-0 ${
                            adults <= 1 ? "opacity-50 cursor-not-allowed" : ""
                          }`}
                          onClick={() => setAdults((a) => Math.max(1, a - 1))}
                          aria-label="Decrease adults"
                          disabled={adults <= 1}
                        >
                          –
                        </button>
                        <span className="min-w-4 text-center font-medium text-sm">
                          {adults}
                        </span>
                        <button
                          className={`w-[22px] h-[22px] rounded border border-[#e0e0e0] bg-white text-sm cursor-pointer text-center leading-5 p-0 ${
                            adults >= 2 ? "opacity-50 cursor-not-allowed" : ""
                          }`}
                          onClick={() => setAdults((a) => Math.min(2, a + 1))}
                          aria-label="Increase adults"
                          disabled={adults >= 2}
                        >
                          +
                        </button>
                      </span>
                    </div>
                    <span className="text-[#b0b0b0] text-xs mb-1.5">
                      Aged 18+
                    </span>
                    <div className="grid grid-cols-2 gap-2">
                      {adultAges.map((age, idx) => (
                        <select
                          key={idx}
                          className="w-full px-4 py-2 rounded-xl border border-[#d0d0d0] text-sm mb-1.5 bg-white"
                          value={age}
                          onChange={(e) => {
                            const val =
                              e.target.value === ""
                                ? ""
                                : Number(e.target.value);
                            setAdultAges((ages) =>
                              ages.map((a, i) => (i === idx ? val : a))
                            );
                          }}
                        >
                          <option value="">Enter Age</option>
                          {ageOptions.map((opt) => (
                            <option key={opt} value={opt}>
                              {opt}
                            </option>
                          ))}
                        </select>
                      ))}
                    </div>
                  </div>
                  {/* Children */}
                  <div className="flex flex-col w-full md:flex-1">
                    <div className="flex flex-row items-start gap-5">
                      <span className="text-base font-medium mb-1">
                        Select Children
                      </span>
                      <span className="flex items-center gap-1 mb-1.5">
                        <button
                          className={`w-[22px] h-[22px] rounded border border-[#e0e0e0] bg-white text-sm cursor-pointer text-center leading-5 p-0 ${
                            children <= 0 ? "opacity-50 cursor-not-allowed" : ""
                          }`}
                          onClick={() => setChildren((c) => Math.max(0, c - 1))}
                          aria-label="Decrease children"
                          disabled={children <= 0}
                        >
                          –
                        </button>
                        <span className="min-w-4 text-center font-medium text-sm">
                          {children}
                        </span>
                        <button
                          className={`w-[22px] h-[22px] rounded border border-[#e0e0e0] bg-white text-sm cursor-pointer text-center leading-5 p-0 ${
                            children >= 4 ? "opacity-50 cursor-not-allowed" : ""
                          }`}
                          onClick={() => setChildren((c) => Math.min(4, c + 1))}
                          aria-label="Increase children"
                          disabled={children >= 4}
                        >
                          +
                        </button>
                      </span>
                    </div>
                    <span className="text-[#b0b0b0] text-xs mb-1.5">
                      Aged less than 25
                    </span>
                    <div className="grid grid-cols-2 gap-2">
                      {childAges.map((age, idx) => (
                        <select
                          key={idx}
                          className="w-full px-4 py-2 rounded-xl border border-[#d0d0d0] text-sm mb-1.5 bg-white"
                          value={age}
                          onChange={(e) => {
                            const val =
                              e.target.value === ""
                                ? ""
                                : Number(e.target.value);
                            setChildAges((ages) =>
                              ages.map((a, i) => (i === idx ? val : a))
                            );
                          }}
                        >
                          <option value="">Enter Age</option>
                          {childAgeOptions.map((opt) => (
                            <option key={opt} value={opt}>
                              {opt}
                            </option>
                          ))}
                        </select>
                      ))}
                    </div>
                  </div>
                  {/* Coverage Amount */}
                  <div className="flex flex-col w-full md:flex-1">
                    <span className="text-base font-medium mb-2 text-[#6c6c6c]">
                      Coverage Amount
                    </span>
                    <select
                      className="w-full p-2 rounded bg-[#f7f8fa] text-sm border-none"
                      value={coverage}
                      onChange={(e) => setCoverage(e.target.value)}
                    >
                      <option value="">Select Coverage Amount</option>
                      {Object.entries(coverageOptions).map(([key, value]) => (
                        <option key={key} value={value}>
                          {key}
                        </option>
                      ))}
                    </select>
                  </div>
                  {/* Pin Code */}
                  <div className="flex flex-col w-full md:flex-1">
                    <span className="text-base font-medium mb-2 text-[#6c6c6c]">
                      Postal code / Pin code
                    </span>
                    <input
                      className="w-full p-2 rounded bg-[#f7f8fa] text-sm border-none"
                      type="text"
                      placeholder="Enter Pin code"
                      value={pin}
                      onChange={(e) => setPin(e.target.value)}
                      maxLength={10}
                    />
                  </div>
                </div>
                <div className="flex justify-center w-full">
                  <button
                    onClick={handleProceed}
                    className="bg-black text-white border-none rounded-5xl py-2.5 text-[15px] font-medium w-full md:w-40 cursor-pointer mt-4"
                  >
                    Proceed
                  </button>
                </div>
              </>
            )}
            {step === "details" && (
              <form
                onSubmit={handleSecondFormSubmit}
                className="flex flex-col w-full items-start gap-5 justify-between mt-3"
              >
                <div className="flex flex-col md:flex-row w-full items-start gap-5 md:gap-10 justify-between mt-3">
                  {/* Full Name */}
                  <div className="flex flex-col w-full md:flex-1">
                    <span className="text-base font-medium mb-1 text-[#6c6c6c]">
                      Full Name
                    </span>
                    <input
                      className="w-full md:w-full border border-[#53B6AC] p-2 rounded bg-[#f7f8fa] text-sm"
                      type="text"
                      placeholder="Enter Full Name"
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      maxLength={50}
                    />
                  </div>
                  {/* Email */}
                  <div className="flex flex-col w-full md:flex-1">
                    <span className="text-base font-medium mb-1 text-[#6c6c6c]">
                      Email
                    </span>
                    <input
                      className="w-full md:w-full border border-[#53B6AC] p-2 rounded bg-[#f7f8fa] text-sm"
                      type="email"
                      placeholder="Enter Email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                  </div>
                  {/* Mobile Number */}
                  <div className="flex flex-col w-full md:flex-1">
                    <span className="text-base font-medium mb-1 text-[#6c6c6c]">
                      Mobile Number
                    </span>
                    <input
                      className="w-full md:w-full border border-[#53B6AC] p-2 rounded bg-[#f7f8fa] text-sm"
                      type="tel"
                      placeholder="Enter Mobile Number"
                      value={mobileNumber}
                      onChange={(e) => setMobileNumber(e.target.value)}
                      maxLength={10}
                    />
                  </div>
                </div>
                <button
                  type="submit"
                  className="bg-black text-white border-none rounded-5xl py-2.5 text-[15px] font-medium w-full md:w-40 cursor-pointer self-center mt-4"
                >
                  Submit
                </button>
              </form>
            )}
            {step === "otp" && (
              <form
                onSubmit={handleOtpSubmit}
                className="flex flex-col w-full items-center gap-5 justify-between mt-3"
              >
                <div className="w-full md:w-1/2 flex flex-col items-center">
                  <span className="text-xl md:text-2xl font-semibold mb-2 text-center">
                    Enter otp here <span className="text-red-600">*</span>
                  </span>
                  <input
                    className="w-full border border-[#53B6AC] p-4 rounded-2xl bg-[#f7f8fa] text-lg text-center mb-4"
                    type="text"
                    placeholder="Otp"
                    value={otp}
                    onChange={(e) => setOtp(e.target.value)}
                    maxLength={6}
                  />
                  <button
                    type="submit"
                    className="bg-black text-white border-none rounded-2xl px-12 py-2.5 text-[18px] font-semibold w-auto mt-2"
                  >
                    Submit
                  </button>
                  <div className="mt-4 text-center">
                    <span className="text-base">{"Didn't received OTP?"}</span>
                    <br />
                    <button
                      type="button"
                      className="text-[#53B6AC] font-semibold text-base mt-1 underline bg-transparent border-none cursor-pointer"
                    >
                      Resend OTP
                    </button>
                  </div>
                </div>
              </form>
            )}
          </div>
        )}
      </div>
      {/* Recommended Policies Section */}
      {step === "recommendations" && (
        <div className="border-2 w-full max-w-[1240px] mx-auto border-[#53B6AC] rounded-2xl px-4 md:px-6 py-4 my-5 bg-white flex flex-col">
          <div className="flex justify-between items-center mb-4">
            <div className="text-xl md:text-2xl font-bold">
              Recommended Policies
            </div>
            {/* <span className="text-2xl">˄</span> */}
          </div>
          {/* Example policy cards */}
          {[1, 2].map((_, idx) => (
            <div
              key={idx}
              className="border border-primary-blue-3 rounded-2xl p-4 mb-4 flex flex-col md:flex-row items-center gap-6"
            >
              <img
                src="https://oneassure.in/_next/image?url=%2Fimages%2Fniva-bupa-logo.png&w=64&q=75"
                alt="niva logo"
                className="w-20 h-10 object-contain"
              />
              <div className="flex-1">
                <div className="font-bold text-lg mb-2">
                  Reassure 2.0 {idx === 0 ? "Bronze+" : "Platinum+"}
                </div>
                <div className="flex flex-row flex-wrap gap-12 text-[#4a5568] text-base">
                  <div>
                    <div>Network Hospitals</div>
                    <div className="font-semibold text-black">0</div>
                  </div>
                  <div>
                    <div>Claim Settlement</div>
                    <div className="font-semibold text-black">100%</div>
                  </div>
                  <div>
                    <div>Sum Insured</div>
                    <div className="font-semibold text-black">₹15L</div>
                  </div>
                  <div>
                    <div>Premium</div>
                    <div className="font-semibold text-black">
                      ₹{idx === 0 ? "10868" : "11680"}
                    </div>
                    <div className="text-xs text-[#4a5568]">
                      Inclusive of all taxes
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default BlogQuote;
