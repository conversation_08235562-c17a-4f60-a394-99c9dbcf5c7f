import Image from "next/image";
import { BodyLarge } from "../../UI/Typography";
import Link from "next/link";
import { ArrowRightIcon } from "@heroicons/react/24/outline";

type BlogCardProps = {
  imageUrl?: string;
  tag?: string;
  title: string;
  date: string;
  author: string;
  description: string;
  url: string;
};

const BlogCard = ({
  imageUrl,
  tag,
  title,
  date,
  author,
  description,
  url,
}: BlogCardProps) => {
  return (
    <Link href={url}>
      <div className="bg-white rounded-2xl shadow-md overflow-hidden flex flex-col h-full cursor-pointer hover:shadow-lg transition-all duration-300">
        <div className="relative h-[15vh] w-full">
          <Image
            src={imageUrl || "/demo-image.jpg"}
            alt={title}
            fill
            className="object-cover w-full h-full"
          />
          <span className="absolute top-4 left-4 bg-[#4D9ED1] rounded shadow-md text-white font-semibold px-4 py-1 text-xs tracking-wide">
            {tag}
          </span>
        </div>
        <div className="px-5 py-3 flex flex-col flex-1 text-left">
          <div className="font-semibold text-lg mb-1 line-clamp-2 text-ntrl-black">
            {title}
          </div>
          <div className="text-gray-400 text-sm mb-2">{author}</div>
          <BodyLarge className="text-gray-700 mb-2 line-clamp-3">
            {description}
          </BodyLarge>
          <div className="flex justify-center md:justify-end mt-auto">
            <button className="bg-[#4D9ED1] hover:bg-blue-600 text-white font-medium px-5 py-2 rounded-full shadow-md flex items-center gap-2 transition-colors">
              Read More <ArrowRightIcon className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default BlogCard;
