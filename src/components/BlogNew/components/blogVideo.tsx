"use client";

import React, { useState } from "react";
const YouTube = require("react-youtube").default;

const BlogVideo: React.FC<{ data: any }> = ({ data }) => {
  const [selected, setSelected] = useState(0);
  const [isAccordionOpen, setIsAccordionOpen] = useState(false);

  const videos =
    data?.items?.filter((item: any) => item.id.kind === "youtube#video") || [];

  const mainVideo = videos[selected];

  // Responsive options for react-youtube
  const opts = {
    width: "100%",
    height: "400",
    playerVars: {
      autoplay: 0,
    },
  };

  if (!videos.length) {
    return <div></div>;
  }

  return (
    <div className="w-full max-w-[1240px] mx-auto px-2 md:px-0 pt-5 md:pt-10">
      <h1 className="text-3xl font-medium text-center mb-2">
        Insurance Videos
      </h1>
      <p className="text-center text-sm text-gray-600 mb-6">
        Turning fine print into fine videos. Come watch insurance videos get
        decoded and de-jargonized.
      </p>
      <div className="flex h-full md:flex-row gap-5 flex-col items-center md:gap-6">
        <div className="h-inherit bg-black rounded-2xl overflow-hidden shadow-md flex items-center justify-center min-h-[400px] md:min-h-[480px] w-full">
          <YouTube
            videoId={mainVideo.id.videoId}
            opts={opts}
            className="rounded-2xl w-full h-auto"
          />
        </div>
        <div className="flex-1 bg-[#18191b] rounded-2xl p-6 text-white w-full md:w-[380px] flex flex-col h-[480px]">
          <div
            className="flex justify-between w-full items-center cursor-pointer md:cursor-default"
            onClick={() => setIsAccordionOpen(!isAccordionOpen)}
          >
            <h2 className="text-xl font-semibold">More Insurance Videos</h2>
            <button className="md:hidden">
              <svg
                className={`w-6 h-6 transform transition-transform ${
                  isAccordionOpen ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
          </div>
          <div
            className={`overflow-y-auto overflow-x-hidden mt-3 flex-1 flex flex-col gap-4 ${
              !isAccordionOpen ? "hidden md:block" : "block"
            } w-full max-h-[300px] md:max-h-none`}
          >
            {videos.map((video: any, idx: number) => (
              <div
                key={video.id.videoId}
                className={`flex gap-3 rounded-lg p-2 cursor-pointer items-center transition-colors ${
                  selected === idx ? "bg-gray-900" : "hover:bg-gray-700"
                }`}
                onClick={() => setSelected(idx)}
              >
                <img
                  src={video.snippet.thumbnails.medium.url}
                  alt={video.snippet.title}
                  className="w-[100px] h-[60px] object-cover rounded-md"
                />
                <div className="flex flex-col align-center justify-center">
                  <div className="text-base font-medium mb-1 text-white line-clamp-2">
                    {video.snippet.title}
                  </div>
                  <div className="text-sm text-gray-300">
                    {video.snippet.channelTitle} |{" "}
                    {new Date(video.snippet.publishedAt).toLocaleDateString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogVideo;
