"use client";

import React, { useEffect } from "react";
import BlogCard from "@/components/BlogNew/components/blogCard";
import Image from "next/image";
import { Manrope } from "next/font/google";
import { BlogPost } from "../types";
import { useSessionStorage } from "usehooks-ts";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
const manrope = Manrope({ subsets: ["latin"] });

const BlogHero = ({ data }: { data: BlogPost[] }) => {
  const pathname = usePathname();
  const router = useRouter();
  const [activeSlide, setActiveSlide] = React.useState(0);
  const scrollContainerRef = React.useRef<HTMLDivElement>(null);
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);
  useEffect(() => {
    if (typeof window !== "undefined") {
      router.replace(
        `${pathname}?${utm_source !== null ? `utm_source=${utm_source}` : ""}${
          utm_medium !== null ? `&utm_medium=${utm_medium}` : ""
        }${utm_campaign !== null ? `&utm_campaign=${utm_campaign}` : ""}${
          utm_content !== null ? `&utm_content=${utm_content}` : ""
        }${utm_term !== null ? `&utm_term=${utm_term}` : ""}`
      );
    }
  }, []);
  return (
    <section className={`bg-white pt-6 text-center ${manrope.className}`}>
      <div className="relative">
        <h1 className="text-3xl md:text-5xl font-bold mb-4 bg-gradient-to-r to-primary-blue-3 from-primary-green-2 bg-clip-text text-transparent">
          Read. Learn. Insure.
        </h1>
        <p className="text-sm md:text-base text-gray-600 mb-8 max-w-2xl mx-auto">
          Explore helpful articles to understand different types of insurance
          and find the right coverage for you.
        </p>
      </div>

      <div className="relative text-center px-4 md:px-8 md:pt-6 pt-2 md:pb-8 pb-4 w-full max-w-[1240px] mx-auto flex flex-col bg-gradient-to-r from-primary-blue-3 to-primary-green-2 md:rounded-5xl text-transparent opacity-80 overflow-hidden">
        <div className="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2">
          <Image
            src={
              "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
            }
            alt=""
            width={570}
            height={570}
            className="hidden md:block"
          />
        </div>

        <div className="absolute bottom-24 left-0 transform -translate-x-1/2 translate-y-1/2">
          <Image
            src={
              "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
            }
            alt=""
            width={524}
            height={524}
          />
        </div>

        <h3 className="text-white text-[24px] md:text-2xl font-semibold">
          Featured Blogs
        </h3>
        <div className="relative w-full h-auto">
          <div className="flex items-center mx-2 gap-4">
            <div
              ref={scrollContainerRef}
              className="overflow-x-scroll md:pb-2 pb-0 scrollbar-hide md:pt-3 pt-2 w-full"
              onScroll={(e) => {
                const container = e.currentTarget;
                const scrollPosition = container.scrollLeft;
                const maxScroll = container.scrollWidth - container.clientWidth;

                // Calculate what percentage of the total scroll we've moved
                const scrollPercentage = scrollPosition / maxScroll;

                // Calculate which slide should be active based on the scroll percentage
                const newActiveSlide = Math.min(
                  Math.round(scrollPercentage * (data.length - 1)),
                  data.length - 1
                );

                setActiveSlide(newActiveSlide);
              }}
            >
              <div
                className="flex items-stretch bg-none md:gap-5 gap-3 px-2 md:px-0 pb-2 md:pb-0"
                style={{ backfaceVisibility: "hidden" }}
              >
                {data.map((blog, idx) => (
                  <div key={idx} className="flex-shrink-0 w-full md:w-[21vw]">
                    <BlogCard
                      imageUrl={blog.attributes.Thumbnail.data.attributes.url}
                      tag={blog.attributes.category.data.attributes.name}
                      title={blog.attributes.Title}
                      date={blog.attributes.createdAt}
                      author={blog.attributes.author.data.attributes.name}
                      description={blog.attributes.subtitle}
                      url={`/insurance/${blog.attributes.category.data.attributes.slug}/${blog.attributes.slug}`}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="flex justify-center gap-2 mt-1">
            {data.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                  index === activeSlide ? "bg-white" : "bg-black/50"
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default BlogHero;
