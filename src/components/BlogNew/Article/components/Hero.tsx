import { ArticleType } from "@/components/BlogNew/Article/types";
import dayjs from "dayjs";
import BookACallBtn from "@/components/globals/BookACall";

const Hero = ({ data }: { data: ArticleType }) => {
  return (
    <div>
      {/* category tag */}
      <div className="flex items-center mb-5">
        <div className="bg-secondary-blue-2 text-white px-2 py-1 rounded-[4px] font-semibold">
          {data.attributes.category.data.attributes.name.toUpperCase()}
        </div>
      </div>

      {/* title */}
      <h1 className="text-4xl font-bold mb-1 text-ntrl-black-1">
        {data.attributes.Title}
      </h1>

      {/* subtitle */}
      <p className="text-lg font-medium mb-2 text-ntrl-black-1">
        {data.attributes.subtitle}
      </p>

      {/* duration,author,date */}
      <div className="flex flex-col md:flex-row items-center gap-6 mt-1">
        <BookACallBtn className="mt-4 md:mt-0 bg-secondary-blue-2 hover:bg-[#4D9ED1] rounded-full shadow-md px-6 py-2 md:px-2 md:py-1 text-white md:text-lg" />
        <div className="flex items-center">
          <p className="text-base font-medium text-ntrl-black-1 border-r-2 border-ntrl-black-1 pr-2">
            {data.attributes.readingtime}
          </p>
          <p className="text-base font-medium text-ntrl-black-1 px-2 border-r-2 border-ntrl-black-1">
            {data.attributes.author.data.attributes.name}
          </p>
          <p className="text-base font-medium text-ntrl-black-1 pl-2">
            {dayjs(data.attributes.createdAt).format("MMMM DD, YYYY")}
          </p>
        </div>
      </div>
    </div>
  );
};

export default Hero;
