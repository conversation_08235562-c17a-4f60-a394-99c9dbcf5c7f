import React from "react";

const ArticleNewsLetter = () => {
  return (
    <div className="flex items-center justify-between bg-white border border-gray-200 rounded-2xl md:mt-12 mt-6 p-6 w-full">
      <h2 className="text-3xl w-full md:w-full font-medium text-black">
        Subscribe to OneAssure Newsletter
      </h2>
      <form className="flex flex-col md:flex-row w-full justify-end items-center gap-4">
        <input
          type="email"
          placeholder="Enter Email"
          className="w-full md:w-10/12 px-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-200 text-base placeholder-gray-400 bg-white"
        />
        <button
          type="submit"
          className="bg-black text-white px-6 py-2 rounded-full font-medium hover:bg-gray-900 transition-colors duration-150"
        >
          Submit
        </button>
      </form>
    </div>
  );
};

export default ArticleNewsLetter;
