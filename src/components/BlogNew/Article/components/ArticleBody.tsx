"use client";

import Container from "@/components/globals/Container";
import parse, { domToReact, DOMNode, Element } from "html-react-parser";
import Image from "next/image";

const ArticleBody = ({ desc }: { desc: string }) => {
  const replace = (domNode: DOMNode, index: number) => {
    if (domNode.type === "tag" && domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":
        case "h2":
          // Check if h2 contains a span and extract its content
          if (domNode.children && domNode.children.length > 0) {
            const spanChild = domNode.children.find(
              (child): child is Element =>
                "name" in child && child.name === "span"
            );

            if (spanChild) {
              // Create a new h2 element with the span's content
              return (
                <h2 className="text-black text-[20px] md:text-3xl font-semibold md:my-5 my-3">
                  {domToReact(spanChild.children as DOMNode[])}
                </h2>
              );
            }
          }
          attrs.className = "text-black text-3xl font-semibold md:my-5 my-3";
          break;

        case "h3":
          attrs.className = "text-black text-xl font-semibold md:my-5 my-3";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-black text-lg font-semibold md:my-5 my-3";
          break;

        case "p":
        case "span":
          attrs.className = "text-black text-lg md:my-5 mb-2";
          break;

        case "u":
        case "a":
          attrs.className = "text-blue400";
          break;

        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto max-w-[300px] md:max-w-full md:h-64 h-48 aspect-video md:my-10 my-3 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );

        case "iframe":
          attrs.className = "my-0 mx-auto w-full md:max-w-4xl";
      }
      return domNode;
    }
  };

  return (
    <div className="mb-10 md:mb-20">
      <Container>
        <div className="grid grid-cols-3 gap-10 md:mb-10 mb-5">
          {/* @ts-ignore */}
          <div className="col-span-3">{parse(desc, { replace })}</div>
        </div>
      </Container>
    </div>
  );
};

export default ArticleBody;
