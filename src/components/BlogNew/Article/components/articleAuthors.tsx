import Image from "next/image";

type Author = {
  name: string;
  role: string;
  bio: string;
  image: string;
  socialLinks: {
    linkedin?: string;
    twitter?: string;
  };
  note: string;
  socials: {
    x: string;
    linkedin: string;
    instagram: string;
  };
};

const dummyAuthors: <AUTHORS>
  {
    name: "<PERSON>",
    role: "Financial Advisor",
    bio: "<PERSON> brings 8 years of expertise in financial planning and insurance solutions, helping clients make informed decisions.",
    image:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop",
    socialLinks: {
      linkedin: "https://linkedin.com",
    },
    note: "He wears multiple hats – one as the chief-editor of Finshots, India's go-to financial newsletter, and another as a relentless advocate for financial clarity. Having penned countless articles over 5 years, he believes in making insurance and finance approachable. At Insurance, he's on a mission to guide Indians on making better decisions when it comes to insurance.",
    socials: {
      x: "#",
      linkedin: "#",
      instagram: "#",
    },
  },
];

const ArticleAuthors = () => {
  return (
    <div className="flex flex-col md:flex-row items-start gap-6 bg-[#E5EFFF] rounded-2xl shadow-sm border border-[#B3D0FF] md:mt-12 mt-6 p-6 md:p-8 max-w-[1448px] mx-auto">
      {/* Author Image with overlay */}
      <div className="relative w-40 h-40 min-w-40 rounded-xl overflow-hidden flex-shrink-0">
        <Image
          src={dummyAuthors[0].image}
          alt={dummyAuthors[0].name}
          fill
          className="object-cover"
        />
        <div className="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/70 to-transparent px-4 py-3">
          <div className="text-white font-medium text-lg leading-tight">
            {dummyAuthors[0].name}
          </div>
          <div className="text-white text-center text-xs opacity-90">
            {dummyAuthors[0].role}
          </div>
        </div>
      </div>
      {/* Right Side */}
      <div className="flex-1 flex flex-col gap-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <h2 className="text-4xl font-medium text-ntrl-black-1">
            {"Author's Note"}
          </h2>
          <div className="flex items-center gap-2">
            <span className="inline-block px-4 py-2 border border-gray-400 rounded-full bg-white font-medium text-gray-800 flex items-center gap-5 text-base shadow-sm">
              Follow For More
              <a
                href={dummyAuthors[0].socials.x}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-blue-600 transition-colors"
              >
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M17.53 6.477h2.477l-5.423 6.217 6.393 7.806h-5.03l-3.93-4.81-4.49 4.81H2.5l5.78-6.197-6.13-7.826h5.08l3.56 4.37 4.24-4.37zm-.87 13.523h2.477l-5.423-6.217 6.393-7.806h-5.03l-3.93 4.81-4.49-4.81H2.5l5.78 6.197-6.13 7.826h5.08l3.56-4.37 4.24 4.37z" />
                </svg>
              </a>
              <a
                href={dummyAuthors[0].socials.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-blue-600 transition-colors"
              >
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                </svg>
              </a>
              <a
                href={dummyAuthors[0].socials.instagram}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-pink-500 transition-colors"
              >
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 1.366.062 2.633.334 3.608 1.308.974.974 1.246 2.241 1.308 3.608.058 1.266.07 1.646.07 4.85s-.012 3.584-.07 4.85c-.062 1.366-.334 2.633-1.308 3.608-.974.974-2.241 1.246-3.608 1.308-1.266.058-1.646.07-4.85.07s-3.584-.012-4.85-.07c-1.366-.062-2.633-.334-3.608-1.308-.974-.974-1.246-2.241-1.308-3.608C2.175 15.647 2.163 15.267 2.163 12s.012-3.584.07-4.85c.062-1.366.334-2.633 1.308-3.608.974-.974 2.241-1.246 3.608-1.308C8.416 2.175 8.796 2.163 12 2.163zm0-2.163C8.741 0 8.332.013 7.052.072 5.771.131 4.659.414 3.678 1.395c-.98.98-1.263 2.092-1.322 3.373C2.013 5.668 2 6.077 2 12c0 5.923.013 6.332.072 7.612.059 1.281.342 2.393 1.322 3.373.981.981 2.093 1.264 3.374 1.323C8.332 23.987 8.741 24 12 24s3.668-.013 4.948-.072c1.281-.059 2.393-.342 3.374-1.323.98-.98 1.263-2.092 1.322-3.373.059-1.28.072-1.689.072-7.612 0-5.923-.013-6.332-.072-7.612-.059-1.281-.342-2.393-1.322-3.373-.981-.981-2.093-1.264-3.374-1.323C15.668.013 15.259 0 12 0zm0 5.838a6.162 6.162 0 1 0 0 12.324 6.162 6.162 0 0 0 0-12.324zm0 10.162a3.999 3.999 0 1 1 0-7.998 3.999 3.999 0 0 1 0 7.998zm6.406-11.845a1.44 1.44 0 1 0 0 2.88 1.44 1.44 0 0 0 0-2.88z" />
                </svg>
              </a>
            </span>
          </div>
        </div>
        <div className="text-gray-700 text-lg leading-relaxed">
          {dummyAuthors[0].note}
        </div>
      </div>
    </div>
  );
};

export default ArticleAuthors;
