import { ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";
import Image from "next/image";
import Link from "next/link";
import React from "react";

type ArticlePlansProps = {
  plans: any;
};

const ArticlePlans: React.FC<ArticlePlansProps> = ({ plans }) => {
  return (
    <div className="w-full md:my-12 my-6 flex flex-col items-center">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
        {plans.map((plan: any, idx: any) => (
          <Link
            key={idx}
            href={`/${plan.attributes.company.data.attributes.category}/${plan.attributes.company.data.attributes.slug}/${plan.attributes.slug}`}
          >
            <div
              className="flex items-center bg-white rounded-xl shadow-md px-6 py-4 min-h-[80px] w-full transition-transform hover:scale-[1.01]"
              style={{ boxShadow: "0px 4px 12px 0px #00000014" }}
            >
              <Image
                src={
                  plan.attributes.company.data.attributes.logo.data.attributes
                    .url
                }
                alt={plan.name}
                width={40}
                height={40}
                className="w-10 h-10 object-contain mr-4"
              />
              <span className="flex-1 text-lg md:text-xl font-medium text-black text-center">
                {plan.attributes.name}
              </span>

              <ArrowTopRightOnSquareIcon className="md:w-[35px] w-8 md:h-[35px] h-8 font-semibold text-primary-1" />
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default ArticlePlans;
