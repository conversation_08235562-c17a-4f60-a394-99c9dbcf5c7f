type Format = {
    name: string;
    hash: string;
    ext: string;
    mime: string;
    path: string | null;
    width: number;
    height: number;
    size: number;
    sizeInBytes: number;
    url: string;
  };
  
  type Formats = {
    thumbnail: Format;
    small: Format;
    medium: Format;
    large: Format;
  };
  
  type ThumbnailAttributes = {
    name: string;
    alternativeText: string;
    caption: string | null;
    width: number;
    height: number;
    formats: Formats;
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: string | null;
    createdAt: string;
    updatedAt: string;
  };
  
  type ThumbnailData = {
    id: number;
    attributes: ThumbnailAttributes;
  };
  
  type Thumbnail = {
    data: ThumbnailData;
  };
  
  type CategoryAttributes = {
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    name: string;
    slug: string;
  };
  
  type CategoryData = {
    id: number;
    attributes: CategoryAttributes;
  };
  
  type Category = {
    data: CategoryData;
  };
  
  type SubCategoryAttributes = {
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    name: string;
    slug: string;
  };
  
  type SubCategoryData = {
    id: number;
    attributes: SubCategoryAttributes;
  };
  
  type SubCategory = {
    data: SubCategoryData;
  };
  
  type AuthorAttributes = {
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    name: string;
  };
  
  type AuthorData = {
    id: number;
    attributes: AuthorAttributes;
  };
  
  type Author = {
    data: AuthorData;
  };
  
  export type RelatedReadAttributes = {
    Title: string;
    subtitle: string;
    createdAt: string;
    slug: string;
    author: {
      data: {
        id: number;
        attributes: {
          name: string;
        };
      };
    };
    category: {
      data: {
        id: number;
        attributes: {
          slug: string;
          name: string;
        };
      };
    };
    Thumbnail: {
      data: {
        id: number;
        attributes: {
          url: string;
        };
      };
    };
  };
  
  export type VariantCompanyAttributes = {
    slug: string;
    category: string;
    logo: {
      data: {
        id: number;
        attributes: {
          url: string;
        };
      };
    };
  };
  
  export type HealthVariantAttributes = {
    name: string;
    slug: string;
    company: {
      data: {
        id: number;
        attributes: VariantCompanyAttributes;
      };
    };
  };
  
  export type TermVariantAttributes = {
    name: string;
    slug: string;
    company: {
      data: {
        id: number;
        attributes: VariantCompanyAttributes;
      };
    };
  };
  
  type BlogAttributes = {
    Title: string;
    subtitle: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    Description: string;
    slug: string;
    readingtime: string;
    author: Author;
    Thumbnail: Thumbnail;
    category: Category;
    relatedReads: {
      data: Array<{
        id: number;
        attributes: RelatedReadAttributes;
      }>;
    };
    healthVariants: {
      data: Array<{
        id: number;
        attributes: HealthVariantAttributes;
      }>;
    };
    termVariants: {
      data: Array<{
        id: number;
        attributes: TermVariantAttributes;
      }>;
    };
    authorNote: any;
  };
  
  export type ArticleType = {
    id: number;
    attributes: BlogAttributes;
  };
  