type ThumbnailAttributes = {
    url: string;
  };
  
  type ThumbnailData = {
    id: number;
    attributes: ThumbnailAttributes;
  };
  
  type Thumbnail = {
    data: ThumbnailData;
  };
  
  type CategoryAttributes = {
    name: string;
    slug: string;
    title: string;
  };
  
  type SubCategoryAttributes = {
    name: string;
    slug: string;
  };
  
  type CategoryData = {
    id: number;
    attributes: CategoryAttributes;
  };
  
  type SubCategoryData = {
    id: number;
    attributes: SubCategoryAttributes;
  };
  
  type Category = {
    data: CategoryData;
  };
  
  type SubCategory = {
    data: SubCategoryData;
  };
  
  type AuthorAttributes = {
    name: string;
  };
  
  type AuthorData = {
    id: number;
    attributes: AuthorAttributes;
  };
  
  type Author = {
    data: AuthorData;
  };
  
  type BlogAttributes = {
    Title: string;
    subtitle: string;
    createdAt: string;
    author: Author;
    slug: string;
    Thumbnail: Thumbnail;
    category: Category;
  };
  
  export type BlogPost = {
    id: number;
    attributes: BlogAttributes;
  };
  