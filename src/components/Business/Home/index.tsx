"use client";

import { Suspense, useState } from "react";
import Hero from "./components/Hero";
import Clients from "./components/Clients";
import WhyUs from "@/components/globals/WhyUs";
import Testimonials from "@/components/globals/Testimonials";
import Benefits from "./components/Benefits";
import { BusinessLP } from "./type";
import Modal from "@/components/globals/Modal";

const BusinessHome = ({ data }: { data: BusinessLP }) => {
  const [openModal, setOpenModal] = useState(false);

  return (
    <>
      <Hero data={data.attributes.hero} setOpenModal={setOpenModal} />
      <Clients data={data.attributes.ourClients} />
      <Benefits data={data.attributes.comprehensiveBenefit} />
      <WhyUs whyUs={data.attributes.whyUs} setOpenModal={setOpenModal} />
      <Testimonials testimonials={data.attributes.testimonials.testimonial} />
      <Suspense>
        <Modal
          open={openModal}
          handleModal={() => setOpenModal(false)}
          msg="Looking for insurance purchase assistance. Thank you!"
          utmSrc="BusinessLP"
        />
      </Suspense>
    </>
  );
};

export default BusinessHome;
