import { StarIcon } from "@heroicons/react/24/solid";
import Image from "next/image";
import { THero } from "../type";
import { Dispatch, SetStateAction } from "react";
import BookACallBtn from "@/components/globals/BookACall";

const Hero = ({
  data,
  setOpenModal,
}: {
  data: THero;
  setOpenModal: Dispatch<SetStateAction<boolean>>;
}) => {
  const titleArr = data.title.split(" ");
  return (
    <div className="relative">
      <div className="w-[100%] relative md:aspect-video h-[650px]">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/Untitled_design_1_d8ffcf1c01.jpg"
          }
          fill={true}
          style={{ objectFit: "cover", objectPosition: "left top" }}
          className="hidden xl:block"
          alt={"business-hero-lg"}
        />
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/Untitled_design_1_d8ffcf1c01.jpg"
          }
          fill={true}
          style={{ objectFit: "cover" }}
          className="block xl:hidden"
          alt={"business-hero-sm"}
        />
      </div>

      <div className="p-5 md:p-10 xl:px-0 md:w-[100%] md:aspect-video absolute bottom-0 xl:top-0 h-[650px]">
        <div className="flex items-end justify-start h-full xl:mx-[calc((100vw-1240px)/2)]">
          <div className="flex flex-col md:items-start md:justify-start items-center justify-center">
            <div className="mb-6 flex flex-wrap flex-col items-center justify-center md:items-start md:justify-start">
              <h1 className="font-generalSans xl:text-[56px]/[60px] text-[32px]/[40px] text-ntrl-white text-center md:text-left font-medium">
                {titleArr.slice(0, titleArr.length - 4).join(" ")}
                <br />
                <span className="font-bold">
                  {titleArr.slice(titleArr.length - 4).join(" ")}
                </span>
              </h1>
            </div>

            <div className="xl:text-[18px]/[28px] text-[16px]/[24px] font-normal text-ntrl-white mb-10 flex flex-wrap items-center justify-center md:w-[50%] text-center md:text-left">
              {data.subtitle}
            </div>

            <BookACallBtn className="py-3 px-6 md:px-8 bg-ntrl-white text-primary-2 rounded-lg" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;

{
  /* <div className="relative">
      <div className="w-[100%] relative md:aspect-video min-h-[700px]">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/business_hero_c09d122166.jpeg"
          }
          fill={true}
          style={{ objectFit: "cover" }}
          alt={"home-hero"}
        />
      </div>

      <div
        className="p-10  md:w-[100%] md:aspect-video absolute top-0 min-h-[700px] flex items-end justify-end md:items-center md:justify-center"
        style={{
          background:
            "linear-gradient(to top, rgba(0, 0, 46, 1), rgba(0, 0, 0, 0))",
        }}
      >
        <div className="flex  items-end justify-center md:h-full md:mx-[calc((100vw-1240px)/2)]">
          <div className="flex flex-col items-center justify-center">

            <div className="mb-6 flex flex-col items-center justify-center">
              <h1 className="font-generalSans  md:text-[56px]/[60px] text-[32px]/[40px] text-ntrl-white font-medium text-center">
                {titleArr.slice(0, titleArr.length - 4).join(" ")}
              </h1>
              <h1 className="font-generalSans  md:text-[56px]/[60px] text-[32px]/[40px] text-ntrl-white font-bold text-center">
                {titleArr.slice(titleArr.length - 4).join(" ")}
              </h1>
            </div>


            <div className="md:text-[18px]/[28px] text-[16px]/[24px] font-normal text-ntrl-white mb-10 text-center">
              {data.subtitle}
            </div>


            <BookACallBtn
              onClick={() => setOpenModal(true)}
              bgColor="bg-ntrl-white text-primary-2 md:mb-20"
            />
          </div>
        </div>
      </div>
    </div> */
}
