import Image from "next/image";

const BenefitCard = ({
  title,
  desc,
  icon,
}: {
  title: string;
  desc: string;
  icon: string;
}) => {
  return (
    <div className="border border-ntrl-grey2 rounded-xl p-8">
      {/* Title */}
      <div className="flex items-center gap-4 mb-4">
        {/* icon */}
        <div className="w-8 h-8 bg-primary-2 flex items-center justify-center rounded">
          <Image src={icon} alt="about-team-member" width={20} height={20} />
        </div>
        <h3 className="text-[18px]/[24px] text-ntrl-black font-medium font-generalSans">
          {title}
        </h3>
      </div>

      {/* Description */}
      <div>
        <p className="text-[16px]/[24px] font-normal text-ntrl-grey1">{desc}</p>
      </div>
    </div>
  );
};

export default BenefitCard;
