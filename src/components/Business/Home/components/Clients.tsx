"use client";

import Image from "next/image";
import Marquee from "react-fast-marquee";
import { OurClients } from "../type";

const Clients = ({ data }: { data: OurClients }) => {
  const logos = data.client;
  return (
    <div className="mx-auto py-6">
      <div className="flex items-center justify-center font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black gap-2 my-10">
        <h2 className="font-normal">Our</h2>
        <h2 className="font-medium">Clients</h2>
      </div>

      <div className="">
        <Marquee pauseOnHover={true} speed={75}>
          {logos.map((logo, idx) => (
            <div key={idx} className="h-20 aspect-video mr-5 relative">
              <Image
                src={logo.logo.data.attributes.url}
                fill={true}
                style={{ objectFit: "contain" }}
                alt={logo.name}
                className=""
              />
            </div>
          ))}
        </Marquee>
      </div>
    </div>
  );
};

export default Clients;
