type Icon = {
  data: {
    id: number;
    attributes: {
      url: string;
    };
  };
};

type Highlight = {
  id: number;
  highlight: string;
  icon: Icon;
};

export type THero = {
  id: number;
  title: string;
  highlights: Highlight[];
};

type Reason = {
  id: number;
  title: string;
  desc: string;
  backgroundColor: string;
  thumbnail: {
    data: {
      id: number;
      attributes: {
        url: string;
      };
    };
  };
};

type WhyUs = {
  id: number;
  reason: Reason[];
};

type CoverItem = {
  id: number;
  title: string;
  description: string;
  icon: Icon;
};

export type Cover = {
  id: number;
  coverItem: CoverItem[];
};

type Attributes = {
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  hero: THero;
  whyUs: WhyUs;
  cover: Cover;
};

export type LiabilityLPResponse = {
  id: number;
  attributes: Attributes;
};
