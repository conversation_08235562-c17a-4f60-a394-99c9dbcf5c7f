import Image from "next/image";

import Container from "@/components/globals/Container";
import { Cover } from "../type";

const WhatIsCovered = ({ data }: { data: Cover }) => {
  return (
    <div className="bg-secondary-2 md:py-20 py-10">
      <Container>
        <div>
          {/* Heading */}
          <div className="md:mb-12 mb-6">
            <div className="font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black flex md:flex-row flex-col md:items-center items-start gap-2">
              <h2 className="font-normal">What is Covered Under</h2>
              <h2 className="font-medium">Liability Insurance?</h2>
            </div>
          </div>

          {/* Reasons */}
          <div className="grid md:grid-cols-3 grid-cols-1 gap-x-[30px] md:gap-y-12 gap-y-6">
            {data.coverItem.map((e, idx) => (
              <div key={idx}>
                <div className="flex items-center gap-4 mb-4 md:mb-6">
                  {/* icon */}
                  <div className="w-8 h-8 rounded bg-secondary-1 flex items-center justify-center">
                    <Image
                      src={e.icon.data.attributes.url}
                      width={20}
                      height={20}
                      alt="liability-hero"
                    />
                  </div>
                  {/* heading */}
                  <h3 className="font-generalSans font-medium text-[20px]/[28px] md:text-[24px]/[30px] text-ntrl-black">
                    {e.title}
                  </h3>
                </div>

                <p className="text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal">
                  {e.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default WhatIsCovered;
