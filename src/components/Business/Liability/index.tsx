"use client";

import Hero from "./components/Hero";
import WhyUs from "@/components/globals/WhyUs";
import WhatIsCovered from "./components/WhatIsCovered";
import { LiabilityLPResponse } from "./type";
import { Suspense, useState } from "react";
import Modal from "@/components/globals/Modal";

const Liability = ({ data }: { data: LiabilityLPResponse }) => {
  const [openModal, setOpenModal] = useState(false);
  return (
    <>
      <Hero data={data.attributes.hero} setOpenModal={setOpenModal} />
      <WhyUs whyUs={data.attributes.whyUs} setOpenModal={setOpenModal} />
      <WhatIsCovered data={data.attributes.cover} />
      <Suspense>
        <Modal
          open={openModal}
          handleModal={() => setOpenModal(false)}
          msg="Looking for insurance purchase assistance. Thank you!"
          utmSrc="Liability_Insurance"
        />
      </Suspense>
    </>
  );
};

export default Liability;
