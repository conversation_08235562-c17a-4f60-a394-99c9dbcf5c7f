import Image from "next/image";

const DashboardCard = ({
  title,
  desc,
  icon,
}: {
  title: string;
  desc: string;
  icon: string;
}) => {
  return (
    <div className="">
      {/* Title */}
      <div className="flex items-center gap-4 mb-4">
        {/* icon */}
        <div className="w-8 h-8 bg-secondary-1 flex items-center justify-center rounded">
          <Image src={icon} alt="about-team-member" width={20} height={20} />
        </div>
        <h3 className="text-[18px]/[24px] text-ntrl-black font-medium font-generalSans">
          {title}
        </h3>
      </div>

      {/* Description */}
      <div>
        <p className="text-[16px]/[24px] font-normal text-ntrl-grey1 text-justify">
          {desc}
        </p>
      </div>
    </div>
  );
};

export default DashboardCard;
