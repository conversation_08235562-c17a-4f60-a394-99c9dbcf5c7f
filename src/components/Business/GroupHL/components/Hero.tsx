import Container from "@/components/globals/Container";
import { THero } from "../type";
import BookACallBtn from "@/components/globals/BookACall";
import { Dispatch, SetStateAction } from "react";
import Image from "next/image";

const Hero = ({
  data,
  setOpenModal,
}: {
  data: THero;
  setOpenModal: Dispatch<SetStateAction<boolean>>;
}) => {
  const titleArr = data.title.split(" ");
  return (
    <div className="md:mb-20">
      {/* Mobile screen blue card */}
      <div className="py-10 px-5 bg-primary-1 inline-block w-[100%] md:hidden">
        {/* Heading and subtitle */}
        <div className="mb-6">
          <div className="font-generalSans text-[32px]/[40px] text-ntrl-white mb-6 text-center">
            <h2 className="font-normal">
              {titleArr.slice(0, titleArr.length - 2).join(" ")}
            </h2>
            <h2 className="font-medium">
              {titleArr.slice(titleArr.length - 2).join(" ")}
            </h2>
          </div>
        </div>

        {/* Get quote button */}
        <div className="flex items-center justify-center">
          <BookACallBtn className="py-3 px-6 md:px-8 bg-primary-1 text-ntrl-white rounded-lg" />
        </div>

        {/* Divider */}
        <div className="border border-primary-3 opacity-50 my-8"></div>

        {/* claim percentage */}
        <div className="flex flex-col gap-6">
          {data.highlight.map((h, idx) => (
            <div className="flex items-center" key={idx}>
              <div className="w-10 h-10 rounded-full bg-green-1 mr-4 flex items-center justify-center">
                <Image
                  src={h.icon.data.attributes.url}
                  width={20}
                  height={20}
                  alt="liability-hero"
                />
              </div>
              <h5 className="font-[24px]/[30px] font-medium text-ntrl-white">
                {h.highlight}
              </h5>
            </div>
          ))}
        </div>
      </div>

      {/* Background image here */}
      <div className="w-[100%] h-[500px] aspect-video bg-primary-3 relative">
        <Image
          src="https://cdn.oasr.in/oa-site/cms-uploads/media/group_4e0376bc17.jpeg"
          fill={true}
          style={{ objectFit: "cover", objectPosition: "left top" }}
          alt="group-hero"
        />
      </div>
      <Container>
        <div className="p-12 bg-primary-1  rounded-2xl w-[50%] -mt-[429px] hidden md:inline-block">
          {/* Heading and subtitle */}
          <div className="mb-6">
            <h1 className="font-generalSans text-[48px]/[58px] text-ntrl-white mb-6 font-normal">
              {titleArr.slice(0, titleArr.length - 2).join(" ")}
              <br />
              <span className="font-medium">
                {titleArr.slice(titleArr.length - 2).join(" ")}
              </span>
            </h1>

            <p className="text-[18px]/[28px] text-ntrl-white">
              At OneAssure, we simplify decision making while buying an
              insurance polilcy. Consult our certified advisors for 100%
              unbiased recommendations.
            </p>
          </div>

          {/* Get quote button */}
          <BookACallBtn className="py-3 px-6 md:px-8 bg-ntrl-white text-primary-2 rounded-lg" />

          {/* Divider */}
          <div className="border border-primary-3 opacity-50 my-8"></div>

          {/* claim percentage */}
          <div className="flex flex-col gap-6">
            {data.highlight.map((h, idx) => (
              <div className="flex items-center" key={idx}>
                <div className="w-10 h-10 rounded-full bg-green-1 mr-4 flex items-center justify-center">
                  <Image
                    src={h.icon.data.attributes.url}
                    width={20}
                    height={20}
                    alt="liability-hero"
                  />
                </div>
                <h5 className="font-[24px]/[30px] font-medium text-ntrl-white">
                  {h.highlight}
                </h5>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default Hero;
