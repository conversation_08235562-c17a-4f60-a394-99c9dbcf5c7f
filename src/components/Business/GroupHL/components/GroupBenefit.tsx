"use client";

import Container from "@/components/globals/Container";
import { useState } from "react";
import BenefitCard from "../../Home/components/BenefitCard";
import { GroupBenefit as GrpBenefit } from "../type";

const GroupBenefit = ({ data }: { data: GrpBenefit }) => {
  const [selectedTabIdx, setSelectedTabIdx] = useState(0);

  const employeeArr = data.benefitItem.filter((b) => b.tag === "employees");
  const employerArr = data.benefitItem.filter((b) => b.tag === "employers");

  return (
    <div className="pt-20 pb-10">
      <Container>
        <div>
          {/* Heading */}
          <div className="mb-12">
            <div className="flex flex-col md:flex-row items-center justify-center font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black gap-2">
              <h2 className="font-normal">Benefits of Group</h2>
              <h2 className="font-medium">Health Insurance</h2>
            </div>
          </div>

          {/* Tabs */}
          <div className="flex items-center justify-center mb-12">
            <div className="inline-flex items-center justify-center gap-8 border-b border-ntrl-grey1 ">
              <div
                className="cursor-pointer"
                onClick={() => setSelectedTabIdx(0)}
              >
                <p
                  className={`font-generalSans font-medium text-[12px]/[18px] md:text-[24px]/[30px] pb-5 ${
                    selectedTabIdx === 0
                      ? "text-secondary-1 border-b-2 border-secondary-1"
                      : "text-ntrl-grey1 "
                  }`}
                >
                  Employee Benefits
                </p>
              </div>
              <div
                className="cursor-pointer"
                onClick={() => setSelectedTabIdx(1)}
              >
                <p
                  className={`font-generalSans font-medium text-[12px]/[18px] md:text-[24px]/[30px] text-ntrl-grey1 pb-5 ${
                    selectedTabIdx === 1
                      ? "text-secondary-1 border-b-2 border-secondary-1"
                      : "text-ntrl-grey1 "
                  }`}
                >
                  Employer Benefits
                </p>
              </div>
            </div>
          </div>

          {/* Employee */}
          <div
            className={` grid-cols-1 md:grid-cols-2 gap-[30px] ${
              selectedTabIdx === 0 ? "grid" : "hidden"
            }`}
          >
            {employeeArr.map((e, idx) => (
              <BenefitCard
                title={e.title}
                desc={e.description}
                icon={e.icon.data.attributes.url}
                key={idx}
              />
            ))}
          </div>

          {/* Employer */}
          <div
            className={` grid-cols-1 md:grid-cols-2 gap-[30px] ${
              selectedTabIdx === 1 ? "grid" : "hidden"
            }`}
          >
            {employerArr.map((e, idx) => (
              <BenefitCard
                title={e.title}
                desc={e.description}
                icon={e.icon.data.attributes.url}
                key={idx}
              />
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default GroupBenefit;
