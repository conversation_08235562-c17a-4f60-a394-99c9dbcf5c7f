type Highlight = {
  id: number;
  highlight: string;
  icon: Icon;
};

export type THero = {
  id: number;
  title: string;
  highlight: Highlight[];
};

type Icon = {
  data: {
    id: number;
    attributes: {
      url: string;
    };
  };
};

type Feature = {
  id: number;
  tag: string;
  title: string;
  description: string;
  icon: Icon;
};

export type DigitalDashboard = {
  id: number;
  features: Feature[];
};

type Testimonial = {
  id: number;
  name: string;
  statement: string;
  backgroundColor: string;
  thumbnail: {
    data: {
      id: number;
      attributes: {
        url: string;
      };
    };
  };
};

type Testimonials = {
  id: number;
  testimonial: Testimonial[];
};

type FAQ = {
  id: number;
  question: string;
  ans: string;
};

type FAQs = {
  id: number;
  faq: FAQ[];
};

type BenefitItem = {
  id: number;
  tag: string;
  title: string;
  description: string;
  icon: Icon;
};

export type GroupBenefit = {
  id: number;
  benefitItem: BenefitItem[];
};

type Reason = {
  id: number;
  title: string;
  desc: string;
  backgroundColor: string;
  thumbnail: {
    data: {
      id: number;
      attributes: {
        url: string;
      };
    };
  };
};

type WhyUs = {
  id: number;
  reason: Reason[];
};

type Attributes = {
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  hero: THero;
  digitalDashboard: DigitalDashboard;
  testimonials: Testimonials;
  faqs: FAQs;
  grpBenefit: GroupBenefit;
  whyUs: WhyUs;
};

export type GroupLPResponse = {
  id: number;
  attributes: Attributes;
};
