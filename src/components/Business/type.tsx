export type THero = {
  id: number;
  title: string;
  subtitle: string;
};

type Logo = {
  id: number;
  attributes: {
    url: string;
  };
};

type Client = {
  id: number;
  name: string;
  logo: {
    data: Logo;
  };
};

export type OurClients = {
  id: number;
  client: Client[];
};

type Icon = {
  data: {
    id: number;
    attributes: {
      url: string;
    };
  };
};

type Benefit = {
  id: number;
  tag: string;
  title: string;
  description: string;
  icon: Icon;
};

export type ComprehensiveBenefit = {
  id: number;
  benefits: Benefit[];
};

type Reason = {
  id: number;
  title: string;
  desc: string;
  backgroundColor: string;
  thumbnail: {
    data: {
      id: number;
      attributes: {
        url: string;
      };
    };
  };
};

type WhyUs = {
  id: number;
  reason: Reason[];
};

type Testimonial = {
  id: number;
  name: string;
  statement: string;
  backgroundColor: string;
  thumbnail: {
    data: {
      id: number;
      attributes: {
        url: string;
      };
    };
  };
};

type Testimonials = {
  id: number;
  testimonial: Testimonial[];
};

type Attributes = {
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  hero: THero;
  ourClients: OurClients;
  comprehensiveBenefit: ComprehensiveBenefit;
  whyUs: WhyUs;
  testimonials: Testimonials;
};

export type BusinessLP = {
  id: number;
  attributes: Attributes;
};
