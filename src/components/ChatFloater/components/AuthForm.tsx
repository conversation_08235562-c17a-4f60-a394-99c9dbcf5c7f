"use client";

import { useState, useRef, useEffect } from "react";
import { useOtp } from "@/components/MarketingLandingPage/api/postOtp";
import { useVerifyOtp } from "@/components/MarketingLandingPage/api/postVerifyOtp";
import { useResendOtp } from "@/components/MarketingLandingPage/api/postResendOtp";
import {
  UserIcon,
  PhoneIcon,
  ShieldCheckIcon,
  ArrowPathIcon,
  ArrowRightIcon,
} from "@heroicons/react/24/outline";
import { generateRandomString } from "../utils/generateRandom";

interface AuthFormProps {
  onAuthSuccess: () => void;
}

const AuthForm = ({ onAuthSuccess }: AuthFormProps) => {
  const [step, setStep] = useState<"details" | "otp">("details");
  const [name, setName] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [otp, setOtp] = useState(["", "", "", ""]);
  const [error, setError] = useState("");
  const [countdown, setCountdown] = useState(0);

  // Refs for OTP inputs
  const otpRefs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ];

  // OTP mutation
  const { mutate: otpMutation, isPending: otpIsPending } = useOtp({
    onSuccess: () => {
      setStep("otp");
      startCountdown();
      // Focus the first OTP input when OTP screen is shown
      setTimeout(() => {
        otpRefs[0].current?.focus();
      }, 100);
    },
    onError: (error) => {
      setError("Failed to send OTP. Please try again.");
      console.error("OTP error:", error);
    },
  });

  // Verify OTP mutation
  const { mutate: verifyOtpMutation, isPending: verifyOtpIsPending } =
    useVerifyOtp({
      onSuccess: (data) => {
        if (data) {
          // Store only the phone number in sessionStorage
          sessionStorage.setItem("userPhone", phoneNumber);

          // Generate and store session ID
          const randomPart = generateRandomString(5);
          const sessionId = `${phoneNumber}_${randomPart}`;
          sessionStorage.setItem("chatSessionId", sessionId);

          // Call the success callback
          onAuthSuccess();
        } else {
          setError("Invalid OTP. Please try again.");
        }
      },
      onError: (error) => {
        setError("Failed to verify OTP. Please try again.");
        console.error("Verify OTP error:", error);
      },
    });

  // Resend OTP mutation
  const { mutate: resendOtpMutation, isPending: resendOtpIsPending } =
    useResendOtp({
      onSuccess: () => {
        startCountdown();
        setError("");
        setOtp(["", "", "", ""]);
        setTimeout(() => {
          otpRefs[0].current?.focus();
        }, 100);
      },
      onError: (error) => {
        setError("Failed to resend OTP. Please try again.");
        console.error("Resend OTP error:", error);
      },
    });

  // Start countdown for resend OTP
  const startCountdown = () => {
    setCountdown(30);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Handle OTP input change
  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) {
      // If pasting multiple digits
      const digits = value.split("").slice(0, 4);
      const newOtp = [...otp];

      digits.forEach((digit, i) => {
        if (index + i < 4) {
          newOtp[index + i] = digit;
        }
      });

      setOtp(newOtp);

      // Focus the appropriate input
      const nextIndex = Math.min(index + digits.length, 3);
      otpRefs[nextIndex].current?.focus();
    } else {
      // For single digit input
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      // Auto-focus next input if a digit was entered
      if (value && index < 3) {
        otpRefs[index + 1].current?.focus();
      }
    }
  };

  // Handle key press in OTP inputs
  const handleOtpKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    // Move to previous input on backspace if current input is empty
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      otpRefs[index - 1].current?.focus();
    }

    // Allow only numbers
    if (
      !/^\d$/.test(e.key) &&
      e.key !== "Backspace" &&
      e.key !== "Tab" &&
      e.key !== "ArrowLeft" &&
      e.key !== "ArrowRight"
    ) {
      e.preventDefault();
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (step === "details") {
      if (!name.trim()) {
        setError("Please enter your name");
        return;
      }

      if (!phoneNumber.trim() || !/^[6-9]\d{9}$/.test(phoneNumber)) {
        setError("Please enter a valid 10-digit phone number");
        return;
      }

      const body = {
        phone_number: parseInt(phoneNumber),
        otp_use_case: "ai-chat-advisor",
      };

      // @ts-ignore
      otpMutation(body);
    } else if (step === "otp") {
      const otpValue = otp.join("");

      if (otpValue.length !== 4 || !/^\d+$/.test(otpValue)) {
        setError("Please enter a valid 4-digit OTP");
        return;
      }

      const body = {
        phone_number: phoneNumber,
        user_otp: otpValue,
        otp_use_case: "ai-chat-advisor",
      };

      // @ts-ignore
      verifyOtpMutation(body);
    }
  };

  const handleResendOtp = () => {
    if (countdown === 0) {
      const body = {
        phone_number: parseInt(phoneNumber),
        otp_use_case: "ai-chat-advisor",
      };

      // @ts-ignore
      resendOtpMutation(body);
    }
  };

  return (
    <div className="px-6 py-6">
      {/* Progress indicator */}
      <div className="mb-6">
        <div className="flex items-center justify-center gap-3">
          <div
            className={`flex items-center ${
              step === "details" ? "text-blue-600" : "text-gray-400"
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 ${
                step === "details"
                  ? "bg-blue-100 text-blue-600"
                  : "bg-gray-100 text-gray-400"
              }`}
            >
              <UserIcon className="h-4 w-4" />
            </div>
            <span className="text-sm font-medium">Details</span>
          </div>
          <div className="w-28 h-[3px] mx-2 bg-gray-200 relative">
            <div
              className={`absolute top-0 left-0 h-full bg-blue-600 transition-all duration-300 ${
                step === "otp" ? "w-full" : "w-0"
              }`}
            ></div>
          </div>
          <div
            className={`flex items-center ${
              step === "otp" ? "text-blue-600" : "text-gray-400"
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 ${
                step === "otp"
                  ? "bg-blue-100 text-blue-600"
                  : "bg-gray-100 text-gray-400"
              }`}
            >
              <ShieldCheckIcon className="h-4 w-4" />
            </div>
            <span className="text-sm font-medium">Verify</span>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-5">
        {step === "details" ? (
          <>
            <div className="space-y-5">
              <div>
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-gray-700 mb-1.5"
                >
                  Full Name
                </label>
                <div className="relative group">
                  <div className="absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none">
                    <UserIcon className="h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" />
                  </div>
                  <input
                    type="text"
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="text-sm w-full pl-11 px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all shadow-sm"
                    placeholder="Enter your name"
                  />
                </div>
              </div>
              <div>
                <label
                  htmlFor="phone"
                  className="block text-sm font-medium text-gray-700 mb-1.5"
                >
                  Phone Number
                </label>
                <div className="relative group">
                  <div className="absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none">
                    <PhoneIcon className="h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" />
                  </div>
                  <input
                    type="tel"
                    id="phone"
                    value={phoneNumber}
                    onChange={(e) =>
                      setPhoneNumber(
                        e.target.value.replace(/\D/g, "").slice(0, 10)
                      )
                    }
                    className="w-full text-sm pl-11 px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all shadow-sm"
                    placeholder="Enter your 10-digit phone number"
                    maxLength={10}
                  />
                </div>
                <p className="mt-1.5 text-xs text-gray-500 flex items-center">
                  <ShieldCheckIcon className="h-3.5 w-3.5 mr-1 text-gray-400" />
                  We&apos;ll send a verification code to this number
                </p>
              </div>
            </div>
          </>
        ) : (
          <div className="space-y-5">
            <div>
              <label
                htmlFor="otp-1"
                className="block text-sm font-medium text-gray-700 mb-1.5"
              >
                Verification Code
              </label>

              <div className="flex justify-center space-x-3 mb-2">
                {otp.map((digit, index) => (
                  <input
                    key={index}
                    ref={otpRefs[index]}
                    type="text"
                    inputMode="numeric"
                    maxLength={4}
                    value={digit}
                    onChange={(e) => handleOtpChange(index, e.target.value)}
                    onKeyDown={(e) => handleOtpKeyDown(index, e)}
                    className="w-12 h-12 text-center text-lg font-medium border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all shadow-sm"
                    autoComplete="one-time-code"
                  />
                ))}
              </div>

              <p className="mt-1.5 text-xs text-gray-500 flex items-center justify-center">
                <ShieldCheckIcon className="h-3.5 w-3.5 mr-1 text-gray-400" />
                We&apos;ve sent a 4-digit code to {phoneNumber}
              </p>
            </div>

            <div className="flex justify-between items-center text-sm">
              <button
                type="button"
                onClick={handleResendOtp}
                disabled={countdown > 0}
                className={`flex items-center text-blue-600 hover:text-blue-800 transition-colors ${
                  countdown > 0 ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                {countdown > 0 ? (
                  <span className="flex items-center">
                    <span className="inline-block w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></span>
                    {`Resend in ${countdown}s`}
                  </span>
                ) : (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-1.5" />
                    <span>Resend OTP</span>
                  </>
                )}
              </button>
              <button
                type="button"
                onClick={() => setStep("details")}
                className="text-gray-600 hover:text-gray-800 transition-colors hover:underline"
              >
                Change Number
              </button>
            </div>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-md animate-pulse shadow-sm">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <button
          type="submit"
          disabled={otpIsPending || verifyOtpIsPending}
          className="w-full py-4 px-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-md flex items-center justify-center"
        >
          {otpIsPending || verifyOtpIsPending ? (
            <>
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Processing...
            </>
          ) : step === "details" ? (
            <>
              Get Verification Code
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </>
          ) : (
            <>
              Verify & Continue
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </>
          )}
        </button>

        <p className="text-xs text-center text-gray-500 mt-4">
          By continuing, you agree to our{" "}
          <a href="#" className="text-blue-600 hover:underline">
            Terms of Service
          </a>{" "}
          and{" "}
          <a href="#" className="text-blue-600 hover:underline">
            Privacy Policy
          </a>
        </p>
      </form>
    </div>
  );
};

export default AuthForm;
