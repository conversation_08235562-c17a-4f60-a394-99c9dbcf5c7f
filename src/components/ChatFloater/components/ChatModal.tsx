"use client";

import { useState, useEffect } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import AuthForm from "./AuthForm";
import ChatInterface from "./ChatInterface";
import ProductSelectionModal from "./ProductSelectionModal";

interface ChatModalProps {
  onClose: () => void;
  isAuthenticated: boolean;
  onAuthSuccess: () => void;
}

const ChatModal = ({
  onClose,
  isAuthenticated,
  onAuthSuccess,
}: ChatModalProps) => {
  // State to track modal dimensions
  const [modalSize, setModalSize] = useState({
    width: "max-w-md",
    height: "max-h-[80vh]",
  });

  // State to track product selection
  const [selectedProduct, setSelectedProduct] = useState("");
  const [showProductModal, setShowProductModal] = useState(true);

  // Update modal size when authentication state changes
  useEffect(() => {
    if (isAuthenticated) {
      if (selectedProduct) {
        // Larger size for chat interface
        setModalSize({
          width: "max-w-4xl",
          height: "h-[90vh]",
        });
      } else {
        // Medium size for product selection
        setModalSize({
          width: "max-w-md",
          height: "max-h-[80vh]",
        });
      }
    } else {
      // Smaller size for auth form
      setModalSize({
        width: "max-w-md",
        height: "max-h-[80vh]",
      });
    }
  }, [isAuthenticated, selectedProduct]);

  // Handle product selection
  const handleProductSelect = (productIdentifier: string) => {
    setSelectedProduct(productIdentifier);
    setShowProductModal(false);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div
        className={`bg-white rounded-lg shadow-xl w-full ${modalSize.width} flex flex-col ${modalSize.height} overflow-hidden transition-all duration-300`}
      >
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-lg font-medium">
            {!isAuthenticated
              ? "Sign In to Chat"
              : showProductModal
              ? "Select Insurance Product"
              : "OneAssure Chat Advisor"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors"
            aria-label="Close chat"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        <div className="flex-grow overflow-hidden">
          {!isAuthenticated ? (
            <AuthForm onAuthSuccess={onAuthSuccess} />
          ) : showProductModal ? (
            <div className="p-4">
              <ProductSelectionModal
                onSelect={handleProductSelect}
                onClose={() => {
                  // Default to a product if user closes without selecting
                  handleProductSelect("Aditya Birla Activ Care Premier");
                }}
                onCloseChat={onClose}
              />
            </div>
          ) : (
            <ChatInterface
              onClose={onClose}
              selectedProduct={selectedProduct}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatModal;
