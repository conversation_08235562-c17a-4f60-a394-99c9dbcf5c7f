"use client";

import { useState, useEffect } from "react";
import {
  ChatBubbleLeftRightIcon,
  XMarkIcon,
  SparklesIcon,
} from "@heroicons/react/24/outline";
import ChatModal from "./ChatModal";
import ScrollLock from "./ScrollLock";

const ChatButton = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showBanner, setShowBanner] = useState(false);
  const [isBannerClosing, setIsBannerClosing] = useState(false);

  // Check if user is already authenticated on mount
  useEffect(() => {
    const authStatus = localStorage.getItem("chatAuthStatus");
    if (authStatus === "authenticated") {
      setIsAuthenticated(true);
    }
  }, []);

  // Show banner after a short delay when page loads
  useEffect(() => {
    // Show banner after 1.5 seconds
    const timer = setTimeout(() => {
      setShowBanner(true);
    }, 1500);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    // Hide banner after 8 seconds
    const hideTimer = setTimeout(() => {
      closeBanner();
    }, 9500);

    return () => {
      clearTimeout(hideTimer);
    };
  }, []);

  const handleAuthSuccess = () => {
    setIsAuthenticated(true);
    localStorage.setItem("chatAuthStatus", "authenticated");
  };

  const handleCloseRequest = () => {
    // Show confirmation dialog instead of closing immediately
    setShowConfirmation(true);
  };

  const handleConfirmClose = () => {
    setShowConfirmation(false);
    setIsOpen(false);
  };

  const handleCancelClose = () => {
    setShowConfirmation(false);
  };

  const handleButtonClick = () => {
    closeBanner();
    setIsOpen(true);
  };

  const dismissBanner = (e: React.MouseEvent) => {
    e.stopPropagation();
    closeBanner();
  };

  // Function to handle smooth closing animation
  const closeBanner = () => {
    setIsBannerClosing(true);
    // Wait for animation to complete before hiding
    setTimeout(() => {
      setShowBanner(false);
      setIsBannerClosing(false);
    }, 300); // Match this with the animation duration
  };

  return (
    <>
      {/* ScrollLock component to prevent background scrolling */}
      <ScrollLock isActive={isOpen || showConfirmation} />

      <div className="fixed bottom-10 right-6 z-50 flex flex-col items-end">
        {/* Attention Banner */}
        {showBanner && (
          <div
            className={`mb-4 bg-gradient-to-r from-primary-1 to-primary-2 text-white rounded-xl shadow-lg w-72 relative overflow-hidden transition-all duration-300 ease-in-out ${
              isBannerClosing
                ? "opacity-0 transform translate-y-4"
                : "opacity-100 transform translate-y-0 animate-slide-in-up"
            }`}
          >
            <div className="absolute top-0 right-0 pt-2 pr-2">
              <button
                onClick={dismissBanner}
                className="text-white/80 hover:text-white p-1 rounded-full hover:bg-white/10 transition-colors"
                aria-label="Dismiss banner"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>

            <div className="px-4 pt-4 pb-3">
              <div className="flex items-center">
                <div className="bg-white/20 p-1.5 rounded-full mr-3">
                  <SparklesIcon className="h-5 w-5 text-yellow-300 animate-pulse" />
                </div>
                <span className="font-semibold text-sm">
                  New AI Chat Assistant!
                </span>
              </div>

              <p className="text-xs mt-2 text-white/90 leading-relaxed">
                Get instant answers about insurance plans and coverage options
              </p>

              <div className="mt-3 flex justify-between items-center">
                <button
                  onClick={handleButtonClick}
                  className="text-xs bg-white text-primary-1 font-medium py-1.5 px-4 rounded-full hover:bg-gray-100 transition-colors shadow-sm"
                >
                  Try it now
                </button>

                <div className="text-[10px] text-white/70 italic">
                  Powered by AI
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Chat Button */}
        <button
          onClick={handleButtonClick}
          className={`bg-primary-1 text-white rounded-full p-3 shadow-lg hover:bg-primary-700 transition-all flex items-center justify-center ${
            showBanner && !isBannerClosing ? "animate-bounce-gentle" : ""
          }`}
          aria-label="Chat with OneAssure"
        >
          <ChatBubbleLeftRightIcon className="h-6 w-6" />
        </button>
      </div>

      {isOpen && (
        <ChatModal
          onClose={handleCloseRequest}
          isAuthenticated={isAuthenticated}
          onAuthSuccess={handleAuthSuccess}
        />
      )}

      {/* Confirmation Dialog */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-sm w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-3">
              Are you sure?
            </h3>
            <p className="text-sm text-gray-600 mb-5">
              Closing this chat will end your current conversation. You can
              always start a new chat later.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCancelClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmClose}
                className="px-4 py-2 text-sm font-medium text-white bg-primary-1 hover:bg-primary-2 rounded-md transition-colors"
              >
                Close Chat
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ChatButton;
