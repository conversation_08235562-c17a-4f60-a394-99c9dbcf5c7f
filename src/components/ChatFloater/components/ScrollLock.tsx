"use client";

import { useEffect } from "react";

interface ScrollLockProps {
  isActive: boolean;
}

const ScrollLock = ({ isActive }: ScrollLockProps) => {
  useEffect(() => {
    // Add or remove the no-scroll class on the document body
    if (isActive) {
      document.body.classList.add("overflow-hidden");
    } else {
      document.body.classList.remove("overflow-hidden");
    }

    // Cleanup function to ensure we remove the class when component unmounts
    return () => {
      document.body.classList.remove("overflow-hidden");
    };
  }, [isActive]);

  // This component doesn't render anything
  return null;
};

export default ScrollLock;
