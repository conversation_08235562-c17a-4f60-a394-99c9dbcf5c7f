"use client";

import { useState, useEffect } from "react";
import { XMarkIcon, CheckIcon } from "@heroicons/react/24/outline";
import ScrollLock from "./ScrollLock";
import { catalog } from "../data/catalog";

type ProductCategory = "health" | "term";
type Company = string;
type ProductInfo = {
  productName: string;
  productIdentifier: string;
};

interface ProductSelectionModalProps {
  onSelect: (productIdentifier: string) => void;
  onClose: () => void;
  onCloseChat: () => void;
}

const ProductSelectionModal = ({
  onSelect,
  onClose,
  onCloseChat,
}: ProductSelectionModalProps) => {
  const [category, setCategory] = useState<ProductCategory>("health");
  const [company, setCompany] = useState<Company>("");
  const [product, setProduct] = useState<string>("");
  const [companies, setCompanies] = useState<string[]>([]);
  const [products, setProducts] = useState<ProductInfo[]>([]);

  // Update companies when category changes
  useEffect(() => {
    if (category) {
      const availableCompanies = Object.keys(
        catalog[category as ProductCategory]
      );
      setCompanies(availableCompanies);
      setCompany("");
    } else {
      setCompanies([]);
      setCompany("");
    }
  }, [category]);

  // Update products when company changes
  useEffect(() => {
    if (
      category &&
      company &&
      category in catalog &&
      company in catalog[category as ProductCategory]
    ) {
      setProducts(catalog[category as ProductCategory][company]);
      setProduct("");
    } else {
      setProducts([]);
      setProduct("");
    }
  }, [category, company]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (company && product) {
      onSelect(product);
    }
  };

  const handleClose = () => {
    onCloseChat();
  };

  return (
    <>
      <ScrollLock isActive={true} />
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
        <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Select Insurance Product
            </h3>
            <button
              onClick={handleClose}
              className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          <p className="text-sm text-gray-600 mb-4">
            Please select your insurance product to get personalized assistance.
          </p>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label
                htmlFor="category"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Category
              </label>
              <select
                id="category"
                value={category}
                onChange={(e) => setCategory(e.target.value as ProductCategory)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">Select Category</option>
                {Object.keys(catalog).map((cat) => (
                  <option key={cat} value={cat}>
                    {cat.charAt(0).toUpperCase() + cat.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="company"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Company
              </label>
              <select
                id="company"
                value={company}
                onChange={(e) => setCompany(e.target.value)}
                disabled={!category}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
                required
              >
                <option value="">Select Company</option>
                {companies.map((comp) => (
                  <option key={comp} value={comp}>
                    {comp}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="product"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Product
              </label>
              <select
                id="product"
                value={product}
                onChange={(e) => setProduct(e.target.value)}
                disabled={!company}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:text-gray-500"
                required
              >
                <option value="">Select Product</option>
                {products.map((prod) => (
                  <option
                    key={prod.productIdentifier}
                    value={prod.productIdentifier}
                  >
                    {prod.productName}
                  </option>
                ))}
              </select>
            </div>

            <div className="pt-2">
              <button
                type="submit"
                disabled={!category || !company || !product}
                className="w-full py-2 px-4 bg-primary-1 hover:bg-primary-2 text-white rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
              >
                <CheckIcon className="h-5 w-5 mr-2" />
                Continue with Selected Product
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default ProductSelectionModal;
