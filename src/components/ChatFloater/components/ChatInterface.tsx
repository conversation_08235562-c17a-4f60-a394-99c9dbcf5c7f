"use client";

import { useState, useRef, useEffect } from "react";
import {
  PaperAirplaneIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { useChatQuery } from "../api/useChatQuery";
import ReactMarkdown from "react-markdown";
import { generateRandomString } from "../utils/generateRandom";

type Message = {
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
};

interface ChatInterfaceProps {
  onClose: () => void;
  selectedProduct: string;
}

const ChatInterface = ({ onClose, selectedProduct }: ChatInterfaceProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [sessionId, setSessionId] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Chat query mutation
  const chatQueryMutation = useChatQuery();
  const isLoading = chatQueryMutation.isPending;

  // Initialize session ID and greeting message when component mounts
  useEffect(() => {
    // Get user's phone number from sessionStorage
    const userPhone = sessionStorage.getItem("userPhone");

    // Always generate a new session ID for each chat window
    if (userPhone) {
      // Create session ID by combining phone number with random 5-character string
      const randomPart = generateRandomString(5);
      const newSessionId = `${userPhone}_${randomPart}`;

      // Store the session ID in sessionStorage
      sessionStorage.setItem("chatSessionId", newSessionId);
      setSessionId(newSessionId);
    } else {
      // Fallback if no phone number is found (should not happen in normal flow)
      const randomId = `anonymous_${generateRandomString(10)}`;
      sessionStorage.setItem("chatSessionId", randomId);
      setSessionId(randomId);
    }

    // Set initial greeting message
    setMessages([
      {
        role: "assistant",
        content: `Hi there, this is the OneAssure Chat Advisor. I'll be providing information about ${selectedProduct}. How can I help you today?`,
        timestamp: new Date(),
      },
    ]);
  }, [selectedProduct]);

  // Scroll to bottom of chat when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Handle sending a message
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || !selectedProduct || !sessionId) return;

    const userMessage = {
      role: "user" as const,
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputMessage("");

    try {
      chatQueryMutation.mutate(
        {
          query: inputMessage,
          session_id: sessionId,
          product_identifiers: [selectedProduct],
        },
        {
          onSuccess: (data) => {
            setMessages((prev) => [
              ...prev,
              {
                role: "assistant",
                content: data.response,
                timestamp: new Date(),
              },
            ]);
          },
          onError: (error) => {
            console.error("Error sending message:", error);
            setMessages((prev) => [
              ...prev,
              {
                role: "assistant",
                content:
                  "Sorry, I encountered an error processing your request. Please try again.",
                timestamp: new Date(),
              },
            ]);
          },
        }
      );
    } catch (error) {
      console.error("Error sending message:", error);
      setMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content:
            "Sorry, I encountered an error processing your request. Please try again.",
          timestamp: new Date(),
        },
      ]);
    }
  };

  // Handle key press in textarea
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full z-50">
      {/* Header with selected product */}
      {/* <div className="bg-gradient-to-r from-primary-1 to-primary-2 text-white p-3">
        <h3 className="font-medium text-sm">OneAssure Chat Advisor</h3>
        <p className="text-xs text-white opacity-90 mt-0.5">
          {selectedProduct}
        </p>
      </div> */}

      {/* Chat Messages */}
      <div className="flex-grow overflow-y-auto p-4 bg-gray-50">
        <div className="space-y-4">
          {messages.map((message, index) => (
            <div
              key={index}
              className={`flex ${
                message.role === "user" ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`max-w-[80%] px-4 py-3 rounded-lg shadow-sm ${
                  message.role === "user"
                    ? "bg-secondary-1 text-white"
                    : "bg-white border border-gray-200 text-gray-800"
                }`}
              >
                {message.role === "user" ? (
                  <p className="text-sm whitespace-pre-wrap">
                    {message.content}
                  </p>
                ) : (
                  <div className="markdown-content text-sm">
                    <ReactMarkdown
                      components={{
                        // Style markdown elements
                        p: ({ node, ...props }) => (
                          <p className="mb-2 last:mb-0" {...props} />
                        ),
                        ul: ({ node, ...props }) => (
                          <ul className="list-disc pl-5 mb-2" {...props} />
                        ),
                        ol: ({ node, ...props }) => (
                          <ol className="list-decimal pl-5 mb-2" {...props} />
                        ),
                        li: ({ node, ...props }) => (
                          <li className="mb-1" {...props} />
                        ),
                        h1: ({ node, ...props }) => (
                          <h1 className="text-lg font-bold mb-2" {...props} />
                        ),
                        h2: ({ node, ...props }) => (
                          <h2 className="text-base font-bold mb-2" {...props} />
                        ),
                        h3: ({ node, ...props }) => (
                          <h3 className="text-sm font-bold mb-2" {...props} />
                        ),
                        a: ({ node, ...props }) => (
                          <a
                            className="text-blue-600 hover:underline"
                            target="_blank"
                            rel="noopener noreferrer"
                            {...props}
                          />
                        ),
                        strong: ({ node, ...props }) => (
                          <strong className="font-bold" {...props} />
                        ),
                        em: ({ node, ...props }) => (
                          <em className="italic" {...props} />
                        ),
                        code: ({ node, ...props }) => (
                          <code
                            className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono"
                            {...props}
                          />
                        ),
                        pre: ({ node, ...props }) => (
                          <pre
                            className="bg-gray-100 p-2 rounded overflow-x-auto my-2 text-xs font-mono"
                            {...props}
                          />
                        ),
                      }}
                    >
                      {message.content}
                    </ReactMarkdown>
                  </div>
                )}
                <p className="text-xs mt-1 opacity-70 text-right">
                  {message.timestamp.toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="flex justify-start">
              <div className="max-w-[80%] px-4 py-3 rounded-lg bg-white border border-gray-200 text-gray-800 shadow-sm">
                <div className="flex items-center space-x-2">
                  <div className="relative w-4 h-4">
                    <div className="absolute top-0 left-0 w-4 h-4 rounded-full border-2 border-blue-600 border-t-transparent animate-spin"></div>
                  </div>
                  <p className="text-sm text-gray-600">Thinking...</p>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area */}
      <div className="p-4 border-t bg-white">
        <div className="flex space-x-2">
          <textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type your message here..."
            disabled={isLoading}
            className="flex-grow px-4 py-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none shadow-sm disabled:bg-gray-100 disabled:text-gray-500"
            rows={1}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading}
            className="p-3 bg-secondary-1 hover:bg-secondary-2 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-sm"
          >
            <PaperAirplaneIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Disclaimer */}
        <div className="mt-3 flex items-center space-x-2 text-[12px] text-gray-500 px-1">
          <InformationCircleIcon className="h-3 w-3 flex-shrink-0" />
          <p>
            AI responses are informational only, consult our advisors for
            accurate information.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
