type ProductCategory = "health" | "term";
type Company = string;
type Product = {
  productName: string;
  productIdentifier: string;
};

export const catalog: Record<ProductCategory, Record<Company, Product[]>> = {
  health: {
    Acko: [
      {
        productName: "Platinum Health",
        productIdentifier: "Acko Platinum Health",
      },
      {
        productName: "Standard Health",
        productIdentifier: "Acko Standard Health",
      },
    ],
    "Aditya Birla": [
      {
        productName: "Activ Care Premier",
        productIdentifier: "Aditya Birla Activ Care Premier",
      },
      {
        productName: "Activ Care Standard",
        productIdentifier: "Aditya Birla Activ Care Standard",
      },
      {
        productName: "Activ Health Platinum Enhanced",
        productIdentifier: "Aditya Birla Activ Health Platinum Enhanced",
      },
      {
        productName: "Activ Health Platinum Essential",
        productIdentifier: "Aditya Birla Activ Health Platinum Essential",
      },
      {
        productName: "Activ Care Classic",
        productIdentifier: "Aditya Birla Activ Care Classic",
      },
      {
        productName: "Activ Fit Plus",
        productIdentifier: "Aditya Birla Activ Fit Plus",
      },
      {
        productName: "Activ One VIP",
        productIdentifier: "Aditya Birla Activ One VIP",
      },
      {
        productName: "Activ One VYTL",
        productIdentifier: "Aditya Birla Activ One VYTL",
      },
      {
        productName: "Activ One VIP+",
        productIdentifier: "Aditya Birla Activ One VIP+",
      },
      {
        productName: "Activ One NXT",
        productIdentifier: "Aditya Birla Activ One NXT",
      },
      {
        productName: "Super Health Plus Top-Up",
        productIdentifier: "Aditya Birla Super Health Plus Top-Up",
      },
      {
        productName: "Activ Assured Diamond",
        productIdentifier: "Aditya Birla Activ Assured Diamond",
      },
      {
        productName: "Activ One SAVR",
        productIdentifier: "Aditya Birla Activ One SAVR",
      },
      {
        productName: "Activ Fit Preferred",
        productIdentifier: "Aditya Birla Activ Fit Preferred",
      },
      {
        productName: "Activ One MAX",
        productIdentifier: "Aditya Birla Activ One MAX",
      },
      {
        productName: "Activ One MAX+",
        productIdentifier: "Aditya Birla Activ One MAX+",
      },
    ],
    "Apollo Munich": [
      {
        productName: "Easy Health Exclusive",
        productIdentifier: "Apollo Munich Easy Health Exclusive",
      },
      {
        productName: "Easy Health Premium",
        productIdentifier: "Apollo Munich Easy Health Premium",
      },
      {
        productName: "Optima Restore",
        productIdentifier: "Apollo Munich Optima Restore",
      },
      {
        productName: "Energy Gold",
        productIdentifier: "Apollo Munich Energy Gold",
      },
      {
        productName: "Optima Senior",
        productIdentifier: "Apollo Munich Optima Senior",
      },
      {
        productName: "Easy Health Standard",
        productIdentifier: "Apollo Munich Easy Health Standard",
      },
      {
        productName: "Energy Silver",
        productIdentifier: "Apollo Munich Energy Silver",
      },
    ],
    "Bajaj Allianz": [
      {
        productName: "Extra Care Top-up",
        productIdentifier: "Bajaj Allianz Extra Care Top-up",
      },
      {
        productName: "Health Ensure Family Plan",
        productIdentifier: "Bajaj Allianz Health Ensure Family Plan",
      },
      {
        productName: "Health Care Supreme Smart",
        productIdentifier: "Bajaj Allianz Health Care Supreme Smart",
      },
      {
        productName: "Health Care Supreme Ultimo",
        productIdentifier: "Bajaj Allianz Health Care Supreme Ultimo",
      },
      {
        productName: "Health Care Supreme Vital",
        productIdentifier: "Bajaj Allianz Health Care Supreme Vital",
      },
      {
        productName: "Health Guard Gold",
        productIdentifier: "Bajaj Allianz Health Guard Gold",
      },
      {
        productName: "Silver Health",
        productIdentifier: "Bajaj Allianz Silver Health",
      },
      {
        productName: "Health Guard Platinum",
        productIdentifier: "Bajaj Allianz Health Guard Platinum",
      },
      {
        productName: "Extra Care Plus Super Top-up",
        productIdentifier: "Bajaj Allianz Extra Care Plus Super Top-up",
      },
      {
        productName: "Health Guard Silver",
        productIdentifier: "Bajaj Allianz Health Guard Silver",
      },
    ],
    "Bharti Axa": [
      {
        productName: "Smart Super Health Assure",
        productIdentifier: "Bharti Axa Smart Super Health Assure",
      },
      {
        productName: "Smart Super Health",
        productIdentifier: "Bharti Axa Smart Super Health",
      },
      {
        productName: "Smart Super Top-up",
        productIdentifier: "Bharti Axa Smart Super Top-up",
      },
    ],
    Care: [
      {
        productName: "Care Ultimate",
        productIdentifier: "Care Care Ultimate",
      },
      {
        productName: "Joy Today",
        productIdentifier: "Care Joy Today",
      },
      {
        productName: "Care Plus Complete",
        productIdentifier: "Care Care Plus Complete",
      },
      {
        productName: "Enhance Super Top-up",
        productIdentifier: "Care Enhance Super Top-up",
      },
      {
        productName: "Care Heart",
        productIdentifier: "Care Care Heart",
      },
      {
        productName: "Care Freedom plan",
        productIdentifier: "Care Care Freedom plan",
      },
      {
        productName: "Care Advantage",
        productIdentifier: "Care Care Advantage",
      },
      {
        productName: "Care Plus Youth",
        productIdentifier: "Care Care Plus Youth",
      },
      {
        productName: "Care Classic",
        productIdentifier: "Care Care Classic",
      },
      {
        productName: "Care Senior",
        productIdentifier: "Care Care Senior",
      },
      {
        productName: "Care Supreme - Senior Super",
        productIdentifier: "Care Care Supreme - Senior Super",
      },
      {
        productName: "Care Supreme - Super Saver",
        productIdentifier: "Care Care Supreme - Super Saver",
      },
      {
        productName: "Senior Health Advantage",
        productIdentifier: "Care Senior Health Advantage",
      },
      {
        productName: "Supreme Enhance Super Top-up",
        productIdentifier: "Care Supreme Enhance Super Top-up",
      },
      {
        productName: "Care Supreme - Senior Premium",
        productIdentifier: "Care Care Supreme - Senior Premium",
      },
      {
        productName: "Care Supreme - Value for Money",
        productIdentifier: "Care Care Supreme - Value for Money",
      },
      {
        productName: "Care Supreme",
        productIdentifier: "Care Care Supreme",
      },
      {
        productName: "Care",
        productIdentifier: "Care Care",
      },
      {
        productName: "Joy Tomorrow",
        productIdentifier: "Care Joy Tomorrow",
      },
    ],
    Digit: [
      {
        productName: "Digit Insurance Comfort Option",
        productIdentifier: "Digit Digit Insurance Comfort Option",
      },
      {
        productName: "Digit Insurance Smart Option",
        productIdentifier: "Digit Digit Insurance Smart Option",
      },
      {
        productName: "Worldwide Treatment",
        productIdentifier: "Digit Worldwide Treatment",
      },
      {
        productName: "Infinity Wallet",
        productIdentifier: "Digit Infinity Wallet",
      },
      {
        productName: "Double Wallet",
        productIdentifier: "Digit Double Wallet",
      },
    ],
    "HDFC Ergo": [
      {
        productName: "Energy Gold",
        productIdentifier: "HDFC Ergo Energy Gold",
      },
      {
        productName: "Optima Restore",
        productIdentifier: "HDFC Ergo Optima Restore",
      },
      {
        productName: "Energy Silver",
        productIdentifier: "HDFC Ergo Energy Silver",
      },
      {
        productName: "EquiCover",
        productIdentifier: "HDFC Ergo EquiCover",
      },
      {
        productName: "myHealth Medisure Super Top-up",
        productIdentifier: "HDFC Ergo myHealth Medisure Super Top-up",
      },
      {
        productName: "Health Wallet",
        productIdentifier: "HDFC Ergo Health Wallet",
      },
      {
        productName: "myHealth Suraksha Gold",
        productIdentifier: "HDFC Ergo myHealth Suraksha Gold",
      },
      {
        productName: "myHealth Suraksha Platinum",
        productIdentifier: "HDFC Ergo myHealth Suraksha Platinum",
      },
      {
        productName: "Optima Lite",
        productIdentifier: "HDFC Ergo Optima Lite",
      },
      {
        productName: "Optima Secure Global Plus",
        productIdentifier: "HDFC Ergo Optima Secure Global Plus",
      },
      {
        productName: "myHealth Suraksha Silver",
        productIdentifier: "HDFC Ergo myHealth Suraksha Silver",
      },
      {
        productName: "myHealth Koti Suraksha",
        productIdentifier: "HDFC Ergo myHealth Koti Suraksha",
      },
      {
        productName: "Optima Secure",
        productIdentifier: "HDFC Ergo Optima Secure",
      },
      {
        productName: "Optima Super Secure",
        productIdentifier: "HDFC Ergo Optima Super Secure",
      },
      {
        productName: "Optima Secure Global",
        productIdentifier: "HDFC Ergo Optima Secure Global",
      },
    ],
    "ICICI Lombard": [
      {
        productName: "iHealth",
        productIdentifier: "ICICI Lombard iHealth",
      },
      {
        productName: "Golden Shield",
        productIdentifier: "ICICI Lombard Golden Shield",
      },
      {
        productName: "Max Protect Classic",
        productIdentifier: "ICICI Lombard Max Protect Classic",
      },
      {
        productName: "Health Booster Super Top-up",
        productIdentifier: "ICICI Lombard Health Booster Super Top-up",
      },
      {
        productName: "Health Shield 360 Retail",
        productIdentifier: "ICICI Lombard Health Shield 360 Retail",
      },
      {
        productName: "Health Elite Plus",
        productIdentifier: "ICICI Lombard Health Elite Plus",
      },
      {
        productName: "Health Shield 360",
        productIdentifier: "ICICI Lombard Health Shield 360",
      },
      {
        productName: "iHealth Plus",
        productIdentifier: "ICICI Lombard iHealth Plus",
      },
      {
        productName: "Health AdvantEdge",
        productIdentifier: "ICICI Lombard Health AdvantEdge",
      },
      {
        productName: "Elevate",
        productIdentifier: "ICICI Lombard Elevate",
      },
      {
        productName: "Max Protect Premium",
        productIdentifier: "ICICI Lombard Max Protect Premium",
      },
    ],
    "Iffco Tokio": [
      {
        productName: "Family Health Protector",
        productIdentifier: "Iffco Tokio Family Health Protector",
      },
      {
        productName: "Individual Health Protector",
        productIdentifier: "Iffco Tokio Individual Health Protector",
      },
    ],
    "Manipal Cigna": [
      {
        productName: "ProHealth Plus",
        productIdentifier: "Manipal Cigna ProHealth Plus",
      },
      {
        productName: "Super Top-up",
        productIdentifier: "Manipal Cigna Super Top-up",
      },
      {
        productName: "ProHealth Preferred",
        productIdentifier: "Manipal Cigna ProHealth Preferred",
      },
      {
        productName: "ProHealth Protect",
        productIdentifier: "Manipal Cigna ProHealth Protect",
      },
      {
        productName: "ProHealth Premier",
        productIdentifier: "Manipal Cigna ProHealth Premier",
      },
      {
        productName: "SecureHealth",
        productIdentifier: "Manipal Cigna SecureHealth",
      },
      {
        productName: "ProHealth Select",
        productIdentifier: "Manipal Cigna ProHealth Select",
      },
      {
        productName: "ProHealth Prime Active",
        productIdentifier: "Manipal Cigna ProHealth Prime Active",
      },
      {
        productName: "ProHealth Prime Senior Classic",
        productIdentifier: "Manipal Cigna ProHealth Prime Senior Classic",
      },
      {
        productName: "ProHealth Prime Protect",
        productIdentifier: "Manipal Cigna ProHealth Prime Protect",
      },
      {
        productName: "ProHealth Prime Advantage",
        productIdentifier: "Manipal Cigna ProHealth Prime Advantage",
      },
      {
        productName: "LifeTime Health Global",
        productIdentifier: "Manipal Cigna LifeTime Health Global",
      },
      {
        productName: "ProHealth Prime Senior Elite",
        productIdentifier: "Manipal Cigna ProHealth Prime Senior Elite",
      },
      {
        productName: "LifeTime Health",
        productIdentifier: "Manipal Cigna LifeTime Health",
      },
      {
        productName: "ProHealth Accumulate",
        productIdentifier: "Manipal Cigna ProHealth Accumulate",
      },
    ],
    "Max Bupa": [
      {
        productName: "Aspire Diamond +",
        productIdentifier: "Max Bupa Aspire Diamond +",
      },
      {
        productName: "GoActive",
        productIdentifier: "Max Bupa GoActive",
      },
      {
        productName: "Health Companion",
        productIdentifier: "Max Bupa Health Companion",
      },
      {
        productName: "HeartBeat Gold",
        productIdentifier: "Max Bupa HeartBeat Gold",
      },
      {
        productName: "HeartBeat Platinum",
        productIdentifier: "Max Bupa HeartBeat Platinum",
      },
      {
        productName: "Aspire Platinum +",
        productIdentifier: "Max Bupa Aspire Platinum +",
      },
      {
        productName: "ReAssure 2.0 Titanium+",
        productIdentifier: "Max Bupa ReAssure 2.0 Titanium+",
      },
      {
        productName: "Health Premia Platinum",
        productIdentifier: "Max Bupa Health Premia Platinum",
      },
      {
        productName: "Health Premia Silver",
        productIdentifier: "Max Bupa Health Premia Silver",
      },
      {
        productName: "Health Pulse Enhanced",
        productIdentifier: "Max Bupa Health Pulse Enhanced",
      },
      {
        productName: "Health Recharge Super Top-up",
        productIdentifier: "Max Bupa Health Recharge Super Top-up",
      },
      {
        productName: "ReAssure",
        productIdentifier: "Max Bupa ReAssure",
      },
      {
        productName: "ReAssure 2.0 Bronze+",
        productIdentifier: "Max Bupa ReAssure 2.0 Bronze+",
      },
      {
        productName: "ReAssure 2.0 Platinum+",
        productIdentifier: "Max Bupa ReAssure 2.0 Platinum+",
      },
      {
        productName: "Aspire Gold +",
        productIdentifier: "Max Bupa Aspire Gold +",
      },
      {
        productName: "Aspire Titanium+",
        productIdentifier: "Max Bupa Aspire Titanium+",
      },
      {
        productName: "Senior First Gold",
        productIdentifier: "Max Bupa Senior First Gold",
      },
      {
        productName: "Senior First Platinum",
        productIdentifier: "Max Bupa Senior First Platinum",
      },
      {
        productName: "Health Premia Gold",
        productIdentifier: "Max Bupa Health Premia Gold",
      },
    ],
    "National Insurance": [
      {
        productName: "National Parivar Mediclaim policy",
        productIdentifier:
          "National Insurance National Parivar Mediclaim policy",
      },
      {
        productName: "National Mediclaim policy",
        productIdentifier: "National Insurance National Mediclaim policy",
      },
      {
        productName: "National Mediclaim Plus policy",
        productIdentifier: "National Insurance National Mediclaim Plus policy",
      },
      {
        productName: "National Super Top-up Mediclaim",
        productIdentifier: "National Insurance National Super Top-up Mediclaim",
      },
      {
        productName: "National Senior Citizen Mediclaim policy",
        productIdentifier:
          "National Insurance National Senior Citizen Mediclaim policy",
      },
      {
        productName: "National Parivar Mediclaim Plus policy",
        productIdentifier:
          "National Insurance National Parivar Mediclaim Plus policy",
      },
    ],
    Navi: [
      {
        productName: "Navi Health",
        productIdentifier: "Navi Navi Health",
      },
    ],
    "New India Assurance": [
      {
        productName: "Yuva Bharat Health Gold",
        productIdentifier: "New India Assurance Yuva Bharat Health Gold",
      },
      {
        productName: "Mediclaim",
        productIdentifier: "New India Assurance Mediclaim",
      },
      {
        productName: "Premier Mediclaim Plan",
        productIdentifier: "New India Assurance Premier Mediclaim Plan",
      },
      {
        productName: "New India Top-up Mediclaim",
        productIdentifier: "New India Assurance New India Top-up Mediclaim",
      },
      {
        productName: "Sixty Plus Mediclaim",
        productIdentifier: "New India Assurance Sixty Plus Mediclaim",
      },
      {
        productName: "Yuva Bharat Health",
        productIdentifier: "New India Assurance Yuva Bharat Health",
      },
      {
        productName: "Floater Mediclaim",
        productIdentifier: "New India Assurance Floater Mediclaim",
      },
      {
        productName: "Yuva Bharat Health Platinum",
        productIdentifier: "New India Assurance Yuva Bharat Health Platinum",
      },
      {
        productName: "Senior Citizen Mediclaim",
        productIdentifier: "New India Assurance Senior Citizen Mediclaim",
      },
    ],
    "Oriental Insurance": [
      {
        productName: "Happy Family Floater Policy Silver",
        productIdentifier:
          "Oriental Insurance Happy Family Floater Policy Silver",
      },
      {
        productName: "Mediclaim Insurance Policy",
        productIdentifier: "Oriental Insurance Mediclaim Insurance Policy",
      },
      {
        productName: "Health of Privileged Elders",
        productIdentifier: "Oriental Insurance Health of Privileged Elders",
      },
      {
        productName: "Happy Family Floater Policy Diamond",
        productIdentifier:
          "Oriental Insurance Happy Family Floater Policy Diamond",
      },
      {
        productName: "Super Health Top-up",
        productIdentifier: "Oriental Insurance Super Health Top-up",
      },
      {
        productName: "Happy Family Floater Policy Gold",
        productIdentifier:
          "Oriental Insurance Happy Family Floater Policy Gold",
      },
      {
        productName: "Happy Family Floater Policy Platinum",
        productIdentifier:
          "Oriental Insurance Happy Family Floater Policy Platinum",
      },
    ],
    "Royal Sundaram": [
      {
        productName: "Family Plus",
        productIdentifier: "Royal Sundaram Family Plus",
      },
      {
        productName: "Lifeline Classic",
        productIdentifier: "Royal Sundaram Lifeline Classic",
      },
      {
        productName: "Lifeline Elite",
        productIdentifier: "Royal Sundaram Lifeline Elite",
      },
      {
        productName: "Lifeline Supreme",
        productIdentifier: "Royal Sundaram Lifeline Supreme",
      },
      {
        productName: "Advanced Top Up",
        productIdentifier: "Royal Sundaram Advanced Top Up",
      },
      {
        productName: "Multiplier Health",
        productIdentifier: "Royal Sundaram Multiplier Health",
      },
      {
        productName: "NextGen",
        productIdentifier: "Royal Sundaram NextGen",
      },
    ],
    SBI: [
      {
        productName: "Arogya Plus",
        productIdentifier: "SBI Arogya Plus",
      },
      {
        productName: "Super Health Premier",
        productIdentifier: "SBI Super Health Premier",
      },
      {
        productName: "Arogya Top-up",
        productIdentifier: "SBI Arogya Top-up",
      },
      {
        productName: "Retail Health Policy",
        productIdentifier: "SBI Retail Health Policy",
      },
      {
        productName: "Arogya Premier",
        productIdentifier: "SBI Arogya Premier",
      },
      {
        productName: "Super Health Platinum Infinite",
        productIdentifier: "SBI Super Health Platinum Infinite",
      },
      {
        productName: "Arogya Supreme Premium",
        productIdentifier: "SBI Arogya Supreme Premium",
      },
      {
        productName: "Arogya Supreme Plus",
        productIdentifier: "SBI Arogya Supreme Plus",
      },
      {
        productName: "Arogya Supreme Pro",
        productIdentifier: "SBI Arogya Supreme Pro",
      },
      {
        productName: "Super Health Elite",
        productIdentifier: "SBI Super Health Elite",
      },
      {
        productName: "Super Health Platinum",
        productIdentifier: "SBI Super Health Platinum",
      },
      {
        productName: "Super Health Prime",
        productIdentifier: "SBI Super Health Prime",
      },
    ],
    "Star Health": [
      {
        productName: "Special Care Gold",
        productIdentifier: "Star Health Special Care Gold",
      },
      {
        productName: "Health Premier",
        productIdentifier: "Star Health Health Premier",
      },
      {
        productName: "Health Gain",
        productIdentifier: "Star Health Health Gain",
      },
      {
        productName: "Super Surplus Gold",
        productIdentifier: "Star Health Super Surplus Gold",
      },
      {
        productName: "Medi Classic Gold",
        productIdentifier: "Star Health Medi Classic Gold",
      },
      {
        productName: "Family Health Optima",
        productIdentifier: "Star Health Family Health Optima",
      },
      {
        productName: "Red Carpet Senior Citizens",
        productIdentifier: "Star Health Red Carpet Senior Citizens",
      },
      {
        productName: "Cardiac Care Platinum",
        productIdentifier: "Star Health Cardiac Care Platinum",
      },
      {
        productName: "Young Star",
        productIdentifier: "Star Health Young Star",
      },
      {
        productName: "Diabetes Safe",
        productIdentifier: "Star Health Diabetes Safe",
      },
      {
        productName: "Assure",
        productIdentifier: "Star Health Assure",
      },
      {
        productName: "Super Star",
        productIdentifier: "Star Health Super Star",
      },
      {
        productName: "Women Care",
        productIdentifier: "Star Health Women Care",
      },
      {
        productName: "Medi Classic",
        productIdentifier: "Star Health Medi Classic",
      },
      {
        productName: "Comprehensive",
        productIdentifier: "Star Health Comprehensive",
      },
      {
        productName: "Special Care",
        productIdentifier: "Star Health Special Care",
      },
      {
        productName: "Smart Health Pro",
        productIdentifier: "Star Health Smart Health Pro",
      },
      {
        productName: "Cancer Care Platinum",
        productIdentifier: "Star Health Cancer Care Platinum",
      },
    ],
    "TATA AIG": [
      {
        productName: "Medicare Plus Super Top-up",
        productIdentifier: "TATA AIG Medicare Plus Super Top-up",
      },
      {
        productName: "Medicare",
        productIdentifier: "TATA AIG Medicare",
      },
      {
        productName: "Medicare Premier",
        productIdentifier: "TATA AIG Medicare Premier",
      },
      {
        productName: "Medicare Senior",
        productIdentifier: "TATA AIG Medicare Senior",
      },
      {
        productName: "Medicare Protect",
        productIdentifier: "TATA AIG Medicare Protect",
      },
      {
        productName: "Elder Care",
        productIdentifier: "TATA AIG Elder Care",
      },
      {
        productName: "Medicare LITE",
        productIdentifier: "TATA AIG Medicare LITE",
      },
      {
        productName: "Health SuperCharge",
        productIdentifier: "TATA AIG Health SuperCharge",
      },
    ],
    "United India": [
      {
        productName: "Senior Citizen Plan",
        productIdentifier: "United India Senior Citizen Plan",
      },
      {
        productName: "Family Medicare",
        productIdentifier: "United India Family Medicare",
      },
      {
        productName: "Medicare Super Top-up",
        productIdentifier: "United India Medicare Super Top-up",
      },
      {
        productName: "Individual Gold Plan",
        productIdentifier: "United India Individual Gold Plan",
      },
      {
        productName: "Individual Platinum Plan",
        productIdentifier: "United India Individual Platinum Plan",
      },
    ],
    "Universal Sompo": [
      {
        productName: "Complete Healthcare Basic",
        productIdentifier: "Universal Sompo Complete Healthcare Basic",
      },
      {
        productName: "Complete Healthcare Essential",
        productIdentifier: "Universal Sompo Complete Healthcare Essential",
      },
      {
        productName: "Complete Healthcare Privilege",
        productIdentifier: "Universal Sompo Complete Healthcare Privilege",
      },
    ],
    "Zuno (erstwhile Edelweiss)": [
      {
        productName: "Health Insurance Gold",
        productIdentifier: "Zuno (erstwhile Edelweiss) Health Insurance Gold",
      },
      {
        productName: "Health Insurance Platinum",
        productIdentifier:
          "Zuno (erstwhile Edelweiss) Health Insurance Platinum",
      },
      {
        productName: "Health Insurance Silver",
        productIdentifier: "Zuno (erstwhile Edelweiss) Health Insurance Silver",
      },
    ],
  },
  term: {
    "Bajaj Allianz Life": [
      {
        productName: "Smart Protect Goal",
        productIdentifier: "Bajaj Allianz Life Smart Protect Goal",
      },
      {
        productName: "eTouch II",
        productIdentifier: "Bajaj Allianz Life eTouch II",
      },
    ],
    "ICICI Prudential": [
      {
        productName: "iProtect Smart",
        productIdentifier: "ICICI Prudential iProtect Smart",
      },
    ],
    "HDFC Life": [
      {
        productName: "Click2Protect Life",
        productIdentifier: "HDFC Life Click2Protect Life",
      },
      {
        productName: "Click2Protect Super",
        productIdentifier: "HDFC Life Click2Protect Super",
      },
    ],
    "Max Life": [
      {
        productName: "Smart Secure Plus",
        productIdentifier: "Max Life Smart Secure Plus",
      },
      {
        productName: "Smart Total Elite Protection",
        productIdentifier: "Max Life Smart Total Elite Protection",
      },
      {
        productName: "Smart Term Plan Plus",
        productIdentifier: "Max Life Smart Term Plan Plus",
      },
    ],
    "TATA AIA": [
      {
        productName: "Sampoorna Raksha Promise",
        productIdentifier: "TATA AIA Sampoorna Raksha Promise",
      },
      {
        productName: "Maha Raksha Supreme Select",
        productIdentifier: "TATA AIA Maha Raksha Supreme Select",
      },
    ],
  },
};
