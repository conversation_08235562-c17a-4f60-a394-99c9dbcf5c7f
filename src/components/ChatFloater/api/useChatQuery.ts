import { postRequest } from "@/scripts/requests";
import { useMutation } from "@tanstack/react-query";

type ChatQueryRequest = {
  query: string;
  session_id: string;
  product_identifiers?: string[];
};

type ChatQueryResponse = {
  response: string;
  query: string;
  session_id: string;
  product_identifiers: string[];
};

export const useChatQuery = () => {
  return useMutation<ChatQueryResponse, Error, ChatQueryRequest>({
    mutationFn: async (data) => {
      // Add default product identifier if not provided
      const requestData = {
        ...data,
        product_identifiers: data.product_identifiers,
      };

      const response = await postRequest(
        `${process.env.NEXT_PUBLIC_BROKER_URL}/chat/v1/query`,
        requestData,
        {
          requireAuth: false,
          headers: {
            "Content-Type": "application/json",
            Token: process.env.NEXT_PUBLIC_CHAT_TOKEN,
          },
        }
      );

      return response.data;
    },
  });
};
