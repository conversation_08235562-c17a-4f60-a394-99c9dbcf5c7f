"use client";
import Hero from "./components/Hero";
import Benefits from "./components/Benefits";
import Testimonial from "./components/Testimonial";
import Team from "./components/Team";
import Openings from "./components/Openings";

const CareersNew = (props: any) => {
  return (
    <div>
      <Hero {...props.hero} />
      <Benefits {...props.benefits} />
      <Testimonial {...props.testimonials} />
      <Team {...props.team} />
      <Openings {...props.job} />
    </div>
  );
};

export default CareersNew;
