import React, { useState } from "react";
import {
  BriefcaseIcon,
  ClockIcon,
  MapPinIcon,
} from "@heroicons/react/24/outline";

const PAGE_SIZE = 5;

const Openings = (props: {
  title: string;
  jobItems: {
    postedDate: Date;
    jobTitle: string;
    department: string;
    type: string;
    location: string;
    link: string;
  }[];
}) => {
  const [page, setPage] = useState(1);

  // Handle cases where jobItems might be undefined or null
  const jobItems = props.jobItems || [];
  const totalPages = Math.ceil(jobItems.length / PAGE_SIZE);

  const paginatedOpenings = jobItems.slice(
    (page - 1) * PAGE_SIZE,
    page * PAGE_SIZE
  );

  // If no job items, show a message
  if (jobItems.length === 0) {
    return (
      <div
        id="openings"
        className="w-full flex flex-col items-center px-4 md:px-0 mt-8 mb-16"
      >
        <h2 className="text-4xl font-semibold mb-7 text-center">
          {props.title || "Job Openings"}
        </h2>
        <div className="text-center text-gray-600 text-lg">
          No job openings available at the moment. Please check back later!
        </div>
      </div>
    );
  }

  return (
    <div
      id="openings"
      className="w-full flex flex-col items-center px-4 md:px-0 mt-8 mb-16"
    >
      <h2 className="text-4xl font-semibold mb-7 text-center">{props.title}</h2>
      <div className="flex flex-col gap-6 w-full max-w-6xl">
        {paginatedOpenings.map((job, index) => (
          <div
            key={index}
            className="border border-blue-100 rounded-2xl p-6 bg-white flex flex-col md:flex-row md:items-center shadow-sm hover:shadow-md transition-shadow relative gap-4 md:gap-8"
          >
            <div className="flex flex-col justify-center flex-1 min-w-0 gap-2">
              {/* Time since posted */}
              <span className="inline-block bg-blue-100 text-blue-700 text-xs font-medium px-5 py-1.5 rounded-[10px] w-fit">
                {job.postedDate.toString()}
              </span>
              {/* Job title */}
              <div className="text-2xl font-semibold truncate">
                {job.jobTitle}
              </div>
              {/* Job details */}
              <div className="flex flex-wrap gap-4 md:gap-6 text-gray-600 text-base mt-1 justify-between">
                <div className="w-full md:w-auto flex justify-between md:gap-6">
                  <span className="flex flex-col md:flex-row justify-center text-sm items-center gap-2">
                    <BriefcaseIcon className="w-5 h-5 text-green-500" />
                    {job.department}
                  </span>
                  <span className="flex flex-col md:flex-row justify-center text-sm items-center gap-2">
                    <ClockIcon className="w-5 h-5 text-green-500" />
                    {job.type}
                  </span>
                  <span className="flex flex-col md:flex-row justify-center text-sm items-center gap-2">
                    <MapPinIcon className="w-5 h-5 text-green-500" />
                    {job.location}
                  </span>
                </div>
                <div className="flex justify-end w-full md:w-auto">
                  <button
                    className="bg-[#1976D2] hover:bg-[#1565C0] text-white font-semibold px-8 py-2 rounded-xl transition-colors shadow-md whitespace-nowrap self-end md:self-center md:ml-4 mt-4 md:mt-0 w-full md:w-auto"
                    onClick={() => window.open(job.link, "_blank")}
                  >
                    Job Details
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center gap-4 mt-8">
          <button
            className={`px-6 py-2 rounded-xl font-semibold transition-colors shadow-md
              ${
                page === 1
                  ? "bg-blue-100 text-blue-400 cursor-not-allowed opacity-60"
                  : "bg-[#1976D2] hover:bg-[#1565C0] text-white"
              }
            `}
            onClick={() => setPage((p) => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            Previous
          </button>
          <span className="text-gray-700 text-base">
            {jobItems.length === 0
              ? "0"
              : `${(page - 1) * PAGE_SIZE + 1}-${Math.min(
                  page * PAGE_SIZE,
                  jobItems.length
                )} of ${jobItems.length}`}
          </span>
          <button
            className={`px-10 py-2 rounded-xl font-semibold transition-colors shadow-md
              ${
                page === totalPages
                  ? "bg-blue-100 text-blue-400 cursor-not-allowed opacity-60"
                  : "bg-[#1976D2] hover:bg-[#1565C0] text-white"
              }
            `}
            onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
            disabled={page === totalPages}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

export default Openings;
