import React, { useState, useRef } from "react";

const Team = (props: {
  title: string;
  subtitle: string;
  images: {
    data: {
      id: number;
      attributes: {
        url: string;
      };
    }[];
  };
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Handle scroll to update active dot
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    const scrollPosition = container.scrollLeft;
    const maxScroll = container.scrollWidth - container.clientWidth;

    // Calculate what percentage of the total scroll we've moved
    const scrollPercentage = scrollPosition / maxScroll;

    // Calculate which slide should be active based on the scroll percentage
    const newActiveIndex = Math.min(
      Math.round(scrollPercentage * (props.images.data.length - 1)),
      props.images.data.length - 1
    );

    setActiveIndex(newActiveIndex);
  };

  return (
    <div className="flex flex-col items-center max-w-[1240px] mx-auto px-4 md:px-0 mb-10">
      {/* Title */}
      <h2 className="text-4xl font-semibold text-center mb-2 mt-4 md:mt-0">
        {props.title}
      </h2>
      {/* Subtitle */}
      <p className="text-lg text-center mb-5 text-gray-700">{props.subtitle}</p>
      {/* Cards */}
      <div className="flex flex-col md:flex-row justify-center items-center gap-8 w-full md:mb-8 mb-4">
        <div
          ref={scrollRef}
          className="flex overflow-x-auto md:overflow-x-auto scrollbar-hide w-full md:w-full md:gap-8 gap-4 pb-4 md:pb-0 snap-x snap-mandatory"
          style={{ scrollSnapType: "x mandatory" }}
          onScroll={handleScroll}
        >
          {props.images.data.map((img, i) => (
            <div
              key={i}
              className="bg-white rounded-2xl shadow-lg overflow-hidden flex-shrink-0 w-[320px] md:w-[320px] snap-start"
            >
              <img
                src={img.attributes.url}
                alt={`Team ${i + 1}`}
                className="w-full h-72 object-cover object-center"
              />
            </div>
          ))}
        </div>
      </div>
      {/* Carousel Dots */}
      <div className="flex items-center justify-center gap-3">
        {props.images.data.map((_, i) => (
          <span
            key={i}
            className={`h-3 w-3 rounded-full border border-gray-300 transition-all duration-200 ${
              i === activeIndex ? "bg-gray-400 border-gray-500" : "bg-gray-200"
            }`}
          />
        ))}
      </div>
    </div>
  );
};

export default Team;
