import React, { useRef, useState } from "react";
import Image from "next/image";
import { FaQuoteLeft, FaQuoteRight } from "react-icons/fa";

const Testimonial = (props: { title: string; testimonial: any[] }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Handle scroll to update active dot
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    const scrollPosition = container.scrollLeft;
    const maxScroll = container.scrollWidth - container.clientWidth;

    // Calculate what percentage of the total scroll we've moved
    const scrollPercentage = scrollPosition / maxScroll;

    // Calculate which slide should be active based on the scroll percentage
    const newActiveIndex = Math.min(
      Math.round(scrollPercentage * (props.testimonial.length - 1)),
      props.testimonial.length - 1
    );

    setActiveIndex(newActiveIndex);
  };

  return (
    <div className="relative md:max-w-[1240px] mx-auto text-center md:mb-10 mb-5 w-full flex flex-col bg-gradient-to-r from-primary-blue-3 to-primary-green-2 rounded-lg md:rounded-xl text-white opacity-80 overflow-hidden px-2 md:px-0 py-4">
      {/* Top right background SVG */}
      <div className="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 pointer-events-none select-none z-0">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={570}
          height={570}
          className="hidden md:block"
        />
      </div>
      {/* Bottom left background SVG */}
      <div className="absolute bottom-24 left-0 transform -translate-x-1/2 translate-y-1/2 pointer-events-none select-none z-0">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={524}
          height={524}
        />
      </div>
      <h2 className="md:text-3xl text-2xl font-medium z-10 px-4 text-white">
        {props.title}
      </h2>
      {/* Carousel */}
      <div className="relative w-full z-10 mt-3">
        <div
          ref={scrollRef}
          className="flex gap-8 overflow-x-auto scrollbar-hide px-4 md:px-8 py-4 snap-x snap-mandatory"
          style={{ scrollSnapType: "x mandatory" }}
          onScroll={handleScroll}
        >
          {props.testimonial.map((t: any, idx: number) => (
            <div
              key={idx}
              className="bg-white rounded-2xl shadow-lg min-w-[300px] max-w-[320px] w-full flex flex-col justify-between px-6 py-4 mx-auto"
              style={{ color: "#222" }}
            >
              <div className="flex flex-col h-full justify-between">
                <div className="flex flex-col items-start">
                  <FaQuoteLeft className="text-3xl text-primary-blue-3 font-bold" />
                  <p className="text-sm font-normal text-left">{t.statement}</p>
                  <FaQuoteRight className="text-3xl text-primary-green-2 font-bold self-end" />
                </div>
                <div className="text-left">
                  <span className="font-semibold text-base">{t.name}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
        {/* Dots */}
        <div className="flex justify-center gap-2.5 py-1">
          {props.testimonial.map((_, idx) => (
            <div
              key={idx}
              className={`w-3 h-3 rounded-full transition-colors duration-300 ${
                idx === activeIndex ? "bg-primary-green-2" : "bg-white/60"
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Testimonial;
