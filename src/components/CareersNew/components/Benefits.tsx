import React from "react";

export default function Benefits(props: {
  title: string;
  benefitItems: {
    title: string;
    subtitle: string;
    icon: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
  }[];
}) {
  return (
    <div className="max-w-5xl mx-auto px-4 mb-16">
      <h2 className="text-center text-3xl md:text-4xl font-semibold md:mb-10 mb-5 text-gray-900 tracking-tight">
        {props.title}
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8">
        {props.benefitItems && props.benefitItems.length > 0 ? (
          props.benefitItems.map((card, index) => {
            const row = Math.floor(index / 2);
            const col = index % 2;

            // For first row: blue-green
            // For second row: green-blue
            const isBlue = row % 2 === 0 ? col === 0 : col === 1;
            return (
              <div
                key={index}
                className={`rounded-2xl shadow-md px-10 py-8 flex flex-row items-center justify-between ${
                  isBlue ? "bg-[#89C2E3]" : "bg-[#8ECBC0]"
                }`}
              >
                <div className="max-w-md">
                  <div className="text-xl md:text-2xl font-semibold text-white mb-1">
                    {card.title}
                  </div>
                  <div className="text-sm md:text-base text-white font-normal leading-relaxed">
                    {card.subtitle}
                  </div>
                </div>
                <img
                  src={card.icon.data.attributes.url}
                  alt={card.title}
                  className="w-20 h-20 text-white ml-6 flex-shrink-0"
                />
              </div>
            );
          })
        ) : (
          <div className="col-span-2 text-center text-gray-500 py-8">
            No benefit items found
          </div>
        )}
      </div>
    </div>
  );
}
