import React from "react";

const Hero = (props: { title: string; subtitle: string }) => {
  const handleJoinUsClick = () => {
    const openingsElement = document.getElementById("openings");
    if (openingsElement) {
      openingsElement.scrollIntoView({ behavior: "smooth" });
    }
  };

  // Split title into words and separate first 4 words from the rest
  const words = props.title.split(" ");
  const firstFourWords = words.slice(0, 4).join(" ");
  const remainingWords = words.slice(4).join(" ");

  return (
    <section className="w-full max-w-[1240px] mx-auto flex flex-col items-center justify-center pt-10 md:pt-16 md:pb-4 bg-white relative overflow-hidden">
      <h1 className="text-3xl md:text-5xl font-semibold text-center text-black mb-2 md:mb-4">
        {firstFourWords}
      </h1>
      {remainingWords && (
        <h2 className="text-3xl md:text-5xl font-semibold text-center mb-6 bg-gradient-to-r from-teal-400 to-blue-400 bg-clip-text text-transparent">
          {remainingWords}
        </h2>
      )}
      <p className="text-base md:text-lg text-center text-black">
        {props.subtitle}
      </p>

      <button
        onClick={handleJoinUsClick}
        className="bg-[#1A191B] text-white rounded-full px-20 py-3 text-xl font-medium shadow-lg my-8 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition"
      >
        Join Us
      </button>
    </section>
  );
};

export default Hero;
