import React from "react";
import parse from "html-react-parser";

const Hero = (props: { title: string; subtitle: string }) => {
  return (
    <section className="flex flex-col items-center justify-center pt-10 md:pt-16 pb-10 md:pb-16 bg-white">
      <h1 className="text-4xl md:text-5xl font-bold text-center text-black mb-4">
        {props.title}
      </h1>
      <p className="text-lg md:text-xl text-center text-black font-normal">
        {parse(props.subtitle)}
      </p>
    </section>
  );
};

export default Hero;
