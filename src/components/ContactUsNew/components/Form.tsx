"use client";
import React from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useCreateLead } from "@/components/LeadModal/api/createLead";
import { usePathname } from "next/navigation";

const Form = (props: { title: string; subtitle: string }) => {
  const { mutate } = useCreateLead();
  const pathname = usePathname();

  const LeadSchema = Yup.object().shape({
    lead_name: Yup.string()
      .matches(/^[A-Za-z ]*$/, "Please enter valid name")
      .required("Required"),
    lead_email: Yup.string().email("Invalid email").required("Required"),
    lead_phone_number: Yup.string()
      .length(10, "Invalid phone number")
      .matches(
        /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/,
        "Phone number is not valid"
      )
      .required("Required"),
    lead_message: Yup.string().trim().required("Required"),
  });

  const formik = useFormik({
    initialValues: {
      lead_name: "",
      lead_email: "",
      lead_phone_number: "",
      lead_message: "",
    },
    validationSchema: LeadSchema,
    onSubmit: (values, { resetForm }) => {
      const { lead_name, lead_phone_number, ...restObj } = values;

      mutate(
        {
          lead_name,
          lead_phone_number,
          lead_point: pathname,
          ...restObj,
        },
        {
          onSuccess: () => {
            alert("Your request is submitted! We will get back to you soon");
            resetForm();
          },
        }
      );
    },
  });

  return (
    <div className="flex flex-col md:flex-row gap-6 md:gap-12 items-center border-2 border-[#3DBAAB] rounded-2xl px-8 md:px-12 py-6 bg-white w-full max-w-full box-border mb-10">
      {/* Left Side */}
      <div
        className={`flex-1 flex flex-col items-center md:items-start justify-center px-0 md:px-4 md:mb-8 ${
          props.subtitle ? "" : "justify-center"
        }`}
      >
        <h1
          className={`
            ${
              props.subtitle && props.subtitle.trim() !== ""
                ? "text-2xl md:text-4xl font-medium bg-gradient-to-r from-[#2889C8] to-[#3DBAAB] bg-clip-text text-transparent text-center md:text-left mb-2"
                : "text-xl md:text-5xl max-w-[300px] font-medium bg-gradient-to-r from-[#2889C8] to-[#3DBAAB] bg-clip-text text-transparent text-left mb-0 leading-4"
            }
            m-0
          `}
          style={{ fontFamily: "inherit" }}
        >
          {(() => {
            const title = props.title;
            const words = title.split(" ");
            if (
              props.subtitle &&
              props.subtitle.trim() !== "" &&
              words.length > 4
            ) {
              return (
                <>
                  {words.slice(0, 4).join(" ")}
                  <br />
                  {words.slice(4).join(" ")}
                </>
              );
            }
            return title;
          })()}
        </h1>
        {props.subtitle && props.subtitle.trim() !== "" && (
          <p className="text-base md:text-lg text-center md:text-left">
            {props.subtitle}
          </p>
        )}
      </div>
      {/* Right Side (Form) */}
      <form
        onSubmit={formik.handleSubmit}
        className="flex flex-col gap-5 flex-[1.2] max-w-[520px] w-full md:max-w-full"
      >
        <div>
          <input
            className="h-14 border border-[#2889C8] rounded-xl px-8 text-lg outline-none focus:border-cyan-400 transition placeholder:text-gray-400 w-full"
            type="text"
            name="lead_name"
            placeholder="Enter Name"
            onChange={formik.handleChange}
            value={formik.values.lead_name}
          />
          {formik.touched.lead_name && formik.errors.lead_name && (
            <p className="text-sm text-red-600 mt-1">
              {formik.errors.lead_name}
            </p>
          )}
        </div>
        <div>
          <input
            className="h-14 border border-[#2889C8] rounded-xl px-8 text-lg outline-none focus:border-cyan-400 transition placeholder:text-gray-400 w-full"
            type="text"
            name="lead_phone_number"
            placeholder="Enter Phone No."
            onChange={formik.handleChange}
            value={formik.values.lead_phone_number}
          />
          {formik.touched.lead_phone_number &&
            formik.errors.lead_phone_number && (
              <p className="text-sm text-red-600 mt-1">
                {formik.errors.lead_phone_number}
              </p>
            )}
        </div>
        <div>
          <input
            className="h-14 border border-[#2889C8] rounded-xl px-8 text-lg outline-none focus:border-cyan-400 transition placeholder:text-gray-400 w-full"
            type="email"
            name="lead_email"
            placeholder="Enter Mail"
            onChange={formik.handleChange}
            value={formik.values.lead_email}
          />
          {formik.touched.lead_email && formik.errors.lead_email && (
            <p className="text-sm text-red-600 mt-1">
              {formik.errors.lead_email}
            </p>
          )}
        </div>
        <div>
          <textarea
            className="min-h-[75px] border border-[#2889C8] rounded-xl px-8 pt-4 text-lg outline-none resize-none focus:border-cyan-400 transition placeholder:text-gray-400 w-full"
            name="lead_message"
            placeholder="Your Query"
            onChange={formik.handleChange}
            value={formik.values.lead_message}
          />
          {formik.touched.lead_message && formik.errors.lead_message && (
            <p className="text-sm text-red-600">{formik.errors.lead_message}</p>
          )}
        </div>
        <div className="flex justify-center">
          <button
            className="h-12 px-[6rem] rounded-xl bg-gradient-to-r from-[#2889C8] to-[#3DBAAB] text-white text-xl font-semibold shadow-[0_4px_8px_0_rgba(61,182,209,0.15)] transition hover:from-[#1e90c7] hover:to-[#3db6d1]"
            type="submit"
          >
            Submit
          </button>
        </div>
      </form>
    </div>
  );
};

export default Form;
