import Hero from "./components/Hero";
import Form from "./components/Form";
import ContactTiles from "./components/ContactTiles";

const heroProps = {
  title: "Get in Touch With Our Insurance Experts",
  subtitle: "",
};

const ContactUsNew = (props: { data: any }) => {
  return (
    <div className="bg-white max-w-[1240px] mx-auto px-4 md:px-0">
      <Hero title={props.data.hero.title} subtitle={props.data.hero.subtitle} />
      <Form {...heroProps} />
      <ContactTiles contactCards={props.data.contactCards} />
    </div>
  );
};

export default ContactUsNew;
