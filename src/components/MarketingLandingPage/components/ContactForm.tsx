// @ts-nocheck
"use client";

import { useFormik } from "formik";
import * as Yup from "yup";
import { useOtp } from "../api/postOtp";
import { useState } from "react";
import Otp from "./Otp";
import Image from "next/image";

const ContactForm = ({ source }: { source: string }) => {
  const { mutate: mutateOtp, isPending: otpIsPending, error } = useOtp();
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [formValues, setFormValues] = useState({});

  const leadType = [
    {
      label: "Beginner",
      value: "beginner",
    },
    {
      label: "Intermediate",
      value: "intermediate",
    },
  ];

  const MasterclassLeadSchema = Yup.object().shape({
    lead_name: Yup.string()
      .matches(/^[A-Za-z ]*$/, "Please enter valid name")
      .required("Required"),
    lead_phone_number: Yup.string()
      .length(10, "Invalid phone number")
      .matches(
        /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/,
        "Phone number is not valid"
      )
      .required("Required"),
    lead_email: Yup.string()
      .trim()
      .email("Invalid email address")
      .required("Required"),
    // lead_message: Yup.string().trim().required("Required"),
    lead_type: Yup.string().required("Required"),
    lead_pincode: Yup.string()
      .matches(
        /^[1-9][0-9]{5}$/,
        "Pincode must be a valid 6-digit Indian pincode"
      )
      .required("Pincode is required"),
    lead_city: Yup.string().required("City is required"),
    // lead_dob: Yup.date().required("DOB is required"),
  });

  const AdLeadSchema = Yup.object().shape({
    lead_name: Yup.string()
      .matches(/^[A-Za-z ]*$/, "Please enter valid name")
      .required("Required"),
    lead_phone_number: Yup.string()
      .length(10, "Invalid phone number")
      .matches(
        /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/,
        "Phone number is not valid"
      )
      .required("Required"),
  });

  const MasterClassInitialValue = {
    lead_name: "",
    lead_phone_number: "",
    lead_email: "",
    // lead_message: "",
    lead_type: "",
    lead_pincode: "",
    lead_city: "",
    // lead_dob: "",
  };

  const AdInitialValue = {
    lead_name: "",
    lead_phone_number: "",
  };

  const formik = useFormik({
    initialValues:
      source === "fb-campaign" ? AdInitialValue : MasterClassInitialValue,
    validationSchema:
      source === "fb-campaign" ? AdLeadSchema : MasterclassLeadSchema,
    onSubmit: (values, { resetForm }) => {
      // console.log("onSubmit running...");
      const { lead_name, lead_phone_number, ...restObj } = values;
      setFormValues(values);

      const body = {
        phone_number: parseInt(lead_phone_number),
        otp_use_case: "landing-page-phone-number-verification",
      };
      // @ts-ignore
      mutateOtp(body, {
        onSuccess: (data) => {
          setShowOtpInput(true);
        },
      });
    },
  });

  return (
    <div className="">
      {!showOtpInput ? (
        <form
          onSubmit={formik.handleSubmit}
          className="text-[16px]/[24px] text-ntrl-grey1"
        >
          <div className="grid grid-cols-1 gap-6">
            <div className="mb-6">
              <label className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-black font-semibold">
                Full Name <span className="text-red-1">*</span>
              </label>
              <input
                type="text"
                name="lead_name"
                id="lead_name"
                onChange={formik.handleChange}
                value={formik.values.lead_name}
                className=" w-full py-3 px-5 rounded-lg"
                placeholder="Full name"
              />
              {formik.touched.lead_name && formik.errors.lead_name && (
                <p className=" text-sm text-red600">
                  {formik.errors.lead_name}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-6">
            <div className="">
              <label className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-black font-semibold">
                Whatsapp No.<span className="text-red-1">*</span>
              </label>
              <input
                type="text"
                name="lead_phone_number"
                id="lead_phone_number"
                onChange={formik.handleChange}
                value={formik.values.lead_phone_number}
                className="w-full py-3 px-5 rounded-lg"
                placeholder="Whatsapp no."
              />
              {formik.touched.lead_phone_number &&
                formik.errors.lead_phone_number && (
                  <p className=" text-sm text-red600">
                    {formik.errors.lead_phone_number}
                  </p>
                )}
            </div>

            {source !== "fb-campaign" && (
              <div className="">
                <label className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-black font-semibold">
                  Email <span className="text-red-1">*</span>
                </label>
                <input
                  type="text"
                  name="lead_email"
                  id="lead_email"
                  onChange={formik.handleChange}
                  // @ts-ignore
                  value={formik.values.lead_email}
                  className="w-full py-3 px-5 rounded-lg"
                  placeholder="Email"
                />
                {/* @ts-ignore */}
                {formik.touched.lead_email && formik.errors.lead_email && (
                  <p className=" text-sm text-red600">
                    {/* @ts-ignore */}
                    {formik.errors.lead_email}
                  </p>
                )}
              </div>
            )}

            {source !== "fb-campaign" && (
              <>
                <div className="">
                  <label className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-black font-semibold">
                    Pincode <span className="text-red-1">*</span>
                  </label>
                  <input
                    type="text"
                    name="lead_pincode"
                    id="lead_pincode"
                    onChange={formik.handleChange}
                    // @ts-ignore
                    value={formik.values.lead_pincode}
                    className="w-full py-3 px-5 rounded-lg"
                    placeholder="Pincode"
                  />

                  {/* @ts-ignore */}
                  {formik.touched.lead_pincode &&
                    formik.errors.lead_pincode && (
                      <p className=" text-sm text-red600">
                        {/* @ts-ignore */}
                        {formik.errors.lead_pincode}
                      </p>
                    )}
                </div>

                <div className="">
                  <label className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-black font-semibold">
                    City <span className="text-red-1">*</span>
                  </label>
                  <input
                    type="text"
                    name="lead_city"
                    id="lead_city"
                    onChange={formik.handleChange}
                    // @ts-ignore
                    value={formik.values.lead_city}
                    className="w-full py-3 px-5 rounded-lg"
                    placeholder="City"
                  />

                  {/* @ts-ignore */}
                  {formik.touched.lead_city && formik.errors.lead_city && (
                    <p className=" text-sm text-red600">
                      {/* @ts-ignore */}
                      {formik.errors.lead_city}
                    </p>
                  )}
                </div>
              </>
            )}

            <div className=" mb-6">
              <label className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-black font-semibold">
                Which Cohort would you like to join?
                <span className="text-red-1">*</span>
              </label>
              <div className="grid grid-cols-3">
                {leadType.map((type, idx) => (
                  <label key={idx} className="flex items-center space-x-2 my-2">
                    <input
                      type="radio"
                      name="lead_type"
                      value={type.value}
                      // @ts-ignore
                      checked={formik.values.lead_contact_type === type.value}
                      onChange={formik.handleChange}
                      className="form-radio"
                    />
                    <span>{type.label}</span>
                  </label>
                ))}
              </div>

              {formik.touched.lead_type && formik.errors.lead_type && (
                <p className=" text-sm text-red600">
                  {formik.errors.lead_type}
                </p>
              )}
            </div>
          </div>

          {/* <div className=" mb-6">
            <label className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-black font-semibold">
              Detailed Query <span className="text-red-1">*</span>
            </label>
            <textarea
              name="lead_message"
              id="lead_message"
              onChange={formik.handleChange}
              value={formik.values.lead_message}
              className="w-full py-3 px-5 rounded-lg"
              placeholder="Message"
            />
            {formik.touched.lead_message && formik.errors.lead_message && (
              <p className=" text-sm text-red600">
                {formik.errors.lead_message}
              </p>
            )}
          </div> */}

          <div className="flex items-center justify-center md:items-start md:justify-start">
            <button
              className="py-3 px-20 bg-primary-1 rounded-xl text-[16px]/[24px] text-ntrl-white font-semibold  border-2 cursor-pointer mt-5"
              type="submit"
            >
              {source === "masterclass-campaign"
                ? "Continue"
                : "Book a free call"}
            </button>
          </div>
        </form>
      ) : (
        <Otp
          formValues={formValues}
          setShowConfirmation={setShowConfirmation}
          showConfirmation={showConfirmation}
          source={source}
        />
      )}

      {showConfirmation && source !== "fb-campaign" && (
        <Image
          src="https://cdn.oasr.in/oa-site/cms-uploads/media/Registration_Successful_b167eda96f.svg"
          alt="registered"
          fill={true}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      )}

      {showConfirmation && source === "fb-campaign" && (
        <Image
          src="https://cdn.oasr.in/oa-site/cms-uploads/media/Book_A_Call_550d16da7e.svg"
          alt="registered"
          fill={true}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        // <h2 className="font-generalSans font-medium text-[48px]/[58px] text-primary-1 my-4 text-center md:text-left">
        //   Thank you for contacting us!
        // </h2>
      )}
    </div>
  );
};

export default ContactForm;
