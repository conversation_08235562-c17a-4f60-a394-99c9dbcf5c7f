import { Dispatch, SetStateAction, useState } from "react";
import { useVerifyOtp } from "../api/postVerifyOtp";
import { useCreateLead } from "@/components/LeadModal/api/createLead";
import { usePathname } from "next/navigation";
import { useOtp } from "../api/postOtp";
import { useEffect } from "react";

const Otp = ({
  formValues,
  setShowConfirmation,
  showConfirmation = false,
  source,
  handleState = () => {},
}: {
  formValues: any;
  setShowConfirmation?: Dispatch<SetStateAction<boolean>>;
  showConfirmation?: boolean;
  source: string;
  handleState?: () => void;
}) => {
  const pathname = usePathname();
  const [isClickable, setIsClickable] = useState(false);
  const [timer, setTimer] = useState(120); // 2 minutes = 120 seconds
  const [otp, setOtp] = useState("");
  const [otpError, setOtpError] = useState("");
  const { mutate, isPending: otpVerificationIsPending, error } = useVerifyOtp();
  const { mutate: mutateOtp } = useOtp();
  const {
    mutateAsync,
    isPending,
    isSuccess,
    mutate: mutateLead,
  } = useCreateLead();

  useEffect(() => {
    if (timer > 0) {
      const countdown = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);

      return () => clearInterval(countdown); // Cleanup interval on component unmount
    } else {
      setIsClickable(true); // Enable the button once the timer runs out
    }
  }, [timer]);

  const handleValidatorsBeforeLogin = () => {
    if (otp.length === 4) {
      return true;
    }
    return false;
  };

  const handleVerifyOtp = () => {
    const otpValidator = handleValidatorsBeforeLogin();
    if (otpValidator) {
      const body = {
        phone_number: formValues.lead_phone_number,
        user_otp: otp,
        otp_use_case: "landing-page-phone-number-verification",
      };

      // @ts-ignore
      mutate(body, {
        onSuccess: (data) => {
          const { lead_name, lead_phone_number, ...restObj } = formValues;
          // SUMBIT THE FORM RESPONSE
          mutateLead(
            {
              lead_name,
              lead_phone_number,
              source: source,
              lead_slug: pathname,
              lead_point: pathname,
              ...restObj,
            },
            {
              onSuccess: () => {
                if (setShowConfirmation) {
                  setShowConfirmation(true);
                }
                setOtp("");
                handleState();
                // resetForm();
                // Push the new value to the GTM dataLayer
                if (source === "fb-campaign") {
                  window.dataLayer = window.dataLayer || [];
                  window.dataLayer.push({
                    event: "adLeadConverted", // Custom event name
                  });
                } else {
                  window.dataLayer = window.dataLayer || [];
                  window.dataLayer.push({
                    event: "leadConverted", // Custom event name
                  });
                }
              },
            }
          );
        },
        onError: () => {
          setOtpError("Incorrect OTP");
        },
      });
    } else {
      setOtpError("OTP must be 4 digits");
    }
  };

  const handleResendOtp = () => {
    setOtp("");
    if (isClickable) {
      setIsClickable(false); // Disable the button again
      setTimer(120); // Reset the timer
    }
    // setOtpError('');
    const body = {
      phone_number: parseInt(formValues.lead_phone_number),
      otp_use_case: "landing-page-phone-number-verification",
    };

    //   @ts-ignore
    mutateOtp(body, {
      onSuccess: () => {
        alert("OTP has been resent successfully!");
      },
    });
  };

  return (
    <div className={`md:pt-5 ${showConfirmation && "hidden"}`}>
      {/* <pre>{JSON.stringify(otp, null, 2)}</pre> */}
      <div className="mb-6">
        <label className="md:text-[18px]/[28px] text-[16px]/[24px] text-center text-ntrl-black font-semibold">
          Enter otp here <span className="text-red-1">*</span>
        </label>
        <input
          type="text"
          name="lead_name"
          id="lead_name"
          maxLength={6}
          onChange={(e) => {
            setOtpError("");
            setOtp(e.target.value);
          }}
          value={otp}
          className="w-full py-3 px-4 rounded-xl border border-none bg-gray1300 mt-4"
          placeholder="Otp"
        />
        {otpError !== "" && <p className=" text-sm text-red600">{otpError}</p>}
      </div>
      <button
        className="py-2 px-10 bg-black rounded-xl text-[16px]/[24px] text-ntrl-white font-semibold mr-10 border-none cursor-pointer mb-4"
        type="submit"
        onClick={handleVerifyOtp}
      >
        Submit
      </button>
      <div className="flex flex-col items-start gap-4">
        {/* eslint-disable-next-line react/no-unescaped-entities */}
        <p className="text-base mr-1">Didn't received OTP?</p>

        <p
          className={`text-base font-semibold ${
            isClickable
              ? "cursor-pointer text-secondary-1"
              : "cursor-not-allowed text-ntrl-grey1"
          }`}
          onClick={isClickable ? handleResendOtp : () => {}}
        >
          Resend OTP
          {!isClickable && <span>{` (after ${timer}s)`}</span>}
        </p>
      </div>
    </div>
  );
};

export default Otp;
