// components/RegisterButton.js
import { useEffect, useState } from "react";
import Link from "next/link";

const RegisterButton = ({ contactType }: { contactType: string }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const heroSection = document.querySelector("#lp-hero");

    const observer = new IntersectionObserver(
      ([entry]) => {
        // Button is invisible when the hero section is visible
        setIsVisible(!entry.isIntersecting);
      },
      { threshold: 0.5 } // Trigger visibility when at least 50% of the hero is visible
    );

    if (heroSection) observer.observe(heroSection);

    return () => {
      if (heroSection) observer.unobserve(heroSection);
    };
  }, []);

  return (
    <>
      <Link href="#lp-hero">
        <button
          className={`fixed bottom-5 right-5 py-3 px-20 bg-gradient-to-r from-ntrl-black to-primary-1 rounded-xl text-[16px]/[24px] text-ntrl-white font-semibold shadow-lg transition-opacity duration-300 md:block hidden z-50 ${
            isVisible ? "opacity-100" : "opacity-0"
          }`}
        >
          {contactType === "masterclass" ? "Register Now!" : "Get in touch!"}
        </button>
      </Link>
      <div
        className={`fixed bottom-0 py-5 px-20 bg-gradient-to-r from-primary-3 to-secondary-2 border-t border-ntrl-grey1 text-[16px]/[24px] text-ntrl-white font-semibold shadow-lg transition-opacity duration-300 md:hidden w-full flex items-center justify-center z-50 ${
          isVisible ? "opacity-100" : "opacity-0"
        }`}
      >
        <Link href="#lp-hero">
          <button
            className={`py-3 px-5 bg-gradient-to-r from-ntrl-black to-primary-1 rounded-xl text-[16px]/[24px] text-ntrl-white font-semibold shadow-lg w-full`}
          >
            {contactType === "masterclass" ? "Register Now!" : "Get in touch!"}
          </button>
        </Link>
      </div>
    </>
  );
};

export default RegisterButton;
