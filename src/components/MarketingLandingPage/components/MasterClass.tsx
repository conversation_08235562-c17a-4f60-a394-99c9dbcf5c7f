import { Dispatch, SetStateAction, useEffect, useState } from "react";
import Image from "next/image";
import ContactForm from "./ContactForm";
import { useSearchParams } from "next/navigation";

const MasterClass = ({
  title,
  date,
  time,
  ctaUrl,
  thumbnail,
  actualPrice,
  discountedPrice,
  source,
  setShowRefundPolicy,
}: {
  title: string;
  date: string;
  time: string;
  ctaUrl: string;
  thumbnail: string;
  actualPrice: string;
  discountedPrice: string;
  source: string;
  setShowRefundPolicy: Dispatch<SetStateAction<boolean>>;
}) => {
  const [showForm, setShowForm] = useState(false);
  const searchParams = useSearchParams().toString();
  return (
    <div className="relative min-h-[500px]">
      {/* Image of the instructor */}
      <div
        className={`w-full min-h-[300px] rounded-xl bg-ntrl-black mb-5 relative ${
          showForm ? "hidden" : "block"
        }`}
      >
        <Image
          src={thumbnail}
          alt={title}
          fill={true}
          style={{ objectFit: "cover", borderRadius: "12px" }}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>

      {/* Name of course */}
      <h2
        className={`font-generalSans font-medium text-ntrl-black text-[24px]/[30px] mb-6 text-center md:text-left ${
          showForm ? "hidden" : "block"
        }`}
      >
        {title}
      </h2>

      {/* Date and Time */}
      <div
        className={`rounded-xl bg-ntrl-grey2 p-2 flex items-center gap-5 mb-6 ${
          showForm ? "hidden" : "block"
        }`}
      >
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 relative">
            <Image
              src={
                "https://cdn.oasr.in/oa-site/cms-uploads/media/Calendar_1b99076a1a.svg"
              }
              alt={"date"}
              fill={true}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              style={{ objectFit: "contain" }}
            />
          </div>
          <h3 className="font-generalSans font-medium text-ntrl-grey1 md:text-[24px]/[30px] text-[18px]/[20px]">
            {date}
          </h3>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 relative">
            <Image
              src={
                "https://cdn.oasr.in/oa-site/cms-uploads/media/Clock_8d42585353.svg"
              }
              alt={"date"}
              fill={true}
              style={{ objectFit: "contain" }}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
          <h3 className="font-generalSans font-medium text-ntrl-grey1 md:text-[24px]/[30px] text-[18px]/[20px]">
            {time}
          </h3>
        </div>
      </div>

      {/* Payment CTA */}
      {ctaUrl !== "" && (
        <a
          className="flex items-center justify-center"
          href={`${ctaUrl}?${searchParams}`}
          target="_blank"
        >
          <button className="py-3 px-20 bg-gradient-to-r from-ntrl-black to-primary-1 rounded-xl text-[16px]/[24px] text-ntrl-white font-semibold border-2 cursor-pointer w-full">
            {`Register now @ ₹${discountedPrice} | `}{" "}
            <span className="line-through">{` ₹${actualPrice}`}</span>
          </button>
        </a>
      )}

      {ctaUrl === "" && (
        <>
          <button
            className={`py-3 px-20 bg-gradient-to-r from-ntrl-black to-primary-1 rounded-xl text-[16px]/[24px] text-ntrl-white font-semibold border-2 cursor-pointer w-full ${
              showForm ? "hidden" : "block"
            }`}
            onClick={() => setShowForm(true)}
          >
            {`Register now ! `}
          </button>
          {showForm && <ContactForm source={source} />}
        </>
      )}

      {ctaUrl !== "" && (
        <div className="flex items-center justify-center mt-5">
          <p
            className="text-sm underline text-ntrl-grey1 cursor-pointer"
            onClick={() => setShowRefundPolicy(true)}
          >
            Cancellation and Refund policy
          </p>
        </div>
      )}
    </div>
  );
};

export default MasterClass;
