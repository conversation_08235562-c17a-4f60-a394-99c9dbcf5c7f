"use client";

import Container from "@/components/globals/Container";
import Image from "next/image";
import parse, { attributesToProps } from "html-react-parser";
import { Element } from "html-react-parser";
import { useState } from "react";
import { WhyUsComponent } from "../type";
import ExpertCarousel from "@/components/HealthInsurance/components/ExpertCarousel";

const WhyUs = ({ data }: { data: WhyUsComponent }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black md:mb-4";
          break;

        case "h3":
          attrs.className = "text-primary-1 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-primary-1 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;

        case "table":
          attrs.className =
            "mx-auto font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5 drop-shadow-lg";
          break;

        case "tr":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;
        case "td":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "iframe":
          attrs.className =
            "my-20 mx-auto rounded-xl md:min-w-[1000px] md:min-h-[500px] shadow-md max-w-[350px]";
      }
      return domNode;
    }
  };
  return (
    <div className={`md:py-20 py-10 bg-${data.bgColor}`}>
      <Container>
        <div className="mb-12">
          <div className="font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black mb-4">
            <h2 className="font-normal text-center md:text-left">
              {data.title}
            </h2>
          </div>

          <p className="text-[16px]/[24px] md:text-[18px]/[28px] text-ntrl-grey1 md:w-[50%] text-center md:text-left">
            {data.subTitle}
          </p>
        </div>

        <div className="flex flex-col md:flex-row md:items-start items-center justify-center md:justify-start">
          <div className="rounded-xl min-h-[470px] md:w-[496px] w-[100%] relative border border-ntrl-grey1">
            <Image
              src={data.thumbnail.data.attributes.url}
              fill={true}
              style={{ objectFit: "cover" }}
              alt="about-team-member"
              className="rounded-xl"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>

          <div className="mt-6 ml-0  grid grid-cols-1 gap-7 md:hidden">
            {data.Options.map((expert, idx) => (
              <div
                className="rounded-lg p-5 border border-ntrl-outline bg-ntrl-white mb-6"
                key={idx}
              >
                <div className="w-10 h-10 rounded-full bg-primary-1 flex items-center justify-center mb-6">
                  <Image
                    src={expert.icon.data.attributes.url}
                    width={35}
                    height={35}
                    alt="health-experts"
                  />
                </div>

                <h2 className="font-generalSans font-medium text-ntrl-black text-[24px]/[30px] mb-6">
                  {expert.title}
                </h2>

                {/* @ts-ignore */}
                {parse(expert.description, { replace })}
              </div>
            ))}
          </div>

          <ExpertCarousel data={data.Options} />
        </div>
      </Container>
    </div>
  );
};

export default WhyUs;
