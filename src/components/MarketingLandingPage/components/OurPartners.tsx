// "use client";

// import Container from "@/components/globals/Container";
// import CustomLink from "@/components/globals/CustomLink";
// import { ChevronRightIcon, ChevronLeftIcon } from "@heroicons/react/24/solid";
// import Image from "next/image";
// import { useEffect, useState, useRef } from "react";
// import { Partner } from "../type";

// const OurPartners = ({ partners }: { partners: Partner[] }) => {
//   const utm = "";
//   const [showReadMore, setShowReadMore] = useState(false);
//   const [activePartner, setActivePartner] = useState<number | null>(null);
//   const [isAtStart, setIsAtStart] = useState(true);
//   const [isAtEnd, setIsAtEnd] = useState(false);

//   const checkScrollPosition = () => {
//     if (scrollRef.current) {
//       const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
//       setIsAtStart(scrollLeft === 0);
//       setIsAtEnd(scrollLeft + clientWidth >= scrollWidth);
//     }
//   };

//   const scrollRef = useRef(null);

//   const scrollLeft = () => {
//     if (scrollRef.current) {
//       // @ts-ignore
//       scrollRef.current.scrollBy({ left: -300, behavior: "smooth" });
//     }
//   };

//   const scrollRight = () => {
//     if (scrollRef.current) {
//       // @ts-ignore
//       scrollRef.current.scrollBy({ left: 300, behavior: "smooth" });
//     }
//   };

//   useEffect(() => {
//     checkScrollPosition();
//     if (scrollRef.current) {
//       // @ts-ignore
//       scrollRef.current.addEventListener("scroll", checkScrollPosition);
//     }

//     return () => {
//       if (scrollRef.current) {
//         // @ts-ignore
//         scrollRef.current.removeEventListener("scroll", checkScrollPosition);
//       }
//     };
//   }, []);

//   return (
//     <section className="bg-primary-3 md:py-20 py-10" id="features">
//       <Container>
//         <div>
//           {/* Heading */}
//           <div className="md:mb-12 mb-6">
//             <div className="font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black flex md:flex-row flex-col md:items-center items-start gap-2">
//               <h2 className="font-normal">Other Trusted</h2>
//               <h2 className="font-medium">Partners</h2>
//             </div>
//           </div>

//           {/* companies */}
//           <div
//             className="flex items-center gap-3 overflow-x-scroll no-scrollbar"
//             ref={scrollRef}
//           >
//             {/* @ts-ignore */}
//             {partners.map((comp, idx) => (
//               <CustomLink key={idx} href={comp.url} utm={utm}>
//                 <div
//                   className="border border-ntrl-grey2 bg-primary-2 mr-3 rounded-xl h-28 md:h-44"
//                   onMouseEnter={() => {
//                     setShowReadMore(true);
//                     setActivePartner(idx);
//                   }}
//                   onMouseLeave={() => {
//                     setShowReadMore(false);
//                     setActivePartner(null);
//                   }}
//                 >
//                   <div
//                     className={`px-10 md:py-12 rounded-xl bg-ntrl-white flex items-center justify-center min-w-[223px] ${
//                       showReadMore && idx === activePartner
//                         ? "h-[100%] md:h-[70%]"
//                         : "h-[100%]"
//                     }`}
//                   >
//                     <Image
//                       src={comp.logo.data.attributes.url}
//                       // fill={true}
//                       // style={{ objectFit: "contain" }}
//                       width={87}
//                       height={56}
//                       alt={comp.name}
//                       className="block md:block"
//                     />
//                   </div>

//                   {showReadMore && idx === activePartner && (
//                     <div className="rounded-b-xl text-ntrl-white px-10 py-[14px]  items-center justify-center font-generalSans text-[16px/[24px] font-medium hidden md:flex">
//                       Read more
//                     </div>
//                   )}
//                 </div>
//               </CustomLink>
//             ))}
//           </div>

//           <div className="flex gap-5 mt-5">
//             <button
//               onClick={scrollLeft}
//               className="border-2 border-black rounded-full bg-ntrl-white p-4 disabled:border-ntrl-grey1"
//               disabled={isAtStart}
//             >
//               <ChevronLeftIcon
//                 className={`w-5 h-5 ${
//                   isAtStart ? "text-ntrl-grey1" : "text-ntrl-black"
//                 } font-bold`}
//               />
//             </button>
//             <button
//               onClick={scrollRight}
//               className="border-2 border-black rounded-full bg-ntrl-white p-4 disabled:border-ntrl-grey1"
//               disabled={isAtEnd}
//             >
//               <ChevronRightIcon
//                 className={`w-5 h-5 ${
//                   isAtEnd ? "text-ntrl-grey1" : "text-ntrl-black"
//                 } font-bold`}
//               />
//             </button>
//           </div>
//         </div>
//       </Container>
//     </section>
//   );
// };

// export default OurPartners;
