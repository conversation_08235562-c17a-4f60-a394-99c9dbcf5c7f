import { Dialog } from "@headlessui/react";
import { Dispatch, SetStateAction } from "react";
import parse, { attributesToProps } from "html-react-parser";
import { Element } from "html-react-parser";

const RefundPolicy = ({
  showRefundPolicy,
  setShowRefundPolicy,
  description,
}: {
  showRefundPolicy: boolean;
  setShowRefundPolicy: Dispatch<SetStateAction<boolean>>;
  description: string;
}) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black md:mb-4";
          break;

        case "h3":
          attrs.className = "text-ntrl-grey1 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-grey1 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;

        case "li":
          attrs.className =
            "text-ntrl-grey1 text-base relative before:content-['•'] before:absolute before:-left-4 before:-top-2 before:text-ntrl-grey1 before:text-4xl ml-4";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "iframe":
          attrs.className =
            "my-20 mx-auto rounded-xl md:min-w-[1000px] md:min-h-[500px] shadow-md max-w-[350px]";
      }
      return domNode;
    }
  };
  return (
    <Dialog
      open={showRefundPolicy}
      onClose={() => setShowRefundPolicy(false)}
      className="fixed inset-0 z-[100] overflow-y-auto"
    >
      <div className="flex items-end justify-center min-h-screen pt-4 md:px-4 pb-20 text-center sm:block sm:p-0">
        <Dialog.Overlay className="fixed inset-0 transition-opacity bg-gray-500 opacity-75" />

        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>

        <Dialog.Panel
          className={`inline-block align-bottom bg-white rounded-3xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:w-full  md:max-w-7xl relative`}
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-headline"
        >
          <div className="p-10">
            <h1 className="text-center font-generalSans text-xl md:text-[32px]/[40px] text-ntrl-grey1 font-medium mb-20">
              Cancellation and Refund Policy
            </h1>
            <div className="no-underline">
              {/* @ts-ignore */}
              {parse(description, { replace })}
            </div>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default RefundPolicy;
