// import Container from "@/components/globals/Container";
// import Image from "next/image";
// import { SocialValidation as TSocialValidation } from "../type";

// const SocialValidation = ({ data }: { data: TSocialValidation[] }) => {
//   return (
//     <div className="my-10 md:my-20">
//       <Container>
//         <div className="w-full bg-primary-1 py-6 px-12 rounded-2xl grid md:grid-cols-3 grid-cols-1 gap-6">
//           {data.map((val, idx) => (
//             <div className="flex items-center gap-6" key={idx}>
//               <div className="w-12 h-12 rounded-full bg-ntrl-white flex items-center justify-center">
//                 <Image
//                   src={val.image.data.attributes.url}
//                   width={24}
//                   height={24}
//                   alt="partners"
//                 />
//               </div>
//               <div>
//                 <h2 className="text-ntrl-white font-generalSans font-medium text-[40px]/[40px]">
//                   {val.title.substring(0, val.title.indexOf(" "))}
//                 </h2>
//                 <h3 className="text-ntrl-white font-semibold text-[18px]/[28px]">
//                   {val.title.substring(val.title.indexOf(" ") + 1)}
//                 </h3>
//               </div>
//             </div>
//           ))}
//         </div>
//       </Container>
//     </div>
//   );
// };

// export default SocialValidation;
