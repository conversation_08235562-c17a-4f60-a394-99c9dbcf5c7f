import BookACallBtn from "@/components/globals/BookACall";
import Container from "@/components/globals/Container";
import Image from "next/image";
import ContactForm from "./ContactForm";
import MasterClass from "./MasterClass";
import { HeroComponent } from "../type";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { useSessionStorage } from "usehooks-ts";
const Hero = ({
  data,
  source,
  setShowRefundPolicy,
}: {
  data: HeroComponent;
  source: string;
  setShowRefundPolicy: Dispatch<SetStateAction<boolean>>;
}) => {
  const searchParams = useSearchParams();

  const [utm_source, setUtmSource] = useSessionStorage("utm_source", "");
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", "");
  const [utm_campaign, setUtmCampaign] = useSessionStorage("utm_campaign", "");
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", "");
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", "");

  // Update session storage when UTM parameters are present in URL
  useEffect(() => {
    const urlUtmSource = searchParams.get("utm_source");
    const urlUtmMedium = searchParams.get("utm_medium");
    const urlUtmCampaign = searchParams.get("utm_campaign");
    const urlUtmContent = searchParams.get("utm_content");
    const urlUtmTerm = searchParams.get("utm_term");

    if (urlUtmSource) setUtmSource(urlUtmSource);
    if (urlUtmMedium) setUtmMedium(urlUtmMedium);
    if (urlUtmCampaign) setUtmCampaign(urlUtmCampaign);
    if (urlUtmContent) setUtmContent(urlUtmContent);
    if (urlUtmTerm) setUtmTerm(urlUtmTerm);
  }, [
    searchParams,
    setUtmSource,
    setUtmMedium,
    setUtmCampaign,
    setUtmContent,
    setUtmTerm,
  ]);

  const validations = data.socialValidations
    ? data.socialValidations.validation.data
    : [];
  const partners = data.ourPartners ? data.ourPartners.insurer.data : [];
  return (
    <div id="lp-hero">
      {/* Header */}
      <div className="pt-8 md:py-12 bg-gradient-to-r from-[#EDFCFB] to-[#EBF2FF]">
        <Container>
          <div
            className={`grid md:grid-cols-2 grid-cols-1 gap-[60px] ${
              data.contactForm.formType === "masterclass"
                ? "items-center"
                : "items-start"
            }`}
          >
            {/* headers, social validations and partners */}
            <div
              className={`${
                source === "fb-campaign"
                  ? "row-start-1"
                  : "row-start-2 md:row-start-1"
              }`}
            >
              {/* HEADERS */}
              <div>
                <h2 className="md:text-[24px]/[30px] text-[16px]/[18px] font-generalSans font-medium text-primary-1 text-center md:text-left">
                  {data.subTitle}
                </h2>
                <h1 className="font-generalSans font-medium md:text-[48px]/[58px] text-3xl text-ntrl-black my-4 text-center md:text-left">
                  {data.title}
                </h1>
              </div>

              {/* SOCIAL VALIDATIONS */}
              <div className="grid lg:grid-cols-3 grid-cols-2 gap-[30px] my-10">
                {validations &&
                  validations.map((v, idx) => (
                    <div
                      key={idx}
                      className={`py-1 flex items-center gap-2 ${
                        idx !== validations.length - 1
                          ? "border-r-2 border-ntrl-grey1"
                          : "border-0"
                      }`}
                    >
                      <div className="rounded-full w-14 h-14 bg-ntrl-white relative">
                        <Image
                          src={v.attributes.icon.data.attributes.url}
                          fill={true}
                          style={{ objectFit: "contain" }}
                          alt={v.attributes.title}
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                      </div>
                      <div>
                        <h3 className="text-[24px]/[30px] font-generalSans font-medium text-ntrl-black">
                          {v.attributes.title}
                        </h3>
                        <p className="text-[12px]/[18px] font-generalSans font-medium text-ntrl-grey1">
                          {v.attributes.subTitle}
                        </p>
                      </div>
                    </div>
                  ))}
              </div>

              {/* OUR PARTNERS */}
              <div className="flex items-center gap-[30px] overflow-x-scroll whitespace-nowrap no-scrollbar">
                {partners &&
                  partners.map((p, idx) => (
                    <div
                      key={idx}
                      className="min-w-[100px] w-[100px] h-[50px]  rounded-xl bg-ntrl-white cursor-pointer relative p-1 border border-ntrl-grey2"
                    >
                      <Image
                        src={p.attributes.logo.data.attributes.url}
                        fill={true}
                        style={{ objectFit: "contain" }}
                        alt="logo"
                      />
                    </div>
                  ))}
              </div>

              {/* <BookACallBtn className="py-3 px-6 md:px-8 bg-secondary-1 rounded-xl text-ntrl-white" /> */}
            </div>

            {/* Form */}
            <div className={`${source === "fb-campaign" ? "" : "row-start-1"}`}>
              <div className="bg-white p-5 rounded-xl shadow-md h-full min-h-[350px] relative">
                {data.contactForm.formType === "contact-us" && (
                  <ContactForm source={source} />
                )}
                {data.contactForm.formType === "masterclass" && (
                  <Suspense>
                    <MasterClass
                      title={
                        data.contactForm.title ? data.contactForm.title : ""
                      }
                      date={data.contactForm.date ? data.contactForm.date : ""}
                      time={data.contactForm.time ? data.contactForm.time : ""}
                      ctaUrl={
                        data.contactForm.ctaUrl ? data.contactForm.ctaUrl : ""
                      }
                      thumbnail={
                        data.contactForm.thumbnail.data.attributes.url
                          ? data.contactForm.thumbnail.data.attributes.url
                          : ""
                      }
                      actualPrice={
                        data.contactForm.actualPrice
                          ? data.contactForm.actualPrice
                          : ""
                      }
                      discountedPrice={
                        data.contactForm.discountedPrice
                          ? data.contactForm.discountedPrice
                          : ""
                      }
                      source={source}
                      setShowRefundPolicy={setShowRefundPolicy}
                    />
                  </Suspense>
                )}
              </div>
            </div>
          </div>
        </Container>
      </div>
    </div>
  );
};

export default Hero;
