import Container from "@/components/globals/Container";
import parse, { attributesToProps } from "html-react-parser";
import { Element } from "html-react-parser";

const Benefits = ({
  description,
  title,
  bgColor,
}: {
  description: string;
  title: string;
  bgColor: string;
}) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black md:mb-4";
          break;

        case "h3":
          attrs.className = "text-primary-1 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-primary-1 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;

        case "table":
          attrs.className =
            "mx-auto font-normal text-[18px]/[28px] text-ntrl-grey1 mb-5 drop-shadow-lg";
          break;

        case "tr":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;
        case "td":
          attrs.className =
            "border border-ntrl-outline bg-ntrl-white p-5 text-left";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "iframe":
          attrs.className =
            "my-20 mx-auto rounded-xl md:min-w-[1000px] md:min-h-[500px] shadow-md max-w-[350px]";
      }
      return domNode;
    }
  };
  return (
    <div className={`bg-${bgColor} md:py-20 py-10`}>
      <Container>
        {/* Heading */}
        <div className="mb-12">
          <div className="flex items-center justify-center font-generalSans md:text-[48px]/[58px] text-[24px]/[32px] text-ntrl-black gap-2 mb-4 ">
            <h2 className="font-normal">{title}</h2>
          </div>
        </div>

        <div className="no-underline">
          {/* @ts-ignore */}
          {parse(description, { replace })}
        </div>
      </Container>
    </div>
  );
};

export default Benefits;
