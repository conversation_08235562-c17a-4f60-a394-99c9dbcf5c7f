import { ResendOtp } from "../types";
import { AxiosResponse } from "axios";
import { postRequest } from "@/scripts/requests";
import { MutationConfig } from "@/lib/react-query";
import { useMutation } from "@tanstack/react-query";

type Body = {
  phone_number: Number;
  otp_use_case?: string;
};

export const resendOtp = async (
  body: Body
): Promise<AxiosResponse<ResendOtp>> => {
  let url = `${process.env.NEXT_PUBLIC_USER_API}/users/resend_otp/v1/`;
  return await postRequest<Body, ResendOtp>(url, body);
};

export const useResendOtp = (config?: MutationConfig<typeof resendOtp>) => {
  return useMutation({
    // @ts-ignore
    mutationFn: resendOtp,
    ...config,
  });
};
