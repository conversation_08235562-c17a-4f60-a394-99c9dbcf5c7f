import { Otp } from "../types";
import { AxiosResponse } from "axios";
import { postRequest } from "../../../scripts/requests";
import { MutationConfig } from "@/lib/react-query";
import { useMutation } from "@tanstack/react-query";

type Body = {
  phone_number: Number;
  otp_use_case?: string;
};

export const otp = async <T, V>(body: T): Promise<AxiosResponse<V>> => {
  // console.log("Otp called");
  let url = `${process.env.NEXT_PUBLIC_USER_API}/users/generate_otp/v1/`;
  return await postRequest<T, V>(url, body);
};

export const useOtp = (config?: MutationConfig<typeof otp>) => {
  return useMutation({
    mutationFn: otp,
    ...config,
  });
};
