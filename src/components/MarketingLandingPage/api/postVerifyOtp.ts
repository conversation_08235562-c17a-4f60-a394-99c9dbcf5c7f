import { useMutation } from "@tanstack/react-query";
import { postRequest } from "@/scripts/requests";
import { MutationConfig } from "@/lib/react-query";
import { VerifyOtp } from "../types";
import { AxiosResponse } from "axios";

type Body = {
  phone_number: string;
  user_otp: string;
  otp_use_case: string;
};

export const verifyOtp = async (
  body: Body
): Promise<AxiosResponse<VerifyOtp>> => {
  let url = `${process.env.NEXT_PUBLIC_USER_API}/users/verify_otp/v1/`;
  return await postRequest<Body, VerifyOtp>(url, body, { requireAuth: false });
};

export const useVerifyOtp = (config?: MutationConfig<typeof verifyOtp>) => {
  return useMutation({
    // @ts-ignore
    mutationFn: verifyOtp,
    ...config,
  });
};
