type ImageD = {
  id: number;
  attributes: {
    url: string;
  };
};

type Seo = {
  id: number;
  metaTitle: string;
  metaDescription: string;
  keyword: string;
  source: string;
};

export type HeroComponent = {
  id: number;
  title: string;
  subTitle: string;
  socialValidations: {
    id: number;
    validation: {
      data: Array<{
        id: number;
        attributes: {
          title: string;
          subTitle: string;
          icon: {
            data: {
              id: number;
              attributes: {
                url: string;
              };
            };
          };
        };
      }>;
    };
  };
  ourPartners: {
    id: number;
    insurer: {
      data: Array<{
        id: number;
        attributes: {
          name: string;
          url: string;
          logo: {
            data: {
              id: number;
              attributes: {
                url: string;
              };
            };
          };
        };
      }>;
    };
  };
  contactForm: {
    id: number;
    formType: string;
    title?: string;
    date?: string;
    time?: string;
    ctaUrl?: string;
    actualPrice?: string;
    discountedPrice?: string;
    refundPolicy?: string;
    thumbnail: {
      data: {
        id: number;
        attributes: {
          url: string;
        };
      };
    };
  };
};

type BenefitsComponent = {
  id: number;
  __component: "marketing.benefits";
  description: string;
  title: string;
  bgColor: string;
};

type InsurancePlansComponent = {
  id: number;
  __component: "marketing.insurance-plans";
  description: string;
  title: string;
  bgColor: string;
};

export type WhyUsComponent = {
  id: number;
  __component: "marketing.why-us";
  title: string;
  subTitle: string;
  bgColor: string;
  thumbnail: {
    data: {
      id: number;
      attributes: {
        url: string;
      };
    };
  };
  Options: Array<{
    id: number;
    title: string;
    description: string;
    icon: {
      data: {
        id: number;
        attributes: {
          url: string;
        };
      };
    };
  }>;
};

type Testimonial = {
  id: number;
  name: string;
  statement: string;
  backgroundColor: string;
  thumbnail: {
    data: ImageD;
  };
};

type TestimonialComponent = {
  id: number;
  __component: "marketing.testimonial";
  testimonial: Testimonial[];
};

type Faq = {
  id: number;
  question: string;
  ans: string;
};

type FaqsComponent = {
  id: number;
  __component: "marketing.faqs";
  faq: Faq[];
};

export type Block =
  | BenefitsComponent
  | InsurancePlansComponent
  | WhyUsComponent
  | TestimonialComponent
  | FaqsComponent;

type PageAttributes = {
  title: string;
  slug: string;
  seo: Seo;
  hero: HeroComponent;
  block: Block[];
};

export type Page = {
  id: number;
  attributes: PageAttributes;
};
