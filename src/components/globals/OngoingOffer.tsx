"use client"
import { useState } from "react";
import { Im<PERSON><PERSON> } from "react-icons/im";


const OngoingOffer: React.FC<{
    offerDescription: string
}> = (props) => {
    const [isVisible, setIsVisible] = useState(true);
    if (!isVisible) return null;
    return (
        <div className="w-full bg-secondary-1 text-white flex items-center justify-center text-center py-2 shadow-md z-50">
            <p className="font-bold text-xs md:text-lg md:tracking-wider">{props.offerDescription}</p>
            <ImCross onClick={() => setIsVisible(false)} className="absolute text-white h-2 w-2 md:size-4 right-2 cursor-pointer" />
        </div>
    )
}

export default OngoingOffer