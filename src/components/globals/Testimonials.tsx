"use client";

import TestimonialCarousel from "./TestimonialCarousel";
import { LPTestimonial } from "../HealthInsurance/types";
const Testimonials = ({ testimonials }: { testimonials: LPTestimonial[] }) => {
  // console.log(testimonials);
  // useEffect(() => {
  //   if (emblaApi) {
  //     console.log(emblaApi.slideNodes()); // Access API
  //   }
  // }, [emblaApi]);

  return (
    <div className="md:mb-14 mb-5">
      <div className="">
        {/* Heading */}
        <div className="flex items-center justify-center gap-2">
          <h2 className="md:text-3xl text-xl font-medium">
            {"What Our Customers Say"}
          </h2>
        </div>
      </div>

      <TestimonialCarousel testimonials={testimonials} />
    </div>
  );
};

export default Testimonials;
