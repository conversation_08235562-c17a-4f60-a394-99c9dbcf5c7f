"use client";

import React from "react";
import Image from "next/image";
import Book<PERSON>all from "@/components/globals/BookACall";

const TalkToExpert = () => {
  return (
    <div className="relative px-5 pt-6 md:p-10 flex flex-col gap-4 md:flex-row items-center bg-gradient-to-r from-gradient-3-green-dark to-gradient-3-green-light bg-200% animate-gradient rounded-5xl text-center w-full shadow-whyOneassure font-medium overflow-hidden md:mb-14 mb-5">
      <div className="absolute bottom-0 left-0 md:left-28 transform -translate-x-1/2 translate-y-1/2 z-0">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={500}
          height={500}
          objectFit="cover"
        />
      </div>

      <div className="hidden md:block absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 z-0">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={570}
          height={570}
          objectFit="cover"
        />
      </div>

      <div className="text-center flex justify-center z-10">
        <p className="md:text-[36px]/[60px] text-2xl text-white md:w-2/3 md:mx-0 mx-2">
          Talk to an OneAssure Insurance Expert
        </p>
      </div>

      <div className="text-center flex flex-col w-full md:w-1/2 items-center gap-4 md:mb-0 mb-2 z-10">
        <p className="md:text-[24px]/[40px] text-lg text-white md:mx-0 mx-4">
          Get the best policy with proper guidance
          <br />
          Get on a Call Now.
        </p>
        <BookACall className="md:mt-4 mt-0 px-12 py-4 bg-white text-black rounded-full w-fit  hover:bg-gray-100 shadow-md" />
      </div>

      <div></div>
    </div>
  );
};

export default TalkToExpert;
