import Image from "next/image";
import parse, { Element } from "html-react-parser";
import { PlusCircleIcon } from "@heroicons/react/24/outline";
import Accordian from "@/components/globals/Accordian";
const AddOns: React.FC<{
  addOns: {
    title: string;
    description: string;
  }[];
}> = (props) => {
  const plusIcon = `
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke-width="1.5"
      stroke="currentColor"
      aria-hidden="true"
      data-slot="icon"
      className="h-4 mt-1 flex-none text-blue-5 mr-3 md:hidden"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
      ></path>
    </svg>`;
  const generateAddOnsHtml = (addOn: {
    title: string;
    description: string;
  }): string => {
    if (addOn.description.startsWith("<p>")) {
      return `<div className="flex">${plusIcon}<p><strong>${
        addOn.title
      } - </strong>${addOn.description.slice(3)}</div>`;
    } else {
      return `<strong>${addOn.title} - </strong>${addOn.description}`;
    }
  };
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }
      if (domNode.name === "strong") {
      }
      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[30px] text-ntrl-black font-light";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );
        case "strong":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[24px] text-ntrl-black font-semibold";
          break;

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  return (
    <section
      id="addOns"
      className="md:mt-5 md:px-[50px] md:py-7 scroll-m-28 bg-white rounded-3xl"
    >
      <div className="text-custom-32 text-ntrl-black gap-2 hidden md:flex items-center py-2">
        <h2 className="font-semibold">Add-ons</h2>
      </div>
      <div className="hidden md:block">
        {props.addOns.map((addOn, index) => (
          <div key={index} className="py-2 flex flex-row">
            <PlusCircleIcon className="h-6 text-blue-5 mt-1 mr-4 flex-none" />
            {/* @ts-ignore */}
            {parse(generateAddOnsHtml(addOn), { replace })}
          </div>
        ))}
      </div>
      <Accordian
        className="md:hidden"
        name="Add-ons"
        description={`<ol>${props.addOns
          .map((addOn) => `<li>${generateAddOnsHtml(addOn)}</li>`)
          .join("")}</ol>`}
      />
    </section>
  );
};

export default AddOns;
