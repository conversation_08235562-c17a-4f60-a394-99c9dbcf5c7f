import FAQCard from "@/components/globals/FAQCard";
import { FiArrowUpRight } from "react-icons/fi";
import { HomeDataObject } from "@/components/Home/types";
import BookACall from "@/components/globals/BookACall";

interface FaqProps {
  faq: HomeDataObject["attributes"]["faq"];
}

const Faq = ({ faq }: FaqProps) => {
  return (
    <div className="bg-white md:mb-14 mb-5">
      <div className="grid grid-cols-1 md:grid-cols-10 md:gap-5">
        {/* Heading */}
        <div className="col-span-10 md:col-span-4">
          <div className="md:mb-12 mb-4">
            <div className="flex flex-col items-center justify-center md:items-start md:pl-20 md:pr-10 md:pt-10">
              <h2 className="inline-block font-medium md:text-[50px]/[70px] text-lg">
                <span className="block bg-gradient-to-r from-primary-green-2 to-primary-blue-3 bg-clip-text text-transparent">
                  {faq.title}
                </span>
              </h2>

              <p className="hidden md:flex flex-row items-center text-[16px]/[30px] font-normal">
                <span className="bg-gradient-to-r from-primary-green-2 to-primary-blue-3 bg-clip-text text-transparent">
                  {faq.subTitle || "Still have a doubt?"}
                </span>
                <BookACall
                  className="ml-2 inline-flex items-center md:text-lg font-semibold"
                  label="Ask us"
                />
                <FiArrowUpRight />
              </p>
            </div>
          </div>
          {/* Optional Image could go here */}
        </div>

        {/* FAQ Cards */}
        <div className="col-span-10 md:col-span-6 flex flex-col gap-4">
          {faq.faqs.map((f, index) => (
            <FAQCard key={index} question={f.question} ans={f.ans} />
          ))}
        </div>
        <p className="md:hidden flex flex-row items-center text-[14px]/[30px] font-normal justify-center mt-4">
          <span className="bg-gradient-to-r from-primary-green-2 to-primary-blue-3 bg-clip-text text-transparent">
            {faq.subTitle || "Still have a doubt?"}
          </span>
          <BookACall
            className="ml-2 inline-flex items-center md:text-lg font-bold"
            label="Ask us"
          />
          <FiArrowUpRight />
        </p>
      </div>
    </div>
  );
};

export default Faq;
