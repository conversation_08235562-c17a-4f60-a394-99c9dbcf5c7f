"use client";

import { useEffect } from "react";
import { ArrowUpRightIcon } from "@heroicons/react/24/solid";
import { orufyHandler } from "@/utils/orufyHandler";

const BookACallMobileBtn = ({
  onClick,
  bgColor,
}: {
  onClick?: () => void;
  bgColor: string;
}) => {
  useEffect(() => {
    function isOrufyEvent(e: MessageEvent) {
      return (
        e.origin === "https://orufybookings.com" &&
        e.data.event &&
        e.data.event.indexOf("orufybookings.") === 0
      );
    }

    function handleOrufyMessage(e: MessageEvent) {
      if (isOrufyEvent(e)) {
        console.log("Event name:", e.data.event);
        console.log("Event details:", e.data.payload);
      }
    }

    if (typeof window !== "undefined") {
      window.addEventListener("message", handleOrufyMessage);
    }

    return () => {
      window.removeEventListener("message", handleOrufyMessage);
    };
  }, []);

  function handleClick() {
    orufyHandler(process.env.NEXT_PUBLIC_ONE_ASSURE_ORUFY_LINK as string);
  }

  return (
    <button
      onClick={handleClick}
      id="orufy-inline-widget"
      className={`p-3 font-semibold cursor-pointer ${bgColor}`}
    >
      <ArrowUpRightIcon className={`w-6 h-6 text-ntrl-black font-semibold`} />
    </button>
  );
};

export default BookACallMobileBtn;
