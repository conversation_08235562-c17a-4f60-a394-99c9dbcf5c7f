import CustomLink from "./CustomLink";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { XCircleIcon } from "@heroicons/react/24/outline";

const MobileNavbar = ({ utm }: { utm: string | null }) => {
  const pathname = usePathname();
  const [toggle, setToggle] = useState(1);

  // const [isOpen, setIsOpen] = useState(false);

  // const handleMenu = () => {
  //   setIsOpen(!isOpen);
  // };

  const business = [
    "/insurance/corporate-insurance",
    "/insurance/sell-insurance",
    "/one-cloud",
    "/business/home",
    "/business/group-health-life",
    "/business/liability-insurance",
  ];

  useEffect(() => {
    // console.log(pathname);
    // console.log("Bus : ", business.includes(pathname));
    if (business.includes(pathname)) {
      setToggle(2);
    } else {
      setToggle(1);
    }
  }, [pathname]);

  const navOpts = [
    [
      {
        title: "Health Insurance",
        url: "/health-insurance",
      },
      {
        title: "Term Insurance",
        url: "/term-insurance",
      },
      // {
      //   title: "Claims",
      //   url: "/claims",
      // },
    ],
    [
      {
        title: "Group Health & Life",
        url: "/business/group-health-life",
      },
      {
        title: "Liability Insurance",
        url: "/business/liability-insurance",
      },
    ],
  ];

  return (
    <>
      {/* Hamburger icon */}
      {/* <div
        onClick={handleMenu}
        className="lg:hidden"
        // className={`${styles.menuIcon} ${isOpen ? styles.open : ""}`}
      >
        <div className=" w-8 h-1 bg-blue600 mb-1 rounded-full"></div>
        <div className=" w-8 h-1 bg-blue600 my-1 rounded-full"></div>
        <div className=" w-8 h-1 bg-blue600 mt-1 rounded-full"></div>
      </div> */}
    </>
  );
};

export default MobileNavbar;
