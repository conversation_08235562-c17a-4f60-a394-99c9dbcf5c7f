import GetInTouch from "@/components/globals/GetInTouch";
import Image from "next/image";
const ContactForm = () => {
  return (
    <div className="relative bg-gradient-to-tr from-blue1100 to-blue800 md:mb-14 mb-5 rounded-3xl overflow-hidden bg-opacity-80 ">
      <div className="absolute left-16 bottom-40 transform -translate-x-1/2 translate-y-1/2 opacity-50">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt="Decorative circles"
          width={524}
          height={524}
          objectFit="cover"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-[40%_60%] px-10 md:py-10 py-6">
        <div className="justify-center text-[50px]/[70px] text-ntrl-white mb-5 flex">
          <h2 className="text-[24px]/[36px] md:text-[45px]/[60px] md:w-2/3">
            Get In Touch with Our Insurance Experts
          </h2>
        </div>
        <div className="z-10">
          <GetInTouch />
        </div>
      </div>
    </div>
  );
};

export default ContactForm;
