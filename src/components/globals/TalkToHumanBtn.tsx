"use client";

import { forwardRef, useEffect, useRef } from "react";
import { useSessionStorage } from "usehooks-ts";
import { Button } from "@/components/UI/Button";
import { orufyHandler } from "@/utils/orufyHandler";

const TalkToHumanBtn = forwardRef(function BookACallSqrBtn(
  {
    onClick,
    className,
    label = "Book a Call",
    utm,
    ...props
  }: {
    onClick?: () => void;
    className?: string;
    label?: string;
    utm?: string;
    [key: string]: any;
  },
  ref
) {
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", "");
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", "");
  const [utm_campaign, setUtmCampaign] = useSessionStorage("utm_campaign", "");
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", "");
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", "");

  useEffect(() => {
    function isOrufyEvent(e: MessageEvent) {
      return (
        e.origin === "https://orufybookings.com" &&
        e.data.event &&
        e.data.event.indexOf("orufybookings.") === 0
      );
    }

    function handleOrufyMessage(e: MessageEvent) {
      if (isOrufyEvent(e)) {
        console.log("Event name:", e.data.event);
        console.log("Event details:", e.data.payload);
      }
    }

    if (typeof window !== "undefined") {
      window.addEventListener("message", handleOrufyMessage);
    }

    return () => {
      window.removeEventListener("message", handleOrufyMessage);
    };
  }, []);

  function handleClick() {
    const orufyLink = `${process.env.NEXT_PUBLIC_ONE_ASSURE_ORUFY_LINK}?utm_source=${utm_source}&utm_medium=${utm_medium}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&utm_term=${utm_term}`;
    orufyHandler(orufyLink);
  }

  return (
    <Button
      onClick={handleClick}
      id="orufy-inline-widget"
      className={className}
      {...props}
      // @ts-ignore
      ref={ref}
    >
      {label}
    </Button>
  );
});

export default TalkToHumanBtn;