"use client";

import { useState, useRef } from "react";
import { ChevronDownIcon, ArrowRightIcon } from "@heroicons/react/24/solid";
import { useCreateLead } from "@/components/LeadModal/api/createLead";
import { usePathname } from "next/navigation";
import { BodyLarge, BodyMedium, BodySmall, HeadingLarge } from "@/components/UI/Typography";
import { Button } from "../UI/Button";
import { ArrowLeftIcon } from "lucide-react";

const CustomDropdown = ({
  value,
  onChange,
  options,
  placeholder,
  name,
  error,
}: {
  value: string;
  onChange: (value: string) => void;
  options: { label: string; value: string }[];
  placeholder: string;
  name: string;
  error?: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`w-full text-left px-3.5 py-1.5 md:py-2.5 border-2 rounded-xl bg-neutral-100 flex items-center justify-between ${
          error
            ? "border-red-500"
            : ""
        } transition-colors ${isOpen ? "border border-primary-200" : "border-neutral-100"}`}
      >
        <BodyMedium
          className={`${value ? "text-neutral-1100" : "text-neutral-500"}`}
        >
          {value
            ? options.find((opt) => opt.value === value)?.label || value
            : placeholder}
        </BodyMedium>
        <ChevronDownIcon className="w-3 h-3 md:w-5 md:h-5 text-gray-400" />
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-auto text-neutral-1100">
          {options.map((option, index) => (
            <button
              key={index}
              type="button"
              onClick={() => {
                onChange(option.value);
                setIsOpen(false);
              }}
              className="w-full text-left px-4 py-2 hover:bg-gray-50 transition-colors text-base text-neutral-1100"
            >
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

type Step = "form" | "contact" | "thankyou";

const GenerateQuote = () => {
  // Step State
  const [step, setStep] = useState<Step>("form");

  // Form States
  const [adults, setAdults] = useState("1");
  const [children, setChildren] = useState("1");
  const [adultAges, setAdultAges] = useState([""]);
  const [childAges, setChildAges] = useState([""]);
  const [coverageAmount, setCoverageAmount] = useState("");
  const [pincode, setPincode] = useState("");

  // Contact States
  const [leadName, setLeadName] = useState("");
  const [leadEmail, setLeadEmail] = useState("");
  const [leadPhone, setLeadPhone] = useState("");

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fixedHeight, setFixedHeight] = useState<number | null>(null);
  const contentRef = useRef<HTMLDivElement | null>(null);
  const [cachedLeadMessage, setCachedLeadMessage] = useState("");

  // Lead API hook
  const { mutate: createLead } = useCreateLead();
  const pathname = usePathname();

  // Coverage values
  const coverageValues = [
    { label: "5 Lakhs", value: "500000" },
    { label: "7.5 Lakhs", value: "750000" },
    { label: "10 Lakhs", value: "1000000" },
    { label: "15 Lakhs", value: "1500000" },
    { label: "20 Lakhs", value: "2000000" },
    { label: "25 Lakhs", value: "2500000" },
    { label: "50 Lakhs", value: "5000000" },
    { label: "75 Lakhs", value: "7500000" },
    { label: "1 Crore", value: "10000000" },
    { label: "2 Crore", value: "20000000" },
  ];

  // Adult options
  const adultOptions = [
    { label: "1 Adult", value: "1" },
    { label: "2 Adults", value: "2" },
  ];

  // Child options
  const childOptions = [
    { label: "0 Children", value: "0" },
    { label: "1 Child", value: "1" },
    { label: "2 Children", value: "2" },
    { label: "3 Children", value: "3" },
    { label: "4 Children", value: "4" },
  ];

  // Age options for adults (18-65)
  const adultAgeOptions = Array.from({ length: 48 }, (_, i) => ({
    label: `${i + 18} yr`,
    value: `${i + 18}`,
  }));

  // Age options for children (1-25)
  const childAgeOptions = Array.from({ length: 25 }, (_, i) => ({
    label: `${i + 1} yr`,
    value: `${i + 1}`,
  }));

  // Handle adult count change
  const handleAdultsChange = (value: string) => {
    setAdults(value);
    const newAdultCount = parseInt(value);
    if (newAdultCount === 1) {
      setAdultAges([adultAges[0] || ""]);
    } else if (newAdultCount === 2 && adultAges.length === 1) {
      setAdultAges([adultAges[0], ""]);
    }
  };

  // Handle children count change
  const handleChildrenChange = (value: string) => {
    setChildren(value);
    const newChildCount = parseInt(value);
    if (newChildCount === 0) {
      setChildAges([]);
    } else if (newChildCount === 1) {
      setChildAges([childAges[0] || ""]);
    } else if (newChildCount === 2) {
      setChildAges([childAges[0] || "", childAges[1] || ""]);
    } else if (newChildCount === 3) {
      setChildAges([
        childAges[0] || "",
        childAges[1] || "",
        childAges[2] || "",
      ]);
    } else if (newChildCount === 4) {
      setChildAges([
        childAges[0] || "",
        childAges[1] || "",
        childAges[2] || "",
        childAges[3] || "",
      ]);
    }
  };

  // First step submit: move to contact step and cache message
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const selectedCoverage = coverageValues.find(
      (c) => c.value === coverageAmount
    );
    const leadMessage = `Health Insurance Quote Request:\n- Adults: ${adults} (Ages: ${adultAges.join(
      ", "
    )})\n- Children: ${children} ${
      parseInt(children) > 0 ? `(Ages: ${childAges.join(", ")})` : ""
    }\n- Coverage Amount: ${selectedCoverage?.label}\n- Pincode: ${pincode}`;

    // Fix container height on first transition
    if (!fixedHeight && contentRef.current) {
      setFixedHeight(contentRef.current.clientHeight);
    }

    setCachedLeadMessage(leadMessage);
    setIsSubmitting(false);
    setStep("contact");
  };

  // Contact submit: send lead and show thank you
  const handleContactSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    createLead(
      {
        lead_name: leadName || "Health Insurance Prospect",
        lead_phone_number: leadPhone || "**********",
        lead_email: leadEmail || undefined,
        lead_message: cachedLeadMessage,
        lead_point: pathname,
        lead_contact_type: "Health Insurance Quote Calculator",
        source: "Health Insurance Quote Form",
      },
      {
        onSuccess: () => {
          setIsSubmitting(false);
          setStep("thankyou");
        },
        onError: () => {
          alert("Something went wrong. Please try again.");
          setIsSubmitting(false);
        },
      }
    );
  };

  return (
    <div className="bg-white rounded-xl border border-primary-300 p-4 md:p-6">
      <div
        ref={contentRef}
        className={
          step === "thankyou" ? "flex items-center justify-center" : undefined
        }
        // style={fixedHeight ? { minHeight: fixedHeight } : undefined}
      >
        {step === "form" && (
          <>
            {/* Heading - HeadingLarge, semibold */}
            <HeadingLarge className="text-center text-neutral-1100 mb-3 md:mb-5">
              Get a Quote
            </HeadingLarge>

            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-2 gap-3 md:gap-6 mb-3">
                {/* Left Column */}
                <div className="flex flex-col justify-between">
                  <div className="space-y-3 mb-3">
                    {/* Number of Adults */}
                    <div>
                      {/* Label - BodyLarge, semibold */}
                      <BodyMedium className="text-neutral-1100 font-medium mb-2">
                        Number of Adults
                      </BodyMedium>
                      <CustomDropdown
                        value={adults}
                        onChange={handleAdultsChange}
                        options={adultOptions}
                        placeholder="2 Adults"
                        name="adults"
                      />
                    </div>

                    {/* Age (Adults) */}
                    <div>
                      {/* Label - BodyLarge, semibold */}
                      <BodyMedium className="text-neutral-1100 font-medium mb-2">
                        Age (Adults)
                      </BodyMedium>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {adultAges.map((age, index) => (
                          <CustomDropdown
                            key={index}
                            value={age}
                            onChange={(value) => {
                              const newAges = [...adultAges];
                              newAges[index] = value;
                              setAdultAges(newAges);
                            }}
                            options={adultAgeOptions}
                            placeholder={`${32 - index} yrs`}
                            name={`adult${index + 1}`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Select Coverage Amount */}
                  <div>
                    {/* Label - BodyLarge, semibold */}
                    <BodyMedium className="text-neutral-1100 font-medium mb-2">
                      Select Coverage Amount
                    </BodyMedium>
                    <CustomDropdown
                      value={coverageAmount}
                      onChange={setCoverageAmount}
                      options={coverageValues}
                      placeholder="50 Lakhs"
                      name="coverage"
                    />
                  </div>
                </div>

                {/* Right Column */}
                <div className="flex flex-col justify-between">
                  <div className="space-y-3 mb-3">
                    {/* Number of Children */}
                    <div>
                      {/* Label - BodyLarge, semibold */}
                      <BodyMedium className="text-neutral-1100 font-medium mb-2">
                        Number of Children
                      </BodyMedium>
                      <CustomDropdown
                        value={children}
                        onChange={handleChildrenChange}
                        options={childOptions}
                        placeholder="4 Children"
                        name="children"
                      />
                    </div>

                    {/* Age (Children) */}
                    {parseInt(children) > 0 && (
                      <div>
                        {/* Label - BodyLarge, semibold */}
                        <BodyMedium className="text-neutral-1100 font-medium mb-2">
                          Age (Children)
                        </BodyMedium>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {childAges.map((age, index) => (
                            <CustomDropdown
                              key={index}
                              value={age}
                              onChange={(value) => {
                                const newAges = [...childAges];
                                newAges[index] = value;
                                setChildAges(newAges);
                              }}
                              options={childAgeOptions}
                              placeholder={`${12 - index} yrs`}
                              name={`child${index + 1}`}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  {/* Enter Pincode */}
                  <div>
                    {/* Label - BodyLarge, semibold */}
                    <BodyMedium className="text-neutral-1100 font-medium mb-2">
                      Enter Pincode
                    </BodyMedium>
                    <input
                      type="text"
                      name="pincode"
                      value={pincode}
                      onChange={(e) => setPincode(e.target.value)}
                      placeholder="560065"
                      className="w-full rounded-xl border-none px-4 py-2.5 md:py-3 bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-200 text-xs md:text-sm text-neutral-1100"
                    />
                  </div>
                </div>
              </div>

              {/* Next Button */}
              {/* <div className="mt-8">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-primary-800 hover:bg-primary-900 text-white px-auto py-2 md:py-4 md:px-6 rounded-lg text-base font-semibold disabled:opacity-50 disabled:cursor-wait flex items-center justify-center gap-2 transition-colors"
                >
                  {isSubmitting ? "Please wait..." : "Continue"}
                  <ArrowRightIcon className="w-5 h-5" />
                </button>
              </div> */}
              <div className="w-full flex justify-center items-center mt-4">
                <Button type="submit" variant="primary" disabled={isSubmitting} className="px-6 w-full">
                  <span className="text-sm font-normal">{isSubmitting ? "Please wait..." : "Get Quote"}</span>
                  <ArrowRightIcon className="w-5 h-5" />
                </Button>
              </div>

              {/* Disclaimer */}
              <BodySmall className="text-neutral-1100 text-center mt-3">
                By continuing, you agree to our Terms of Service and Privacy
                Policy
              </BodySmall>
            </form>
          </>
        )}

        {step === "contact" && (
          <>
            <HeadingLarge className="text-center text-neutral-1100 mb-3 md:mb-5">
              Your Details
            </HeadingLarge>
            <form onSubmit={handleContactSubmit}>
              <div className="grid grid-cols-1 gap-3 mb-3">
                <div>
                  <BodyMedium className="text-neutral-1100 font-medium mb-2">
                    Full Name
                  </BodyMedium>
                  <input
                    type="text"
                    name="name"
                    value={leadName}
                    onChange={(e) => setLeadName(e.target.value)}
                    placeholder="John Doe"
                    className="w-full rounded-xl border-none px-4 py-2.5 md:py-3 bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-200 text-xs md:text-sm text-neutral-1100"
                    required
                  />
                </div>
                <div>
                  <BodyMedium className="text-neutral-1100 font-medium mb-2">
                    Phone Number
                  </BodyMedium>
                  <input
                    type="tel"
                    name="phone"
                    value={leadPhone}
                    onChange={(e) => setLeadPhone(e.target.value)}
                    placeholder="9876543210"
                    className="w-full rounded-xl border-none px-4 py-2.5 md:py-3 bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-200 text-xs md:text-sm text-neutral-1100"
                    pattern="[0-9]{10}"
                    required
                  />
                </div>
                <div>
                  <BodyMedium className="text-neutral-1100 font-medium mb-2">
                    Email Address (optional)
                  </BodyMedium>
                  <input
                    type="email"
                    name="email"
                    value={leadEmail}
                    onChange={(e) => setLeadEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full rounded-xl border-none px-4 py-2.5 md:py-3 bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-200 text-xs md:text-sm text-neutral-1100"
                  />
                </div>
              </div>

              <div className="mt-3 flex justify-center gap-4">
                {/* <button
                  type="button"
                  onClick={() => setStep("form")}
                  className="w-full bg-neutral-200 hover:bg-neutral-300 text-neutral-900 px-auto py-2 md:py-4 md:px-6 rounded-lg text-base font-semibold transition-colors"
                >
                  Back
                </button> */}
                <Button
                  variant="secondary"
                  onClick={() => setStep("form")}
                  className="rounded-xl w-1/2"
                >
                  <ArrowLeftIcon className="w-5 h-5" />
                  <span className="text-sm font-normal">Back</span>
                </Button>
                {/* <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-primary-800 hover:bg-primary-900 text-white px-auto py-2 md:py-4 md:px-6 rounded-lg text-base font-semibold disabled:opacity-50 disabled:cursor-wait flex items-center justify-center gap-2 transition-colors"
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                  <ArrowRightIcon className="w-5 h-5" />
                </button> */}
                <Button type="submit" variant="primary" disabled={isSubmitting} className="w-1/2">
                  <span className="text-sm font-normal">{isSubmitting ? "Submitting..." : "Submit"}</span>
                  <ArrowRightIcon className="w-5 h-5" />
                </Button>
              </div>
            </form>
          </>
        )}

        {step === "thankyou" && (
          <div className="text-center py-8 md:py-12">
            <HeadingLarge className="text-neutral-1100 md:mb-2">
              Thank you!
            </HeadingLarge>
            <BodyLarge className="text-neutral-900">
              Your request has been submitted. Our experts will reach out to you
              shortly.
            </BodyLarge>
            <BodySmall className="text-neutral-700 mt-2">
              Keep your phone handy. We may contact you to confirm details and
              share the best quotes.
            </BodySmall>
          </div>
        )}
      </div>
    </div>
  );
};

export default GenerateQuote;
