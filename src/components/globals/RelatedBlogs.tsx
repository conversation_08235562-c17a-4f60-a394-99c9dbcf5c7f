import React, { useState, useEffect, useRef } from "react";
import SectionContainer from "@/components/globals/SectionContainer";
import { HeadingXLarge } from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import NavigationButton from "@/components/globals/NavigationButton";
import MobileCarousel from "@/components/UI/MobileCarousel";
import BlogCard, { BlogCardProps } from "@/components/globals/RelatedBlogCard";

type RelatedBlogsProps = {
  blogData: {
    heading: string;
    blogs: BlogCardProps[];
  };
};

const RelatedBlogs: React.FC<RelatedBlogsProps> = ({ blogData }) => {
  const { heading, blogs } = blogData;
  const [currentPage, setCurrentPage] = useState(0);
  const blogsPerPage = 3;
  const totalPages = Math.ceil(blogs.length / blogsPerPage);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const handlePrevPage = () => {
    setCurrentPage((prev) => Math.max(0, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(totalPages - 1, prev + 1));
  };

  // Handle scroll events for mobile view
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const scrollLeft = container.scrollLeft;
      const cardWidth = container.scrollWidth / blogs.length;
      const currentIndex = Math.round(scrollLeft / cardWidth);
      setCurrentPage(currentIndex);
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [blogs.length]);

  const PageIndicator = ({
    isActive,
    onClick,
  }: {
    isActive: boolean;
    onClick: () => void;
  }) => (
    <button
      onClick={onClick}
      className={`w-2.5 h-2.5 rounded-full transition-all duration-200 ${
        isActive
          ? "bg-neutral-1100 scale-110"
          : "bg-gray-300 hover:bg-gray-400 hover:scale-105"
      }`}
    />
  );

  return (
    <SectionContainer
      className="w-full mb-6 md:mb-14 bg-white !px-0 md:!px-0"
      id="related-blogs"
    >
      <HeadingXLarge
        as="h2"
        className="text-neutral-1100 text-center mb-4 md:mb-6"
      >
        Related Blogs
      </HeadingXLarge>

      {/* Desktop view */}
      <div className="relative hidden md:block">
        <div className="flex items-center">
          {blogs.length > blogsPerPage && (
            <div className="flex-shrink-0 mr-4">
              <NavigationButton
                onClick={handlePrevPage}
                disabled={currentPage === 0}
                direction="prev"
              />
            </div>
          )}

          <SectionContainerLarge className="flex-1 overflow-hidden !mb-0 md:!mb-0 !px-0 md:!px-0">
            <div
              className="flex transition-transform duration-500 ease-in-out"
              style={{
                transform: `translateX(-${currentPage * (100 / totalPages)}%)`,
                width: `${totalPages * 100}%`,
              }}
            >
              {Array.from({ length: totalPages }, (_, pageIndex) => (
                <div
                  key={pageIndex}
                  className="grid grid-cols-1 md:grid-cols-3 gap-6 flex-shrink-0"
                  style={{ width: `${100 / totalPages}%` }}
                >
                  {blogs
                    .slice(
                      pageIndex * blogsPerPage,
                      (pageIndex + 1) * blogsPerPage
                    )
                    .map((blog, index) => (
                      <BlogCard
                        key={pageIndex * blogsPerPage + index}
                        {...blog}
                      />
                    ))}
                </div>
              ))}
            </div>
          </SectionContainerLarge>

          {blogs.length > blogsPerPage && (
            <div className="flex-shrink-0 ml-4">
              <NavigationButton
                onClick={handleNextPage}
                disabled={currentPage === totalPages - 1}
                direction="next"
              />
            </div>
          )}
        </div>

        {/* Page Indicators */}
        {blogs.length > blogsPerPage && (
          <div className="flex justify-center items-center mt-6 gap-2">
            {Array.from({ length: totalPages }, (_, index) => (
              <PageIndicator
                key={index}
                isActive={currentPage === index}
                onClick={() => setCurrentPage(index)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Mobile view */}
      <div className="relative md:hidden">
        <div
          ref={scrollContainerRef}
          className="overflow-x-auto scroll-smooth no-scrollbar"
          style={{
            scrollbarWidth: "none",
            msOverflowStyle: "none",
          }}
        >
          <MobileCarousel totalSlides={blogs.length}>
            {blogs.map((blog, index) => (
              <BlogCard key={index} {...blog} />
            ))}
          </MobileCarousel>
        </div>
      </div>
    </SectionContainer>
  );
};

export default RelatedBlogs;
