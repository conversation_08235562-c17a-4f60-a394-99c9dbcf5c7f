import OurPartners from "./OurPartners";
import Hero from "./Hero";
import { Suspense } from "react";
import Testimonials from "../Testimonials";
import ContactForm from "@/components/globals/ContactForm";
import ProductDropdown from "./ProductDropdown";

type InsuranceType = "health" | "term";

const InsuranceAllProducts = ({
  companyData,
  testimonials,
  category,
}: {
  companyData: any;
  testimonials: {
    id: number;
    testimonial: {
      id: number;
      name: string;
      statement: string;
      backgroundColor: string;
      thumbnail: {
        data: {
          id: number;
          attributes: {
            url: string;
          };
        };
      };
    }[];
  };
  category: string;
}) => {
  return (
    <>
      <Hero />
      <Suspense>
        <ProductDropdown
          companyData={companyData}
          category={category as InsuranceType}
        />
        <OurPartners companyData={companyData} category={category} />
      </Suspense>
      <Testimonials testimonials={testimonials.testimonial} />
      <Suspense>
        <ContactForm />
      </Suspense>
    </>
  );
};

export default InsuranceAllProducts;
