"use client";
import Container from "@/components/globals/Container";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";
import { useSessionStorage } from "usehooks-ts";

const Hero = () => {
  const pathname = usePathname();
  const router = useRouter();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);
  useEffect(() => {
    if (typeof window !== "undefined") {
      router.replace(
        `${pathname}?${utm_source !== null ? `utm_source=${utm_source}` : ""}${
          utm_medium !== null ? `&utm_medium=${utm_medium}` : ""
        }${utm_campaign !== null ? `&utm_campaign=${utm_campaign}` : ""}${
          utm_content !== null ? `&utm_content=${utm_content}` : ""
        }${utm_term !== null ? `&utm_term=${utm_term}` : ""}`
      );
    }
  }, []);

  return (
    <div className="bg-primary-1 py-10 md:py-20 relative">
      <Image
        src={
          "https://cdn.oasr.in/oa-site/cms-uploads/media/Tick_cf13f9082f.png"
        }
        fill={true}
        style={{ objectFit: "cover" }}
        alt="tick"
      />
      <Container>
        <div className="grid grid-cols-1 md:grid-cols-2">
          {/* Heading */}
          <div className="flex items-center gap-2 flex-wrap text-[48px]/[58px] font-generalSans text-ntrl-white mb-4 md:mb-0">
            <h1 className="font-normal text-center md:text-left">Meet our</h1>
            <h1 className="font-medium text-center md:text-left">
              Trusted Partners
            </h1>
          </div>
          {/* Paragraph */}
          <div className="text-ntrl-white text-[18px]/[24px] text-center md:text-left ">
            <p>
              OneAssure works closely with their partners to provide customised
              solutions for their clients.
            </p>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default Hero;
