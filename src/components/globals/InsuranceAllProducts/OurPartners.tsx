"use client";

import Container from "../Container";
import CustomLink from "../CustomLink";
import { useSearchParams } from "next/navigation";
import Image from "next/image";
import { ChevronRightIcon } from "@heroicons/react/24/solid";

const OurPartners = ({
  companyData,
  category,
}: {
  companyData: any;
  category: string;
}) => {
  const search = useSearchParams();
  const utm = search.get("utm_source");
  return (
    <div className="md:mb-20 my-10 md:my-0 md:mt-20">
      <Container>
        {/* Heading */}
        <div className="mb-12">
          <div className="flex items-center justify-center font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black gap-2">
            <h2 className="font-normal">Our Trusted</h2>
            <h2 className="font-medium">Partners</h2>
          </div>
        </div>

        {/* Partner List */}
        <div className="grid grid-cols-2 md:grid-cols-3 md:gap-12 gap-4">
          {/* @ts-ignore */}
          {companyData.data.companies.map((item, idx) => (
            // company cards
            <div
              key={idx}
              className="border border-ntrl-outline rounded-xl grid md:grid-rows-2"
            >
              <div className="md:py-20 py-3 flex items-center justify-center">
                <div className="md:w-[100px] md:h-[112px] w-[116px] h-[36px] relative">
                  <Image
                    src={item.icon}
                    fill={true}
                    style={{ objectFit: "contain" }}
                    alt={item.name}
                  />
                </div>
              </div>

              {/* Description view for mobile */}

              <div className="bg-secondary-2  md:hidden p-2 rounded-b-xl">
                <CustomLink
                  href={`/${category}-insurance/${item.slug}`}
                  utm={utm}
                  style="flex items-center justify-between"
                >
                  <h2 className="text-[12px]/[18px] text-ntrl-black w-[80%]">
                    {item.name}
                  </h2>
                  <ChevronRightIcon className="w-4 h-4 text-primary-1" />
                </CustomLink>
              </div>

              {/* Description view for web */}
              <div className="bg-secondary-2 p-6 md:flex flex-col justify-between hidden">
                <div className="">
                  <h2 className="font-generalSans text-ntrl-black font-medium text-[24px]/[30px] mb-4">
                    {item.name}
                  </h2>
                  <p className="text-[16px]/[24px] text-ntrl-grey1 mb-4">
                    From purchasing to handling claims, we’ve got you covered.
                  </p>
                </div>
                <CustomLink
                  href={`/${category}-insurance/${item.slug}`}
                  utm={utm}
                >
                  <span className="py-3 bg-primary-1 w-full flex items-start justify-center rounded-full text-ntrl-white">
                    Read More
                  </span>
                </CustomLink>
              </div>
            </div>
          ))}
        </div>
      </Container>
    </div>
  );
};

export default OurPartners;
