import Container from "@/components/globals/Container";
import parse from "html-react-parser";
import { Element } from "html-react-parser";
import Image from "next/image";

const InsuranceDesc = ({
  title,
  description,
}: {
  title: string;
  description: string;
}) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }
      if (domNode.name === "strong") {
      }
      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-white md:text-[16px]/[24px] text-[14px]/[22px]";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );
        case "strong":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[24px] text-ntrl-black font-semibold";
          break;

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };

  return (
    <div className="md:mb-14 mb-5 -mx-4 md:-mx-0">
      <div className="relative text-center md:w-full flex flex-col bg-gradient-to-r from-gradient-1-blue to-gradient-1-green md:rounded-5xl overflow-hidden bg-opacity-80 p-6">
        <div className="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2">
          <Image
            src={
              "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
            }
            alt=""
            width={570}
            height={570}
          />
        </div>

        <div className="absolute bottom-0 left-[-60px] transform -translate-x-1/2 translate-y-1/2">
          <Image
            src={
              "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
            }
            alt=""
            width={524}
            height={524}
          />
        </div>

        <div className=" items-center z-10">
          <div className="md:col-span-2 text-center">
            <div className="mb-6 items-center gap-2">
              <h2 className="text-white font-medium md:text-3xl text-lg">
                {title}
              </h2>
            </div>

            <div className=" mt-3 text-center">
              {/* @ts-expect-error unknown type */}
              {parse(description, { replace })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsuranceDesc;
