"use client";

import { useSearchParams } from "next/navigation";
import { useState, useEffect, useCallback } from "react";
import CustomLink from "../CustomLink";
// Types
type InsuranceType = "health" | "term";
type Variant = {
  name: string;
  slug: string;
};

interface CompanyAttributes {
  category: string;
  slug: string;
  name: string;
  health_variants?: {
    data: Array<{
      attributes: {
        name: string;
        slug: string;
      };
    }>;
  };
  term_variants?: {
    data: Array<{
      attributes: {
        name: string;
        slug: string;
      };
    }>;
  };
}

interface Company {
  attributes: CompanyAttributes;
}

interface ProductDropdownProps {
  companyData: Company[];
  category?: InsuranceType;
}

// Constants
const INSURANCE_TYPES = [
  {
    attributes: {
      slug: "health" as const,
      name: "Health Insurance",
    },
  },
  {
    attributes: {
      slug: "term" as const,
      name: "Term Insurance",
    },
  },
];

const ProductDropdown = ({ companyData, category }: ProductDropdownProps) => {
  const search = useSearchParams();
  const utm = search.get("utm_source");

  // console.log("Company Data", companyData);

  const [selectedCategory, setSelectedCategory] =
    useState<InsuranceType | null>(category || null);
  const [selectedCompanySlug, setSelectedCompanySlug] = useState<string | null>(
    null
  );
  const [selectedVariantSlug, setSelectedVariantSlug] = useState<string | null>(
    null
  );
  const [companies, setCompanies] = useState<Company[]>([]);
  const [activeVariants, setActiveVariants] = useState<Variant[] | null>(null);
  const [disableBtn, setDisableBtn] = useState(false);

  const handleCategoryChange = useCallback((value: string) => {
    setSelectedCategory(value as InsuranceType);
    setSelectedCompanySlug(null);
    setSelectedVariantSlug(null);
  }, []);

  const handleCompanyChange = useCallback((value: string) => {
    if (value === "") {
      setSelectedCompanySlug(null);
      setSelectedVariantSlug(null);
    } else {
      setSelectedCompanySlug(value);
      setSelectedVariantSlug(null);
    }
  }, []);

  const handleVariantChange = useCallback((value: string) => {
    setSelectedVariantSlug(value === "" ? null : value);
  }, []);

  useEffect(() => {
    if (selectedCategory !== null) {
      const filteredCompanies = companyData.filter(
        (item) => item.attributes.category === `${selectedCategory}-insurance`
      );
      setCompanies(filteredCompanies);
    }
  }, [selectedCategory, companyData]);

  useEffect(() => {
    if (selectedCompanySlug !== null) {
      const selectedCompany = companies.find(
        (item) => item.attributes.slug === selectedCompanySlug
      );

      if (selectedCompany) {
        const variants =
          selectedCategory === "health"
            ? selectedCompany.attributes.health_variants?.data
            : selectedCompany.attributes.term_variants?.data;

        setActiveVariants(
          variants?.map((variant) => ({
            name: variant.attributes.name,
            slug: variant.attributes.slug,
          })) || []
        );
      }
    }
  }, [selectedCompanySlug, selectedCategory, companies]);

  useEffect(() => {
    setDisableBtn(
      typeof selectedCompanySlug !== "string" ||
        typeof selectedVariantSlug !== "string"
    );
  }, [selectedCompanySlug, selectedVariantSlug]);

  return (
    <div className="text-xs text-ntrl-black-2 font-normal md:mb-4  mt-5 md:mt-8 ">
      <p className="text-ntrl-black-2 text-base md:py-2 pb-2 pl-5 md:pl-0 font-semibold">
        Looking for a Specific policy?
      </p>
      <div className="flex flex-col md:flex-row md:items-center justify-between border border-gradient-1-green rounded-3xl px-6 py-6 md:py-0 md:px-1 ">
        {!category && (
          <div className="border-b border-ntrl-grey-2 md:border-none w-full md:w-1/3 relative">
            <div className="relative group">
              <select
                name="insurance-type"
                className="w-full rounded-l-5xl mt-2 md:mt-0 border-0 focus:ring-0 text-sm transition-all duration-200 hover:bg-gray-50"
                onChange={(e) => handleCategoryChange(e.target.value)}
                value={selectedCategory || ""}
                aria-label="Select Insurance Type"
              >
                <option value="">Type</option>
                {INSURANCE_TYPES.map((item, idx) => (
                  <option value={item.attributes.slug} key={idx}>
                    {item.attributes.name}
                  </option>
                ))}
              </select>
              {selectedCategory && (
                <div className="hidden md:group-hover:block absolute z-50 bg-white text-ntrl-grey1 shadow-md text-xs rounded-lg py-2 px-3 mt-1 w-full">
                  {
                    INSURANCE_TYPES.find(
                      (item) => item.attributes.slug === selectedCategory
                    )?.attributes.name
                  }
                </div>
              )}
            </div>
          </div>
        )}
        <div className="md:w-1/3 border-b border-ntrl-grey-2 w-full rounded-l-5xl">
          <div className="relative group">
            <select
              name="company"
              className="w-full rounded-l-5xl border-0 focus:ring-0 text-sm transition-all duration-200 hover:bg-gray-50"
              onChange={(e) => handleCompanyChange(e.target.value)}
              value={selectedCompanySlug || ""}
              aria-label="Select Insurer"
            >
              <option value="">Insurer</option>
              {companies.map((item, idx) => (
                <option value={item.attributes.slug} key={idx}>
                  {item.attributes.name}
                </option>
              ))}
            </select>
            {selectedCompanySlug && (
              <div className="hidden md:group-hover:block absolute z-50 bg-white text-ntrl-grey1 shadow-md text-xs rounded-lg py-2 px-3 mt-1 w-full">
                {
                  companies.find(
                    (item) => item.attributes.slug === selectedCompanySlug
                  )?.attributes.name
                }
              </div>
            )}
          </div>
        </div>
        <div className="md:w-1/3 w-full">
          <div className="relative group">
            <select
              name="variant"
              className="w-full rounded-lg border-0 focus:ring-0 text-sm transition-all duration-200 hover:bg-gray-50"
              onChange={(e) => handleVariantChange(e.target.value)}
              value={selectedVariantSlug || ""}
              aria-label="Select Product"
            >
              <option value="">Product</option>
              {activeVariants?.map((item, idx) => (
                <option value={item.slug} key={idx}>
                  {item.name}
                </option>
              ))}
            </select>
            {selectedVariantSlug && (
              <div className="hidden md:group-hover:block absolute z-50 bg-white text-ntrl-grey1 shadow-md text-xs rounded-lg py-2 px-3 mt-1 w-full">
                {
                  activeVariants?.find(
                    (item) => item.slug === selectedVariantSlug
                  )?.name
                }
              </div>
            )}
          </div>
        </div>
        <button
          className="bg-ntrl-black-2 md:w-1/5 w-full text-ntrl-white md:font-bold font-normal cursor-pointer md:text-sm text-base flex items-center justify-center disabled:cursor-not-allowed disabled:opacity-50 rounded-5xl py-3 md:py-1 px-4 md:px-3 mt-2 md:mt-0 transition-all duration-200 hover:bg-ntrl-black-3"
          disabled={disableBtn}
          aria-label="Check selected policy"
        >
          {selectedCategory && selectedCompanySlug && selectedVariantSlug ? (
            <CustomLink
              href={`/${selectedCategory}-insurance/${selectedCompanySlug}/${selectedVariantSlug}`}
              utm={utm}
              style="w-full text-white cursor-pointer"
            >
              Check
            </CustomLink>
          ) : (
            <span className="w-full text-white">Check</span>
          )}
        </button>
      </div>
    </div>
  );
};

export default ProductDropdown;
