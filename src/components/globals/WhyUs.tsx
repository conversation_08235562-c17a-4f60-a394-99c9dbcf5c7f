import Container from "@/components/globals/Container";
import WhyUsCard from "./WhyUsCard";
import BookACallBtn from "@/components/globals/BookACall";
import { Dispatch, SetStateAction } from "react";

const WhyUs = ({
  whyUs,
  setOpenModal,
}: {
  whyUs: {
    id: number;
    reason: {
      id: number;
      title: string;
      desc: string;
      backgroundColor: string;
      thumbnail: {
        data: {
          id: number;
          attributes: {
            url: string;
          };
        };
      };
    }[];
  };
  setOpenModal: Dispatch<SetStateAction<boolean>>;
}) => {
  return (
    <div className="md:mb-20 my-10">
      <Container>
        <div className="mb-12">
          {/* Heading */}
          <div className="flex items-center justify-center font-generalSans text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black gap-2 mb-4 ">
            <h2 className="font-normal">Why</h2>
            <h2 className="font-medium">Oneassure</h2>
          </div>

          <p className="tet-[16px]/[24px] md:text-[18px]/[28px] text-center text-ntrl-grey1">
            Make wise insurance decisions with OneAssure. Receive 100% unbiased
            recommendations
          </p>
        </div>

        {/* Cards */}
        <div className="xl:grid grid-cols-12 gap-[30px] mb-12 hidden">
          {whyUs.reason.map((r, idx) => (
            <WhyUsCard
              title={r.title}
              subTitle={r.desc}
              thumbnail={r.thumbnail.data.attributes.url}
              color={`bg-${r.backgroundColor} min-w-[284px]`}
              style={
                idx < whyUs.reason.length - 2 ? "col-span-4" : "col-span-6"
              }
              key={idx}
            />
          ))}
        </div>
        <div className="xl:flex items-center justify-center hidden">
          <BookACallBtn
            className="py-3 px-6 md:px-8 bg-primary-2 rounded-lg text-ntrl-white"
            utm="website-personal-homepage"
          />
        </div>
      </Container>

      {/* Mobile screens */}
      <div className="xl:hidden flex items-center gap-[30px] mb-12 overflow-x-auto px-5 no-scrollbar">
        {whyUs.reason.map((r, idx) => (
          <WhyUsCard
            title={r.title}
            subTitle={r.desc}
            thumbnail={r.thumbnail.data.attributes.url}
            color={`bg-${r.backgroundColor} min-w-[284px]`}
            style={""}
            key={idx}
          />
        ))}
      </div>
      <div className="flex items-center justify-center xl:hidden">
        <BookACallBtn className="py-3 px-6 md:px-8 bg-primary-2 rounded-lg text-ntrl-white" />
      </div>
      {/* Mobile screens */}
    </div>
  );
};

export default WhyUs;
