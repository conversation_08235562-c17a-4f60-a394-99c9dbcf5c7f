import Image from "next/image";
import { LPHowToChooseHI } from "../HealthInsurance/types";
import parse, { Element } from "html-react-parser";

const HowToChooseHI = ({ data }: { data: LPHowToChooseHI }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-blue400 text-3xl font-semibold my-5";
          break;

        case "h3":
          attrs.className = "text-blue400 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-blue400 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className = "text-ntrl-white text-[12px] text-left";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;
      }
      return domNode;
    }
  };
  return (
    <div className="-mx-4 md:mb-14 mb-5">
      {/* Heading */}
      <div className="relative mt-6 bg-gradient-to-r from-gradient-1-green to-gradient-1-blue bg-200%  md:rounded-5xl text-center w-full shadow-whyOneassure flex font-medium overflow-hidden p-7">
        <div className="absolute bottom-0 left-0 md:left-28 transform -translate-x-1/2 translate-y-1/2">
          <Image
            src={
              "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
            }
            alt=""
            width={500}
            height={500}
            objectFit="cover"
          />
        </div>

        <div className="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2">
          <Image
            src={
              "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
            }
            alt=""
            width={570}
            height={570}
            objectFit="cover"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-[30%_70%] items-center">
          <div>
            <h2 className="md:text-3xl text-lg font-medium text-ntrl-white mb-4 text-left">
              {data.title}
            </h2>
            {/* @ts-expect-error unknown type */}
            {parse(data.description, { replace })}
          </div>

          <div className="md:ml-7 mt-4 md:mt-0 ml-0 grid grid-cols-1 md:grid-cols-3 gap-4">
            {data.sectionData.map((expert, idx) => (
              <div
                className="rounded-2xl relative p-6 flex flex-col justify-between mb-4 md:mb-0"
                key={idx}
              >
                <div className="bg-cardBackground absolute inset-0 rounded-4xl backdrop-blur-[6px] opacity-40 border-[1px]"></div>

                <div className="relative z-10">
                  <div className="items-start mb-2">
                    <div className="relative w-[45px] h-[46px]">
                      <Image
                        src={expert.icon?.data?.attributes.url || ""}
                        width={45}
                        height={46}
                        alt="health-experts"
                        fill={false}
                        style={{ objectFit: "contain" }}
                      />
                    </div>
                    <h3 className="text-white font-semibold text-[16px]/[20px] mt-2 text-left pt-2">
                      {expert.title || "Hight sum assured at low premium"}
                    </h3>
                  </div>
                  {/* @ts-expect-error unknown type */}
                  {parse(expert.description, { replace })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HowToChooseHI;
