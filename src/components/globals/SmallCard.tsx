import { FaWeight, FaCalculator, FaPen, FaCheckCircle } from "react-icons/fa";
import { BodyLarge, HeadingSmall } from "../UI/Typography";

// Define the data structure for each card
type ResultCardData = {
  id: string | number;
  iconKey?: string;
  title: string;
  description: string;
};

const getIconByKey = (key?: string) => {
  switch (key) {
    case "phone":
      return <FaWeight className="w-4 h-4 text-tertiary-orange-400" />;
    case "calculator":
      return <FaCalculator className="w-4 h-4 text-tertiary-orange-400" />;
    case "graph":
      return <FaPen className="w-4 h-4 text-tertiary-orange-400" />;
    case "check":
      return <FaCheckCircle className="w-4 h-4 text-tertiary-orange-400" />;
    default:
      return <FaPen className="w-4 h-4 text-tertiary-orange-400" />;
  }
};

// Result Card Component
const SmallCard = ({ card }: { card: ResultCardData }) => (
  <div className="bg-white rounded-xl p-6 shadow-sm border border-primary-200 flex flex-col items-center gap-3 text-center w-full">
    <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
      {getIconByKey(card.iconKey)}
    </div>
    <HeadingSmall as="h3" className="font-semibold text-neutral-1100">
      {card.title}
    </HeadingSmall>
    <BodyLarge as="p" className="text-neutral-900">
      {card.description}
    </BodyLarge>
  </div>
);

export default SmallCard;
