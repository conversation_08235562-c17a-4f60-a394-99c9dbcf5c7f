import { useSearchParams } from "next/navigation";
import CustomLink from "./CustomLink";
import { IoIosArrowForward } from "react-icons/io";

const Breadcrumb = ({
  path,
  fullUrl,
}: {
  path: string[];
  fullUrl?: string;
}) => {
  const search = useSearchParams();
  const utm = search.get("utm_source");
  let link = "";

  const capitalizeWords = (str: string) => {
    return str
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const getDisplayName = (pathElement: string) => {
    if (pathElement.includes("/")) {
      const parts = pathElement.split("/");
      return parts.length > 1 ? parts[1] : parts[0];
    }
    return pathElement;
  };

  return (
    <div className="flex flex-wrap items-center justify-center md:justify-start">
      {path.map((p, idx) => {
        let pathName = getDisplayName(p);
        pathName = pathName.split("-").join(" ");
        pathName = capitalizeWords(pathName);

        // Handle OneAssure and home cases
        if (pathName === "OneAssure" || pathName.toLowerCase() === "home") {
          link = "/";
        } else if (pathName === "Health Insurance") {
          link = "/health-insurance";
        } else if (pathName === "Compare Health Insurance Plans") {
          link = "/compare-health-insurance-plans";
        } else if (pathName === "Partner" || pathName === "Our Partners") {
          link = "/partner";
        } else {
          // For the last item, use fullUrl if provided (includes dynamic route)
          if (idx === path.length - 1 && fullUrl) {
            link = fullUrl;
          } else {
            // Build path using proper URL slugs instead of display names
            const pathParts = [];
            for (let i = 0; i <= idx; i++) {
              if (path[i] !== "OneAssure" && path[i].toLowerCase() !== "home") {
                // Convert display names back to URL slugs
                if (path[i] === "Health Insurance") {
                  pathParts.push("health-insurance");
                } else if (path[i] === "Compare Health Insurance Plans") {
                  pathParts.push("compare-health-insurance-plans");
                } else if (path[i] === "Partner" || path[i] === "Our Partners") {
                  pathParts.push("partner");
                } else {
                  pathParts.push(path[i]);
                }
              }
            }
            link = pathParts.length > 0 ? "/" + pathParts.join("/") : "/";
          }
        }

        return (
          <span key={idx} className="flex items-center text-center">
            {idx === path.length - 1 ? (
              <p className="text-xs text-neutral-800 break-words overflow-hidden">
                {pathName}
              </p>
            ) : (
              <CustomLink href={link} utm={utm}>
                <p className="text-xs transition-all duration-200 ease-in-out text-neutral-1100 hover:text-primary-500 break-words overflow-hidden">
                  {pathName}
                </p>
              </CustomLink>
            )}
            {idx < path.length - 1 && (
              <IoIosArrowForward
                className="mx-2 text-neutral-1100 flex-shrink-0"
                size={16}
              />
            )}
          </span>
        );
      })}
    </div>
  );
};

export default Breadcrumb;
