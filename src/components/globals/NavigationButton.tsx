import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";

const NavigationButton = ({
  onClick,
  disabled,
  direction,
}: {
  onClick: () => void;
  disabled: boolean;
  direction: "prev" | "next";
}) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={`w-10 h-10 rounded-full shadow-base flex items-center justify-center transition-all duration-300 ${
      disabled
        ? "bg-gray-100 text-gray-300 cursor-not-allowed"
        : "bg-gray-100 text-neutral-1100 hover:text-white border border-gray-200 hover:shadow-xl"
    }`}
  >
    {direction === "prev" ? <IoIosArrowBack /> : <IoIosArrowForward />}
  </button>
);

export default NavigationButton;
