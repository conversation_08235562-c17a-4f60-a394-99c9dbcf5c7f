import { orufyHand<PERSON> } from "@/utils/orufyHandler";
import React from "react";
import { FaPhone } from "react-icons/fa";

const OrufyFloater: React.FC = () => {
  const handlePhoneClick = () => {
    orufyHandler(process.env.NEXT_PUBLIC_ONE_ASSURE_ORUFY_LINK as string);
  };

  return (
    <div className="fixed bottom-24 right-6 z-50">
      <button
        onClick={handlePhoneClick}
        className="w-12 h-12 bg-primary-800 hover:bg-primary-900 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 ease-in-out transform hover:scale-110"
        aria-label="Call us"
      >
        <FaPhone size={20} />
      </button>
    </div>
  );
};

export default OrufyFloater;
