"use client";

import React, { useState } from "react";
import { BiSolidQuoteAltRight } from "react-icons/bi";
import SectionContainer from "@/components/globals/SectionContainer";
import SectionHeader from "@/components/globals/SectionHeader";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { BodyLarge } from "@/components/UI/Typography";
import NavigationButton from "@/components/globals/NavigationButton";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import parse, { DOMNode, Element, domToReact } from "html-react-parser";

//custom html-renderer for this component
const htmlRenderer = (domNode: DOMNode) => {
  if (domNode.type === "tag" && domNode.attribs) {
    let attrs = domNode.attribs;
    if (attrs.style) {
      attrs.style = "";
    }

    switch (domNode.name) {
      case "p":
        // Check if p contains a span and extract its content
        if (domNode.children && domNode.children.length > 0) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            // Create a new p element with the span's content
            return (
              <BodyLarge as="p">
                {domToReact(spanChild.children as DOMNode[])}
              </BodyLarge>
            );
          }
        }
        return (
          <BodyLarge as="p">
            {domToReact(domNode.children as DOMNode[])}
          </BodyLarge>
        );
    }
    return domNode;
  }
};

type SectionHeaderProps = {
  pill: string;
  heading: string;
  subheading: string;
};

type Testimonial = {
  id: string;
  name: string;
  content: string;
};

const TestimonialDS: React.FC<{
  testimonials: Testimonial[];
  sectionHeaderProps: SectionHeaderProps;
  pill: string;
}> = ({ testimonials, sectionHeaderProps, pill }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === testimonials.length - 3 ? 0 : prevIndex + 1
    );
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? testimonials.length - 3 : prevIndex - 1
    );
  };

  const visibleTestimonials = testimonials.slice(
    currentIndex,
    currentIndex + 3
  );

  return (
    <SectionContainer id="testimonials" className="!px-0">
      <SectionHeader
        pill={pill}
        heading={sectionHeaderProps.heading}
        subheading={sectionHeaderProps.subheading}
        component="h2"
        className="px-6 md:px-0"
      />

      {/* Desktop Navigation Layout */}
      <div className="hidden md:flex items-center justify-center">
        <NavigationButton
          onClick={prevTestimonial}
          direction="prev"
          disabled={currentIndex === 0}
        />

        {/* Testimonials Container */}
        <SectionContainerLarge className="flex gap-4 justify-center items-stretch !mb-0 md:!mb-0">
          {visibleTestimonials.map((testimonial, index) => (
            <div
              key={testimonial.id}
              className="flex flex-col gap-3 justify-between max-w-sm bg-primary-50 border border-primary-300 rounded-xl p-6 text-center shadow-sm hover:shadow-md transition-shadow duration-200"
            >
              {/* Quote Icon */}
              <div className="flex justify-center">
                <div className="w-16 h-16 bg-primary-200 border border-primary-300 rounded-full flex items-center justify-center">
                  <BiSolidQuoteAltRight className="w-8 h-8 text-primary-800" />
                </div>
              </div>

              {/* Testimonial Text */}
              <div className="text-neutral-900 text-justify">
                {parse(testimonial.content, { replace: htmlRenderer })}
              </div>

              {/* Author Name */}
              <BodyLarge className="text-neutral-1100 font-semibold">
                {testimonial.name}
              </BodyLarge>
            </div>
          ))}
        </SectionContainerLarge>

        <NavigationButton
          onClick={nextTestimonial}
          direction="next"
          disabled={currentIndex >= testimonials.length - 3}
        />
      </div>

      {/* Mobile Carousel Layout */}
      <div className="md:hidden">
        <MobileCarousel totalSlides={testimonials.length}>
          {testimonials.map((testimonial, index) => (
            <MobileCarouselItem key={testimonial.id}>
              <div className="flex flex-col gap-3 justify-between bg-primary-50 border border-primary-300 rounded-xl p-6 text-center shadow-sm h-full">
                {/* Quote Icon */}
                <div className="flex justify-center">
                  <div className="w-16 h-16 bg-primary-200 border border-primary-300 rounded-full flex items-center justify-center">
                    <BiSolidQuoteAltRight className="w-8 h-8 text-primary-800" />
                  </div>
                </div>

                {/* Testimonial Text */}
                <div className="text-neutral-900 text-justify">
                  {parse(testimonial.content, { replace: htmlRenderer })}
                </div>

                {/* Author Name */}
                <BodyLarge className="text-neutral-1100 font-semibold">
                  {testimonial.name}
                </BodyLarge>
              </div>
            </MobileCarouselItem>
          ))}
        </MobileCarousel>
      </div>
    </SectionContainer>
  );
};

export default TestimonialDS;
