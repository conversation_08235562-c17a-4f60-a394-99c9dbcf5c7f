"use client";

import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { forwardRef, useEffect, useRef } from "react";
import { IoCall } from "react-icons/io5";
import { useSessionStorage } from "usehooks-ts";
import { usePathname } from "next/navigation";
import { orufyHandler } from "@/utils/orufyHandler";

const BookACallBtn = forwardRef(function BookACallBtn(
  {
    onClick,
    className,
    icon = false,
    arrow = false,
    rightIcon = false,
    label = "Book a Call",
    utm,
    ...props
  }: {
    onClick?: () => void;
    className?: string;
    label?: string;
    icon?: boolean;
    arrow?: boolean;
    rightIcon?: boolean;
    utm?: string;
    [key: string]: any;
  },
  ref
) {
  const pathname = usePathname();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", "");
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", "");
  const [utm_campaign, setUtmCampaign] = useSessionStorage("utm_campaign", "");
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", "");
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", "");

  useEffect(() => {
    function isOrufyEvent(e: MessageEvent) {
      return (
        e.origin === "https://orufybookings.com" &&
        e.data.event &&
        e.data.event.indexOf("orufybookings.") === 0
      );
    }

    function handleOrufyMessage(e: MessageEvent) {
      if (isOrufyEvent(e)) {
        // console.log("Event name:", e.data.event);
        // console.log("Event details:", e.data.payload);
      }
    }

    if (typeof window !== "undefined") {
      window.addEventListener("message", handleOrufyMessage);
    }

    return () => {
      window.removeEventListener("message", handleOrufyMessage);
    };
  }, []);

  function handleClick() {
    const orufyLink = `${process.env.NEXT_PUBLIC_ONE_ASSURE_ORUFY_LINK}?utm_source=${utm_source}&utm_medium=${utm_medium}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&utm_term=${utm_term}`;
    orufyHandler(orufyLink);
  }

  return (
    <button
      onClick={handleClick}
      className={`font-medium cursor-pointer text-base md:text-2xl ${className}`}
      {...props}
      // @ts-ignore
      ref={ref}
    >
      {icon && (
        <div className="bg-secondary-blue-2 p-2 rounded-full">
          <IoCall className="text-xl text-white" />
        </div>
      )}
      {label}
      {arrow && <ArrowRightIcon className="h-5 md:h-7" />}
      {rightIcon && (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
            clipRule="evenodd"
          />
        </svg>
      )}
    </button>
  );
});

export default BookACallBtn;
