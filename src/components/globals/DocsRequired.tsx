import Image from "next/image";
import { LPRequiredDocs } from "../HealthInsurance/types";
import parse, { Element } from "html-react-parser";

type DocsRequiredProps = {
  logo?: string;
  title?: string;
  description?: string;
}[];

const DocsRequired = ({ data }: { data: LPRequiredDocs }) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-blue400 text-3xl font-semibold my-5";
          break;

        case "h3":
          attrs.className = "text-blue400 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-blue400 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[12px]/[18px] font-normal text-ntrl-black-1 text-left md:text-center";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;
      }
      return domNode;
    }
  };
  return (
    <div className="text-center md:mb-14 mb-5">
      <h2 className="md:text-3xl text-lg font-medium text-ntrl-black-1">
        Documents Required to Buy Term Insurance Plan in India
      </h2>
      <p className="text-[14px]/[20px] font-light text-ntrl-black-1">
        Buy Term Insurance with Oneassure in 4 Simple Steps
      </p>

      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 md:gap-10 mt-8 md:px-40">
        {data.sectionData.map((doc, index) => (
          <div
            key={index}
            className="flex flex-row md:flex-col items-center gap-3"
          >
            <Image
              src={doc.icon?.data?.attributes?.url || ""}
              alt="vector"
              width={100}
              height={100}
              className="mb-2 size-10"
              style={{ objectFit: "contain" }}
            />
            <div className="flex flex-col md:items-center md:justify-center">
              <h3 className="text-[14px]/[20px] font-bold text-ntrl-black-1 text-left md:mb-2 md:text-center">
                {doc.title}
              </h3>
              <div className="text-justify">
                {/* @ts-expect-error unknown type */}
                {parse(doc.description, { replace })}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DocsRequired;
