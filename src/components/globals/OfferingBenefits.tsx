"use client";

import React from "react";
import customer from "@/assets/customer.png";
import Image from "next/image";
// import circles2 from "@/assets/circles2.png";
import { custom } from "zod";
import { HomeDataObject } from "../Home/types";
// import Vector from "@/assets/Vector.png";
interface OfferingBenefitsProps {
  className?: string;
  title?: string;
  description?: string;
  offers: {
    title: string;
    description: string;
    logo: {
      data: {
        attributes: {
          url: string;
        };
      };
    };
  }[];
}

const OfferingBenefits: React.FC<OfferingBenefitsProps> = ({
  title,
  description,
  offers,
  className,
}) => {
  return (
    <div className="relative md:mb-14 mb-5 bg-gradient-to-r from-blue1000 to-blue900 bg-200% p-2 md:p-0  rounded-5xl text-center w-full shadow-whyOneassure overflow-hidden ">
      <div className="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 hidden md:block">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={570} // Increased width
          height={570} // Increased height
          objectFit="cover"
        />
      </div>

      <div className="absolute md:bottom-36 md:left-12 left-0 bottom-0 transform -translate-x-1/2 translate-y-1/2">
        <Image
          src={
            "https://cdn.oasr.in/oa-site/cms-uploads/media/background_circles_b65485c812.svg"
          }
          alt=""
          width={570}
          height={570}
          objectFit="cover"
        />
      </div>
      <h2 className="md:pt-6 pt-4 md:text-2xl text-lg text-white font-medium md:mb-0 mb-2">
        {title}
      </h2>
      <p className="text-sm text-white font-light mx-2 md:mx-0">
        {description}
      </p>

      <div className={`${className}`}>
        {offers.map((offer, index) => (
          <div
            key={index}
            className="rounded-2xl relative flex flex-col justify-start items-center "
          >
            <div className="md:size-24 size-20 z-10 rounded-full bg-white shadow-md flex justify-center items-center">
              <Image
                src={offer.logo.data.attributes.url}
                alt="image"
                width={55}
                height={55}
                className="md:size-18 size-12"
              />
            </div>

            <div className="my-2 text-center text-white ">
              <p className="text-lg font-medium ">{offer.title}</p>
            </div>
            <div>
              <p className="text-sm font-light text-white">
                {offer.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default OfferingBenefits;
