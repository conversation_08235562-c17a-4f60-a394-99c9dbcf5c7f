import Breadcrumb from "./Breadcrumb";
import SectionContainer from "./SectionContainer";
import SocialShareButtons from "./SocialShareButtons";

const PageTopBar = ({
  breadcrumbPath,
  fullUrl,
}: {
  breadcrumbPath: string[];
  fullUrl: string;
}) => {
  return (
    <SectionContainer className=" flex flex-col md:flex-row items-center md:justify-between md:mb-6">
      {/* Desktop: Original layout */}
      <div className="hidden md:block">
        <Breadcrumb path={breadcrumbPath} fullUrl={fullUrl} />
      </div>
      <div className="hidden md:block">
        <SocialShareButtons />
      </div>
    </SectionContainer>
  );
};

export default PageTopBar;
