import React from "react";

interface IconTile {
  id: number;
  title: string;
  value: string;
  icon: {
    data: {
      attributes: {
        url: string;
      };
    };
  };
}

const IconTiles = ({ cardData }: { cardData: IconTile[] }) => {
  return (
    <div className="w-full flex flex-col md:flex-row gap-4 md:gap-8 justify-between items-center md:px-10 px-0">
      {cardData.map((card) => (
        <div
          key={card.id}
          className="w-full md:w-auto bg-gradient-to-r from-[#2ca6c9] to-[#55b5aa] p-[2px] rounded-2xl md:max-w-[350px] flex-1"
        >
          <div className="flex justify-start items-center gap-8 bg-white rounded-2xl p-5 shadow-lg h-full">
            <div className="flex-shrink-0 ml-4">
              <img
                src={card.icon.data.attributes.url}
                alt={card.title}
                width={50}
                height={50}
                className="w-12 h-12"
              />
            </div>
            <div className="text-gray-800 text-lg font-medium text-start flex-1">
              <div>
                <span className="text-2xl font-semibold bg-gradient-to-r from-[#2ca6c9] to-[#55b5aa] bg-clip-text text-transparent select-all">
                  {card.value}
                </span>
              </div>
              {card.title}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default IconTiles;
