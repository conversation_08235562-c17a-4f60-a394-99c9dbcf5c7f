"use client";
import { useEffect } from "react";
import { useIntersectionObserver } from "usehooks-ts";

import useNavbarStore from "@/store/navbar";

const FakeIntersection = () => {
  const navbarStore = useNavbarStore();

  const { isIntersecting, ref } = useIntersectionObserver({
    rootMargin: "100px 0px 0px 0px",
  });

  useEffect(() => {
    navbarStore.updateVisibility(isIntersecting);
  }, [isIntersecting]);

  return <div ref={ref} className=""></div>;
};

export default FakeIntersection;
