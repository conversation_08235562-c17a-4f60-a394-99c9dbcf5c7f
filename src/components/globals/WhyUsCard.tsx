"use client";

import Image from "next/image";

const WhyUsCard = ({
  style,
  color,
  title,
  subTitle,
  thumbnail,
}: {
  style?: string;
  color: string;
  title: string;
  subTitle: string;
  thumbnail: string;
}) => {
  return (
    <div
      className={`bg-primary-3 ${color} ${style} grid ${
        style === "col-span-6" ? "grid-cols-2" : "grid-cols-1"
      } rounded-lg`}
    >
      {/* Heading */}
      <div className="h-72 md:h-60 px-6 py-8">
        <h3 className="text-[24px]/[30px] font-generalSans font-medium text-ntrl-black mb-4">
          {title}
        </h3>
        <p className="text[16px]/[24px] text-ntrl-grey1 font-normal">
          {subTitle}
        </p>
      </div>
      {/* Image */}
      <div
        className={`bg-ntrl-grey2 relative ${
          style === "col-span-6" ? "rounded-r-lg h-[100%]" : "rounded-b-lg h-60"
        }`}
      >
        <Image
          src={thumbnail}
          fill={true}
          style={{ objectFit: "cover" }}
          alt={title}
          className="rounded-xl"
        />
      </div>
    </div>
  );
};

export default WhyUsCard;
