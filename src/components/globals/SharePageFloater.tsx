import React, { useState, useRef, useEffect } from "react";
import {
  FaFacebook,
  FaLinkedin,
  FaWhatsapp,
  FaXTwitter,
} from "react-icons/fa6";
import { MdOutlineMail } from "react-icons/md";
import { HiOutlineShare } from "react-icons/hi";

const SharePageFloater: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const shareUrl = typeof window !== "undefined" ? window.location.href : "";
  const shareTitle = typeof document !== "undefined" ? document.title : "";

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const getShareUrl = (platform: string) => {
    switch (platform) {
      case "facebook":
        return `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
          shareUrl
        )}`;
      case "email":
        return `mailto:?subject=${encodeURIComponent(
          shareTitle
        )}&body=${encodeURIComponent(`Check out this page: ${shareUrl}`)}`;
      case "x":
        return `https://x.com/intent/tweet?url=${encodeURIComponent(
          shareUrl
        )}&text=${encodeURIComponent(shareTitle)}`;
      case "linkedin":
        return `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
          shareUrl
        )}`;
      case "whatsapp":
        return `https://wa.me/?text=${encodeURIComponent(
          `${shareTitle} ${shareUrl}`
        )}`;
      default:
        return shareUrl;
    }
  };

  const handleShare = (platform: string) => {
    const shareUrlFinal = getShareUrl(platform);

    if (platform === "email") {
      window.location.href = shareUrlFinal;
    } else {
      window.open(shareUrlFinal, "_blank");
    }
    setIsOpen(false);
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      // You could add a toast notification here
      console.log("Link copied to clipboard!");
    } catch (err) {
      console.error("Failed to copy link:", err);
    }
    setIsOpen(false);
  };

  const icons = {
    facebook: <FaFacebook color="#1877F3" size={16} />,
    email: <MdOutlineMail color="#D44638" size={16} />,
    x: <FaXTwitter color="#000000" size={16} />,
    linkedin: <FaLinkedin color="#0077B5" size={16} />,
    whatsapp: <FaWhatsapp color="#25D366" size={16} />,
  };

  return (
    <div className="fixed bottom-8 right-6 z-50" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-12 h-12 bg-primary-800 hover:bg-primary-900 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 ease-in-out transform hover:scale-110"
        aria-label="Share this page"
      >
        <HiOutlineShare size={20} />
      </button>

      {isOpen && (
        <div className="absolute bottom-16 right-0 w-64 bg-white rounded-lg shadow-lg border border-gray-200">
          <div className="p-3">
            <div className="text-xs text-gray-500 mb-3 font-medium">
              Share via
            </div>
            <div className="grid grid-cols-2 gap-2">
              {(
                [
                  { key: "facebook", label: "Facebook" },
                  { key: "x", label: "X" },
                  { key: "linkedin", label: "LinkedIn" },
                  { key: "whatsapp", label: "WhatsApp" },
                  { key: "email", label: "Email" },
                ] as const
              ).map(({ key, label }) => (
                <button
                  key={key}
                  onClick={() => handleShare(key)}
                  className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-50 transition-colors text-left"
                >
                  {icons[key]}
                  <span className="text-sm text-gray-700">{label}</span>
                </button>
              ))}
            </div>
            <div className="border-t border-gray-200 mt-3 pt-3">
              <button
                onClick={handleCopyLink}
                className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-50 transition-colors w-full text-left"
              >
                <svg
                  className="w-4 h-4 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
                <span className="text-sm text-gray-700">Copy Link</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SharePageFloater;
