import { useEffect, useState } from "react";
import BookACallBtn from "@/components/globals/BookACall";
import useNavbarStore from "@/store/navbar";

interface MenuItem {
  title: string;
  id: string;
}

interface SideMenuProps {
  menuItems: MenuItem[];
  classsName?: string;
}

const SideMenu: React.FC<SideMenuProps> = ({ menuItems, classsName }) => {
  const [activeSection, setActiveSection] = useState<string>("");
  const navbarStore = useNavbarStore();

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveSection(entry.target.id);
            const newUrl = `${window.location.pathname}#${entry.target.id}`;
            window.history.replaceState(null, "", newUrl);
          }
        });
      },
      {
        rootMargin: "-20% 0px -70% 0px",
        threshold: 0,
      }
    );

    menuItems.forEach((menu) => {
      const element = document.getElementById(menu.id);
      if (element) observer.observe(element);
    });

    return () => {
      menuItems.forEach((menu) => {
        const element = document.getElementById(menu.id);
        if (element) observer.unobserve(element);
      });
    };
  }, [menuItems]);

  const handleClick = (id: string) => {
    if (typeof window === "undefined") return;

    const element = document.getElementById(id);
    if (!element) return;

    const currentScroll = window.scrollY;
    const navbar = document.querySelector("nav");
    const navbarHeight = navbar ? navbar.getBoundingClientRect().height * 2 : 0;

    const scrollToPosition =
      element.getBoundingClientRect().top + currentScroll - navbarHeight - 20;

    if (navbarStore.isNavbarVisible) {
      window.scrollTo({ top: scrollToPosition, behavior: "smooth" });
    } else {
      element.scrollIntoView({ behavior: "smooth" });
    }

    const newUrl = `${window.location.pathname}#${id}`;
    window.history.replaceState(null, "", newUrl);
  };

  return (
    <section
      id="sideMenu"
      className={`hidden lg:block col-span-2 mb-[30px] ${classsName}`}
    >
      <div className="sticky top-32">
        <div className="bg-white px-5 py-7 rounded-3xl text-center">
          {menuItems.map((menu) => (
            <div key={menu.id} className="my-5">
              <span
                onClick={() => handleClick(menu.id)}
                className={`
                cursor-pointer transition-all duration-200 text-nowrap text-[16px]/[20px]  
                ${
                  activeSection === menu.id
                    ? "text-ntrl-black font-semibold "
                    : "hover:text-primary-1 hover:font-semibold font-light"
                }
                `}
              >
                {menu.title}
              </span>
            </div>
          ))}
        </div>
        <div className="py-5 text-center flex justify-center">
          <BookACallBtn
            icon={true}
            className="py-2 px-3 w-full rounded-2xl bg-gradient-to-r from-gradient-1-blue to-gradient-1-green text-ntrl-white flex gap-4 items-center justify-center md:text-lg"
            label="Book a Call"
          />
        </div>
      </div>
    </section>
  );
};

export default SideMenu;
