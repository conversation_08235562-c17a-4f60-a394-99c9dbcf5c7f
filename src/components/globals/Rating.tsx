"use client";
import { FaRegStar } from "react-icons/fa";
import { FaStarHalfAlt } from "react-icons/fa";
import { FaStar } from "react-icons/fa";

const Rating: React.FC<{
  rating: number;
  classname?: string;
}> = (props) => {
  const fullStars = Math.floor(props.rating);
  const halfStar = props.rating - fullStars >= 0.1 ? 1 : 0;
  const emptyStars = 5 - fullStars - halfStar;
  return (
    <div
      className={`flex items-center flex-row ${props.classname} gap-1 rounded-[10px] border-2 border-[#06C270] px-2 py-1`}
    >
      <p className="text-[#06C270] font-medium hover:underline">
        {props.rating}
      </p>
      <div className="flex items-center gap-1">
        {[...Array(fullStars)].map((e, i) => (
          <FaStar className="text-[#FFD700] h-[13px]" key={i} />
        ))}
        {halfStar !== 0 && (
          <FaStarHalfAlt className="text-[#FFD700] h-[13px]" />
        )}
        {[...Array(emptyStars)].map((e, i) => (
          <FaRegStar key={i} className="text-[#FFD700] h-[13px]" />
        ))}
      </div>
    </div>
  );
};

export default Rating;
