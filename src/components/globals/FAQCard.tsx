"use client";

import { useState } from "react";
import parse from "html-react-parser";
import { Element } from "html-react-parser";
import { PlusIcon } from "@heroicons/react/24/outline";

const FAQCard = ({ question, ans }: { question: string; ans: string }) => {
  const [open, setOpen] = useState(false);

  const replace = (domNode: Element) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }
      if (domNode.name === "p") {
        attrs.className =
          "text-ntrl-grey1 md:text-[16px]/[24px] text-[14px]/[18px] font-normal px-2 md:px-0 md:mt-2";
      }
      return domNode;
    }
  };

  return (
    <div className="p-[1.5px] relative rounded-xl">
      {/* Absolute gradient layer with opacity */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary-green-2 to-primary-blue-3 opacity-30 rounded-4xl shadow-customgreen4d"></div>

      {/* Inner content (fully interactive) */}
      <div
        className="relative py-3 md:pl-8 md:px-4 md:py-5 px-4 cursor-pointer bg-ntrl-white rounded-4xl z-10 shadow-customgreen4d font-semibold"
        onClick={() => setOpen(!open)}
      >
        <h2 className="font-medium text-sm md:text-lg text-ntrl-black font-generalSans flex items-center justify-between">
          {question}
          <PlusIcon
            className={`size-5 flex-shrink-0 flex transition-transform duration-300 ${
              open ? "-rotate-45" : ""
            }`}
          />
        </h2>
        {open && (
          <div className="mt-2">
            {/* @ts-ignore */}
            {parse(ans, { replace })}
          </div>
        )}
      </div>
    </div>
  );
};

export default FAQCard;
