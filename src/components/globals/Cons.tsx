import Image from "next/image";
import parse, { Element } from "html-react-parser";
import { XMarkIcon } from "@heroicons/react/24/outline";
import Accordian from "./Accordian";

interface ConsProps {
  cons: Array<{
    title: string;
    description: string;
  }>;
}

const Cons: React.FC<ConsProps> = (props) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }
      if (domNode.name === "strong") {
      }
      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[30px] text-ntrl-black font-light";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );
        case "strong":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[24px] text-ntrl-black font-semibold";
          break;

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };

  const generateExclusionsHtml = (
    exclusions: Array<{
      exclusion: string;
    }>
  ): string => {
    const crossIcon = `
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        aria-hidden="true"
        data-slot="icon"
        class="flex-none h-4 mt-1 text-red-1 font-bold mr-3"
      >
        <-2ath
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M6 18 18 6M6 6l12 12"
        ></path>
      </svg>`;
    const listItems = exclusions
      .map(({ exclusion }) => {
        const description = exclusion.startsWith("<p>")
          ? exclusion.slice(3, -4)
          : exclusion;
        return `<li><div className="flex">${crossIcon}<p>${description}</p></div></li>`;
      })
      .join("");
      return `<ul>${listItems}</ul>`;
  };

  return (
    <section
      id="cons"
      className="mt-5 md:px-[50px] md:py-7 scroll-m-28 bg-white rounded-3xl"
    >
      <div className="hidden md:flex text-custom-32 text-ntrl-black gap-2 items-center py-2">
        <h2 className="font-semibold">Cons</h2>
      </div>
      <div className="hidden md:block">
        {props.cons.map((con, index) => (
          <div key={index} className="flex flex-row">
            <XMarkIcon className="h-4 w-4 text-red-600 font-bold mt-[6px] mr-2 flex-shrink-0" />
            <div className="text-[14px]/[22px] md:text-[16px]/[26px]">
              {/* @ts-ignore */}
              {parse(con.description, { replace })}
            </div>
          </div>
        ))}
      </div>

      <Accordian
        className="md:hidden mb-4"
        name="Cons"
        description={props.cons.map(con => con.description).join('')}
      />
    </section>
  );
};

export default Cons; // Renamed component to Cons