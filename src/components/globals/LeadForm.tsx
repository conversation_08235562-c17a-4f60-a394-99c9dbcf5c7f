"use client";

import React from "react";
import PillBadge from "./PillBadge";
import { useFormik } from "formik";
import * as Yup from "yup";
import { IoCallOutline } from "react-icons/io5";
import { useCreateLead } from "@/components/LeadModal/api/createLead";
import { usePathname } from "next/navigation";
import { Button } from "../UI/Button";
import { BodyLarge, HeadingXLarge } from "../UI/Typography";
import SectionContainerMedium from "./SectionContainerMedium";

type LeadFormProps = {
  pill: string;
  title: string;
  description: string;
  id?: string;
};

// Move ContactForm outside to prevent recreation on every render
const ContactForm = ({ formik }: { formik: any }) => (
  <section className="w-full bg-white shadow-sm rounded-xl border border-primary-300 p-4 md:px-6 md:py-5">
    <form onSubmit={formik.handleSubmit} className="w-full">
      {/* Name Field */}
      <div className="flex flex-col md:flex-row justify-between gap-2 md:gap-6 mb-2 md:mb-3">
        <div className="w-full md:w-1/2">
          <label
            className="block text-neutral-1100 text-xs md:text-sm font-medium mb-2"
            htmlFor="lead_name"
          >
            Name
          </label>
          <input
            id="lead_name"
            name="lead_name"
            type="text"
            placeholder="Enter Name"
            autoComplete="name"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.lead_name}
            className="w-full rounded-xl border-none px-4 py-2 md:py-3 bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-200 text-xs md:text-sm text-neutral-800"
          />
          {formik.touched.lead_name && formik.errors.lead_name && (
            <p className="text-red-500 text-xs md:text-sm mt-1">
              {formik.errors.lead_name}
            </p>
          )}
        </div>
        {/* Phone Number Field */}
        <div className="w-full md:w-1/2">
          <label
            className="block text-neutral-1100 text-xs md:text-sm font-medium mb-2"
            htmlFor="lead_phone_number"
          >
            Phone Number
          </label>
          <input
            id="lead_phone_number"
            name="lead_phone_number"
            type="tel"
            placeholder="Enter Phone No."
            autoComplete="tel"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.lead_phone_number}
            className="w-full rounded-xl border-none px-4 py-2 md:py-3 bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-200 text-xs md:text-sm text-neutral-800"
          />
          {formik.touched.lead_phone_number &&
            formik.errors.lead_phone_number && (
              <p className="text-red-500 text-xs md:text-sm mt-1">
                {formik.errors.lead_phone_number}
              </p>
            )}
        </div>
      </div>

      {/* Email Field */}
      <div className="mb-2 md:mb-3">
        <label
          className="block text-neutral-1100 text-xs md:text-sm font-medium mb-2"
          htmlFor="lead_email"
        >
          Email
        </label>
        <input
          id="lead_email"
          name="lead_email"
          type="email"
          placeholder="Enter Email"
          autoComplete="email"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.lead_email}
          className="w-full rounded-xl border-none px-4 py-2 md:py-3 bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-200 text-xs md:text-sm text-neutral-800"
        />
        {formik.touched.lead_email && formik.errors.lead_email && (
          <p className="text-red-500 text-xs md:text-sm mt-1">
            {formik.errors.lead_email}
          </p>
        )}
      </div>

      {/* Your Enquiry Field */}
      <div className="mb-3">
        <label
          className="block text-neutral-1100 text-xs md:text-sm font-medium mb-2 "
          htmlFor="lead_message"
        >
          Your Enquiry
        </label>
        <textarea
          id="lead_message"
          name="lead_message"
          placeholder="Enter your query"
          rows={1}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.lead_message}
          className="w-full rounded-xl border-none px-4 py-2 md:py-3 bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-200 text-xs md:text-sm text-neutral-800"
        />
        {formik.touched.lead_message && formik.errors.lead_message && (
          <p className="text-red-500 text-xs md:text-sm mt-1">
            {formik.errors.lead_message}
          </p>
        )}
      </div>

      {/* Submit Button */}
      <div className="w-full flex justify-center items-center">
        <Button type="submit" variant="primary">
          <IoCallOutline size={20} />{" "}
          <span className="text-sm font-normal">Book a Free Call</span>
        </Button>
      </div>
    </form>
  </section>
);

export default function LeadForm({
  pill,
  title,
  description,
  id,
}: LeadFormProps) {
  const { mutate } = useCreateLead();
  const pathname = usePathname();

  const FormSchema = Yup.object().shape({
    lead_name: Yup.string()
      .matches(/^[A-Za-z ]*$/, "Please enter valid name")
      .required("Name is required"),
    lead_email: Yup.string()
      .email("Invalid email")
      .required("Email is required"),
    lead_phone_number: Yup.string()
      .length(10, "Invalid phone number")
      .matches(/^[0-9]+$/, "Phone number must contain only digits")
      .required("Phone number is required"),
    lead_message: Yup.string().trim().required("Enquiry is required"),
  });

  const formik = useFormik({
    initialValues: {
      lead_name: "",
      lead_phone_number: "",
      lead_email: "",
      lead_message: "",
    },
    validationSchema: FormSchema,
    validateOnChange: false, // Only validate on blur to improve performance
    validateOnBlur: true,
    onSubmit: (values, { resetForm }) => {
      const { lead_name, lead_phone_number, ...restObj } = values;
      mutate(
        {
          lead_name,
          lead_phone_number,
          lead_point: pathname,
          ...restObj,
        },
        {
          onSuccess: () => {
            alert("Your request is submitted! We will get back to you soon");
            resetForm();
          },
          onError: (error) => {
            alert("Something went wrong. Please try again.");
          },
        }
      );
    },
  });

  return (
    <SectionContainerMedium className="w-full" id={id}>
      <div className="flex flex-col md:flex-row md:justify-between items-stretch gap-6 md:gap-0">
        {/* Left Column - Informational Section */}
        <section className="w-full md:w-[35%] rounded-l-2xl px-4 py-0 md:p-0 flex flex-col justify-center items-center">
          <div className="mb-2 md:mb-6">
            <PillBadge pill={pill} />
          </div>
          <HeadingXLarge
            weight="semibold"
            as="h2"
            className="text-neutral-1100 mb-2 md:mb-3 text-center"
          >
            {title}
          </HeadingXLarge>
          <BodyLarge className="text-neutral-1100 text-center">
            {description}
          </BodyLarge>
        </section>

        {/* Right Column - Contact Form */}
        {/* Mobile: Form wrapped in primary background */}
        <div className="md:hidden">
          <ContactForm formik={formik} />
        </div>

        {/* Desktop: Form without wrapper */}
        <div className="hidden md:block md:w-[52%]">
          <ContactForm formik={formik} />
        </div>
      </div>
    </SectionContainerMedium>
  );
}
