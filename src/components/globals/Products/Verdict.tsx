import { ArrowDownIcon, CheckBadgeIcon } from "@heroicons/react/24/solid";
import Image from "next/image";
import parse, { Element } from "html-react-parser";
const Verdict: React.FC<{
  oneAssureVerdict: string;
  docs?: any;
}> = (props) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }
      if (domNode.name === "strong") {
      }
      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[12px]/[18px] md:text-[16px]/[24px] text-ntrl-black font-normal";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );
        case "strong":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[24px] text-ntrl-black font-semibold";
          break;

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  return (
    <section
      id="verdict"
      className="md:mt-5 px-2.5 md:px-[50px] md:py-7 scroll-m-28 bg-white rounded-3xl"
    >
      <div className="text-[22px]/[28px] md:text-[32px]/[36px] text-ntrl-black gap-2 flex items-center py-1 md:py-2">
        <h2 className="font-semibold">Our Verdict</h2>
        <CheckBadgeIcon className="h-6 md:h-8 text-green-5" />
      </div>
      <div className="py-1 md:py-2 flex items-center flex-row">
        <div className="">
          {/* @ts-ignore */}
          {parse(props.oneAssureVerdict, { replace })}
        </div>
      </div>
      <div className="flex justify-end gap-5 md:gap-10 my-3 md:mb-2">
        {props.docs &&
          props.docs.map((doc: any, index: number) => (
            <div
              key={index}
              className="flex items-center gap-1 py-1 md:py-2 px-2 md:px-4 rounded-3xl border-[0.5px] border-blue-5 bg-white text-blue-5 cursor-pointer hover:bg-blue-5 hover:text-white transition-colors duration-300"
              onClick={() =>
                window.open(doc.document.data[0].attributes.url, "_blank")
              }
            >
              <p className="text-[14px]/[20px] md:text-[16px]/[20px]">
                {doc.label}
              </p>
              <ArrowDownIcon className="h-3 md:h-5" />
            </div>
          ))}
      </div>
    </section>
  );
};

export default Verdict;
