const CircularProgress = ({
  percentage,
  text,
  className,
}: {
  percentage: number;
  text: string;
  className?: string;
}) => {
  const radius = 13;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div
      className={`flex flex-col items-center relative size-28 md:size-36 ${className}`}
    >
      <svg
        className="size-full -rotate-90"
        viewBox="0 0 36 36"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Background Circle */}
        {/* <circle
            cx="18"
            cy="18"
            r={radius}
            fill="none"
            className="stroke-current text-gray-200 dark:text-neutral-700"
            strokeWidth="2"
          /> */}
        {/* Progress Circle */}
        <circle
          cx="18"
          cy="18"
          r={radius}
          fill="none"
          className="stroke-current text-[#1BA500]"
          strokeWidth="1"
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          style={{ transition: "stroke-dashoffset 0.5s ease" }}
        />
      </svg>
      {/* Percentage Text */}
      <div className="absolute top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2">
        <p className="text-center text-2xl md:text-3xl font-bold text-ntrl-black">
          {text}/
          <span className="text-[14px]/[10px] md:text-[20px]/[14px]">10</span>
        </p>
      </div>
    </div>
  );
};

export default CircularProgress;
