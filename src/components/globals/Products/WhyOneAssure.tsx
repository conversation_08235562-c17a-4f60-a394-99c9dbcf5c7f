import Image from "next/image";
import React, { useState, useEffect } from "react";
import parse, { Element } from "html-react-parser";
const WhyOneAssure: React.FC<{
  data: { title: string; description: string }[];
  className?: string;
}> = (props) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }
      if (domNode.name === "strong") {
        console.log(domNode);
      }
      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] lg:text-[48px]/[58px] text-ntrl-black text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className =
            "text-ntrl-black font-generalSans text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[14px]/[22px] lg:text-[16px]/[24px] text-white font-light";
          break;

        case "a":
          attrs.className = "text-primary-2";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] lg:text-[18px]/[28px] text-ntrl-grey1 font-normal";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );
        case "strong":
          attrs.className =
            "text-[14px]/[22px] lg:text-[16px]/[24px] text-ntrl-black font-semibold";
          break;

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  // Auto-scroll every 3 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      goToNext();
    }, 3000);
    return () => clearInterval(interval);
  }, [currentIndex]); // Restart interval on index change

  // Show next card
  const goToNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === props.data.length - 1 ? 0 : prevIndex + 1
    );
  };

  // Show previous card
  const goToPrev = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? props.data.length - 1 : prevIndex - 1
    );
  };

  // Handle click (left = prev, right = next)
  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const { clientX, currentTarget } = e;
    const middle = currentTarget.offsetWidth / 2;
    if (clientX < middle) {
      goToPrev(); // Clicked left side → Show previous card
    } else {
      goToNext(); // Clicked right side → Show next card
    }
  };

  return (
    <section
      id="whyOneAssure"
      className={`text-center mt-5 p-5 scroll-m-28 bg-gradient-to-r from-gradient-1-blue to-gradient-1-green rounded-3xl ${props.className}`}
    >
      <div className="h-full bg-white/10 p-2 lg:p-5 rounded-2xl border-[0.5px] border-white text-white cursor-pointer transition-opacity duration-500 ease-in-out flex flex-col justify-between">
        <div className="flex flex-col justify-between " onClick={handleClick}>
          <h3 className="text-[18px]/[20px] lg:text-custom-32 font-bold">
            Why OneAssure?
          </h3>

          {/* Title & Description */}
          <div className="mt-5 text-[16px]/[20px] lg:text-[20px]/[24px] font-medium opacity-100 transition-opacity duration-500">
            {props.data[currentIndex].title}
          </div>
          <div className="mt-3 text-[16px]/[24px] font-normal opacity-100 transition-opacity duration-500">
            {/* @ts-ignore */}
            {parse(props.data[currentIndex].description || "", { replace })}
          </div>
        </div>
        {/* Indicator Dots */}
        <div className="mt-auto flex justify-center items-center gap-5">
          {props.data.map((_, index) => (
            <div
              key={index}
              className={`text-3xl transition-all ${
                index === currentIndex
                  ? "font-bold scale-150"
                  : "text-opacity-50"
              }`}
              onClick={() => setCurrentIndex(index)}
            >
              •
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyOneAssure;
