"use client";
import parse, { Element } from "html-react-parser";
import Image from "next/image";
import InfoModel from "./InfoModel";
import { useState } from "react";
import { VariantFeatures } from "../types";

const Features: React.FC<{
  features: VariantFeatures;
}> = (props) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black1 text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className = "text-ntrl-black text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[26px] text-ntrl-black font-normal";
          break;

        case "a":
          attrs.className = "text-blue-5";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[16px]/[30px] text-ntrl-black font-light";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState<{
    title: string;
    description: string;
  } | null>(null);
  return (
    <section
      id="features"
      className="mt-5 md:px-[50px] md:py-7 scroll-m-28 bg-white rounded-3xl"
    >
      <div className="px-2.5 md:px-0 text-[22px]/[28px] md:text-custom-32 text-ntrl-black gap-2 flex items-start py-2">
        <h2 className="font-semibold">Features of the Product</h2>
      </div>
      <div className="columns-1 md:columns-2 gap-4 md:gap-8 space-y-4 md:space-y-8 my-2 md:mb-3">
        {props.features?.map((feature, index) => (
          <div
            key={index}
            className="break-inside-avoid flex flex-col items-start bg-white"
          >
            <div className="p-6 md:p-7 pb-3 md:pb-3 rounded-3xl border-[0.5px] border-blue-5 w-full">
              <div className="flex flex-row gap-2 items-center pb-3 md:pb-4">
                <span className="font-poppins text-[24px]/[18px] md:text-[30px]/[22px] font-bold">
                  {index < 10 ? `0${index + 1}` : index + 1}
                </span>
                <h3 className="text-ntrl-black font-semibold text-[16px]/[18px] md:text-[20px]/[24px]">
                  {feature.title}
                </h3>
              </div>
              <ol>
                {feature.listedFeatures?.map(({ feature }, index) => (
                  <li className="list-disc ml-5 p-1" key={index}>
                    {/* @ts-ignore */}
                    {parse(feature || "", { replace })}
                  </li>
                ))}
              </ol>
              {feature.description && (
                <div className="flex justify-end pt-2">
                  <span
                    className="text-[14px]/[20px] -mr-3 md:-mr-4 text-blue-5 px-3 py-1 rounded-3xl border-[0.5px] border-blue-5 bg-white font-manrope hover:cursor-pointer hover:border-blue-5 hover:bg-blue-5 hover:text-white duration-300"
                    onClick={() => {
                      setSelectedFeature(feature);
                      setModalOpen(true);
                    }}
                  >
                    Learn more
                  </span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
      {selectedFeature && (
        <InfoModel
          open={modalOpen}
          handleModal={() => setModalOpen(false)}
          title={selectedFeature?.title}
          description={selectedFeature?.description}
        />
      )}
    </section>
  );
};

export default Features;
