import { useRef, useState, useEffect } from "react";
import {
  ArrowRightIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";
import Image from "next/image";
import Link from "next/link";
import { Blog } from "../types";

interface BlogCardProps {
  title: string;
  description: string;
  imageUrl: string;
  category: string;
  slug: string;
}

const BlogCard: React.FC<BlogCardProps> = ({
  title,
  description,
  imageUrl,
  category,
  slug,
}) => {
  return (
    <div className="w-full md:w-[32%] flex-shrink-0">
      <div className="h-full p-6 pb-4 rounded-2xl bg-white border-[0.5px] border-blue-5 flex justify-between flex-col">
        <div className="rounded-t-lg">
          <Image
            width={100}
            height={100}
            src={imageUrl}
            alt="Blog"
            className="mx-auto w-full rounded-2xl"
          />
          <h2 className="mt-4 text-lg font-bold text-gray-900">{title}</h2>
          <p className="text-ntrl-black">{description}</p>
        </div>

        {/* Button */}
        <div className="pt-4 flex justify-end">
          <Link
            href={`/insurance/${category}/${slug}`}
            className="text-[14px]/[24px] -mr-2 px-2 md:px-4 md:py-2 text-blue-500 rounded-3xl border-[0.5px] border-blue-500 flex items-center hover:border hover:font-medium"
          >
            Read this article <ArrowRightIcon className="h-4 ml-2" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export const RelatedBlogs: React.FC<{
  blogs: { data: [{ attributes: Blog }] };
}> = ({ blogs }) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isAtStart, setIsAtStart] = useState(true);
  const [isAtEnd, setIsAtEnd] = useState(false);

  const checkScrollPosition = () => {
    if (!scrollRef.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
    setIsAtStart(scrollLeft === 0);
    setIsAtEnd(scrollLeft + clientWidth >= scrollWidth - 1);
  };

  useEffect(() => {
    const container = scrollRef.current;
    if (!container) return;

    checkScrollPosition();
    container.addEventListener("scroll", checkScrollPosition);

    return () => container.removeEventListener("scroll", checkScrollPosition);
  }, []);
  // console.log("blog.attributes",blogs.data[0].attributes)
  const scrollLeft = () => {
    if (scrollRef.current) {
      const scrollAmount = scrollRef.current.scrollWidth / blogs.data.length;
      scrollRef.current.scrollBy({
        left: -scrollAmount - 4,
        behavior: "smooth",
      });
    }
  };

  const scrollRight = () => {
    if (scrollRef.current) {
      const scrollAmount = scrollRef.current.scrollWidth / blogs.data.length;
      scrollRef.current.scrollBy({
        left: scrollAmount + 4,
        behavior: "smooth",
      });
    }
  };

  return (
    <section id="relatedBlogs" className="mt-5 pb-7 scroll-m-28 rounded-3xl">
      <div className="text-[22px]/[28px] md:text-custom-32 text-ntrl-black gap-2 flex items-center pb-5">
        <h2 className="font-semibold">Related Blogs</h2>
      </div>

      {/* Scrollable Blog Cards */}
      <div className="relative">
        <div
          ref={scrollRef}
          className="flex space-x-4 pb-5 overflow-x-scroll scroll-smooth scrollbar-hide"
        >
          {blogs.data.map((blog, index) => (
            <BlogCard
              key={index}
              title={blog.attributes.Title}
              description={blog.attributes.subtitle}
              imageUrl={blog.attributes.Thumbnail.data.attributes.url}
              category={blog.attributes.category.data.attributes.slug}
              slug={blog.attributes.slug}
            />
          ))}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between md:mt-2 mx-5">
          <button
            onClick={scrollLeft}
            disabled={isAtStart}
            className={`px-2 md:px-4 md:py-2 bg-white rounded-3xl border-[0.5px] flex items-center gap-x-1 md:gap-x-3 ${
              isAtStart
                ? "text-gray-400 border-gray-400 cursor-not-allowed"
                : "text-blue-500 border-blue-500 hover:border hover:font-medium"
            }`}
          >
            <ChevronLeftIcon className="h-4" /> Prev
          </button>
          <button
            onClick={scrollRight}
            disabled={isAtEnd}
            className={`px-2 md:px-4 md:py-2 bg-white rounded-3xl border-[0.5px] flex items-center gap-x-1 md:gap-x-3 ${
              isAtEnd
                ? "text-gray-400 border-gray-400 cursor-not-allowed"
                : "text-blue-500 border-blue-500 hover:border hover:font-medium"
            }`}
          >
            Next <ChevronRightIcon className="h-4" />
          </button>
        </div>
      </div>
    </section>
  );
};
