import BookACallBtn from "@/components/globals/BookACall";
import Image from "next/image";

const BookACall: React.FC<{
  classname?: string;
  email?: string;
  contactNumber?: string;
  company?: string;
}> = (props) => {
  return (
    <section
      id="book-a-call"
      className={`mt-5 scroll-m-28 bg-gradient-to-r from-gradient-1-blue to-gradient-1-green rounded-3xl ${
        props.classname || ""
      } `}
    >
      <div className="bg-white/10 p-5 rounded-3xl border-[0.5px] border-white text-white relative h-full">
        <h3 className="text-[22px]/[26px] md:text-custom-32 font-bold">
          Still Confused ?
        </h3>
        <div className="text-[12px]/[20px] md:text-[16px]/[24px] lg:text-[24px]/[30px] mt-4 w-4/5">
          Talk to Our Experts for your Insurance Assistance for FREE.
        </div>
        <BookACallBtn className="mt-4 bg-white text-primary-blue-3 rounded-[8px] p-1 md:text-[18px]/[24px]" />
      </div>
    </section>
  );
};

export default BookACall;
