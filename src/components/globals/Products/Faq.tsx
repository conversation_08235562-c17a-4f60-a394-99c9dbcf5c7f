import { AccordianType } from "@/components/globals/types";
import Accordian from "../Accordian";

const Faqs: React.FC<{
  title: string;
  faqs: Array<{ question: string; ans: string }>;
}> = (props) => {
  const generateFAQsHtml = (aspects: Array<AccordianType>): string => {
    const listItems = aspects
      .map((aspect) => {
        const description = aspect.description.startsWith("<p>")
          ? aspect.description.slice(3, -4)
          : aspect.description;
        return `<li><p class="font-regular "><span class="font-semibold text-[16px]/[24px] text-ntrl-black">Q. ${aspect.title} - </span><p>${description}</p></li>`;
      })
      .join("");
    return `<ol>${listItems}</ol>`;
  };
  return (
    <section
      id="faqs"
      className="mt-5 md:px-[50px] md:py-7 scroll-m-28 bg-white rounded-3xl"
    >
      <div className="md:flex hidden text-[18px]/[28px] md:text-[32px]/[36px] text-ntrl-black gap-2 items-start py-2">
        <h2 className="font-semibold">{props.title}</h2>
      </div>

      <div className="mt-2 mb-3 md:block hidden">
        {props.faqs.map((faq, idx) => (
          <Accordian
            key={idx}
            name={faq.question}
            description={faq.ans}
          />
        ))}
      </div>
      <Accordian
        name="Frequently Asked Questions"
        description={generateFAQsHtml(
          props.faqs.map((faq) => {
            return { title: faq.question, description: faq.ans };
          })
        )}
        className="md:hidden"
      />
    </section>
  );
};

export default Faqs;
