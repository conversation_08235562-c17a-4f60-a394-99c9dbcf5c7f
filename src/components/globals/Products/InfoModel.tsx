"use client";
import { Dialog } from "@headlessui/react";
import Image from "next/image";
import parse, { Element } from "html-react-parser";
import { XMarkIcon } from "@heroicons/react/24/outline";
const InfoModel = ({
  open,
  handleModal,
  title,
  description,
}: {
  open: boolean;
  handleModal: () => void;
  title: string;
  description: string;
}) => {
  const replace = (domNode: Element, index: number) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className =
            "text-[24px]/[32px] md:text-[48px]/[58px] text-ntrl-black1 text-3xl mb-12 my-5";
          break;

        case "h3":
          attrs.className = "text-ntrl-black text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-ntrl-black text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className =
            "text-[14px]/[22px] md:text-[16px]/[26px] text-ntrl-black font-normal";
          break;

        case "a":
          attrs.className = "text-blue-5";
          break;

        case "li":
          attrs.className =
            "list-disc list-inside text-[14px]/[22px] md:text-[16px]/[30px] text-ntrl-black font-light";
          break;
        case "tr":
        case "td":
        case "th":
          attrs.className = "border border-gray800 p-5 text-center";
          break;

        case "figure":
          attrs.className = "overflow-x-auto min-w-[300px]";
          break;

        case "img":
          return (
            <div className="mx-auto h-64 aspect-video my-10 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt="alt"
              />
            </div>
          );

        case "iframe":
          attrs.className = "my-0 mx-auto";
      }
      return domNode;
    }
  };
  return (
    <Dialog
      open={open}
      onClose={handleModal}
      className="fixed inset-0 z-[100] overflow-y-auto flex items-center justify-center"
    >
      <div className="flex items-end justify-center min-h-screen pt-4 md:px-4 pb-20 text-center sm:block sm:p-0">
        <Dialog.Overlay className="fixed inset-0 transition-opacity bg-gray-500 opacity-75" />
        <Dialog.Panel
          className="mx-4 inline-block align-bottom bg-white rounded-3xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:w-full  md:max-w-2xl relative p-6"
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-headline"
        >
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-[20px]/[24px] md:text-[32px]/[36px] font-medium ">
              {title}
            </h2>
            <XMarkIcon className="h-7 cursor-pointer" onClick={handleModal} />
          </div>
          <hr className="py-2" />

          {/* @ts-ignore */}
          {parse(description || "", { replace })}
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default InfoModel;
