"use client";

import { useFormik } from "formik";
import { useCreateLead } from "../LeadModal/api/createLead";
import * as Yup from "yup";
import circles2 from "@/assets/circles2.png";
import Image from "next/image";
import { usePathname } from "next/navigation";
const GetInTouch = ({ source }: { source?: string }) => {
  const { mutateAsync, isPending, isSuccess, mutate } = useCreateLead();
  const pathname = usePathname();

  const leadContactType = [
    {
      label: "Buy insurance",
      value: "buy_insurance",
    },
    {
      label: "Claims",
      value: "claims",
    },
    {
      label: "Become a partner",
      value: "become_partner",
    },
    {
      label: "License our tech",
      value: "license_tech",
    },
    {
      label: "Others",
      value: "others",
    },
  ];

  const LeadSchema = Yup.object().shape({
    lead_name: Yup.string()
      .matches(/^[A-Za-z ]*$/, "Please enter valid name")
      .required("Required"),
    lead_email: Yup.string().email("Invalid email").required("Required"),
    lead_phone_number: Yup.string()
      .length(10, "Invalid phone number")
      .matches(
        /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/,
        "Phone number is not valid"
      )
      .required("Required"),
    lead_message: Yup.string().trim().required("Required"),
    lead_contact_type: Yup.string().required("Required"),
  });

  const formik = useFormik({
    initialValues: {
      lead_name: "",
      lead_email: "",
      lead_phone_number: "",
      lead_message: "",
      lead_contact_type: "",
    },
    validationSchema: LeadSchema,
    onSubmit: (values, { resetForm }) => {
      const { lead_name, lead_phone_number, ...restObj } = values;

      mutate(
        {
          lead_name,
          lead_phone_number,
          lead_point: pathname,
          ...restObj,
        },
        {
          onSuccess: () => {
            alert("Your request is submitted! We will get back to you soon");
            resetForm();
          },
        }
      );
    },
  });

  return (
    <div className="">
      <div className="absolute bottom-0 transform -translate-x-1/2 translate-y-1/2">
        {/* <Image
            src={circles2}
            alt=""
            width={570}
            height={570}
            objectFit="cover"
        /> */}
      </div>
      <form
        onSubmit={formik.handleSubmit}
        className="text-[16px]/[24px] text-ntrl-grey1 border-2 border-ntrl-grey-500 p-4 rounded-xl"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div className="md:mb-2">
            <label className="text-[17px]/[20px] text-white font-helvetica font-normal">
              Full Name
            </label>
            <input
              type="text"
              name="lead_name"
              id="lead_name"
              onChange={formik.handleChange}
              value={formik.values.lead_name}
              className=" w-full py-2 px-5 rounded-xl border-ntrl-grey-500"
              placeholder=""
            />
            {formik.touched.lead_name && formik.errors.lead_name && (
              <p className=" text-sm text-red600">{formik.errors.lead_name}</p>
            )}
          </div>
          <div className="mb-2">
            <label className="text-[17px]/[20px] text-white font-helvetica font-normal">
              E-mail
            </label>
            <input
              type="text"
              name="lead_email"
              id="lead_email"
              onChange={formik.handleChange}
              value={formik.values.lead_email}
              className="w-full py-2 px-5 rounded-xl border-ntrl-grey-500"
              placeholder=""
            />
            {formik.touched.lead_email && formik.errors.lead_email && (
              <p className=" text-sm text-red600">{formik.errors.lead_email}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div className=" md:mb-2">
            <label className="text-[17px]/[20px] text-white font-helvetica font-normal">
              Contact No.
            </label>
            <input
              type="text"
              name="lead_phone_number"
              id="lead_phone_number"
              onChange={formik.handleChange}
              value={formik.values.lead_phone_number}
              className="w-full py-2 px-5 rounded-xl border-ntrl-grey-500"
              placeholder=""
            />
            {formik.touched.lead_phone_number &&
              formik.errors.lead_phone_number && (
                <p className=" text-sm text-red600">
                  {formik.errors.lead_phone_number}
                </p>
              )}
          </div>

          <div className=" mb-2">
            <label className="text-[17px]/[20px] text-white font-helvetica font-normal">
              Like to connect us for
            </label>
            <select
              name="lead_contact_type"
              className="w-full py-2 px-5 rounded-xl border-ntrl-grey-500"
              onChange={formik.handleChange}
              value={formik.values.lead_contact_type}
            >
              <option value=""></option>
              {leadContactType.map((type, idx) => (
                <option value={type.value} key={idx}>
                  {type.label}
                </option>
              ))}
            </select>

            {formik.touched.lead_contact_type &&
              formik.errors.lead_contact_type && (
                <p className=" text-sm text-red600">
                  {formik.errors.lead_contact_type}
                </p>
              )}
          </div>
        </div>

        <div className=" md:mb-2 mb-5">
          <label className="text-[17px]/[20px] text-white font-normal">
            Message
          </label>
          <textarea
            name="lead_message"
            id="lead_message"
            onChange={formik.handleChange}
            value={formik.values.lead_message}
            className="w-full py-2 px-5 rounded-xl border-ntrl-grey-500"
            placeholder=""
          />
          {formik.touched.lead_message && formik.errors.lead_message && (
            <p className=" text-sm text-red600">{formik.errors.lead_message}</p>
          )}
        </div>

        <div className="flex md:justify-end justify-center md:px-0 px-auto">
          <button
            className="md:py-2 md:px-20 px-16 py-4 bg-black md:rounded-xl rounded-full text-base text-white font-normal border-[0.5px] cursor-pointer text-center md:w-auto "
            type="submit"
          >
            Submit
          </button>
        </div>
      </form>
    </div>
  );
};

export default GetInTouch;
