"use client";
import { LPHowToBuy, LPSectionData } from "../HealthInsurance/types";
import parse from "html-react-parser";
import { Element } from "html-react-parser";

const HowToBuyInsuranceFromOA = ({ data }: { data: LPHowToBuy }) => {
  const replace = (domNode: Element) => {
    if (domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":

        case "h2":
          attrs.className = "text-blue400 text-3xl font-semibold my-5";
          break;

        case "h3":
          attrs.className = "text-blue400 text-xl font-semibold my-5";
          break;

        case "h4":
        case "h5":
        case "h6":
          attrs.className = "text-blue400 text-lg font-semibold my-5";
          break;

        case "p":
          attrs.className = "text-ntrl-black-1 text-[14px]/[20px]";
          break;

        case "a":
          attrs.className = "text-primary-1 font-semibold my-0";
          break;
      }
      return domNode;
    }
  };
  return (
    <div className="md:mb-14 md:mt-20 text-ntrl-black-1 grid grid-cols-1 md:grid-cols-[30%_70%] md:px-14 gap-2 mb-5">
      {/* Heading */}
      <div className="flex flex-col items-center justify-center md:items-start">
        {/* title comes here  */}
        <h2 className="md:text-3xl text-lg font-medium text-center md:text-left text-ntrl-black-1">
          {data.title}
        </h2>
        {/* subTitle comes here  */}
        <div className=" mt-4 md:text-left text-center w-4/5 mb-5">
          {/* @ts-expect-error unknown type */}
          {parse(data.description, { replace })}
        </div>
      </div>

      {/* Features List */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-5">
        {data.sectionData.map((feat: LPSectionData, idx: number) => (
          <div key={idx}>
            {/* Number inside a Circle */}
            <div className="w-[50px] h-[50px] flex items-center justify-center rounded-full border text-white bg-green1100 font-bold text-[20px]/[24px]">
              0{idx + 1}
            </div>

            {/* Title & Description */}
            <div className=" text-start gap-2">
              {/* Title */}
              <h3 className="items-center text-black100 font-bold text-[14px]/[20px] my-4 w-[75%]">
                {feat.title}
              </h3>
              {/* @ts-ignore */}
              {parse(feat.description, { replace })}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HowToBuyInsuranceFromOA;
