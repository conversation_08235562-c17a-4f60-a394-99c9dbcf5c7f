import { BodyLarge, HeadingMedium } from "@/components/UI/Typography";

export default function PointCard({
  title,
  points,
  pointIcon,
  className,
  as = "h3",
}: {
  title?: string;
  points?: string[];
  pointIcon?: React.ReactNode;
  className?: string;
  as?: "h2" | "h3" | "h4" | "h5" | "h6";
}) {
  
  return (
    <div className={`bg-white rounded-xl p-6 shadow-sm border border-primary-200 flex flex-col items-center gap-4 text-center w-full h-full ${className}`}>

      {title && (
      <HeadingMedium className="font-semibold text-neutral-1100" as={as}>
        {title}
      </HeadingMedium>
      )}
      {points && (
      <div className="text-neutral-800 text-left">
        {points?.map((point, idx) => (
          <div className="flex gap-2 mb-2" key={idx}>
            {pointIcon}
            <BodyLarge>{point}</BodyLarge>
          </div>
        ))}
      </div>
      )}
    </div>
  );
}
