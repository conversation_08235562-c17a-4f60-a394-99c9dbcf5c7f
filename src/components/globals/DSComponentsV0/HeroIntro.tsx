import Image from "next/image";
import { HeadingXLarge } from "@/components/UI/Typography";
import { htmlParser } from "@/utils/htmlParser";
import PillBadge from "@/components/globals/PillBadge";

const HeroIntro = ({
  name,
  description,
  image,
  pill,
}: {
  name: string;
  description: string;
  image?: string;
  pill?: string;
}) => {
  return (
    <div className="flex flex-col gap-4 items-center text-center">
      <div className="w-full h-full flex justify-center items-center">
        {pill && <PillBadge pill={pill} />}

        {image && (
          <Image
            src={image}
            alt="Intro"
            width={120}
            height={100}
            objectFit="contain"
          />
        )}
      </div>
      <HeadingXLarge
        as="h1"
        weight="semibold"
        className="text-neutral-1100 text-center"
      >
        {name}
      </HeadingXLarge>
      {htmlParser(description, {
        classNames: {
          p: "text-neutral-1100 text-center",
        },
      })}
    </div>
  );
};

export default HeroIntro;
