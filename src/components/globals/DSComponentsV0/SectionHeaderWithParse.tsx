import { HeadingXLarge } from "@/components/UI/Typography";
import { useRef, useState, useEffect } from "react";
import { convertToPlain } from "@/utils/rtfToPlain";
import PillBadge from "@/components/globals/PillBadge";
import { htmlParser } from "@/utils/htmlParser";


const SectionHeader = ({ pill, heading, subheading, component, pillComponent, className }: {
  pill?: string;
  heading: string;
  subheading?: string;
  component?: "h1" | "h2";
  pillComponent?: "h2" | "h3" | "p";
  className?: string;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [needsReadMore, setNeedsReadMore] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);

  // Detect overflow once after mount
  useEffect(() => {
    if (textRef.current) {
      const el = textRef.current;
      if (el.scrollHeight > el.clientHeight) setNeedsReadMore(true);
    }
  }, [subheading]);

  return (
    <div className={`flex flex-col items-center gap-2 md:gap-3 mx-auto mb-6 px-6 ${className}`}>
      {pill && <PillBadge pill={pill} as={pillComponent} />}
        <HeadingXLarge className="text-center text-neutral-1100" as={component}>
          {heading}
        </HeadingXLarge>
      
      { subheading && convertToPlain(subheading).trim() && (
        <div className="text-center">
          {/* Desktop: full text */}
          <div className="hidden md:block">
            {htmlParser(subheading, {
              classNames: {
                p: "text-neutral-1100",
              },
            })}
          </div>

          {/* Mobile: clamp until expanded */}
          <div
            ref={textRef}
            className={` md:hidden
              ${!isExpanded ? "line-clamp-2" : ""}`}
          >
            {htmlParser(subheading, {
              classNames: {
                p: "text-neutral-1100",
              },
            })}
          </div>

          {needsReadMore && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-primary-800 hover:text-primary-600 text-sm font-medium mt-1"
            >
              {isExpanded ? "Read less" : "Read more"}
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default SectionHeader;
