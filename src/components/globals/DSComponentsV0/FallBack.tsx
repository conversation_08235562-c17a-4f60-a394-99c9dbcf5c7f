import Container from "@/components/globals/Container";
import { BodyLarge, HeadingLarge } from "@/components/UI/Typography";

const FallBack = () => {
  return (
    <Container navSpacing={false} className="font-matter h-96">
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <HeadingLarge className="text-2xl font-bold text-gray-800 mb-4">
            No Data Available
          </HeadingLarge>
          <BodyLarge className="text-gray-600">
            Please try again later or contact support.
          </BodyLarge>
        </div>
      </div>
    </Container>
  );
};

export default FallBack;
