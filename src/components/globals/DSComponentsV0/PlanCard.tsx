import { BodyMedium } from "@/components/UI/Typography";
import { FiArrowRight } from "react-icons/fi";
import Link from "next/link";
import Image from "next/image";

type Plan = {
  logo_url: string;
  plan_title: string;
  redirect_url: string;
};

const PlanCard: React.FC<{ plan: Plan }> = ({ plan }) => (
  <Link scroll={false} href={plan.redirect_url} className="block">
    <div className="bg-white border border-primary-200 rounded-xl p-4 flex flex-col justify-between shadow-sm hover:shadow-md transition-shadow cursor-pointer group">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="relative w-12 h-6">
            <Image
              src={plan.logo_url}
              alt={plan.plan_title}
              fill
              className="object-contain"
            />
          </div>
          <BodyMedium weight="medium" className="mx-4 text-neutral-1100">
            {plan.plan_title}
          </BodyMedium>
        </div>
        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-xs text-neutral-1100 group-hover:bg-primary-100 transition-colors">
          <FiArrowRight />
        </div>
      </div>
    </div>
  </Link>
);

export default PlanCard;
