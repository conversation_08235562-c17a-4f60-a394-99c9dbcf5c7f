import { BodyMedium } from "@/components/UI/Typography";

export type PillProps = {
  pill: string;
  as?: "h2" | "h3" | "p";
  className?: string;
  border?: boolean;
  bgColor?: string;
  textColor?: string;
};

const Pill = ({
  pill,
  as,
  className,
  border,
  bgColor,
  textColor,
}: PillProps) => {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <span className={`px-8 py-[0.5px] md:py-[1.5px] rounded-full ${border ? "border-[0.5px] border-secondary-400" : "border-none"} ${bgColor ? bgColor : "bg-secondary-100"}`}>
        <BodyMedium
          {...(as ? { as } : { as: "p" })}
          className={`${textColor ? textColor : "text-secondary-400"}`}
        >
          {pill}
        </BodyMedium>
      </span>
    </div>
  );
};

export default Pill;
