import { BodyLarge, HeadingMedium } from "@/components/UI/Typography";
import { convertToPlain } from "@/utils/rtfToPlain";
import { htmlParser, HtmlParserOptions } from "@/utils/htmlParser";


const SimpleCard = ({
  title,
  description,
  icon,
  className,
  iconBgColor,
  parserProps,
}: {
  title?: string;
  description?: string;
  icon?: React.ReactNode | string;
  className?: string;
  iconBgColor?: string;
  parserProps?: HtmlParserOptions;
}) => {

  const plainDescription = convertToPlain(description || "");

  return (
    <div
      className={`bg-white rounded-xl p-6 shadow-sm border border-primary-200 flex flex-col items-center gap-3 text-center w-full h-full ${className}`}
    >
      {/* if icon is reactnode */}
      {icon && typeof icon === "object" && (
        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${iconBgColor}`}>
          {icon}
        </div>
      )}
      {/* if icon is string */}
      {icon && typeof icon === "string" && (
        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${iconBgColor}`}>
          <img src={icon} alt={title || ""} />
        </div>
      )}

      {title && (
      <HeadingMedium as="h3" className="font-semibold text-neutral-1100">
        {title}
      </HeadingMedium>
      )}

      {plainDescription && plainDescription !== "" && (
      <div className="flex-1">
        {htmlParser(description || "", parserProps || {
          classNames: {
            p: "text-neutral-1100",
          },
        })}
      </div>
      )}
    </div>
  );
};

export default SimpleCard;
