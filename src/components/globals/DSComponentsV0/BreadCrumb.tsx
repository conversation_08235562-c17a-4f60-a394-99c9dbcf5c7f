import { useSearchParams } from "next/navigation";
import CustomLink from "@/components/globals/CustomLink";
import { IoIosArrowForward } from "react-icons/io";
import { BodySmall } from "@/components/UI/Typography";

const Breadcrumb = ({
  path,
}: {
  path: {
    name: string;
    url: string;
  }[];
}) => {
  const search = useSearchParams();
  const utm = search.get("utm_source");

  return (
    <div className="flex flex-wrap items-center justify-center md:justify-start">
      {path.map((p, idx) => {
        let pathName = p.name;
        return (
          <span key={idx} className="flex items-center text-center">
            {idx === path.length - 1 ? (
              <BodySmall className="text-xs text-neutral-800 break-words overflow-hidden">
                {pathName}
              </BodySmall>
            ) : (
              <CustomLink href={p.url} utm={utm}>
                <BodySmall className="text-xs transition-all duration-200 ease-in-out text-neutral-1100 hover:text-primary-500 break-words overflow-hidden">
                  {pathName}
                </BodySmall>
              </CustomLink>
            )}
            {idx < path.length - 1 && (
              <IoIosArrowForward
                className="mx-2 text-neutral-1100 flex-shrink-0"
                size={16}
              />
            )}
          </span>
        );
      })}
    </div>
  );
};

export default Breadcrumb;
