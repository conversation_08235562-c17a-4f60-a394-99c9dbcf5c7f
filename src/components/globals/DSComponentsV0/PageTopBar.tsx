import Breadcrumb from "@/components/globals/DSComponentsV0/BreadCrumb";
import SocialShareButtons from "@/components/globals/SocialShareButtons";

const PageTopBar = ({
  breadcrumbPath,
}: {
  breadcrumbPath: {
    name: string;
    url: string;
  }[];
}) => {
  return (
    <div className="w-full flex flex-col md:flex-row items-center md:justify-between md:mb-6">
      {/* Desktop: Original layout */}
      <div className="hidden md:block">
        <Breadcrumb path={breadcrumbPath} />
      </div>
      <div className="hidden md:block">
        <SocialShareButtons />
      </div>
    </div>
  );
};

export default PageTopBar;
