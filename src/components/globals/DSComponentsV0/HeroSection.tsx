import React from "react";
import SectionContainer from "@/components/globals/SectionContainer";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import Intro from "@/components/Insurer/component/Intro";
import GenerateQuote from "@/components/globals/GenerateQuoteForm";
import HeroStatsCard from "@/components/Insurer/component/HeroStatsCard";
import { FaHospital } from "react-icons/fa";
import { FaShield } from "react-icons/fa6";
import { TbTrendingUp } from "react-icons/tb";
import PageTopBar from "@/components/globals/DSComponentsV0/PageTopBar";
import HeroIntro from "./HeroIntro";

const fullUrl = process.env.NEXT_PUBLIC_BASE_URL;

const HeroSection = ({
  name,
  description,
  image,
  pill,
  slug,
  stats,
  breadcrumbPath,
}: {
  name: string;
  description: string;
  image?: string;
  pill?: string;
  slug?: string;
  stats: {
    id: number | string;
    title: string;
    value: number | string;
    suffix: string;
    prefix: string;
    icon?: string;
  }[];
  breadcrumbPath: {
    name: string;
    url: string;
  }[];
}) => {
  return (
    <SectionContainer className="mt-6">
      <PageTopBar breadcrumbPath={breadcrumbPath} />
      <SectionContainerMedium className="flex flex-col md:flex-row items-center gap-4 md:gap-20 !px-0 !mb-0 md:!mb-0">
        <div className="md:w-[40%] ">
          <HeroIntro name={name} description={description} image={image} pill={pill}/>
        </div>
        <div className="md:w-[50%]">
          <GenerateQuote />
        </div>
      </SectionContainerMedium>
      
      {/* Stats Cards */}
      <SectionContainerMedium className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-4 !p-0 mt-4 md:mt-8">
        {stats.map((stat) => (
          <HeroStatsCard
            key={stat.id}
            value={stat.value}
            label={stat.title}
            icon={stat.icon ? stat.icon : stat.title === "Claim Settlement Ratio" ? <FaShield className="w-6 h-6 text-primary-800" /> : stat.title === "Network Hospitals" ? <FaHospital className="w-6 h-6 text-primary-800" /> : <TbTrendingUp className="w-6 h-6 text-primary-800" />}
            suffix={stat.suffix}
          />
        ))}
      </SectionContainerMedium>
    </SectionContainer>
  );
};

export default HeroSection;
