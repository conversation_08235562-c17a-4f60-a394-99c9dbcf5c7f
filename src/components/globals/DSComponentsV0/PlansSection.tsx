import React, { useState } from "react";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import PlanCard from "@/components/globals/DSComponentsV0/PlanCard";
import SectionHeader from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
import { Button } from "@/components/UI/Button";

type Plan = {
  logo_url: string;
  plan_title: string;
  redirect_url: string;
};

const PlansSection = ({
  heading,
  subheading,
  pill,
  plans,
  className,
  id,
}: {
  plans: Plan[];
  className?: string;
  heading: string;
  subheading?: string;
  pill?: string;
  id?: string;
}) => {
  const [showAllPlans, setShowAllPlans] = useState(false);

  // For mobile: show 4 items initially, all on show more
  const displayedMobilePlans = showAllPlans ? plans : plans.slice(0, 5);
  const hasMorePlans = plans.length > 5;

  const toggleShowAllPlans = () => setShowAllPlans(prev => !prev);

  return (
    <SectionContainerLarge className={className + "!px-0"} id={id}>
      { 
        <SectionHeader
          pill={pill}
          heading={heading}
          subheading={subheading}
          component="h2"
        />
      }

      {/* Desktop view - show all plans */}
      <div className="hidden md:grid md:grid-cols-3 gap-6">
        {plans.map((plan, index) => (
          <PlanCard key={index} plan={plan} />
        ))}
      </div>

      {/* Mobile view - show limited plans with toggle */}
      <div className="md:hidden">
        <div className="grid grid-cols-1 gap-4">
          {displayedMobilePlans.map((plan, index) => (
            <PlanCard key={index} plan={plan} />
          ))}
        </div>

        {/* Toggle button for mobile */}
        {hasMorePlans && (
          <div className="flex justify-center mt-4">
            <Button
              onClick={toggleShowAllPlans}
              variant="primary"
              className="flex items-center"
            >
              <span className="text-sm font-normal">
                {showAllPlans ? "Show Less" : "See More Plans"}
              </span>
            </Button>
          </div>
        )}
      </div>
    </SectionContainerLarge>
  );
};

export default PlansSection;
