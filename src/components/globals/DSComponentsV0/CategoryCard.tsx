"use client";
import Image from "next/image";
import React from "react";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import { IoTrendingUpSharp } from "react-icons/io5";
import { Button } from "@/components/UI/Button";
import {
  BodyLarge,
  BodySmall,
  HeadingMedium,
} from "@/components/UI/Typography";
import { orufyHandler } from "@/utils/orufyHandler";

// Desktop Component - Most popular card scaled, others equal height
const DesktopCards = ({
  title,
  features,
  button,
  icon,
  mostPopular,
  redirectUrl,
  buttonColor,
}: {
  title?: string;
  features?: string[];
  button?: string;
  icon?: React.ReactNode | string;
  mostPopular?: boolean;
  redirectUrl?: string;
  buttonColor?: string;
}) => {
  return (
    <>
      <div
        className={`relative flex flex-col justify-center items-center rounded-2xl w-full p-4 md:px-5 transition-all duration-300 text-center ${
          mostPopular
            ? "scale-y-105 border-2 border-primary-300 shadow-lg md:pt-8 md:pb-4 order-first md:order-none"
            : "md:py-4 shadow-sm border border-primary-200 h-full"
        }`}
      >
        {mostPopular && (
          <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-secondary-100 px-4 rounded-full font-medium z-10 border-[1px] border-secondary-400">
            <BodySmall className="text-secondary-400 flex items-center gap-2">
              <IoTrendingUpSharp className="text-secondary-400" /> Most Popular
            </BodySmall>
          </div>
        )}
        <div
          className={`flex flex-col items-center ${
            mostPopular ? "gap-3 md:gap-4" : "gap-3"
          } w-full h-full justify-between`}
        >
          <div className="flex flex-col items-center gap-3">
            <div className="flex justify-center items-center">
              {typeof icon === "string" ? (
                <Image
                  src={icon}
                  alt={title || ""}
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full"
                />
              ) : (
                icon
              )}
            </div>

            <HeadingMedium
              weight="semibold"
              className="text-neutral-1100 text-center"
            >
              {title}
            </HeadingMedium>

            <ul className="space-y-2 w-full">
              {features?.map((feature, i) => (
                <li key={i} className="flex text-left">
                  <IoMdCheckmarkCircleOutline className="text-green-main-200 mr-2 flex-shrink-0" />
                  <BodyLarge className="text-neutral-800">{feature}</BodyLarge>
                </li>
              ))}
            </ul>
          </div>
          <Button
            variant="primary"
            className={
              "mt-4 md:mt-6 bg-primary-800 hover:bg-primary-900 text-white" +
              buttonColor
            }
            onClick={() => {
              redirectUrl
                ? (window.location.href = redirectUrl)
                : orufyHandler(process.env.NEXT_PUBLIC_ONE_ASSURE_ORUFY_LINK as string);
            }}
          >
            <span className="text-white text-sm">{button}</span>
          </Button>
        </div>
      </div>
    </>
  );
};

// Mobile Component - Carousel with pagination dots
const MobileCards = ({
  title,
  features,
  button,
  icon,
  mostPopular,
  redirectUrl,
  buttonColor,
}: {
  title?: string;
  features?: string[];
  button?: string;
  icon?: React.ReactNode | string;
  mostPopular?: boolean;
  redirectUrl?: string;
  buttonColor?: string;
}) => {
  return (
    <>
      <div className="relative flex flex-col justify-center items-center rounded-xl w-full p-4 transition-all duration-300 text-center shadow-md border border-primary-300 h-full">
        {mostPopular && (
          <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-secondary-100 px-4 rounded-full font-medium z-10 border-[1px] border-secondary-400">
            <BodySmall className="text-secondary-400 flex items-center gap-2">
              <IoTrendingUpSharp className="text-secondary-400" /> Most Popular
            </BodySmall>
          </div>
        )}
        <div className="flex flex-col items-center gap-3 w-full h-full justify-between">
          <div className="flex flex-col items-center gap-3 h-full">
            <div className="flex justify-center items-center">
              {typeof icon === "string" ? (
                <Image
                  src={icon}
                  alt={title || ""}
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full"
                />
              ) : (
                icon
              )}
            </div>

            <HeadingMedium
              weight="semibold"
              className="text-neutral-1100 text-center"
            >
              {title}
            </HeadingMedium>

            <ul className="space-y-2 w-full">
              {features?.map((feature, i) => (
                <li key={i} className="flex text-left">
                  <IoMdCheckmarkCircleOutline className="text-green-main-200 mr-2 flex-shrink-0" />
                  <BodyLarge className="text-neutral-800">{feature}</BodyLarge>
                </li>
              ))}
            </ul>
          </div>
          <Button
            variant="primary"
            className={
              "mt-6 bg-primary-800 hover:bg-primary-900 text-white" +
              buttonColor
            }
            onClick={() => {
              redirectUrl
                ? (window.location.href = redirectUrl)
                : orufyHandler(process.env.NEXT_PUBLIC_ONE_ASSURE_ORUFY_LINK as string);
            }}
          >
            <span className="text-white text-sm">{button}</span>
          </Button>
        </div>
      </div>
    </>
  );
};

const CategoryCard = ({
  title,
  features,
  button,
  icon,
  mostPopular,
  redirectUrl,
  buttonColor,
  className,
}: {
  title?: string;
  features?: string[];
  button?: string;
  icon?: React.ReactNode | string;
  mostPopular?: boolean;
  redirectUrl?: string;
  buttonColor?: string;
  className?: string;
}) => {
  return (
    <>
      {/* Desktop Layout */}
      <div className={`hidden md:block w-full mt-3 md:mt-2 ${className}`}>
        <DesktopCards
          title={title}
          features={features}
          button={button}
          icon={icon}
          mostPopular={mostPopular}
          redirectUrl={redirectUrl}
          buttonColor={buttonColor}
        />
      </div>

      {/* Mobile Layout */}
      <div className={`md:hidden w-full ${className}`}>
        <MobileCards
          title={title}
          features={features}
          button={button}
          icon={icon}
          mostPopular={mostPopular}
          redirectUrl={redirectUrl}
          buttonColor={buttonColor}
        />
      </div>
    </>
  );
};

export default CategoryCard;
