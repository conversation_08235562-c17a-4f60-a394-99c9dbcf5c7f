import { HeadingSmall, BodyLarge } from "@/components/UI/Typography";

type CardData = {
  title: string;
  content: string | string[];
};

type CardProps = {
  data: CardData;
  className?: string | undefined | null;
};

const FlatCard: React.FC<CardProps> = ({ data, className = "" }) => {
  return (
    <div
      className={`bg-white rounded-xl p-6 border border-primary-200 shadow-sm ${className}`}
    >
      <HeadingSmall
        as="h3"
        className="text-neutral-1100 text-center font-semibold"
      >
        {data.title}
      </HeadingSmall>
      {data.content !== undefined && data.content !== "" && (
        <div className="text-gray-700 text-center mt-4">
          {Array.isArray(data.content) && (
            <div className="flex justify-around">
              {data.content.map((item, index) => (
                <BodyLarge key={index}>{item}</BodyLarge>
              ))}
            </div>
          )}
          {typeof data.content === "string" && (
            <BodyLarge>{data.content}</BodyLarge>
          )}
        </div>
      )}
    </div>
  );
};

export default FlatCard;
