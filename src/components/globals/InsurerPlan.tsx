import React, { useState } from "react";
import { Button } from "@/components/UI/Button";
import { HeadingXLarge } from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import InsurerPlanCard from "@/components/globals/InsurerPlanCard";

type InsurerPlanProps = {
  allInsurerData: any[];
};

const InsurerPlan: React.FC<InsurerPlanProps> = ({ allInsurerData }) => {
  const [showAllPlans, setShowAllPlans] = useState(false);

  const displayedPlans = showAllPlans
    ? allInsurerData
    : allInsurerData.slice(0, 6);
  const hasMorePlans = allInsurerData.length > 6;

  const toggleShowAllPlans = () => setShowAllPlans((prev) => !prev);

  return (
    <SectionContainerLarge
      className="w-full bg-white rounded-xl"
      id="health-insurance-providers-in-india"
    >
      <HeadingXLarge
        as="h2"
        className="text-neutral-1100 mb-4 md:mb-6 text-center"
      >
        Health Insurance Providers in India
      </HeadingXLarge>

      {/* Desktop view - show all plans */}
      <div className="hidden md:grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        {allInsurerData.map((item, index) => (
          <InsurerPlanCard key={`desktop-${index}`} plan={item} />
        ))}
      </div>

      {/* Mobile view - show limited plans with toggle */}
      <div className="md:hidden w-full">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {displayedPlans.map((item, index) => (
            <InsurerPlanCard key={`mobile-${index}`} plan={item} />
          ))}
        </div>

        {/* Toggle button for mobile */}
        {hasMorePlans && (
          <div className="flex justify-center mt-4">
            <Button
              onClick={toggleShowAllPlans}
              variant="primary"
              className="flex items-center"
            >
              <span className="text-sm font-normal">
                {showAllPlans ? "Show Less" : "See More Insurers"}
              </span>
            </Button>
          </div>
        )}
      </div>
    </SectionContainerLarge>
  );
};

export default InsurerPlan;
