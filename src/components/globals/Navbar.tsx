"use client";

import Image from "next/image";
import CustomLink from "./CustomLink";
import { useState, useEffect } from "react";
import { Popover } from "@headlessui/react";
import {
  MdOutlineHealthAndSafety,
  MdOutlineHealing,
  MdMenuBook,
} from "react-icons/md";
import { FaAngleDown } from "react-icons/fa6";

import Container from "./Container";
import MobileNavbar from "./MobileNavbar";

import { usePathname, useSearchParams } from "next/navigation";

function Navbar() {
  const pathname = usePathname();
  const search = useSearchParams();
  const utm = search.get("utm_source");
  const [toggle, setToggle] = useState(1);

  const business = [
    "/insurance/corporate-insurance",
    "/insurance/sell-insurance",
    "/one-cloud",
    "/business/home",
    "/business/group-health-life",
    "/business/liability-insurance",
  ];

  const navOpts = [
    [
      {
        title: "Health Insurance",
        url: "/health-insurance",
      },
      {
        title: "Term Insurance",
        url: "/term-insurance",
      },
      // {
      //   title: "Claims",
      //   url: "/claims",
      // },
    ],
    [
      {
        title: "Group Health & Life",
        url: "/business/group-health-life",
      },
      {
        title: "Liability Insurance",
        url: "/business/liability-insurance",
      },
      // {
      //   title: "Sell Insurance",
      //   url: "/insurance/sell-insurance",
      // },
      // {
      //   title: "OneCloud",
      //   url: "/one-cloud",
      // },
    ],
  ];

  useEffect(() => {
    // console.log(pathname);
    // console.log("Bus : ", business.includes(pathname));
    if (business.includes(pathname)) {
      setToggle(2);
    } else {
      setToggle(1);
    }
  }, [pathname]);

  return (
    <nav className="shadow-md py-5 fixed top-0 z-50 w-full bg-white px-5 lg:px-0 ">
      <Container navSpacing={false}>
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <CustomLink
              href={`${toggle === 1 ? "/" : "/business/home"}`}
              utm={utm}
            >
              <div className="flex items-center cursor-pointer mr-8">
                <Image
                  src="https://d1wamg5uas0pit.cloudfront.net/Frame.svg-1697693514"
                  alt=""
                  width={56}
                  height={56}
                />
                <h2 className="font-bold text-xl text-blue600 hidden md:block">
                  OneAssure
                </h2>
              </div>
            </CustomLink>

            <div className="items-center bg-gray200 rounded-full hidden lg:flex">
              <CustomLink href="/" utm={utm}>
                <div
                  className={`px-8 py-2 text-base font-semibold rounded-full cursor-pointer ${
                    toggle === 1 ? "bg-blue600 text-white" : "text-gray900"
                  }`}
                  onClick={() => setToggle(1)}
                >
                  Personal
                </div>
              </CustomLink>

              <CustomLink href="/business/home" utm={utm}>
                <div
                  className={`px-8 py-2 text-base font-semibold rounded-full cursor-pointer ${
                    toggle === 2 ? "bg-blue600 text-white" : "text-gray900"
                  }`}
                  onClick={() => setToggle(2)}
                >
                  Business
                </div>
              </CustomLink>
            </div>
          </div>

          <MobileNavbar utm={utm} />

          <div
            className={`items-center justify-between gap-x-16 hidden lg:flex`}
          >
            {navOpts[toggle - 1].map((opt, index) => {
              return (
                <CustomLink key={index} href={opt.url} utm={utm}>
                  <span className="font-semibold text-lg text-slateGrey">
                    {opt.title}
                  </span>
                </CustomLink>
              );
            })}
          </div>
        </div>
      </Container>
    </nav>
  );
}

export default Navbar;
