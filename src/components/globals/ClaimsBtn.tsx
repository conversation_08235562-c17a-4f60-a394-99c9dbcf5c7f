"use client";

import { useRouter } from "next/navigation";

const ClaimsBtn = ({ onClaimClick }: { onClaimClick?: () => void }) => {
  const router = useRouter();

  const handleClick = () => {
    // Call the optional callback if provided
    if (onClaimClick) {
      onClaimClick();
    }
    router.push("/claims");
  };

  return (
    <button
      onClick={handleClick}
      className="border-2 border-primary-blue-3 text-primary-blue-3 md:text-base font-semibold px-8 py-3 md:py-[10px] mt-8 md:mt-0 rounded-full hover:shadow-lg hover:shadow-primary-blue-3/20 transition-all duration-300"
    >
      Claims
    </button>
  );
};

export default ClaimsBtn;
