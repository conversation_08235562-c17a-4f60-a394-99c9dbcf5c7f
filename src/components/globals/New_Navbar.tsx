"use client";
import "@/app/globals.css";

import useNavbarStore from "@/store/navbar";
import "animate.css";
import Image from "next/image";
import CustomLink from "./CustomLink";
import {
  useState,
  useEffect,
  Fragment,
  use,
  Dispatch,
  SetStateAction,
} from "react";
import { Popover } from "@headlessui/react";
import {
  MdOutlineHealthAndSafety,
  MdOutlineHealing,
  MdMenuBook,
} from "react-icons/md";
import { FaAngleDown } from "react-icons/fa6";

import Container from "./Container";
import MobileNavbar from "./MobileNavbar";

import { usePathname, useSearchParams } from "next/navigation";
import { XCircleIcon } from "@heroicons/react/24/outline";
import { Menu, Transition } from "@headlessui/react";
import {
  ArchiveBoxXMarkIcon,
  ChevronDownIcon,
  PencilIcon,
  Square2StackIcon,
  TrashIcon,
} from "@heroicons/react/16/solid";
import Link from "next/link";

function Submenu({
  menuItem,
  setIsOpen,
  utm,
}: {
  menuItem: {
    title: string;
    subOpts: {
      title: string;
      url: string;
    }[];
  };
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  utm: string | null;
}) {
  const [isSubmenuOpen, setIsSubmenuOpen] = useState(false);
  return (
    <li
      onClick={() => setIsSubmenuOpen(!isSubmenuOpen)}
      className="border-b border-gray100 mb-5"
    >
      <div className="pb-2 flex items-center justify-between">
        <span className="font-semibold text-lg text-slateGrey">
          {menuItem.title}
        </span>

        <ChevronDownIcon className="w-4 h-4 text-slateGrey" />
      </div>

      <div className={`my-2 ml-5 ${isSubmenuOpen ? "block" : "hidden"}`}>
        <ul>
          {menuItem.subOpts.map((opt, idx) => (
            <CustomLink key={idx} href={opt.url} utm={utm}>
              <li
                onClick={() => setIsOpen(false)}
                className={`py-2 ${
                  idx < menuItem.subOpts.length - 1 && "border-b border-gray100"
                }`}
              >
                <span className="font-semibold text-lg text-slateGrey">
                  {opt.title}
                </span>
              </li>
            </CustomLink>
          ))}
        </ul>
      </div>
    </li>
  );
}

function New_Navbar() {
  const pathname = usePathname();
  const search = useSearchParams();
  const utm = search.get("utm_source");
  const [toggle, setToggle] = useState(1);

  const [isOpen, setIsOpen] = useState(false);

  const handleMenu = () => {
    setIsOpen(!isOpen);
  };

  const business = [
    "/insurance/corporate-insurance",
    "/insurance/sell-insurance",
    "/one-cloud",
    "/business/home",
    "/business/group-health-life",
    "/business/liability-insurance",
  ];

  const navOpts = [
    [
      {
        title: "Health Insurance",
        url: "/health-insurance",
        // subOpts: [
        //   {
        //     title: "Generate Quote",
        //     url: "/health-insurance",
        //   },
        //   {
        //     title: "Health Products",
        //     url: "/health-insurance/all-products",
        //   },
        // ],
      },
      {
        title: "Term Insurance",
        url: "/term-insurance",
        // subOpts: [
        //   {
        //     title: "Generate Quote",
        //     url: "/term-insurance",
        //   },
        //   {
        //     title: "Term Products",
        //     url: "/term-insurance/all-products",
        //   },
        // ],
      },
      {
        title: "Blog",
        url: "/insurance",
      },
    ],
    [
      {
        title: "Group Health & Life",
        url: "/business/group-health-life",
      },
      {
        title: "Liability Insurance",
        url: "/business/liability-insurance",
      },
    ],
  ];

  useEffect(() => {
    if (business.includes(pathname)) {
      setToggle(2);
    } else {
      setToggle(1);
    }
  }, [pathname]);

  const navbarStore = useNavbarStore();

  return (
    <>
      <nav
        className={`shadow-md py-5 w-full bg-white px-5 lg:px-0 ${
          !navbarStore.isNavbarVisible && typeof window != undefined
            ? "bg-black fixed w-full top-0 z-50 animate__animated animate__slideInDown animate__faster"
            : "bg-white"
        }`}
      >
        <Container navSpacing={false}>
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <CustomLink
                href={`${toggle === 1 ? "/" : "/business/home"}`}
                utm={utm}
              >
                <div className="flex items-center cursor-pointer mr-8">
                  
                  <h2 className="font- text-xl text-lightPurple hidden md:block">
                    OneAssure
                  </h2>
                </div>
              </CustomLink>

              
            </div>

            <div
              onClick={handleMenu}
              className="lg:hidden hover:cursor-pointer"
              // className={`${styles.menuIcon} ${isOpen ? styles.open : ""}`}
            >
              <div className=" w-8 h-1 bg-blue600 mb-1 rounded-full"></div>
              <div className=" w-8 h-1 bg-blue600 my-1 rounded-full"></div>
              <div className=" w-8 h-1 bg-blue600 mt-1 rounded-full"></div>
            </div>

            {/* <MobileNavbar utm={utm} /> */}

            <div
              className={`items-center justify-between gap-x-16 hidden lg:flex`}
            >
              {navOpts[toggle - 1].map((opt, index) => {
                // @ts-ignore
                if (opt.url) {
                  return (
                    // @ts-ignore
                    <CustomLink key={index} href={opt.url} utm={utm}>
                      <span className="font-semibold text-lg text-slateGrey">
                        {opt.title}
                      </span>
                    </CustomLink>
                  );
                } else {
                  return (
                    <Menu
                      as="div"
                      className="relative inline-block text-left z-10"
                      key={index}
                    >
                      <div>
                        <Menu.Button className="inline-flex items-center focus:outline-none  font-semibold text-lg text-slateGrey">
                          {opt.title}
                          <ChevronDownIcon
                            className="-mr-1 ml-2 h-5 w-5 font-semibold text-lg text-slateGrey"
                            aria-hidden="true"
                          />
                        </Menu.Button>
                      </div>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                      >
                        <Menu.Items className="absolute right-0 mt-1 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none">
                          {/* @ts-ignore */}
                          {opt.subOpts.map((option, idx) => (
                            <div className="px-1 py-1 " key={idx}>
                              <Menu.Item>
                                {({ active }) => (
                                  <Link
                                    href={{
                                      pathname: option.url,
                                      query: { utm_source: utm },
                                    }}
                                    className={`${
                                      active
                                        ? "bg-secondary-2 text-ntrl-black"
                                        : "text-gray-900"
                                    } group flex w-full items-center rounded-md text-[16px]/[24px] px-4 py-3`}
                                  >
                                    {option.title}
                                  </Link>
                                )}
                              </Menu.Item>
                            </div>
                          ))}
                        </Menu.Items>
                      </Transition>
                    </Menu>
                  );
                }
              })}
            </div>
          </div>
        </Container>
      </nav>

      {/*------------------------------------------------------------------------*/}
      {/* Sliding window */}
      <div
        className={`fixed left-0 top-0 h-full w-full bg-white flex flex-col  transform -translate-x-full transition-transform duration-300 ease-in-out z-50 px-5 shadow-shadow100 ${
          isOpen ? "transform translate-x-0" : ""
        }`}
      >
        <div
          className="mt-10 flex items-end justify-end mb-20"
          onClick={handleMenu}
        >
          <XCircleIcon className="text-blue600 w-6 h-6" />
        </div>
        {/* toggle */}
        <div className="items-center rounded-full mb-9">
          <CustomLink href="/" utm={utm}>
            <div
              className={`px-8 py-2 mb-5 text-base font-semibold rounded-full cursor-pointer ${
                toggle === 1
                  ? "bg-blue600 text-white"
                  : "bg-gray200 text-gray900"
              }`}
              onClick={() => {
                setToggle(1);
                setIsOpen(false);
              }}
            >
              Personal
            </div>
          </CustomLink>

          <CustomLink href="/business/home" utm={utm}>
            <div
              className={`px-8 py-2 text-base font-semibold rounded-full cursor-pointer ${
                toggle === 2
                  ? "bg-blue600 text-white"
                  : "text-gray900 bg-gray200"
              }`}
              onClick={() => {
                setToggle(2);
                setIsOpen(false);
              }}
            >
              Business
            </div>
          </CustomLink>
        </div>

        <ul>
          {navOpts[toggle - 1].map((opt, idx) => {
            // @ts-ignore
            if (opt.url) {
              return (
                // @ts-ignore
                <CustomLink key={idx} href={opt.url} utm={utm}>
                  <li
                    onClick={() => setIsOpen(false)}
                    className=" border-b border-gray100 pb-2"
                  >
                    <span className="font-semibold text-lg text-slateGrey">
                      {opt.title}
                    </span>
                  </li>
                </CustomLink>
              );
            } else {
              return (
                <Submenu
                  key={idx}
                  // @ts-ignore
                  menuItem={opt}
                  setIsOpen={setIsOpen}
                  utm={utm}
                />
              );
            }
          })}
        </ul>
      </div>
    </>
  );
}

export default New_Navbar;

// ${!isIntersecting ? "bg-black" : "bg-white"}`}
