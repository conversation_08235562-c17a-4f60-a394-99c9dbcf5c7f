import React, { useState, useEffect } from "react";
import { IoArrowUp } from "react-icons/io5";

const GoToTopFloater: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  if (!isVisible) return null;

  return (
    <button
      onClick={scrollToTop}
      className="fixed bottom-40 right-6 z-50 w-12 h-12 bg-primary-800 hover:bg-primary-900 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 ease-in-out transform hover:scale-110"
      aria-label="Go to top"
    >
      <IoArrowUp size={20} />
    </button>
  );
};

export default GoToTopFloater;
