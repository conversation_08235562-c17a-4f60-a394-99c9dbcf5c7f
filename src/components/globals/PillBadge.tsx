import { BodyMedium } from "../UI/Typography";
import type { ElementType } from "react";

const PillBadge = ({ pill, as }: { pill: string; as?: ElementType }) => {
  return (
    <div className=" flex items-center justify-center">
      <span className="px-8 py-[0.5px] md:py-[1.5px] rounded-full border-[0.5px] border-secondary-400 bg-secondary-100">
        <BodyMedium
          {...(as ? { as } : { as: "p" })}
          className="text-secondary-400"
        >
          {pill}
        </BodyMedium>
      </span>
    </div>
  );
};

export default PillBadge;
