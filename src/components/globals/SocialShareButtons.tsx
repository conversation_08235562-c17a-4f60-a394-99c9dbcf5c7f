import React, { useState, useRef, useEffect } from "react";
import { FaFacebook, FaLinkedin, FaWhatsapp } from "react-icons/fa";
import { MdOutlineMail } from "react-icons/md";
import { IoShareOutline } from "react-icons/io5";
import { BodySmall } from "../UI/Typography";

type SocialShareButtonsProps = {
  url?: string;
  title?: string;
};

const getShareUrl = (platform: string, url: string, title: string) => {
  switch (platform) {
    case "facebook":
      return `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
        url
      )}`;
    case "email":
      return `mailto:?subject=${encodeURIComponent(
        title
      )}&body=${encodeURIComponent(`Check out this page: ${url}`)}`;
    case "x":
      return `https://x.com/intent/tweet?url=${encodeURIComponent(
        url
      )}&text=${encodeURIComponent(title)}`;
    case "linkedin":
      return `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
        url
      )}`;
    case "whatsapp":
      return `https://wa.me/?text=${encodeURIComponent(`${title} ${url}`)}`;
    default:
      return url;
  }
};

const icons = {
  facebook: <FaFacebook color="#1877F3" size={16} />,
  email: <MdOutlineMail color="#D44638" size={16} />,
  x: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 1200 1227"
      width="16"
      height="16"
      fill="black"
    >
      <path d="M773.5 0H992L622.3 462.6 1200 1227H831.6L537.4 819.8 200.6 1227H-18L382.1 716.5 0 0h379.1l258.2 377.4L773.5 0ZM712.2 1115.7h100.7L294.2 104.6H188.3l523.9 1011.1Z" />
    </svg>
  ),
  linkedin: <FaLinkedin color="#0077B5" size={16} />,
  whatsapp: <FaWhatsapp color="#25D366" size={16} />,
};

const SocialShareButtons: React.FC<SocialShareButtonsProps> = ({
  url,
  title,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<"left" | "right">(
    "left"
  );
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const shareUrl =
    url || (typeof window !== "undefined" ? window.location.href : "");
  const shareTitle =
    title || (typeof document !== "undefined" ? document.title : "");

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const dropdownWidth = 256; // w-64 = 16rem = 256px

      // Check if dropdown would overflow on the right
      if (buttonRect.left + dropdownWidth > viewportWidth) {
        setDropdownPosition("right");
      } else {
        setDropdownPosition("left");
      }
    }
  }, [isOpen]);

  const handleShare = (platform: string) => {
    const shareUrlFinal = getShareUrl(platform, shareUrl, shareTitle);

    if (platform === "email") {
      window.location.href = shareUrlFinal;
    } else {
      window.open(shareUrlFinal, "_blank");
    }
    setIsOpen(false);
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      // You could add a toast notification here
      console.log("Link copied to clipboard!");
    } catch (err) {
      console.error("Failed to copy link:", err);
    }
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 bg-white transition-colors cursor-pointer"
        aria-label="Share this page"
      >
        <IoShareOutline className="text-neutral-1100" size={18} />
        <BodySmall className="text-neutral-1100">Share this Page</BodySmall>
      </button>

      {isOpen && (
        <div
          className={`absolute top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-neutral-200 z-50 ${
            dropdownPosition === "right" ? "right-0" : "left-0"
          }`}
        >
          <div className="p-3">
            <div className="text-xs text-neutral-1100 mb-3 font-medium">
              Share via
            </div>
            <div className="grid grid-cols-2 gap-2">
              {(
                [
                  { key: "facebook", label: "Facebook" },
                  { key: "x", label: "X" },
                  { key: "linkedin", label: "LinkedIn" },
                  { key: "whatsapp", label: "WhatsApp" },
                  { key: "email", label: "Email" },
                ] as const
              ).map(({ key, label }) => (
                <button
                  key={key}
                  onClick={() => handleShare(key)}
                  className="flex items-center gap-2 p-2 rounded-md hover:bg-neutral-200 transition-colors text-left"
                >
                  {icons[key]}
                  <span className="text-sm text-neutral-1100">{label}</span>
                </button>
              ))}
            </div>
            <div className="border-t border-neutral-200 mt-3 pt-3">
              <button
                onClick={handleCopyLink}
                className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-50 transition-colors w-full text-left"
              >
                <svg
                  className="w-4 h-4 text-neutral-1100"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
                <span className="text-sm text-neutral-1100">Copy Link</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SocialShareButtons;
