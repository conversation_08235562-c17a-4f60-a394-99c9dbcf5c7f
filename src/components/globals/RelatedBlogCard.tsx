import { BodyMedium, BodySmall } from "@/components/UI/Typography";
import Image from "next/image";
import Link from "next/link";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";

export type BlogCardProps = {
  title: string;
  date: string;
  author: string;
  description: string;
  imageUrl: string;
  url: string;
};

const BlogCard: React.FC<BlogCardProps> = ({
  title,
  date,
  author,
  description,
  imageUrl,
  url,
}) => {
  return (
    <MobileCarouselItem className="md:w-auto">
      <div className="bg-white rounded-xl shadow-sm border border-primary-200 overflow-hidden hover:shadow-md transition-shadow duration-300 w-full h-full">
        <div className="h-36 relative">
          <Image src={imageUrl} alt={title} fill className="object-cover" />
        </div>
        <div className="h-[50%] flex flex-col gap-2 px-6 py-4 justify-between">
          <BodyMedium weight="medium" className="text-neutral-1100">
            {title}
          </BodyMedium>
          <BodySmall className="text-neutral-800">
            {date} | {author}
          </BodySmall>
          <Link
            href={url}
            className="inline-block text-center mt-2 text-xs font-normal bg-primary-800 hover:bg-primary-700 text-white border border-primary-200 px-4 py-3 rounded-base transition-colors duration-200"
          >
            Read More
          </Link>
        </div>
      </div>
    </MobileCarouselItem>
  );
};

export default BlogCard;
