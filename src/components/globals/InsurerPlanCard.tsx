import { BodyMedium } from "@/components/UI/Typography";
import { FiArrowRight } from "react-icons/fi";
import Link from "next/link";
import Image from "next/image";

const InsurerPlanCard: React.FC<{ plan: any }> = ({ plan }) => (
  <Link
    scroll={false}
    href={`${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/${plan.company_slug}`}
    className="block"
  >
    <div className="bg-white border border-primary-200 rounded-xl p-4 flex flex-col justify-between shadow-sm hover:shadow-md transition-shadow cursor-pointer group">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="relative w-12 h-6">
            <Image
              src={plan.logo_url}
              alt={plan.company_name}
              fill
              className="object-contain"
            />
          </div>
          <BodyMedium weight="medium" className="mx-4 text-neutral-1100">
            {plan.company_name}
          </BodyMedium>
        </div>
        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-xs text-neutral-1100 group-hover:bg-primary-100 transition-colors">
          <FiArrowRight />
        </div>
      </div>
    </div>
  </Link>
);

export default InsurerPlanCard;
