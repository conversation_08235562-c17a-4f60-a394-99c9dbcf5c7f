import React, { useState, useRef } from "react";
import { LPTestimonial } from "../HealthInsurance/types";

const TestimonialCarousel = ({
  testimonials,
  onSlideChange,
}: {
  testimonials: LPTestimonial[];
  onSlideChange?: (index: number) => void;
}) => {
  const [activeSlide, setActiveSlide] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  return (
    <div className="relative">
      <div className="overflow-x-scroll scrollbar-hide md:pt-6 pt-5">
        <div
          ref={scrollContainerRef}
          className="flex md:gap-6 gap-4 md:px-8 py-4 z-10 px-4 overflow-x-scroll scrollbar-hide mx-4 items-stretch"
          onScroll={(e) => {
            const container = e.currentTarget;
            const scrollPosition = container.scrollLeft;
            const maxScroll = container.scrollWidth - container.clientWidth;

            // Calculate what percentage of the total scroll we've moved
            const scrollPercentage = scrollPosition / maxScroll;

            // Calculate which slide should be active based on the scroll percentage
            const newActiveSlide = Math.min(
              Math.round(scrollPercentage * (testimonials.length - 1)),
              testimonials.length - 1
            );

            setActiveSlide(newActiveSlide);
            onSlideChange?.(newActiveSlide);
          }}
        >
          {testimonials.map((testimony, index) => (
            <div
              className={`rounded-2xl min-w-[280px] md:min-w-[320px] max-w-[320px] md:max-w-[375px] flex-shrink-0 md:p-8 p-6 text-md font-normal snap-start ${
                index % 2 == 0 ? "bg-secondary-2" : "bg-primary-3"
              } text-ntrl-black-1 shadow-customblue4d relative`}
              key={index}
            >
              <div className="text-center md:my-5 mt-3 mb-4">
                <h3 className="text-[20px]/[28px] font-medium text-ntrl-black-1">
                  {testimony.name}
                </h3>
              </div>
              <p className="mb-2 font-light text-ntrl-black-1 text-center text-[16px]/[24px] break-words whitespace-normal">
                {`"${testimony.statement.trim()}"`}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Dot indicators */}
      <div className="flex justify-center gap-2.5 mt-6 mb-4">
        {testimonials.map((_, index) => (
          <div
            key={index}
            className={`w-2.5 h-2.5 rounded-full transition-colors duration-300 ${
              index === activeSlide ? "bg-black" : "bg-black/30"
            }`}
          />
        ))}
      </div>
    </div>
  );
};

export default TestimonialCarousel;
