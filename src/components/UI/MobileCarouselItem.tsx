import React from "react";

interface MobileCarouselItemProps {
  children: React.ReactNode;
  className?: string;
}

const MobileCarouselItem: React.FC<MobileCarouselItemProps> = ({
  children,
  className = "",
}) => {
  return (
    <div
      className={`w-[calc(100vw-54px)] flex-shrink-0 flex justify-center ${className}`}
    >
      <div className="w-full">{children}</div>
    </div>
  );
};

export default MobileCarouselItem;
