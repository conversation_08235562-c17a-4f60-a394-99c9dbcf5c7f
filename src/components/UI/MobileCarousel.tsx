import React, { useState, useEffect, useRef } from "react";

interface MobileCarouselProps {
  children: React.ReactNode;
  totalSlides: number;
  className?: string;
}

const MobileCarousel: React.FC<MobileCarouselProps> = ({
  children,
  totalSlides,
  className = "",
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
    const container = scrollContainerRef.current;
    if (container) {
      // Calculate the actual card width: 100vw - 54px (from MobileCarouselItem)
      const viewportWidth = window.innerWidth;
      const cardWidth = viewportWidth - 54; // This matches the MobileCarouselItem width
      const gap = 16; // gap-4 = 16px

      const scrollPosition = (cardWidth + gap) * index;

      container.scrollTo({
        left: scrollPosition,
        behavior: "smooth",
      });
    }
  };

  // Handle scroll events for mobile view
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const scrollLeft = container.scrollLeft;
      const viewportWidth = window.innerWidth;
      const cardWidth = viewportWidth - 54; // This matches the MobileCarouselItem width
      const gap = 16; // gap-4 = 16px
      const initialPadding = 24; // w-6 = 24px
      const adjustedScrollLeft = Math.max(0, scrollLeft - initialPadding);
      const currentIndex = Math.round(adjustedScrollLeft / (cardWidth + gap));
      const clampedIndex = Math.max(0, Math.min(currentIndex, totalSlides - 1));
      setCurrentSlide(clampedIndex);
    };

    container.addEventListener("scroll", handleScroll);

    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [totalSlides]);

  return (
    <div className={`w-full ${className}`}>
      {/* Carousel Container */}
      <div className="relative">
        <div
          ref={scrollContainerRef}
          className="overflow-x-auto scrollbar-hide scroll-smooth"
          style={{
            scrollbarWidth: "none",
            msOverflowStyle: "none",
          }}
        >
          <div className="flex">
            <div className="w-6 flex-shrink-0"></div>
            <div className="flex gap-4">{children}</div>
            <div className="w-6 flex-shrink-0"></div>
          </div>
        </div>
      </div>

      {/* Pagination Dots */}
      <div className="flex justify-center mt-6 gap-2">
        {Array.from({ length: totalSlides }).map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-2 h-2 rounded-full transition-colors duration-200 ${
              index === currentSlide ? "bg-neutral-1100" : "bg-neutral-400"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default MobileCarousel;
