import React, { useState, useRef, useEffect } from "react";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import SectionContainer from "@/components/globals/SectionContainer";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";

type DesktopCarouselProps = {
  children: React.ReactNode;
  totalSlides: number;
  className?: string;
  itemsPerPage?: number;
  showArrows?: boolean;
  showDots?: boolean;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  wrapAround?: boolean;
}

const DesktopCarousel: React.FC<DesktopCarouselProps> = ({
  children,
  totalSlides,
  className = "",
  itemsPerPage = 3,
  showArrows = true,
  showDots = true,
  autoPlay = false,
  autoPlayInterval = 3000,
  wrapAround = false,
}) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);

  const totalPages = Math.ceil(totalSlides / itemsPerPage);
  const childrenArray = React.Children.toArray(children);
  const childrenCount = childrenArray.length;

  const handlePrevPage = () => {
    if (wrapAround) {
      setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
    } else {
      setCurrentPage((prev) => Math.max(0, prev - 1));
    }
  };

  const handleNextPage = () => {
    if (wrapAround) {
      setCurrentPage((prev) => (prev + 1) % totalPages);
    } else {
      setCurrentPage((prev) => Math.min(totalPages - 1, prev + 1));
    }
  };

  const goToPage = (pageIndex: number) => {
    setCurrentPage(pageIndex);
  };

  // Auto-play functionality
  useEffect(() => {
    if (autoPlay && !isHovered && totalPages > 1) {
      autoPlayRef.current = setInterval(() => {
        setCurrentPage((prev) => (prev + 1) % totalPages);
      }, autoPlayInterval);
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [autoPlay, isHovered, totalPages, autoPlayInterval, wrapAround]);

  // Navigation Arrow Component
  const NavigationArrow = ({
    direction,
    onClick,
    disabled,
  }: {
    direction: "prev" | "next";
    onClick: () => void;
    disabled: boolean;
  }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`flex items-center justify-center w-10 h-10 rounded-full border bg-neutral-100 text-neutral-1100 shadow-md transition-all duration-200 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed ${
        disabled ? "hover:shadow-md" : "hover:scale-105"
      }`}
      aria-label={`${direction === "prev" ? "Previous" : "Next"} page`}
    >
      {direction === "prev" ? <IoIosArrowBack /> : <IoIosArrowForward />}
    </button>
  );

  // Page Indicator Component
  const PageIndicator = ({
    isActive,
    onClick,
  }: {
    isActive: boolean;
    onClick: () => void;
  }) => (
    <button
      onClick={onClick}
      className={`w-2.5 h-2.5 rounded-full transition-all duration-200 ${
        isActive
          ? "bg-neutral-1100 scale-110"
          : "bg-neutral-300 hover:bg-neutral-400 hover:scale-105"
      }`}
      aria-label={`Go to page ${isActive ? "current" : ""}`}
    />
  );

  return (
    <div
      className={`relative ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Main Carousel Container */}
      <SectionContainer className="flex items-center !mb-0">
        {/* Previous Arrow */}
        {showArrows && totalPages > 1 && (
          <div className="flex-shrink-0 mr-4">
            <NavigationArrow
              direction="prev"
              onClick={handlePrevPage}
              disabled={!wrapAround && currentPage === 0}
            />
          </div>
        )}

        {/* Carousel Content */}
        <SectionContainerLarge className="flex-1 overflow-hidden !mb-0">
          {/* If there are fewer children than itemsPerPage, center them */}
          {childrenCount > 0 && childrenCount < itemsPerPage ? (
            <div className="flex justify-center transition-transform duration-500 ease-in-out">
              <div
                className="grid gap-4"
                style={{
                  width: "100%",
                  maxWidth: `${childrenCount * (100 / itemsPerPage)}%`,
                  gridTemplateColumns: `repeat(${childrenCount}, 1fr)`,
                }}
              >
                {childrenArray.map((child, index) => (
                  <div key={index}>{child}</div>
                ))}
              </div>
            </div>
          ) : (
            <div
              className="flex transition-transform duration-500 ease-in-out"
              style={{
                transform: `translateX(-${currentPage * (100 / totalPages)}%)`,
                width: `${totalPages * 100}%`,
              }}
            >
              {Array.from({ length: totalPages }, (_, pageIndex) => (
                <div
                  key={pageIndex}
                  className="grid gap-4 flex-shrink-0"
                  style={{
                    width: `${100 / totalPages}%`,
                    gridTemplateColumns: `repeat(${itemsPerPage}, 1fr)`,
                  }}
                >
                  {childrenArray
                    .slice(
                      pageIndex * itemsPerPage,
                      (pageIndex + 1) * itemsPerPage
                    )
                    .map((child, index) => (
                      <div key={pageIndex * itemsPerPage + index}>
                        {child}
                      </div>
                    ))}
                </div>
              ))}
            </div>
          )}
        </SectionContainerLarge>

        {/* Next Arrow */}
        {showArrows && totalPages > 1 && (
          <div className="flex-shrink-0 ml-4">
            <NavigationArrow
              direction="next"
              onClick={handleNextPage}
              disabled={!wrapAround && currentPage === totalPages - 1}
            />
          </div>
        )}
      </SectionContainer>

      {/* Page Indicators */}
      {showDots && totalPages > 1 && (
        <div className="flex justify-center items-center mt-6 gap-2">
          {Array.from({ length: totalPages }, (_, index) => (
            <PageIndicator
              key={index}
              isActive={currentPage === index}
              onClick={() => goToPage(index)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default DesktopCarousel;
