import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const gridVariants = cva(
  "grid", // Base grid class
  {
    variants: {
      cols: {
        1: "grid-cols-1",
        2: "grid-cols-2", 
        3: "grid-cols-3",
        4: "grid-cols-4",
        5: "grid-cols-5",
        6: "grid-cols-6",
        12: "grid-cols-12",
        auto: "grid-cols-auto",
      },
      mobileCols: {
        1: "max-sm:grid-cols-1",
        2: "max-sm:grid-cols-2",
        3: "max-sm:grid-cols-3",
        4: "max-sm:grid-cols-4",
        5: "max-sm:grid-cols-5",
        6: "max-sm:grid-cols-6",
      },
      gap: {
        0: "gap-0",
        1: "gap-1",
        2: "gap-2",
        3: "gap-3",
        4: "gap-4",
        5: "gap-5",
        6: "gap-6",
        8: "gap-8",
        10: "gap-10",
        12: "gap-12",
        16: "gap-16",
        20: "gap-20",
        24: "gap-24",
      },
      gapX: {
        0: "gap-x-0",
        1: "gap-x-1",
        2: "gap-x-2",
        3: "gap-x-3",
        4: "gap-x-4",
        5: "gap-x-5",
        6: "gap-x-6",
        8: "gap-x-8",
        10: "gap-x-10",
        12: "gap-x-12",
        16: "gap-x-16",
        20: "gap-x-20",
        24: "gap-x-24",
      },
      gapY: {
        0: "gap-y-0",
        1: "gap-y-1",
        2: "gap-y-2",
        3: "gap-y-3",
        4: "gap-y-4",
        5: "gap-y-5",
        6: "gap-y-6",
        8: "gap-y-8",
        10: "gap-y-10",
        12: "gap-y-12",
        16: "gap-y-16",
        20: "gap-y-20",
        24: "gap-y-24",
      },
      flow: {
        row: "grid-flow-row",
        col: "grid-flow-col",
        dense: "grid-flow-dense",
        "row-dense": "grid-flow-row-dense",
        "col-dense": "grid-flow-col-dense",
      },
      autoCols: {
        auto: "auto-cols-auto",
        min: "auto-cols-min",
        max: "auto-cols-max",
        fr: "auto-cols-fr",
      },
      autoRows: {
        auto: "auto-rows-auto",
        min: "auto-rows-min",
        max: "auto-rows-max",
        fr: "auto-rows-fr",
      },
    },
    defaultVariants: {
      cols: 1,
      gap: 4,
      flow: "row",
    },
  }
);

export interface GridProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof gridVariants> {
  children: React.ReactNode;
  /**
   * Number of columns for the grid (default breakpoint)
   */
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 12 | "auto";
  /**
   * Number of columns for mobile devices (max-width: 640px)
   */
  mobileCols?: 1 | 2 | 3 | 4 | 5 | 6;
  /**
   * Gap between grid items
   */
  gap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24;
  /**
   * Horizontal gap between grid items (overrides gap)
   */
  gapX?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24;
  /**
   * Vertical gap between grid items (overrides gap)
   */
  gapY?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24;
  /**
   * Grid flow direction
   */
  flow?: "row" | "col" | "dense" | "row-dense" | "col-dense";
  /**
   * Auto column sizing
   */
  autoCols?: "auto" | "min" | "max" | "fr";
  /**
   * Auto row sizing
   */
  autoRows?: "auto" | "min" | "max" | "fr";
  /**
   * Custom grid template columns (CSS grid-template-columns value)
   */
  templateCols?: string;
  /**
   * Custom grid template rows (CSS grid-template-rows value)
   */
  templateRows?: string;
  /**
   * Custom grid areas (CSS grid-template-areas value)
   */
  areas?: string;
}

const Grid = React.forwardRef<HTMLDivElement, GridProps>(
  (
    {
      className,
      children,
      cols,
      mobileCols,
      gap,
      gapX,
      gapY,
      flow,
      autoCols,
      autoRows,
      templateCols,
      templateRows,
      areas,
      ...props
    },
    ref
  ) => {
    // Build custom styles for template columns/rows/areas
    const customStyles: React.CSSProperties = {};
    
    if (templateCols) {
      customStyles.gridTemplateColumns = templateCols;
    }
    
    if (templateRows) {
      customStyles.gridTemplateRows = templateRows;
    }
    
    if (areas) {
      customStyles.gridTemplateAreas = areas;
    }

    return (
      <div
        ref={ref}
        className={cn(
          gridVariants({
            cols,
            mobileCols,
            gap: gapX && gapY ? undefined : gap, // Only use gap if gapX/gapY not specified
            gapX,
            gapY,
            flow,
            autoCols,
            autoRows,
            className,
          })
        )}
        style={customStyles}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Grid.displayName = "Grid";

export { Grid, gridVariants };
