"use client";

import Container from "@/components/globals/Container";
import parse, {
  domToReact,
  DOMNode,
  Element,
  Text as DomText,
} from "html-react-parser";
import Image from "next/image";
import Breadcrumb from "@/components/globals/Breadcrumb";
import { Suspense } from "react";

interface FooterDocsProps {
  desc: any;
  path?: string[];
}

const Document = ({ desc, path }: FooterDocsProps) => {
  const replace = (domNode: DOMNode, index: number) => {
    if (domNode.type === "tag" && domNode.attribs) {
      let attrs = domNode.attribs;
      if (attrs.style) {
        attrs.style = "";
      }

      switch (domNode.name) {
        case "h1":
          attrs.className =
            "text-black text-3xl md:text-4xl font-bold mb-4 mt-2";
          break;
        case "h2":
          attrs.className =
            "text-black text-2xl md:text-3xl font-bold mb-4 mt-8";
          break;
        case "h3":
          attrs.className = "text-black text-xl font-semibold mb-3 mt-6";
          break;
        case "h4":
        case "h5":
          attrs.className = "text-black text-lg font-semibold mb-2 mt-6";
          break;
        case "h6":
          attrs.className =
            "text-red-600 font-bold uppercase text-base md:text-lg mt-6 mb-0 tracking-wide";
          break;
        case "p":
        case "span":
          attrs.className =
            "text-neutral-800 text-base md:text-lg mb-3 leading-relaxed";
          break;
        case "ul":
          attrs.className =
            "list-disc ml-6 mb-3 text-neutral-800 text-base md:text-lg";
          break;
        case "ol":
          attrs.className =
            "list-decimal ml-6 mb-3 text-neutral-800 text-base md:text-lg";
          break;
        case "li":
          attrs.className = "mb-2";
          break;
        case "strong":
        case "b":
          attrs.className = "text-black font-bold";
          break;
        case "u":
        case "a":
          attrs.className = "text-blue-600 underline";
          break;
        case "img":
          if (!attrs.src) {
            return null; // Don't render image if no src
          }
          return (
            <div className="mx-auto max-w-[300px] md:max-w-full md:h-64 h-48 aspect-video md:my-10 my-3 relative">
              <Image
                src={attrs.src}
                fill={true}
                style={{ objectFit: "contain" }}
                alt={attrs.alt || "image"}
              />
            </div>
          );
        case "iframe":
          attrs.className = "my-0 mx-auto w-full md:max-w-4xl";
          break;
      }
      return domNode;
    }
  };

  // Safely parse content
  const renderContent = () => {
    try {
      if (!desc.content || typeof desc.content !== "string") {
        return <div>No content available</div>;
      }
      return parse(desc.content, { replace });
    } catch (error) {
      console.error("Error parsing content:", error);
      return <div>Error rendering content</div>;
    }
  };

  // Safely render document file section
  const renderDocFile = () => {
    if (
      !desc.showDoc ||
      !desc.docFile ||
      !Array.isArray(desc.docFile) ||
      desc.docFile.length === 0
    ) {
      return null;
    }

    const docFile = desc.docFile[0];
    if (
      !docFile ||
      !docFile.title ||
      !docFile.file ||
      !docFile.file.data ||
      !docFile.file.data.attributes ||
      !docFile.file.data.attributes.url
    ) {
      return null;
    }

    return (
      <div className="flex items-center justify-between border border-[#2889C8] rounded-2xl p-2 mt-6 shadow-sm bg-white">
        <span className="text-[#2889C8] md:pl-4 pl-2 text-base md:text-lg font-semibold">
          {docFile.title}
        </span>
        <a
          href={`${docFile.file.data.attributes.url}`}
          target="_blank"
          rel="noopener noreferrer"
          className="bg-[#2889C8] hover:bg-[#2889C8] text-white text-sm md:text-base font-medium rounded-xl px-4 py-3 shadow-md transition-colors duration-200"
        >
          View Document
        </a>
      </div>
    );
  };

  // Safely render breadcrumb
  const renderBreadcrumb = () => {
    try {
      return (
        <Suspense fallback={<div className="h-8"></div>}>
          <Breadcrumb path={path || ["home"]} />
        </Suspense>
      );
    } catch (error) {
      console.error("Error rendering breadcrumb:", error);
      return <div className="h-8"></div>;
    }
  };

  return (
    <div
      className="mb-6 md:mb-12 mt-4 px-4 font-sans"
      style={{ fontFamily: "Inter, ui-rounded, system-ui, sans-serif" }}
    >
      <Container>
        <div className="max-w-3xl md:max-w-4xl mx-auto bg-white rounded-2xl p-6 md:p-8 shadow-md">
          <div className="mb-4">
            {renderBreadcrumb()}
            <h1 className="text-3xl md:text-4xl font-bold mb-4 mt-2">
              {desc.title || "Untitled Document"}
            </h1>
          </div>
          <div>{renderContent()}</div>
          {renderDocFile()}
        </div>
      </Container>
    </div>
  );
};

export default Document;
