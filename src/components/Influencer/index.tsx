"use client";
import React, { useEffect } from "react";
import HeroWithForm from "@/components/Influencer/components/HeroWithForm";
import CompanyBackground from "@/components/Influencer/components/CompanyBackground";
import WhyTrustUs from "@/components/Influencer/components/WhyTrustUs";
import WhyChooseUs from "@/components/Influencer/components/WhyChooseUs";
import FeaturedSection from "@/components/Influencer/components/FeaturedSection";
import InfluencerSocialProof from "@/components/Influencer/components/InfluencerSocialProof";
import InfluencerYouTubeVideo from "@/components/Influencer/components/InfluencerYouTubeVideo";
import Testimonial from "@/components/globals/Testimonial";
import FaqSection from "@/components/globals/DSComponentsV0/FaqSection";
import FinalCTA from "@/components/Influencer/components/FinalCTA";
import { Influencer } from "@/components/Influencer/types";
import { usePathname, useRouter } from "next/navigation";
import { useSessionStorage } from "usehooks-ts";
import ConsultationSection from "@/components/Influencer/components/ConsultationSection";

const InfluencerRoot: React.FC<{
  influencer: Influencer;
}> = ({ influencer }) => {
  const pathname = usePathname();
  const router = useRouter();
  const [utm_source, setUtmSource] = useSessionStorage("utm_source", null);
  const [utm_medium, setUtmMedium] = useSessionStorage("utm_medium", null);
  const [utm_campaign, setUtmCampaign] = useSessionStorage(
    "utm_campaign",
    null
  );
  const [utm_content, setUtmContent] = useSessionStorage("utm_content", null);
  const [utm_term, setUtmTerm] = useSessionStorage("utm_term", null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      router.replace(
        `${pathname}?${utm_source !== null ? `utm_source=${utm_source}` : ""}${
          utm_medium !== null ? `&utm_medium=${utm_medium}` : ""
        }${utm_campaign !== null ? `&utm_campaign=${utm_campaign}` : ""}${
          utm_content !== null ? `&utm_content=${utm_content}` : ""
        }${utm_term !== null ? `&utm_term=${utm_term}` : ""}`
      );
    }
  }, []);

  const breadcrumbPath = ["OneAssure", "Partner", influencer.name];

  return (
    <div className="bg-white font-poppins">
      <HeroWithForm
        title={influencer.hero.title}
        description={influencer.hero.description}
        imageUrl={influencer.hero.imageUrl}
        breadcrumbPath={breadcrumbPath}
        fullUrl={pathname}
        data_access_link={influencer.data_access_link}
      />

      {/* Company Background */}
      {influencer.companyBackground && influencer.companyBackground.credentials && (
        <CompanyBackground
          companyBackground={influencer.companyBackground}
        />
      )}

      {/* Trust Section */}
      {influencer.whyTrustUs && (
        <WhyTrustUs
          whyTrustUs={influencer.whyTrustUs}
        />
      )}

      {/* Why Choose Us */}
      {influencer.whyChooseUs && influencer.whyChooseUs.points && (
        <WhyChooseUs
          whyChooseUs={influencer.whyChooseUs}
        />
      )}
      {/* Featured Section */}
      {/* <FeaturedSection featuredPoints={influencer.attributes.featuredPoints} /> */}

      {/* Consultation Section */}
      <ConsultationSection data_access_link={influencer.data_access_link} />

      {/* Influencer Social Proof */}
      {influencer.InfluencerSocialProof && influencer.InfluencerSocialProof.content && (
        <InfluencerSocialProof
          influencerSocialProof={influencer.InfluencerSocialProof}
        />
      )}

      {/* Influencer YouTube Video */}
      {influencer.InfluencerSocialProof.videoUrl && (
        <InfluencerYouTubeVideo
          influencerVideo={influencer.InfluencerSocialProof}
        />
      )}

      {/* Customer Testimonials */}
      {influencer.influencerTestimonials && influencer.influencerTestimonials.length > 0 && (
        <Testimonial
          testimonials={influencer.influencerTestimonials}
          sectionHeaderProps={{
            pill: "Testimonials",
            heading: "Customer Testimonials",
            subheading: "What our customers say about us",
          }}
          pill="Testimonials"
        />
      )}

      {/* FAQ Section */}
      {influencer.influencerFaqs && influencer.influencerFaqs.length > 0 && (
        <FaqSection
          faq={{
            id: "influencer-faqs",
            title: "Frequently Asked Questions",
            description:
              "Get answers to common questions about our insurance policies and services.",
            pill: "FAQ",
            faqs: influencer.influencerFaqs.map((faq) => ({
              id: faq.id.toString(),
              question: faq.question,
              answer: faq.answer,
            })),
          }}
        />
      )}

      {/* Final Call-to-Action */}
      <FinalCTA finalCTA={{
        title: "Final Call-to-Action",
        description: "Get in touch with us to learn more about our services",
        buttonText: "Contact Us",
        data_access_link: influencer.data_access_link,
      }} />
    </div>
  );
};

export default InfluencerRoot;
