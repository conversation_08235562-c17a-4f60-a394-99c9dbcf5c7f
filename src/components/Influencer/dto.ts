export type influencerApiResponse = {
  id: string;
  name: string;
  slug: string;
  data_access_link: string;
  hero_title: string;
  hero_image_url: string;
  hero_description: string;
  influencer_faqs: {
    id: string;
    question: string;
    answer: string;
  }[];
  influencer_one_assure_backgrounds: {
    id: string;
    points: string[];
    title: string;
  }[];
  influencer_recommendations: {
    id: string;
    name: string;
    pills_content: string[];
    social_handles: string[];
    title: string;
    video_description: string;
    video_title: string;
    video_url: string;
    designation: string;
    image_url: string;
    content: string;
  }[];
  influencer_testimonials: {
    id: string;
    name: string;
    content: string;
  }[];
  influencer_why_choose_one_assures: {
    id: string;
    title: string;
    description: string;
    influencer_why_choose_one_assure_points: {
      id: string;
      title: string;
      description: string;
    }[];
  }[];
  influencer_why_trust_us: {
    id: string;
    title: string;
    description: string;
    influencer_why_trust_us_points: {
      id: string;
      title: string;
      description: string;
    }[];
  }[];
};

export const transformInfluencerData = (data: influencerApiResponse) => {

  const hero = {
    title: data.hero_title,
    description: data.hero_description,
    imageUrl: data.hero_image_url,
  }

  const companyBackground = {
    title: data.influencer_one_assure_backgrounds[0]?.title,
    credentials: data.influencer_one_assure_backgrounds[0]?.points,
  }

  const whyChooseUs = {
    title: data.influencer_why_choose_one_assures[0]?.title,
    description: data.influencer_why_choose_one_assures[0]?.description,
    points: data.influencer_why_choose_one_assures[0]?.influencer_why_choose_one_assure_points,
  }

  const whyTrustUs = {
    title: data.influencer_why_trust_us[0]?.title,
    description: data.influencer_why_trust_us[0]?.description,
    points: data.influencer_why_trust_us[0]?.influencer_why_trust_us_points,
  }

  const InfluencerSocialProof = {
    title: data.influencer_recommendations[0]?.title || "",
    content: data.influencer_recommendations[0]?.content || "",
    name: data.influencer_recommendations[0]?.name || "",
    socialHandles: data.influencer_recommendations[0]?.social_handles || [],
    videoTitle: data.influencer_recommendations[0]?.video_title || "",
    videoDescription: data.influencer_recommendations[0]?.video_description || "",
    videoUrl: data.influencer_recommendations[0]?.video_url || "",
    designation: data.influencer_recommendations[0]?.designation || "",
    imageUrl: data.influencer_recommendations[0]?.image_url || "",
    pillsContent: data.influencer_recommendations[0]?.pills_content || [],
  };

  const influencerTestimonials = data.influencer_testimonials;

  const influencerFaqs = data.influencer_faqs;

  return {
    name: data.name,
    slug: data.slug,
    data_access_link: data.data_access_link,
    hero,
    companyBackground,
    whyChooseUs,
    whyTrustUs,
    InfluencerSocialProof,
    influencerTestimonials,
    influencerFaqs,
  };
};