import { Meta } from "@/types";

type Hero = {
  title: string;
  description: string;
  imageUrl: string;
}

type CompanyBackground = {
  title: string;
  credentials: string[];
}
type WhyChooseUs = {
  title: string;
  description: string;
  points: {
    title: string;
    description: string;
  }[];
}
type WhyTrustUs = {
  title: string;
  description: string;
  points: {
    title: string;
    description: string;
  }[];
}
type InfluencerSocialProof = {
  title: string;
  content: string;
  name: string;
  socialHandles: string[];
  videoTitle: string;
  videoDescription: string;
  videoUrl: string;
  designation: string;
  imageUrl: string;
  pillsContent: string[];
}
type InfluencerTestimonials = {
  id: string;
  name: string;
  content: string;
}[];
type InfluencerFaqs = {
  id: string;
  question: string;
  answer: string;
}[];

export type Influencer = {
  name: string;
  slug: string;
  data_access_link: string;
  hero: Hero;
  companyBackground: CompanyBackground;
  whyChooseUs: WhyChooseUs;
  whyTrustUs: WhyTrustUs;
  InfluencerSocialProof: InfluencerSocialProof;
  influencerTestimonials: InfluencerTestimonials;
  influencerFaqs: InfluencerFaqs;
}
export type InfluencerData = {
  data: Influencer[];
  meta: Meta;
};
