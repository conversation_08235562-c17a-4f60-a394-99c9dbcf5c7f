export type Influencer = {
  id: string;
  name: string;
  slug: string;
  hero_image_url: string;
  influencer_recommendations: {
    designation: string;
  }[];
}

export type InfluencersGridProps = {
  influencers: Influencer[];
}

export type InfluencerCardProps = {
  name: string;
  slug: string;
  imageUrl: string;
  designation: string;
}

export type PartnersPageProps = {
  influencers: Influencer[];
}

