"use client";
import InfluencersGrid from "./components/InfluencersGrid";
import { usePathname } from "next/navigation";
import PageTopBar from "@/components/globals/PageTopBar";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionHeader from "@/components/globals/SectionHeader";
import { PartnersPageProps } from "./types";

const PartnersPage = ({ influencers }: PartnersPageProps) => {
  const breadCrumb = ["OneAssure", "Partner"];
  const fullUrl = usePathname();

  return (
    <SectionContainerLarge className="py-6">
      <PageTopBar breadcrumbPath={breadCrumb} fullUrl={fullUrl} />
      <SectionHeader
        pill="Our Partners"
        heading="Our Partners Trust OneAssure. You Can Too."
        subheading="Discover trusted insurance recommendations from our expert partners"
        component="h1"
        className="!mb-12"
      />
      <InfluencersGrid influencers={influencers} />
    </SectionContainerLarge>
  );
};

export default PartnersPage;
