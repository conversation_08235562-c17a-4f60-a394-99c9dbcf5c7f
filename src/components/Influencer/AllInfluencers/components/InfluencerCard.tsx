import Image from "next/image";
import Link from "next/link";
import { BodyMedium, HeadingSmall } from "@/components/UI/Typography";
import { InfluencerCardProps } from "../types";

const InfluencerCard = ({ name, slug, imageUrl, designation }: InfluencerCardProps) => {
  return (
    <Link href={`/partner/${slug}-insurance-recommendation`}>
      <div className="bg-white rounded-xl border border-primary-200 shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer overflow-hidden group">
        <div className="aspect-square relative overflow-hidden">
          <Image
            src={imageUrl}
            alt={name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>
        <div className="p-6">
          <HeadingSmall 
            as="h2" 
            className="text-neutral-1100 text-center"
            weight="semibold"
          >
            {name}
          </HeadingSmall>
          <BodyMedium
            as="p" 
            className="text-secondary-500 text-center"
          >
            {designation}
          </BodyMedium>
        </div>
      </div>
    </Link>
  );
};

export default InfluencerCard;
