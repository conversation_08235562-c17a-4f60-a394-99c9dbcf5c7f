import { Grid } from "@/components/UI/Grid";
import InfluencerCard from "./InfluencerCard";
import { InfluencersGridProps } from "../types";

const InfluencersGrid = ({ influencers }: InfluencersGridProps) => {
  return (
    <Grid cols={4} gap={4} mobileCols={1}>
      {influencers.map((influencer) => (
        <InfluencerCard
          key={influencer.id}
          name={influencer.name}
          slug={influencer.slug}
          imageUrl={influencer.hero_image_url}
          designation={influencer.influencer_recommendations[0]?.designation || ""}
        />
      ))}
    </Grid>
  );
};

export default InfluencersGrid;