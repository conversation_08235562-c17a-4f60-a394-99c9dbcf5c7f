"use client";
import React from "react";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import {
  HeadingXLarge,
  HeadingSmall,
  BodyLarge,
  BodyMedium,
} from "@/components/UI/Typography";
import {
  IoCheckmarkCircle,
  IoShieldCheckmark,
  IoPeople,
  IoStar,
  IoTime,
  IoSettings,
  IoRibbon,
  IoThumbsUp,
} from "react-icons/io5";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";

type FeaturedCardData = {
  id: string | number;
  iconKey?: string;
  title: string;
  description: string;
};

const FeaturedSection: React.FC<{
  featuredPoints: string[];
}> = ({ featuredPoints }) => {
  const getIconByKey = (title: string, index: number) => {
    if (
      title.toLowerCase().includes("expert") ||
      title.toLowerCase().includes("advice")
    ) {
      return <IoStar className="w-4 h-4 text-primary-600" />;
    }
    if (
      title.toLowerCase().includes("compare") ||
      title.toLowerCase().includes("insurer")
    ) {
      return <IoRibbon className="w-4 h-4 text-primary-600" />;
    }
    if (
      title.toLowerCase().includes("cost") ||
      title.toLowerCase().includes("zero")
    ) {
      return <IoThumbsUp className="w-4 h-4 text-primary-600" />;
    }
    if (
      title.toLowerCase().includes("support") ||
      title.toLowerCase().includes("claim")
    ) {
      return <IoPeople className="w-4 h-4 text-primary-600" />;
    }
    if (
      title.toLowerCase().includes("trust") ||
      title.toLowerCase().includes("reliable")
    ) {
      return <IoShieldCheckmark className="w-4 h-4 text-primary-600" />;
    }
    if (
      title.toLowerCase().includes("service") ||
      title.toLowerCase().includes("quality")
    ) {
      return <IoSettings className="w-4 h-4 text-primary-600" />;
    }
    return <IoCheckmarkCircle className="w-4 h-4 text-primary-600" />;
  };

  // Convert featuredPoints to card format
  const cards: FeaturedCardData[] = featuredPoints.map((point, index) => ({
    id: index,
    iconKey: point.toLowerCase(),
    title: "", // Remove title/heading
    description: point, // Use the point as description
  }));

  // Featured Card Component
  const FeaturedCard = ({ card }: { card: FeaturedCardData }) => (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-primary-200 flex flex-col items-center gap-3 text-center w-full">
      <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
        {getIconByKey(card.description, card.id as number)}
      </div>
      <BodyLarge>{card.description}</BodyLarge>
    </div>
  );

  return (
    <SectionContainerLarge id="featured-points" className="!px-0">
      <HeadingXLarge as="h2" className="text-center mb-6">
        Featured Points
      </HeadingXLarge>

      {/* Desktop Grid Layout */}
      <div className="hidden md:grid grid-cols-4 gap-4">
        {cards.map((card) => (
          <FeaturedCard key={card.id} card={card} />
        ))}
      </div>

      {/* Mobile Carousel Layout */}
      <div className="md:hidden">
        <MobileCarousel totalSlides={cards.length}>
          {cards.map((card, index) => (
            <MobileCarouselItem key={card.id}>
              <FeaturedCard card={card} />
            </MobileCarouselItem>
          ))}
        </MobileCarousel>
      </div>
    </SectionContainerLarge>
  );
};

export default FeaturedSection;
