"use client";

import React from "react";
import {
  IoCallOutline,
  IoArrowForward,
  IoTimeOutline,
  IoShieldCheckmarkOutline,
  IoStarOutline,
} from "react-icons/io5";
import { Button } from "@/components/UI/Button";
import {
  BodySmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import Image from "next/image";
import PageTopBar from "@/components/globals/PageTopBar";
import SectionContainer from "@/components/globals/SectionContainer";
import parse from "html-react-parser";
import { orufyHandler } from "@/utils/orufyHandler";

type HeroWithFormProps = {
  title: string;
  description: string;
  imageUrl: string;
  breadcrumbPath: string[];
  fullUrl: string;
  data_access_link: string;
};

export default function HeroWithForm({
  title,
  description,
  imageUrl,
  breadcrumbPath,
  fullUrl,
  data_access_link,
}: HeroWithFormProps) {
  // const pathname = usePathname();

  const handleBookCall = () => {
    orufyHandler(data_access_link);
  };

  return (
    <SectionContainer className="mt-6 md:mt-8">
      <PageTopBar breadcrumbPath={breadcrumbPath} fullUrl={fullUrl} />

      <div className="flex flex-col md:flex-row md:justify-between items-stretch gap-8">
        {/* Left Column - Content and CTA */}
        <section className="w-full flex-6 flex flex-col justify-center items-center md:items-start text-left">
          {/* Status pill */}
          <div className="mb-4 flex justify-center md:justify-start">
            <div className="inline-flex items-center rounded-full border border-neutral-300 bg-white px-3 py-1">
              <span className="mr-2 inline-block h-2 w-2 rounded-full bg-green-500" />
              <BodySmall className="text-neutral-700">
                Average call time: 30 minutes
              </BodySmall>
            </div>
          </div>
          <HeadingXLarge
            as="h1"
            className="mb-2 md:mb-3 text-center md:text-left text-neutral-1100"
          >
            {title}
          </HeadingXLarge>
          <div className="mb-5 md:mb-6 text-center md:text-left text-neutral-1100">
            {parse(description)}
          </div>

          {/* CTA Button */}
          <div className="flex w-full flex-row items-center justify-center md:justify-start md:gap-3 mb-3">
            <Button
              onClick={handleBookCall}
              variant="primary"
              className="text-xs px-4 py-1.5 md:text-base md:px-4 md:py-3"
            >
              Book A Free Call
              <IoArrowForward
                size={14}
                className="ml-1 md:ml-2 md:size-[18px]"
              />
            </Button>
            {/* <Button
              variant="secondary"
              className="text-xs px-6 py-1.5 md:text-base md:px-4 md:py-3"
            >
              Why Choose Us?
            </Button> */}
          </div>

          {/* Subtle avatar group for social proof */}
          <div className="mb-2 flex items-center gap-12 md:gap-3">
            <div className="flex -space-x-2">
              <div className="h-8 w-8 rounded-full bg-primary-600 text-neutral-000 text-xs flex items-center justify-center border-2 border-white">
                AK
              </div>
              <div className="h-8 w-8 rounded-full bg-neutral-900 text-neutral-000 text-xs flex items-center justify-center border-2 border-white">
                SJ
              </div>
              <div className="h-8 w-8 rounded-full bg-primary-800 text-neutral-000 text-xs flex items-center justify-center border-2 border-white">
                RS
              </div>
              <div className="h-8 w-8 rounded-full bg-neutral-700 text-neutral-000 text-xs flex items-center justify-center border-2 border-white">
                MN
              </div>
            </div>
            <BodySmall className="text-neutral-700">
              Joined by 10,000+ policyholders
            </BodySmall>
          </div>

          {/* Trust row */}
          <div className="flex flex-wrap items-center gap-x-6 gap-y-2 text-neutral-700">
            <div className="flex items-center gap-2">
              <IoShieldCheckmarkOutline className="text-neutral-900" />
              <BodySmall>No spam, only unbiased advice</BodySmall>
            </div>
            <div className="flex items-center gap-2">
              <IoStarOutline className="text-neutral-900" />
              <BodySmall>4.9/5 rating</BodySmall>
            </div>
            <div className="flex items-center gap-2">
              <IoTimeOutline className="text-neutral-900" />
              <BodySmall>30-min expert review</BodySmall>
            </div>
          </div>
        </section>

        {/* Right Column - Image */}
        <section className="relative w-full h-56 md:h-[28rem] shadow-lg flex-4">
            <Image
              src={imageUrl}
              alt={title}
              fill
              className="object-cover rounded-xl"
              sizes="(max-width: 1024px) 100vw, 55vw"
            />
            {/* Floating assurance chip on image */}
            <div className="absolute bottom-3 left-3 rounded-full border border-neutral-200 bg-white/90 backdrop-blur-sm px-3 py-2 shadow-md flex items-center gap-2">
                <IoShieldCheckmarkOutline className="text-primary-700" />
                <BodySmall className="font-medium text-neutral-900">
                  Free 30‑min review • No pressure
                </BodySmall>
            </div>
            <div className="absolute top-3 right-3 rounded-full bg-white/90 backdrop-blur-sm px-3 py-1 border border-neutral-200 shadow-sm">
              <BodySmall className="font-medium text-neutral-900">
                10k+ helped
              </BodySmall>
            </div>
        </section>
      </div>
    </SectionContainer>
  );
}
