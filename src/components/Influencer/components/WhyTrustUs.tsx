"use client";
import React from "react";
import {
  HeadingXLarge,
} from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import {
  IoCheckmarkCircle,
  IoShieldCheckmark,
  IoPeople,
  IoStar,
  IoTime,
  IoSettings,
  IoRibbon,
  IoThumbsUp,
} from "react-icons/io5";
import parse from "html-react-parser";

type WhyTrustUsProps = {
  whyTrustUs: {
    title: string;
    description: string;
    points: Array<{
      title: string;
      description: string;
    }>;
  };
}

const WhyTrustUs: React.FC<WhyTrustUsProps> = ({
  whyTrustUs
}) => {
  const getIcon = (title: string, index: number) => {
    if (
      title.toLowerCase().includes("expert") ||
      title.toLowerCase().includes("advice")
    ) {
      return <IoStar className="w-8 h-8 text-primary-600" />;
    }
    if (
      title.toLowerCase().includes("compare") ||
      title.toLowerCase().includes("insurer")
    ) {
      return <IoRibbon className="w-8 h-8 text-primary-600" />;
    }
    if (
      title.toLowerCase().includes("irdai") ||
      title.toLowerCase().includes("licensed")
    ) {
      return <IoThumbsUp className="w-8 h-8 text-primary-600" />;
    }
    if (
      title.toLowerCase().includes("support") ||
      title.toLowerCase().includes("claim")
    ) {
      return <IoPeople className="w-8 h-8 text-primary-600" />;
    }
    if (
      title.toLowerCase().includes("trust") ||
      title.toLowerCase().includes("reliable")
    ) {
      return <IoShieldCheckmark className="w-8 h-8 text-primary-600" />;
    }
    if (
      title.toLowerCase().includes("service") ||
      title.toLowerCase().includes("quality")
    ) {
      return <IoSettings className="w-8 h-8 text-primary-600" />;
    }
    return <IoCheckmarkCircle className="w-8 h-8 text-primary-600" />;
  };

  // Card Component
  const WhyTrustUsCard = ({ point, index }: { point: any; index: number }) => (
    <div className="p-6 bg-white rounded-xl border border-neutral-200 shadow-sm hover:shadow-md hover:border-primary-200 transition-all duration-300 h-full">
      {/* Icon */}
      <div className="flex justify-center">
        <div className="w-14 h-14 bg-primary-100 rounded-full flex items-center justify-center mb-4 mx-auto md:mx-0">
          {getIcon(point.title, index)}
        </div>
      </div>

      {/* Content */}
      <div className="text-center">
        {/* <HeadingSmall className="mb-3 text-neutral-1100">{point.title}</HeadingSmall> */}
        <div className="text-neutral-800">{point.title}</div>
      </div>
    </div>
  );

  return (
    <SectionContainerLarge className="!px-0">
      {/* <div className="text-center mb-6"> */}
        <HeadingXLarge className=" text-neutral-1100 text-center mb-6">
          {whyTrustUs.title}
        </HeadingXLarge>
      {/* </div> */}

      <div className="border-2 border-primary-200 rounded-xl px-4 py-3 md:py-4 md:px-6 bg-white mx-6 md:mx-0 mb-4 md:p-2 text-center text-neutral-1100">
          {parse(whyTrustUs.description)}
      </div>

      <div className="mx-auto">
        {/* Desktop Grid Layout */}
        <div className="hidden md:grid grid-cols-2 md:grid-cols-4 gap-6">
          {whyTrustUs.points.map((point, index) => (
            <WhyTrustUsCard key={index} point={point} index={index} />
          ))}
        </div>

        {/* Mobile Carousel Layout */}
        <div className="md:hidden">
          <MobileCarousel totalSlides={whyTrustUs.points.length}>
            {whyTrustUs.points.map((point, index) => (
              <MobileCarouselItem key={index}>
                <WhyTrustUsCard point={point} index={index} />
              </MobileCarouselItem>
            ))}
          </MobileCarousel>
        </div>
      </div>
    </SectionContainerLarge>
  );
};

export default WhyTrustUs;
