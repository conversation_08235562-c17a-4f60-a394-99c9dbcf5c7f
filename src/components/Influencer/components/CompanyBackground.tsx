"use client";
import React from "react";
import {
  HeadingXLarge,
  BodyLarge,
  BodyMedium,
} from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import {
  IoCheckmarkCircle,
  IoShieldCheckmark,
  IoPeople,
  IoStar,
  IoTime,
  IoSettings,
} from "react-icons/io5";

type CompanyBackgroundProps = {
  companyBackground: {
    title: string;
    credentials: string[];
  };
};

const CompanyBackground: React.FC<CompanyBackgroundProps> = ({
  companyBackground,
}) => {
  const getIcon = (credential: string) => {
    if (
      credential.toLowerCase().includes("irdai") ||
      credential.toLowerCase().includes("licensed")
    ) {
      return <IoShieldCheckmark className="w-5 h-5 text-primary-600" />;
    }
    if (
      credential.toLowerCase().includes("years") ||
      credential.toLowerCase().includes("industry")
    ) {
      return <IoTime className="w-5 h-5 text-primary-600" />;
    }
    if (
      credential.toLowerCase().includes("customers") ||
      credential.toLowerCase().includes("happy")
    ) {
      return <IoPeople className="w-5 h-5 text-primary-600" />;
    }
    if (
      credential.toLowerCase().includes("partners") ||
      credential.toLowerCase().includes("insurance")
    ) {
      return <IoStar className="w-5 h-5 text-primary-600" />;
    }
    if (
      credential.toLowerCase().includes("support") ||
      credential.toLowerCase().includes("24/7")
    ) {
      return <IoSettings className="w-5 h-5 text-primary-600" />;
    }
    if (
      credential.toLowerCase().includes("award") ||
      credential.toLowerCase().includes("service")
    ) {
      return <IoCheckmarkCircle className="w-5 h-5 text-primary-600" />;
    }
    return <IoCheckmarkCircle className="w-5 h-5 text-primary-600" />;
  };

  // Group credentials into pairs for 3 columns
  const groupedCredentials = [];
  for (let i = 0; i < companyBackground.credentials.length; i += 2) {
    groupedCredentials.push(companyBackground.credentials.slice(i, i + 2));
  }

  return (
    <SectionContainerLarge className="!px-0">
      <HeadingXLarge className="text-neutral-1100 text-center mb-6">
        {companyBackground.title}
      </HeadingXLarge>

      <div className="mx-auto">
        <div className="hidden md:grid grid-cols-2 md:grid-cols-3 gap-8">
          {groupedCredentials.map((group, groupIndex) => (
            <div key={groupIndex} className="space-y-4 relative">
              {group.map((credential, index) => (
                <div
                  key={`${groupIndex}-${index}`}
                  className="flex items-start gap-3 p-3"
                >
                  <div className="flex-shrink-0 mt-1">
                    {getIcon(credential)}
                  </div>
                  <BodyLarge  className="text-neutral-900">
                    {credential}
                  </BodyLarge>
                </div>
              ))}
              {groupIndex < groupedCredentials.length - 1 && (
                <div className="hidden md:block absolute right-0 top-0 bottom-0 w-px bg-primary-200"></div>
              )}
            </div>
          ))}
        </div>

        {/* Mobile-only layout - Scrollable Cards */}
        <div className="md:hidden">
          <MobileCarousel totalSlides={companyBackground.credentials.length}>
            {companyBackground.credentials.map((credential, index) => (
              <MobileCarouselItem
                key={index}
                className="bg-white rounded-xl p-6 shadow-sm border border-primary-200 flex-col items-center gap-3 text-center"
              >
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                  {getIcon(credential)}
                </div>
                <BodyLarge  className="text-neutral-900">
                  {credential}
                </BodyLarge>
              </MobileCarouselItem>
            ))}
          </MobileCarousel>
        </div>
      </div>
    </SectionContainerLarge>
  );
};

export default CompanyBackground;
