"use client";
import React from "react";
import { Button } from "@/components/UI/Button";
import {
  HeadingXLarge,
  BodyLarge,
  BodyMedium,
} from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { IoArrowForward, IoCheckmarkCircle } from "react-icons/io5";
import { orufyHandler } from "@/utils/orufyHandler";

type FinalCTAProps = {
  finalCTA: {
    title: string;
    description: string;
    buttonText: string;
    data_access_link: string;
  };
  influencerName?: string;
};

const FinalCTA: React.FC<FinalCTAProps> = ({ finalCTA, influencerName }) => {

  const handleBookCall = () => {
    orufyHandler(finalCTA.data_access_link);
  };

  return (
    <SectionContainerLarge>
      {/* Simple Card Design */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 rounded-xl p-8 md:p-12 shadow-xl grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
        {/* Left side - Heading */}
        <HeadingXLarge className="text-neutral-000">
          {influencerName
            ? `Join ${influencerName} and thousands of families`
            : "Join thousands of families"}{" "}
          who trust OneAssure
        </HeadingXLarge>

        {/* Right side - Benefits and CTA */}
        <div className="space-y-6">
          {/* Benefits list */}
          <div className="space-y-3">
            <div className="flex items-center gap-3 text-neutral-000">
              <IoCheckmarkCircle className="text-neutral-000 w-5 h-5" />
              <BodyMedium>Get instant insurance quotes</BodyMedium>
            </div>
            <div className="flex items-center gap-3 text-neutral-000">
              <IoCheckmarkCircle className="text-neutral-000 w-5 h-5" />
              <BodyMedium>Up to 50% savings on premiums</BodyMedium>
            </div>
            <div className="flex items-center gap-3 text-neutral-000">
              <IoCheckmarkCircle className="text-neutral-000 w-5 h-5" />
              <BodyMedium>24/7 expert support</BodyMedium>
            </div>
          </div>

          {/* CTA Button */}
          <Button
            className="bg-white text-primary-600 hover:bg-primary-50 px-10 py-4 text-lg font-bold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-2"
            onClick={handleBookCall}
          >
            Book Now
            <IoArrowForward className="w-5 h-5" />
          </Button>
        </div>
      </div>
    </SectionContainerLarge>
  );
};

export default FinalCTA;
