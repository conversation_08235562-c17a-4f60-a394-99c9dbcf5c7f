"use client";
import React from "react";
import { Button } from "@/components/UI/Button";
import {
  HeadingXLarge,
  BodyLarge,
  BodyMedium,
  BodySmall,
} from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import Image from "next/image";
import { IoArrowForward } from "react-icons/io5";
import { orufyHandler } from "@/utils/orufyHandler";

const ConsultationSection= ({data_access_link}:{data_access_link:string}) => {


  const handleBookCall = () => {
    orufyHandler(data_access_link);
  };


  return (
    <SectionContainerLarge className="bg-gradient-to-br from-primary-50 to-secondary-100 rounded-xl overflow-hidden">
      {/* Mobile Layout */}
      <div className="block md:hidden pt-6">
        {/* Image Section */}
        <div className="relative h-64 w-full rounded-xl overflow-hidden">
          <Image
            src="https://images.unsplash.com/photo-1511895426328-dc8714191300?w=800&h=600&fit=crop&crop=center"
            alt="Happy family celebrating successful insurance claims settlement"
            fill
            className="object-cover"
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
          <div className="absolute bottom-4 left-4 right-4">
            <HeadingXLarge className="text-neutral-000 mb-2 text-center">
              Unlock Your Free Consultation Now
            </HeadingXLarge>
          </div>

          {/* Claims Success Overlay */}
          <div className="absolute top-4 right-4 bg-secondary-600 text-neutral-000 px-3 py-1 rounded-full">
            <BodySmall className="font-semibold">✓ Claim Settled</BodySmall>
          </div>
          <div className="absolute top-4 left-4 bg-primary-600 text-neutral-000 px-3 py-1 rounded-full">
            <BodySmall className="font-semibold">98% Success Rate</BodySmall>
          </div>
        </div>

        {/* Content Section */}
        <div className="p-6">
          <div className="space-y-3 mb-6">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-primary-600 rounded-full mt-0.5 flex-shrink-0"></div>
              <BodySmall className="text-neutral-700">
                <span className="font-semibold text-primary-700">98%</span>{" "}
                claim settlement ratio - highest in industry
              </BodySmall>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-primary-600 rounded-full mt-0.5 flex-shrink-0"></div>
              <BodySmall className="text-neutral-700">
                <span className="font-semibold text-primary-700">24/7</span>{" "}
                claims support and assistance
              </BodySmall>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-primary-600 rounded-full mt-0.5 flex-shrink-0"></div>
              <BodySmall className="text-neutral-700">
                <span className="font-semibold text-primary-700">100%</span>{" "}
                claims processed within 7 days
              </BodySmall>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-primary-600 rounded-full mt-0.5 flex-shrink-0"></div>
              <BodySmall className="text-neutral-700">
                <span className="font-semibold text-primary-700">500,000+</span>{" "}
                successful claims settled
              </BodySmall>
            </div>
          </div>

          <Button
            onClick={handleBookCall}
            variant="primary"
            className="w-full py-3 text-base font-semibold rounded-xl shadow-lg"
          >
            Book Now
            <IoArrowForward size={18} className="ml-2" />
          </Button>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden md:flex flex-row items-center gap-12 p-8">
        {/* Left Content */}
        <div className="flex-1 text-left">
          <HeadingXLarge className="mb-4 text-neutral-900">
            Unlock Your Free Consultation Now
          </HeadingXLarge>

          <div className="space-y-4 mb-6">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
              <BodyMedium className="text-neutral-700">
                <span className="font-semibold text-primary-700">98%</span>{" "}
                claim settlement ratio - highest in industry
              </BodyMedium>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
              <BodyMedium className="text-neutral-700">
                <span className="font-semibold text-primary-700">24/7</span>{" "}
                claims support and assistance
              </BodyMedium>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
              <BodyMedium className="text-neutral-700">
                <span className="font-semibold text-primary-700">100%</span>{" "}
                claims processed within 7 days
              </BodyMedium>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
              <BodyMedium className="text-neutral-700">
                <span className="font-semibold text-primary-700">500,000+</span>{" "}
                successful claims settled
              </BodyMedium>
            </div>
          </div>

          <Button
            onClick={handleBookCall}
            variant="primary"
            className="px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
          >
            Book Now
            <IoArrowForward size={20} className="ml-2" />
          </Button>
        </div>

        {/* Right Image */}
        <div className="flex-1 relative w-full h-96 rounded-xl overflow-hidden shadow-xl">
            <Image
              src="https://images.unsplash.com/photo-1511895426328-dc8714191300?w=800&h=600&fit=crop&crop=center"
              alt="Happy family celebrating successful insurance claims settlement"
              fill
              className="object-cover"
              sizes="(max-width: 1024px) 100vw, 50vw"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>

            {/* Claims Success Overlay */}
            <div className="absolute top-4 right-4 bg-secondary-600 text-neutral-000 px-3 py-1 rounded-full">
              <BodySmall className="font-semibold">✓ Claim Settled</BodySmall>
            </div>
            <div className="absolute top-4 left-4 bg-primary-600 text-neutral-000 px-3 py-1 rounded-full">
              <BodySmall className="font-semibold">98% Success Rate</BodySmall>
            </div>
          </div>
      </div>
    </SectionContainerLarge>
  );
};

export default ConsultationSection;
