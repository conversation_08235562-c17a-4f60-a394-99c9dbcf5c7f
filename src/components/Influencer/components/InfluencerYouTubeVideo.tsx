"use client";

import React from "react";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import {
  HeadingXLarge,
  BodyLarge,
  BodyMedium,
} from "@/components/UI/Typography";
import parse from "html-react-parser";
const YouTube = require("react-youtube").default;
import SectionContainerSmall from "@/components/globals/SectionContainerSmall";

const InfluencerYouTubeVideo: React.FC<{
  influencerVideo: {
    title: string;
    content: string;
    name: string;
    socialHandles: string[];
    videoTitle: string;
    videoDescription: string;
    videoUrl: string;
    designation: string;
    imageUrl: string;
    pillsContent: string[];
  };
}> = ({ influencerVideo }) => {
  // Default video URL - you can replace this with any YouTube video
  //   const DEFAULT_VIDEO_URL = "https://www.youtube.com/watch?v=Ptl7zES-jj4";
  const DEFAULT_VIDEO_URL = "https://www.youtube.com/watch?v=VVLB9IKg0Yo";

  const isValidUrl = (url?: string) => {
    if (!url) return false;
    if (url.includes("example.com")) return false;
    if (url.includes("placeholder")) return false;
    return true;
  };

  const videoUrl = isValidUrl(influencerVideo.videoUrl)
    ? influencerVideo.videoUrl || DEFAULT_VIDEO_URL
    : DEFAULT_VIDEO_URL;

  const getVideoId = (url: string) => {
    if (url.includes("youtu.be")) {
      return url.split("/").pop()?.split("?")[0];
    } else if (url.includes("youtube.com")) {
      return url.split("v=")[1]?.split("&")[0];
    }
    return null;
  };

  const videoId = getVideoId(videoUrl);

  return (
    <SectionContainerLarge>
      <div className="text-center mb-8">
        <HeadingXLarge className="mb-4 text-neutral-1100">{influencerVideo.videoTitle}</HeadingXLarge>
        <SectionContainerSmall className="mx-auto !mb-0">
          {parse(influencerVideo.videoDescription)}
        </SectionContainerSmall>
      </div>

      <SectionContainerSmall className="mx-auto relative rounded-xl overflow-hidden">
            <YouTube
              videoId={videoId}
              opts={{
                width: "100%",
                height: "500",
                playerVars: {
                  autoplay: 0,
                },
              }}
              className="rounded-xl w-full h-auto hidden md:block overflow-hidden"
            />
            <YouTube
              videoId={videoId}
              opts={{
                width: "100%",
                height: "300",
                playerVars: {
                  autoplay: 0,
                },
              }}
              className="rounded-xl w-full h-auto block md:hidden overflow-hidden"
            />
      </SectionContainerSmall>
    </SectionContainerLarge>
  );
};

export default InfluencerYouTubeVideo;
