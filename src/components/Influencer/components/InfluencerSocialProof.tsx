"use client";
import React from "react";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import {
  HeadingXLarge,
  HeadingMedium,
  BodyLarge,
  BodyMedium,
  BodySmall,
} from "@/components/UI/Typography";
import { IoCheckmarkCircle } from "react-icons/io5";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaInstagram, FaLinkedin, FaTwitter } from "react-icons/fa";
import parse from "html-react-parser";
import { FaFacebook, FaLink, FaXTwitter, FaYoutube } from "react-icons/fa6";
import Link from "next/link";

type InfluencerSocialProofProps = {
  influencerSocialProof: {
    title: string;
    content: string;
    name: string;
    socialHandles: string[];
    videoTitle: string;
    videoDescription: string;
    videoUrl: string;
    designation: string;
    imageUrl: string;
    pillsContent: string[];
  };
}

const getSocialIcon = (handle: string) => {
  if (handle.toLowerCase().includes("instagram")) {
    return <FaInstagram className="text-primary-500 w-5 h-5 hover:text-primary-600 cursor-pointer transition-colors" />;
  }
  if (handle.toLowerCase().includes("linkedin")) {
    return <FaLinkedin className="text-primary-500 w-5 h-5 hover:text-primary-600 cursor-pointer transition-colors" />;
  }
  if (handle.toLocaleLowerCase().includes("twitter")) {
    return <FaTwitter className="text-primary-500 w-5 h-5 hover:text-primary-600 cursor-pointer transition-colors" />;
  }
  if(handle.toLowerCase().includes("x.com")){
    return <FaXTwitter className="text-primary-500 w-5 h-5 hover:text-primary-600 cursor-pointer transition-colors" />;
  }
  if(handle.toLowerCase().includes("facebook")){
    return <FaFacebook className="text-primary-500 w-5 h-5 hover:text-primary-600 cursor-pointer transition-colors" />;
  }
  if(handle.toLowerCase().includes("youtube")){
    return <FaYoutube className="text-primary-500 w-5 h-5 hover:text-primary-600 cursor-pointer transition-colors" />;
  }
  if(handle.toLowerCase().includes("youtu.be")){
    return <FaYoutube className="text-primary-500 w-5 h-5 hover:text-primary-600 cursor-pointer transition-colors" />;
  }
  return <FaLink className="text-primary-500 w-5 h-5 hover:text-primary-600 cursor-pointer transition-colors" />;
}

const InfluencerSocialProof: React.FC<InfluencerSocialProofProps> = ({
  influencerSocialProof
}) => {
  return (
    <SectionContainerLarge>
      <HeadingXLarge className="text-center mb-8 text-neutral-1100">
        {influencerSocialProof.title}
      </HeadingXLarge>

      <div className="bg-secondary-100 rounded-xl p-8 border border-secondary-300 shadow-lg hover:shadow-xl transition-shadow duration-300">
        {/* Mobile View */}
        <div className="md:hidden">
          {/* Profile Image and Name - Centered at top */}
          <div className="flex flex-col items-center space-y-4 mb-6">
            {/* Profile Image - Circular with enhanced styling */}
            <div className="relative">
              <img
                src={influencerSocialProof.imageUrl}
                alt={influencerSocialProof.name}
                className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-xl ring-2 ring-secondary-100"
                onError={(e) => {
                  e.currentTarget.style.display = "none";
                  const nextElement = e.currentTarget
                    .nextElementSibling as HTMLElement;
                  nextElement && (nextElement.style.display = "flex");
                }}
              />
              <div className="absolute -bottom-1 -right-1 w-7 h-7 bg-primary-600 rounded-full flex items-center justify-center border-3 border-white shadow-lg">
                <FaCheck className="text-neutral-000 w-3 h-3 font-bold" />
              </div>
            </div>

            {/* Name with enhanced styling */}
            <div className="text-center">
              <HeadingMedium className="text-primary-900 font-bold mb-1">
                {influencerSocialProof.name}
              </HeadingMedium>
              <BodySmall className="text-primary-600 mb-3">
                {influencerSocialProof.designation}
              </BodySmall>

              {/* Social Media Icons */}
              <div className="flex justify-center gap-3">
                {influencerSocialProof.socialHandles.map((handle, index) => (
                  <Link
                    key={index}
                    href={handle}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {getSocialIcon(handle)}
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/* Description - Full width */}
          <div className="w-full">
            <div className="text-secondary-700 italic text-base leading-relaxed">
              {parse(influencerSocialProof.content)}
            </div>

            {/* Additional trust indicators */}
            <div className="mt-6 flex flex-col items-start gap-3">
              {influencerSocialProof.pillsContent.map((pill, index) => (
                <div
                  key={index}
                  className="flex items-center gap-2 text-slate-600"
                >
                  <div className="w-2 h-2 bg-secondary-500 rounded-full"></div>
                  <BodySmall>{pill}</BodySmall>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Desktop View */}
        <div className="hidden md:flex gap-10 items-start">
          {/* Left Side - Three stacked elements */}
          <div className="w-1/4 flex flex-col items-center space-y-6">
            {/* Profile Image - Circular with enhanced styling */}
            <div className="relative">
              <img
                src={influencerSocialProof.imageUrl}
                alt={influencerSocialProof.name}
                className="w-28 h-28 rounded-full object-cover border-4 border-white shadow-xl ring-2 ring-secondary-100"
                onError={(e) => {
                  e.currentTarget.style.display = "none";
                  const nextElement = e.currentTarget
                    .nextElementSibling as HTMLElement;
                  nextElement && (nextElement.style.display = "flex");
                }}
              />
              <div className="absolute -bottom-1 -right-1 w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center border-3 border-white shadow-lg">
                <FaCheck className="text-neutral-000 w-3 h-3 font-bold" />
              </div>
            </div>

            {/* Name with enhanced styling */}
            <div className="text-center">
              <HeadingMedium className="text-primary-900 font-bold mb-1">
                {influencerSocialProof.name}
              </HeadingMedium>
              <BodySmall className="text-primary-600 mb-3">
                {influencerSocialProof.designation}
              </BodySmall>

              {/* Social Media Icons */}
              <div className="flex justify-center gap-3">
                {influencerSocialProof.socialHandles.map((handle, index) => (
                  <Link
                    key={index}
                    href={handle}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {getSocialIcon(handle)}
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/* Right Side - Description with enhanced styling */}
          <div className="w-3/4">
            <div className="text-secondary-700 italic text-base leading-relaxed">
              {parse(influencerSocialProof.content)}
            </div>

            {/* Additional trust indicators */}
            <div className="mt-6 flex items-center gap-4">
              {influencerSocialProof.pillsContent.map((pill, index) => (
                <div
                  key={index}
                  className="bg-white text-slate-700 px-4 py-1 rounded-full border border-slate-200 flex items-center gap-2"
                >
                  <div className="w-2 h-2 bg-secondary-500 rounded-full"></div>
                  <BodySmall className="font-medium">{pill}</BodySmall>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </SectionContainerLarge>
  );
};

export default InfluencerSocialProof;
