import { Instrument_Sans, Manrope, Pop<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Footer from "@/components/globals/Footer";
import Navbar from "@/components/globals/Navbar_New";
import ReactQueryProvider from "@/providers/ReactQueryProvider";
import { GoogleTagManager } from "@next/third-parties/google";
import { ToastContainer } from "react-toastify";
import localFont from "next/font/local";
import FakeIntersection from "@/components/globals/FakeIntersection";
import { Suspense } from "react";

const instrumentSans = Instrument_Sans({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});
const manrope = Manrope({
  subsets: ["latin"],
  weight: ["400", "500", "600"],
  variable: "--font-manrope",
});
const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600"],
  variable: "--font-poppins",
});

const generalSans = localFont({
  src: [
    {
      path: "./GeneralSans-Bold.woff2",
      weight: "700",
      style: "normal",
    },
    {
      path: "./GeneralSans-Semibold.woff2",
      weight: "600",
      style: "normal",
    },
    {
      path: "./GeneralSans-Medium.woff2",
      weight: "500",
      style: "normal",
    },
    {
      path: "./GeneralSans-Regular.woff2",
      weight: "400",
      style: "normal",
    },
  ],
  variable: "--font-generalSans",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID!} />
      <body
        className={`${instrumentSans.className} ${manrope.variable} ${poppins.variable} ${generalSans.variable}`}
      >
        <FakeIntersection />
        <Suspense>
          <Navbar />
        </Suspense>
        <ReactQueryProvider>
          <main>{children}</main>
          <ToastContainer
            position="bottom-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="colored"
          />
        </ReactQueryProvider>
        <Suspense>
          <Footer />
        </Suspense>
      </body>
    </html>
  );
}