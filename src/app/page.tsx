import Home from "@/components/Home";
import type { Metadata } from "next";
import NewHomePage from "@/components/NewHomePage";
import { fetchStudioCmsData } from "./api/studio-cms/apis/fetchDataCMS";
import transformData from "@/components/NewHomePage/dto/dto";

export const metadata: Metadata = {
  title: "Compare & Buy Health & Term Insurance Online - OneAssure",
  description:
    "Compare, buy, and renew health and term insurance policies for yourself and your family with OneAssure. Get instant insurance quotes from various insurers in India and specially curated insurance plans for yourself",
  openGraph: {
    title: "Oneassure",
    description:
      "Compare, buy, and renew health and term insurance policies for yourself and your family with OneAssure. Get instant insurance quotes from various insurers in India and specially curated insurance plans for yourself",
    type: "website",
    url: "https://www.oneassure.in",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/",
  },
};

async function getHomePageData() {
  const query = `
    query MyQuery {
      site_home_page {
        id
        hero_title
        hero_tagline
        hero_subtitle
        home_page_why_choose_us {
          id
          pill_content
          title
          home_page_why_choose_us_cards {
            id
            title
            icon_url
            description
          }
        }
        home_page_how_oneassure_works {
          id
          pill_content
          title
          home_page_how_oneassure_works_cards {
            id
            title
            description
            icon_url
          }
        }
        home_page_ai_chat_section {
          id
          pill_content
          title
          description
          home_page_ai_chat_cards {
            id
            subtitle
            title
            home_page_ai_card_chats {
              id
              type
              content
            }
          }
        }
        home_page_meet_the_team {
          id
          title
          image_url
          description
        }
        home_page_testimonial_section {
          id
          pill_content
          title
          home_page_testimonial_story {
            id
            title
            points
          }
          home_page_testimonials {
            id
            name
            content
            video_url
          }
        }
      }
      health_insurers(where:{temp_slug:{_is_null: false}}){
        id
        logo_url
        temp_slug
        name
        products {
          product_variants(where: {temp_slug: { _is_null: false }}){
            id
            temp_slug
            variant_name
          }
        }
      }
      term_insurers(where:{temp_slug:{_is_null: false}}){
        id
        logo_url
        temp_slug
        name
        products {
          product_variants(where: {temp_slug: { _is_null: false }}){
            id
            variant_name
            temp_slug
          }
        }
      }
    }
`;

  const operationName = "MyQuery";
  const variables = {};
  const response = await fetchStudioCmsData(query, operationName, variables);

  return response.payload.data;
}

export default async function HomePage() {
  const data = await getHomePageData();
  const transformedData = transformData(data);
  
  return (
    <>
       <NewHomePage data={transformedData}/>
    </>
  );
}
