import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  const isTrueSet = process.env.NEXT_PUBLIC_GENERATE_ROBOTS === "true";

  if (isTrueSet) {
    return {
      rules: [
        {
          userAgent: "*",
          // allow: "/",
          disallow: [
            "*/_files/*",
            "*/_next/*",
            "*/tag/*",
            "*trk=public_post-text*"
          ],
        },
      ],
      sitemap: "https://www.oneassure.in/sitemap.xml",
    };
  }

  // Block all crawlers for / if NEXT_PUBLIC_GENERATE_ROBOTS is not TRUE
  return {
    rules: [
      {
        userAgent: "*",
        disallow: "/",
      },
    ],
  };
}