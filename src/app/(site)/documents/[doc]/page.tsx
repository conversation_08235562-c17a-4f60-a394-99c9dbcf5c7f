import Document from "@/components/Document";

export const dynamicParams = false;

export async function generateStaticParams() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/oa-docs?fields[0]=slug&pagination[pageSize]=100&pagination[page]=1`,
    { headers }
  ).then((res) => res.json());

  const generatedPaths = data.data.map(
    (doc: {
      attributes: {
        slug: string;
      };
    }) => ({
      doc: doc.attributes.slug,
    })
  );

  return generatedPaths;
}

const getDoc = async (slug: string) => {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  const url = `${process.env.STRAPI_BASEURL}/api/oa-docs?filters[slug][$eq]=${slug}&fields[0]=title&fields[1]=content&fields[2]=slug&fields[3]=showDoc&populate[docFile][fields][0]=title&populate[docFile][populate][file][fields][0]=url`;

  const res = await fetch(url, { headers });
  const data = await res.json();

  return data;
};

interface DocsPageProps {
  params: { doc: string };
}

export default async function Docs({ params }: DocsPageProps) {
  const data = await getDoc(params.doc);
  const desc = data.data[0].attributes;

  return <Document desc={desc} path={["home", `documents/${params.doc}`]} />;
}
