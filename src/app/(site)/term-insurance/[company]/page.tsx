import { Metadata } from "next";

import CompanyRoot from "@/components/Company";
import { ratingCalculator } from "@/components/Company/utility";
import getCompanyData from "@/components/Company/actions/getCompanyData";
import { CompanyData, StatisticsProps } from "@/components/Company/types";

export const dynamicParams = false;

export async function generateStaticParams() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=term-insurance&fields[0]=slug&pagination[pageSize]=100&pagination[page]=1`,
    { headers }
  ).then((res) => res.json());

  const generatedPaths = data.data.map(
    (comp: {
      attributes: {
        slug: string;
      };
    }) => ({
      company: comp.attributes.slug,
    })
  );

  //console.log("term generated comp path ", generatedPaths);
  return generatedPaths;
}

export async function generateMetadata({
  params,
}: {
  params: { company: string };
}): Promise<Metadata> {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[slug][$eq]=${params.company}&populate[seo][fields][0]=metaTitle&populate[seo][fields][1]=metaDescription&populate[seo][fields][2]=keyword`,
    {
      headers,
    }
  ).then((res) => res.json());

  let companyData = data.data[0];

  return {
    title: companyData.attributes.seo.metaTitle,
    description: companyData.attributes.seo.metaDescription,
    keywords: companyData.attributes.seo.keyword,
    openGraph: {
      title: companyData.attributes.seo.metaTitle,
      description: companyData.attributes.seo.metaDescription,
      type: "website",
    },
    metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
    alternates: {
      canonical: `/term-insurance/${params.company}`,
    },
  };
}

async function getCompanySlugsData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=term-insurance&fields[0]=name&fields[1]=slug&fields[2]=category&[populate][logo][fields][0]=url&populate[ratings][fields][0]=solvency&populate[ratings][fields][1]=aum&populate[ratings][fields][2]=growth`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }

  return res;
}

export default async function TermInsuranceCompanyPage({
  params,
}: {
  params: { company: string };
}) {
  const data = await getCompanyData<CompanyData>(params.company);
  const allComp = await getCompanySlugsData();

  // Solvency
  let solvency = allComp.data.map(
    (item: any) => item.attributes.ratings && item.attributes.ratings.solvency
  );
  solvency = [Math.min(...solvency), Math.max(...solvency)];

  // AUM
  let aum = allComp.data.map(
    (item: any) => item.attributes.ratings && item.attributes.ratings.aum
  );
  aum = [Math.min(...aum), Math.max(...aum)];

  // Growth
  let growth = allComp.data.map(
    (item: any) => item.attributes.ratings && item.attributes.ratings.growth
  );
  growth = [Math.min(...growth), Math.max(...growth)];

  const rating = [
    {
      title: "Solvency",
      rating: ratingCalculator({
        parameter: "SOLVENCY",
        minRating: 2,
        maxRating: 4,
        paramCurrentValue: allComp.data[0].attributes.ratings.solvency!,
        paramMaxValue: solvency[1],
        paramMinValue: solvency[0],
      }),
      outOf: 4,
    },
    {
      title: "AUM",
      rating: ratingCalculator({
        parameter: "AUM",
        minRating: 1.5,
        maxRating: 3,
        paramCurrentValue: allComp.data[0].attributes.ratings.aum!,
        paramMaxValue: aum[1],
        paramMinValue: aum[0],
      }),
      outOf: 3,
    },
    {
      title: "Growth",
      rating: ratingCalculator({
        parameter: "GROWTH",
        minRating: 1.5,
        maxRating: 3,
        paramCurrentValue: allComp.data[0].attributes.ratings.growth!,
        paramMaxValue: growth[1],
        paramMinValue: growth[0],
      }),
      outOf: 3,
    },
  ];
  const statisticsData: StatisticsProps = {
    companyName: data.data[0].attributes.name,
    graphs: [
      {
        title: "Premium Underwritten",
        industry:
          data.data[0].attributes.statistics.premiumUnderwritten!.industry,
        company:
          data.data[0].attributes.statistics.premiumUnderwritten!.company,
        suffix: "",
        description:
          data.data[0].attributes.statistics.premiumUnderwritten?.description,
      },
      {
        title: "Solvency Ratio",
        industry: data.data[0].attributes.statistics.solvencyRatio!.industry,
        company: data.data[0].attributes.statistics.solvencyRatio!.company,
        suffix: "",
        description:
          data.data[0].attributes.statistics.solvencyRatio?.description,
      },
    ],
    rating: rating,
  };
  return (
    <CompanyRoot
      company={data.data[0]}
      category={"term"}
      allComp={allComp.data}
      statistics={statisticsData}
    />
  );
}
