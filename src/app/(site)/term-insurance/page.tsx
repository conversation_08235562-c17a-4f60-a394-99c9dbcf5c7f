import LifeInsurance from "@/components/LifeInsurance";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Best term life Insurance Plan in India | OneAssure",
  description:
    "With the Best Term Life Insurance Plan in India protect your loved ones from financial loss in the event of death. Buy term insurance with oneassure now!",
  keywords: [
    "term insurance",
    "life insurance",
    "term insurance plan",
    "term life policy",
    "term coverage life insurance",
  ],
  openGraph: {
    title: "Best term life Insurance Plan in India | OneAssure",
    description:
      "With the Best Term Life Insurance Plan in India protect your loved ones from financial loss in the event of death. Buy term insurance with oneassure now!",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: `/term-insurance`,
  },
};

async function getTermPageData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/term-lp?populate[hero][fields][0]=title&populate[hero][fields][1]=subTitle&populate[hero][fields][2]=getQuoteurl&populate[hero][fields][3]=subTitleRichText&populate[hero][populate][highlights][fields][0]=highlight&populate[testimonials][populate][testimonial][fields][0]=name&populate[testimonials][populate][testimonial][fields][1]=statement&populate[testimonials][populate][testimonial][fields][2]=backgroundColor&populate[testimonials][populate][testimonial][populate][thumbnail][fields][0]=url&populate[whatIsTI][fields][0]=title&populate[whatIsTI][fields][1]=description&populate[HowDoesTIWorks][fields][0]=title&populate[HowDoesTIWorks][fields][1]=description&populate[benefitsOfTI][fields][0]=title&populate[benefitsOfTI][fields][1]=description&populate[benefitsOfTI][populate][sectionData][fields][0]=title&populate[benefitsOfTI][populate][sectionData][fields][1]=description&populate[benefitsOfTI][populate][sectionData][populate][icon][fields][0]=url&populate[whoShouldBuyTI][fields][0]=title&populate[whoShouldBuyTI][fields][1]=description&populate[whoShouldBuyTI][populate][sectionData][fields][0]=title&populate[whoShouldBuyTI][populate][sectionData][fields][1]=description&populate[whoShouldBuyTI][populate][sectionData][populate][icon][fields][0]=url&populate[whyToBuyTI][fields][0]=title&populate[whyToBuyTI][fields][1]=description&populate[whyToBuyTI][populate][sectionData][fields][0]=title&populate[whyToBuyTI][populate][sectionData][fields][1]=description&populate[whyToBuyTI][populate][sectionData][populate][icon][fields][0]=url&populate[howToBuyTI][fields][0]=title&populate[howToBuyTI][fields][1]=description&populate[howToBuyTI][populate][sectionData][fields][0]=title&populate[howToBuyTI][populate][sectionData][fields][1]=description&populate[riderForTI][fields][0]=title&populate[riderForTI][fields][1]=description&populate[riderForTI][populate][sectionData][fields][0]=title&populate[riderForTI][populate][sectionData][fields][1]=description&populate[riderForTI][populate][sectionData][populate][icon][fields][0]=url&populate[howToBuyTIOneAssure][fields][0]=title&populate[howToBuyTIOneAssure][fields][1]=description&populate[howToBuyTIOneAssure][populate][sectionData][fields][0]=title&populate[howToBuyTIOneAssure][populate][sectionData][fields][1]=description&populate[TIEligibility][fields][0]=title&populate[TIEligibility][fields][1]=description&populate[TIEligibility][populate][sectionData][fields][0]=title&populate[TIEligibility][populate][sectionData][fields][1]=description&populate[TIEligibility][populate][sectionData][populate][icon][fields][0]=url&populate[requiredDocsTI][fields][0]=title&populate[requiredDocsTI][fields][1]=description&populate[requiredDocsTI][populate][sectionData][fields][0]=title&populate[requiredDocsTI][populate][sectionData][fields][1]=description&populate[requiredDocsTI][populate][sectionData][populate][icon][fields][0]=url`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }

  return res;
}

async function getCompanySlugsData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=term-insurance&pagination[pageSize]=100&fields[0]=name&fields[1]=slug&fields[2]=category&populate[logo][fields][0]=url&populate[health_variants][fields][0]=name&populate[health_variants][fields][1]=slug&populate[term_variants][fields][0]=name&populate[term_variants][fields][1]=slug`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }
  // console.log("Company slugs data : ", res);
  return res;
}

export default async function LifeInsurancePage() {
  const termPageData = await getTermPageData();
  const companySlugData = await getCompanySlugsData();
  return (
    <LifeInsurance
      data={termPageData.data}
      otherCompanies={companySlugData.data}
    />
  );
}
