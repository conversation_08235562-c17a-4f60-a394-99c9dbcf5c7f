import ClaimsNew from "@/components/ClaimsNew";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Insurance Claim - Oneassure",
  description:
    "File your insurance claims effortlessly with Oneassure. Our streamlined claims process ensures fast and fair settlements, helping you get the support you need when it matters most.",
  openGraph: {
    title: "Insurance Claim - Oneassure",
    description:
      "File your insurance claims effortlessly with Oneassure. Our streamlined claims process ensures fast and fair settlements, helping you get the support you need when it matters most.",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/claims",
  },
};

const getClaims = async () => {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/claim?populate[hero][fields][0]=title&populate[hero][fields][1]=subtitle&populate[support][fields][0]=title&populate[support][fields][1]=disclaimer&populate[support][populate][supportItems][fields][0]=title&populate[support][populate][supportItems][fields][1]=subtitle&populate[support][populate][supportItems][populate][icon][fields][0]=url&populate[testimonials][fields][0]=title&populate[testimonials][fields][1]=subtitle&populate[testimonials][populate][testimonial][fields][0]=name&populate[testimonials][populate][testimonial][fields][1]=statement&populate[testimonials][populate][testimonial][fields][2]=backgroundColor&populate[testimonials][populate][testimonial][populate][thumbnail][fields][0]=url&populate[process][fields][0]=title&populate[process][fields][1]=healthTitle&populate[process][fields][2]=termTitle&populate[process][populate][healthCashless][fields][0]=step&populate[process][populate][healthCashless][fields][1]=title&populate[process][populate][healthCashless][fields][2]=desc&populate[process][populate][healthCashless][populate][icon][fields][0]=url&populate[process][populate][healthReimbursement][fields][0]=step&populate[process][populate][healthReimbursement][fields][1]=title&populate[process][populate][healthReimbursement][fields][2]=desc&populate[process][populate][healthReimbursement][populate][icon][fields][0]=url&populate[process][populate][termProcess][fields][0]=step&populate[process][populate][termProcess][fields][1]=title&populate[process][populate][termProcess][fields][2]=desc&populate[process][populate][termProcess][populate][icon][fields][0]=url`,
    {
      headers,
    }
  );
  const data = await res.json();
  return data;
};

export default async function ClaimsPage() {
  const data = await getClaims();
  return <ClaimsNew {...data.data.attributes} />;
}
