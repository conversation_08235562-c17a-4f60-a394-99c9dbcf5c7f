import { Metadata } from "next";
import BlogNew from "@/components/BlogNew";
import { BlogCategorySchemas } from "@/components/SchemaMarkup/BlogCategorySchema";

export const metadata: Metadata = {
  title: "Blog | Oneassure",
  description: "Find answers to all your queries",
  keywords: [],
  openGraph: {
    title: "Blog | Oneassure",
    description: "Find answers to all your queries",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: `/insurance`,
  },
};

async function getData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/blogs?filters[topBlog][$eq]=true&populate[Thumbnail][fields][0]=url&populate[category][fields][0]=name&populate[category][fields][1]=slug&populate[author][fields][0]=name&fields[0]=title&fields[1]=subtitle&fields[2]=createdAt&fields[3]=slug&populate[subCategory][fields][0]=name&populate[subCategory][fields][1]=slug&pagination[pageSize]=100&pagination[page]=1&sort[0]=createdAt:desc`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }

  return res;
}

async function getCategory() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/categories?fields[0]=name&fields[1]=slug&populate[icon][fields][0]=url&pagination[pageSize]=100&pagination[page]=1&publicationState=live`,
    { headers }
  ).then((res) => res.json());

  return res;
}

async function BlogYoutubeVideos() {
  const data = await fetch(
    `https://youtube.googleapis.com/youtube/v3/search?part=snippet&channelId=${process.env.NEXT_PUBLIC_YT_CNL_ID}&maxResults=10&order=date&key=${process.env.NEXT_PUBLIC_YT_API_KEY}`
  ).then((res) => res.json());
  return data;
}

export default async function BlogLandingPage() {
  const data = await getData();
  const category = await getCategory();
  const youtubeData = await BlogYoutubeVideos();
  // return <AllBlogs data={data.data} title="All blogs" category={category} />;
  return (
    <>
      <BlogCategorySchemas
        categories={category.data.map((cat: any) => ({
          name: cat.attributes.name,
          description: `This is a blog series about ${cat.attributes.name}`,
        }))}
      />
      <BlogNew
        data={data.data}
        title="All blogs"
        category={category}
        youtubeData={youtubeData}
      />
    </>
  );
}
