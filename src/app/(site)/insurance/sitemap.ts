import { MetadataRoute } from "next";

export default async function sitemap() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  const categoryData = await fetch(
    `${process.env.STRAPI_BASEURL}/api/categories?fields[0]=slug`,
    { headers }
  ).then((res) => res.json());

  const categoryPaths = categoryData.data.map(
    (category: {
      id: number;
      attributes: {
        slug: string;
      };
    }) => ({
      url: `${process.env.NEXT_BASE_URL}/insurance/${category.attributes.slug}`,
      lastModified: new Date(),
    })
  );

  const articleData = await fetch(
    `${process.env.STRAPI_BASEURL}/api/blogs?populate[category][fields][0]=name&populate[category][fields][1]=slug&fields[0]=slug&pagination[pageSize]=100&pagination[page]=1`,
    { headers }
  ).then((res) => res.json());

  //   @ts-ignore
  const articlePaths = [];

  articleData.data.map(
    (article: {
      id: number;
      attributes: {
        slug: string;
        category: {
          data: {
            id: number;
            attributes: {
              name: string;
              slug: string;
            };
          };
        };
      };
    }) => {
      const temp = {
        url: `${process.env.NEXT_BASE_URL}/insurance/${article.attributes.category.data.attributes.slug}/${article.attributes.slug}`,
        lastModified: new Date(),
      };

      articlePaths.push(temp);
    }
  );

  // console.log("blog sitemap: ", [
  //   ...categoryPaths,
  //   // @ts-ignore
  //   ...articlePaths,
  // ]);
  return [
    ...categoryPaths,
    // @ts-ignore
    ...articlePaths,
  ];
}
