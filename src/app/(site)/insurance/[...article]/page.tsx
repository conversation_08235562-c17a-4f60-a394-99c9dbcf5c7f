// import Article from "@/components/Blog/Article";
import { Metadata } from "next";
import Article from "@/components/BlogNew/Article";
import ArticleSchema from "@/components/SchemaMarkup/ArticleSchema";

export const dynamicParams = false;

export async function generateMetadata({
  params,
}: {
  params: { article: string[] };
}): Promise<Metadata> {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  try {
    const data = await fetch(
      `${process.env.STRAPI_BASEURL}/api/blogs?populate=*&filters[slug][$eq]=${params.article[1]}`,
      { headers }
    ).then((res) => res.json());

    if (!data.data || !data.data[0] || !data.data[0].attributes?.Seo) {
      return {
        title: "Article Not Found",
        description: "The requested article could not be found",
      };
    }

    return {
      title: data.data[0].attributes.Seo.metaTitle,
      description: data.data[0].attributes.Seo.metaDescription,
      openGraph: {
        title: data.data[0].attributes.Seo.metaTitle,
        description: data.data[0].attributes.Seo.metaDescription,
        type: "website",
      },
      metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
      alternates: {
        canonical: `/insurance/${params.article[0]}/${params.article[1]}`,
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Error",
      description: "An error occurred while loading the article",
    };
  }
}

export async function generateStaticParams() {
  let page = 1;
  // @ts-ignore
  let allArticles = [];
  let hasMore = true;
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  while (hasMore) {
    const data = await fetch(
      `${process.env.STRAPI_BASEURL}/api/blogs?populate[category][fields][0]=name&populate[category][fields][1]=slug&fields[0]=slug&pagination[pageSize]=100&pagination[page]=${page}`,
      { headers }
    ).then((res) => res.json());

    if (data.data && data.data.length > 0) {
      // @ts-ignore
      allArticles = allArticles.concat(data.data);
      page++;
    } else {
      hasMore = false;
    }
  }

  const generatedPaths: { article: string[] }[] = [];

  allArticles.map(
    (article: {
      id: number;
      attributes: {
        slug: string;
        category: {
          data: {
            id: number;
            attributes: {
              name: string;
              slug: string;
            };
          };
        };
      };
    }) => {
      const temp = [
        article.attributes.category.data.attributes.slug,
        article.attributes.slug,
      ];

      generatedPaths.push({
        article: temp,
      });
    }
  );

  return generatedPaths;
}

async function getData(article: string) {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  try {
    const res = await fetch(
      `${process.env.STRAPI_BASEURL}/api/blogs?filters[slug][$eq]=${article}&fields[0]=Title&fields[1]=subtitle&fields[2]=slug&fields[3]=Description&fields[4]=readingtime&fields[5]=createdAt&populate[category][fields][0]=name&populate[category][fields][1]=slug&populate[author][fields][0]=name&populate[Seo][fields][0]=metaTitle&populate[Seo][fields][1]=metaDescription&populate[Seo][fields][2]=keywords&populate[relatedReads][populate][author][fields][0]=name&populate[relatedReads][category][fields][0]=name&populate[relatedReads][populate][category][fields][0]=slug&populate[relatedReads][populate][category][fields][1]=name&populate[relatedReads][populate][Thumbnail][fields][0]=url&populate[relatedReads][fields][0]=title&populate[relatedReads][fields][1]=subtitle&populate[relatedReads][fields][2]=createdAt&populate[relatedReads][fields][3]=slug&populate[healthVariants][fields][0]=name&populate[healthVariants][fields][1]=slug&populate[healthVariants][populate][company][fields][0]=slug&populate[healthVariants][populate][company][fields][1]=category&populate[healthVariants][populate][company][populate][logo][fields][0]=url&populate[termVariants][fields][0]=name&populate[termVariants][fields][1]=slug&populate[termVariants][populate][company][fields][0]=slug&populate[termVariants][populate][company][fields][1]=category&populate[termVariants][populate][company][populate][logo][fields][0]=url&populate[Thumbnail][fields][0]=url&fields[6]=topBlog&populate[authorNote][fields][0]=note&populate[authorNote][populate][image][fields][0]=url&populate[authorNote][populate][socialMedia][fields][0]=url&populate[authorNote][populate][socialMedia][populate][icon][fields][0]=url`,
      { headers }
    );

    if (!res.ok) {
      throw new Error(`Failed to fetch data: ${res.status}`);
    }

    const data = await res.json();

    if (!data.data || !data.data[0]) {
      throw new Error("Article not found");
    }

    return data;
  } catch (error) {
    console.error("Error fetching article data:", error);
    throw error;
  }
}

export default async function BlogArticlePage({
  params,
}: {
  params: { article: string[] };
}) {
  try {
    const data = await getData(params.article[1]);
    return (
      <>
        <ArticleSchema
          title={data.data[0].attributes.Title}
          description={data.data[0].attributes.Description}
          image={data.data[0].attributes.Thumbnail.data.attributes.url}
          datePublished={data.data[0].attributes.createdAt}
          dateModified={data.data[0].attributes.createdAt}
          author={data.data[0].attributes.author.data.attributes.name}
          publisherName="OneAssure"
          publisherLogo="https://oneassure.com/logo.png"
          category={data.data[0].attributes.category.data.attributes.name}
        />
        <Article data={data.data[0]} />
      </>
    );
  } catch (error) {
    console.error("Error in BlogArticlePage:", error);
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Error Loading Article</h1>
          <p className="text-gray-600">
            Sorry, something unexpected happened. Please try again later.
          </p>
        </div>
      </div>
    );
  }
}
