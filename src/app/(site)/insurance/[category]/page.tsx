import { Metadata } from "next";
import CategoryPage from "@/components/BlogNew/components/CategoryPage";
import { title } from "process";
export const dynamicParams = false;

export async function generateStaticParams() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/categories?fields[0]=slug`,
    { headers }
  ).then((res) => res.json());

  return data.data.map(
    (category: {
      id: number;
      attributes: {
        slug: string;
      };
    }) => ({
      category: category.attributes.slug,
    })
  );
}

export async function generateMetadata({
  params,
}: {
  params: { category: string };
}): Promise<Metadata> {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  const catInfo = await fetch(
    `${process.env.STRAPI_BASEURL}/api/categories?filters[slug][$eq]=${params.category}&fields[0]=name&fields[1]=slug&populate[Seo][fields][0]=metaTitle&populate[Seo][fields][1]=metaDescription&populate[Seo][fields][2]=keyword`,
    { headers }
  ).then((res) => res.json());

  return {
    title: catInfo.data[0].attributes.Seo.metaTitle,
    description: catInfo.data[0].attributes.Seo.metaDescription,
    openGraph: {
      title: catInfo.data[0].attributes.Seo.metaTitle,
      description: catInfo.data[0].attributes.Seo.metaDescription,
      type: "website",
    },
    metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
    alternates: {
      canonical: `/insurance/${params.category}`,
    },
  };
}

async function getData(category: string) {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/blogs?filters[category][slug][$eq]=${category}&populate[Thumbnail][fields][0]=url&populate[category][fields][0]=name&populate[category][fields][1]=slug&populate[category][fields][2]=title&populate[author][fields][0]=name&fields[0]=title&fields[1]=subtitle&fields[2]=createdAt&fields[3]=slug&pagination[pageSize]=100&pagination[page]=1&populate[subCategory][fields][0]=name&populate[subCategory][fields][1]=slug&sort[0]=createdAt:desc`,
    { headers }
  );
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }

  return res.json();
}

async function getCategory() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/categories?fields[0]=name&fields[1]=slug&populate[icon][fields][0]=url&pagination[pageSize]=100&pagination[page]=1&publicationState=live`,
    { headers }
  ).then((res) => res.json());

  return res;
}

export default async function CategoryBlogsLAndingPage({
  params,
}: {
  params: { category: string };
}) {
  const data = await getData(params.category);
  const category = await getCategory();

  return (
    // <AllBlogs
    //   data={data.data}
    //   category={category}
    //   title={`${
    //     params.category.split("-").join(" ").substring(0, 1).toUpperCase() +
    //     params.category.split("-").join(" ").substring(1)
    //   } blogs`}
    // />

    <CategoryPage
      data={data.data}
      category={category.data}
      title={data.data[0].attributes.category.data.attributes.title}
      currentCategory={params.category}
    />
  );
}
