import type { Metadata } from "next";
import MarketingLandingPage from "@/components/MarketingLandingPage";
export const dynamicParams = false;

export async function generateStaticParams() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const data = await fetch(`${process.env.STRAPI_BASEURL}/api/pages`, {
    headers,
  }).then((res) => res.json());

  return data.data.map(
    (page: {
      attributes: {
        slug: string;
      };
    }) => ({
      lp: page.attributes.slug,
    })
  );
}

export async function generateMetadata({
  params,
}: {
  params: { lp: string };
}): Promise<Metadata> {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/pages?filters[slug][$eq]=${params.lp}&populate[seo][fields][0]=metaTitle&populate[seo][fields][1]=metaDescription&populate[seo][fields][2]=keyword&populate[seo][fields][3]=source`,
    { headers }
  ).then((res) => res.json());

  // @ts-ignore
  let campaignData = data.data[0];

  return {
    title: campaignData.attributes.seo.metaTitle,
    description: campaignData.attributes.seo.metaDescription,
    openGraph: {
      title: campaignData.attributes.seo.metaTitle,
      description: campaignData.attributes.seo.metaDescription,
      type: "website",
    },
    metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
    alternates: {
      canonical: `/pages/${params.lp}`,
    },
  };
}

async function getData(slug: string) {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/pages?filters[slug][$eq]=${slug}&populate[seo][fields][0]=metaTitle&populate[seo][fields][1]=metaDescription&populate[seo][fields][2]=keyword&populate[seo][fields][3]=source&populate[hero][fields][0]=title&populate[hero][fields][1]=subTitle&populate[hero][populate][socialValidations][populate][validation][fields][0]=title&populate[hero][populate][socialValidations][populate][validation][fields][1]=subTitle&populate[hero][populate][socialValidations][populate][validation][populate][icon][fields][0]=url&populate[hero][populate][ourPartners][populate][insurer][fields][0]=name&populate[hero][populate][ourPartners][populate][insurer][fields][1]=url&populate[hero][populate][ourPartners][populate][insurer][populate][logo][fields][0]=url&populate[hero][populate][contactForm][fields][0]=formType&populate[hero][populate][contactForm][fields][1]=title&populate[hero][populate][contactForm][fields][2]=date&populate[hero][populate][contactForm][fields][3]=time&populate[hero][populate][contactForm][fields][4]=ctaUrl&populate[hero][populate][contactForm][fields][5]=actualPrice&populate[hero][populate][contactForm][fields][6]=discountedPrice&populate[hero][populate][contactForm][fields][7]=refundPolicy&populate[hero][populate][contactForm][populate][thumbnail][fields][0]=url&populate[block][populate][thumbnail][fields][0]=url&populate[block][populate][Options][fields][0]=title&populate[block][populate][Options][fields][1]=description&populate[block][populate][Options][populate][icon][fields][0]=url&populate[block][populate][faq][fields][0]=question&populate[block][populate][faq][fields][1]=ans&populate[block][populate][testimonial][fields][0]=name&populate[block][populate][testimonial][fields][1]=statement&populate[block][populate][testimonial][fields][2]=backgroundColor&populate[block][populate][testimonial][populate][thumbnail][fields][0]=url&fields[0]=title&fields[1]=slug&pagination[pageSize]=10&pagination[page]=1&status=published&locale[0]=en`,
    { headers }
  );
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }

  return res.json();
}

export default async function HomePage({ params }: { params: { lp: string } }) {
  const data = await getData(params.lp);

  return <MarketingLandingPage data={data.data[0]} />;
}
