import AboutNew from "@/components/AboutNew";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "About Us - Oneassure",
  description:
    "Discover more about <PERSON><PERSON><PERSON>, your trusted partner in navigating the complex world of insurance. Learn about our mission, values, and how we're dedicated to simplifying insurance processes for you",
  openGraph: {
    title: "About Us - Oneassure",
    description:
      "Discover more about <PERSON><PERSON><PERSON>, your trusted partner in navigating the complex world of insurance. Learn about our mission, values, and how we're dedicated to simplifying insurance processes for you",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/about",
  },
};

const getAboutUsData = async () => {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/about?populate[hero][fields][0]=title&populate[hero][fields][1]=subtitle&populate[goal][fields][0]=title&populate[goal][fields][1]=desc&populate[impact][fields][0]=title&populate[impact][populate][impactItems][fields][0]=title&populate[impact][populate][impactItems][fields][1]=value&populate[impact][populate][impactItems][populate][icon][fields][0]=url&populate[journey][fields][0]=title&populate[journey][populate][mobile][fields][0]=url&populate[journey][populate][web][fields][0]=url&populate[founder][fields][0]=title&populate[founder][fields][1]=desc&populate[founder][fields][2]=name&populate[founder][fields][3]=designation&populate[founder][fields][4]=company&populate[founder][populate][image][fields][0]=url&populate[investors][fields][0]=title&populate[investors][fields][1]=subtitle&populate[investors][populate][investorsItems][fields][0]=name&populate[investors][populate][investorsItems][populate][image][fields][0]=url&populate[team][fields][0]=title&populate[team][fields][1]=subtitle`,
    { headers }
  );

  if (!res.ok) {
    throw new Error(
      `Failed to fetch about data: ${res.status} ${res.statusText}`
    );
  }

  return res.json();
};

async function getMemberListData() {
  const headers = {
    Authorization:
      "Bearer ********************************************************",
  };
  const res = await fetch("https://slack.com/api/users.list", { headers });

  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  return res.json();
}

async function getChannelMemberIds() {
  const headers = {
    Authorization:
      "Bearer ********************************************************",
  };
  const res = await fetch(
    "https://slack.com/api/conversations.members?channel=C06RHCCJ52R",
    { headers }
  );

  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  return res.json();
}

export default async function AboutUsPage() {
  const memberListData = await getMemberListData();
  const channelIds = await getChannelMemberIds();
  const data = await getAboutUsData();

  // @ts-ignore
  const filteredMemberListOnId = memberListData.members.filter((member) =>
    channelIds.members.includes(member.id)
  );

  const activeMembersList = filteredMemberListOnId.filter(
    // @ts-ignore
    (member) => !member.deleted && !member.is_bot
  );

  return <AboutNew data={data} team={activeMembersList} />;
}
