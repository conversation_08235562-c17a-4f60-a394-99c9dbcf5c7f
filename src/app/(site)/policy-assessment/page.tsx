import { PolicyForm } from "@/components/PolicyAssessment/components/PolicyForm";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Policy Assessment",
  description:
    "Submit your health and term insurance policy details and get expert analysis from OneAssure. Our experts will help you understand and optimize your insurance coverage.",
  openGraph: {
    title: "Submit Your Policy for Expert Analysis - OneAssure",
    description:
      "Submit your health and term insurance policy details and get expert analysis from OneAssure. Our experts will help you understand and optimize your insurance coverage.",
    type: "website",
    url: "https://www.oneassure.in/policy-assessment",
  },
  keywords: [
    "policy assessment",
    "policy analysis",
    "insurance policy",
    "health insurance",
    "term insurance",
    "insurance coverage",
  ],
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/policy-assessment",
  },
};

function PolicyAssessmentPage() {
  return <PolicyForm />;
}

export default PolicyAssessmentPage;
