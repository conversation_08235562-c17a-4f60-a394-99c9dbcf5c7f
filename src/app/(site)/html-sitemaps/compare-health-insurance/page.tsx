// import AllComparePage from "@/components/Compare/AllComparePage/index";
import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import BreadcrumbsSchema from "@/components/SchemaMarkup/Breadcrumbs";
import { PageSchema } from "@/components/SchemaMarkup/PageSchema";
import TableOfContentSchema from "@/components/SchemaMarkup/TableOfContentSchema";
import { Comparisons } from "@/components/Compare/Comparisons";
import { SiteHealthVariantStaticContent } from "@/components/Compare/Comparisons/types";

// Define the types based on the API response
export const dynamic = 'force-dynamic';

type ApiResponse = {
  payload: {
    data: {
      site_health_variant_static_content: SiteHealthVariantStaticContent[];
    };
  };
  metadata: null;
};

export async function generateMetadata() {
  const title = "List of Insurance Provider Wise Heath insurance Plans Comparison ";
  const description = "Compare health insurance plans from top providers in India at OneAssure.in. Find premiums, benefits, and features side by side to choose the best health cover for your needs.";
  const canonical = `${process.env.NEXT_PUBLIC_BASE_URL}/html-sitemaps/compare-health-insurance/`;
  
  return {
    title,
    description,
    canonical,
    openGraph: {
      title,
      description,
      type: "website",
      url: canonical,
    },
    metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
    alternates: {
      canonical,
    },
    other: {
      // Facebook Meta Tags
      "og:url": canonical,
      "og:type": "website",
      "og:title": title,
      "og:description": description,

      // Twitter Meta Tags
      "twitter:card": "summary_large_image",
      "twitter:domain": "oneassure.in",
      "twitter:url": canonical,
      "twitter:title": title,
      "twitter:description": description,
    },
  };
}

async function getComparisonData(): Promise<SiteHealthVariantStaticContent[]> {
  try {
    const query = `query MyQuery {
      site_health_variant_static_content(where: {comparison_enabled: {_eq: true}}) {
        product_variant {
          id
          variant_name
          variant_slug
          product {
            insurer {
              name
              slug
            }
          }
        }
      }
    }`;
    const operationName = "MyQuery";
    const variables = {};

    const data: ApiResponse = await fetchStudioCmsData(
      query,
      operationName,
      variables
    );

    if (
      data &&
      data.payload &&
      data.payload.data.site_health_variant_static_content
    ) {
      return data.payload.data.site_health_variant_static_content;
    } else {
      return [];
    }
  } catch (error) {
    return [];
  }
}

const breadcrumbs = [
  { name: "OneAssure", item: `${process.env.NEXT_PUBLIC_BASE_URL}/` },
  {
    name: "Compare Health Insurance Plans",
    item: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans`,
  },
  {
    name: "All Comparisons",
    item: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans/comparisons`,
  },
];

export default async function AllComparePage() {
  const comparisonData = await getComparisonData();

  const pageSchema = {
    name: "All Health Insurance Plan Comparisons",
    url: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans/comparisons`,
    headline: "Compare All Health Insurance Plans - Find the Best Coverage"
  };

  return (
    <>
      <BreadcrumbsSchema breadcrumbs={breadcrumbs} />
      <PageSchema
        name={pageSchema.name}
        url={pageSchema.url}
        headline={pageSchema.headline}
        description="Explore and compare all available health insurance plans. Filter by insurer and find the perfect coverage for your needs."
      />
        <Comparisons 
          comparisonData={comparisonData}
        />
    </>
  );
}
