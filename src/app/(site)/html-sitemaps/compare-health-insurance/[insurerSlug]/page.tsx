 import { Comparisons } from "@/components/Compare/Comparisons";
import { SiteHealthVariantStaticContent } from "@/components/Compare/Comparisons/types";
import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import BreadcrumbsSchema from "@/components/SchemaMarkup/Breadcrumbs";
import { PageSchema } from "@/components/SchemaMarkup/PageSchema";

export const dynamic = 'force-dynamic';

type ApiResponse = {
  payload: {
    data: {
      site_health_variant_static_content: SiteHealthVariantStaticContent[];
    };
  };
  metadata: null;
};

async function getComparisonData(): Promise<SiteHealthVariantStaticContent[]> {
  try {
    const query = `query MyQuery {
      site_health_variant_static_content(where: {comparison_enabled: {_eq: true}}) {
        product_variant {
          id
          variant_name
          variant_slug
          product {
            insurer {
              name
              slug
            }
          }
        }
      }
    }`;
    const operationName = "MyQuery";
    const variables = {};

    const data: ApiResponse = await fetchStudioCmsData(
      query,
      operationName,
      variables
    );

    if (
      data &&
      data.payload &&
      data.payload.data.site_health_variant_static_content
    ) {
      return data.payload.data.site_health_variant_static_content;
    } else {
      return [];
    }
  } catch (error) {
    return [];
  }
}

async function getInsurerName({insurerSlug}: { insurerSlug: string }){
  const query = `query MyQuery($insurerSlug: String!) {
      health_insurers(where: {slug: {_eq: $insurerSlug}}) {
        name
      }
    }`;
  const operationName = "MyQuery";
  const variables = {insurerSlug};
  const response = await fetchStudioCmsData(query, operationName, variables);
  return response.payload.data.health_insurers[0]?.name || insurerSlug;
}

export async function generateMetadata({
  params,
}: {
  params: { insurerSlug: string };
}) {

  const insurerName = await getInsurerName(params);

  const title = `Comparison of ${insurerName} Insurance Plans with other insurer Plans `;
  const description = `Compare ${insurerName} health insurance plans from all insurance providers in India at OneAssure.in. Compare premiums, benefits, and features side by side to choose the best health cover for your needs.`;
  const canonical = `${process.env.NEXT_PUBLIC_BASE_URL}/html-sitemaps/compare-health-insurance/${params.insurerSlug}`;
  
  return {
    title,
    description,
    canonical,
    openGraph: {
      title,
      description,
      type: "website",
      url: canonical,
    },
    metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
    alternates: {
      canonical,
    },
    other: {
      // Facebook Meta Tags
      "og:url": canonical,
      "og:type": "website",
      "og:title": title,
      "og:description": description,

      // Twitter Meta Tags
      "twitter:card": "summary_large_image",
      "twitter:domain": "oneassure.in",
      "twitter:url": canonical,
      "twitter:title": title,
      "twitter:description": description,
    },
  };
}


export default async function InsurerComparePage({
  params,
}: {
  params: { insurerSlug: string };
}) {
  const comparisonData = await getComparisonData();
  const insurerName = await getInsurerName(params);
  
  // Find the insurer name for display
  
  const breadcrumbs = [
    { name: "OneAssure", item: `${process.env.NEXT_PUBLIC_BASE_URL}/` },
    { name: "Health Insurance", item: `${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/` },
    { name: "Compare Plans", item: `${process.env.NEXT_PUBLIC_BASE_URL}/html-sitemaps/compare-health-insurance/` },
    { name: insurerName, item: `${process.env.NEXT_PUBLIC_BASE_URL}/html-sitemaps/compare-health-insurance/${params.insurerSlug}` },
  ];

  return (
    <>
      <BreadcrumbsSchema breadcrumbs={breadcrumbs} />
      <PageSchema
        name={`${insurerName} Health Insurance Comparisons`}
        url={`${process.env.NEXT_PUBLIC_BASE_URL}/html-sitemaps/compare-health-insurance/${params.insurerSlug}`}
        headline={`Compare ${insurerName} Health Insurance Plans`}
        description={`Compare all ${insurerName} health insurance plans and find the best coverage for your needs.`}
      />
      <Comparisons 
        comparisonData={comparisonData}
        preSelectedInsurer={params.insurerSlug}
        insurerName={insurerName}
      />
    </>
  );
}