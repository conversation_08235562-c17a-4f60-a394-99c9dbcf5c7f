import BusinessHome from "@/components/Business/Home";
import { Metadata } from "next";

export const metadata: Metadata = {
  title:
    "Book an appointment with OneAssure | Best insurance solution for businesses in India",
  description:
    "If you have any queries about how we can solve your organization's insurance needs, then please book an appointment with us. We promise to answer your questions to your satisfaction.",
  openGraph: {
    title:
      "Book an appointment with OneAssure | Best insurance solution for businesses in India",
    description:
      "If you have any queries about how we can solve your organization's insurance needs, then please book an appointment with us. We promise to answer your questions to your satisfaction.",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: `/business/home`,
  },
};

async function getData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/business-lp?populate[hero][fields][0]=title&populate[hero][fields][1]=subtitle&populate[hero][fields][2]=thumbnail&populate[ourClients][populate][client][fields][0]=name&populate[ourClients][populate][client][populate][logo][fields][0]=url&populate[comprehensiveBenefit][populate][benefits][fields][0]=tag&populate[comprehensiveBenefit][populate][benefits][fields][1]=title&populate[comprehensiveBenefit][populate][benefits][fields][2]=description&populate[comprehensiveBenefit][populate][benefits][populate][icon][fields][0]=url&populate[whyUs][populate][reason][fields][0]=title&populate[whyUs][populate][reason][fields][1]=desc&populate[whyUs][populate][reason][fields][2]=backgroundColor&populate[whyUs][populate][reason][populate][thumbnail][fields][0]=url&populate[testimonials][populate][testimonial][fields][0]=name&populate[testimonials][populate][testimonial][fields][1]=statement&populate[testimonials][populate][testimonial][fields][2]=backgroundColor&populate[testimonials][populate][testimonial][populate][thumbnail][fields][0]=url`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }

  return res;
}

export default async function BusinessHomePage() {
  const data = await getData();
  return <BusinessHome data={data.data} />;
}
