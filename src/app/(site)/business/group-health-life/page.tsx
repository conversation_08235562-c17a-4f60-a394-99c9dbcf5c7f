import { Metadata } from "next";
import GroupHealthLife from "@/components/Business/GroupHL";

export const metadata: Metadata = {
  title:
    "Book an appointment with OneAssure | Best insurance solution for businesses in India",
  description:
    "If you have any queries about how we can solve your organization's insurance needs, then please book an appointment with us. We promise to answer your questions to your satisfaction.",
};

async function getData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/group-lp?populate[hero][fields][0]=title&populate[hero][populate][highlight][fields][0]=highlight&populate[hero][populate][highlight][populate][icon][fields][0]=url&populate[digitalDashboard][populate][features][fields][0]=tag&populate[digitalDashboard][populate][features][fields][1]=title&populate[digitalDashboard][populate][features][fields][2]=description&populate[digitalDashboard][populate][features][populate][icon][fields][0]=url&populate[testimonials][populate][testimonial][fields][0]=name&populate[testimonials][populate][testimonial][fields][1]=statement&populate[testimonials][populate][testimonial][fields][2]=backgroundColor&populate[testimonials][populate][testimonial][populate][thumbnail][fields][0]=url&populate[faqs][populate][faq][fields][0]=question&populate[faqs][populate][faq][fields][1]=ans&populate[grpBenefit][populate][benefitItem][fields][0]=tag&populate[grpBenefit][populate][benefitItem][fields][1]=title&populate[grpBenefit][populate][benefitItem][fields][2]=description&populate[grpBenefit][populate][benefitItem][populate][icon][fields][0]=url&populate[whyUs][populate][reason][fields][0]=title&populate[whyUs][populate][reason][fields][1]=desc&populate[whyUs][populate][reason][fields][2]=backgroundColor&populate[whyUs][populate][reason][populate][thumbnail][fields][0]=url`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }

  return res;
}

export default async function GroupHealthLifePage() {
  const data = await getData();

  return <GroupHealthLife data={data.data} />;
}
