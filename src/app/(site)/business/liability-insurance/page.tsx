import { Metadata } from "next";
import Liability from "@/components/Business/Liability";

export const metadata: Metadata = {
  title:
    "Book an appointment with OneAssure | Best insurance solution for businesses in India",
  description:
    "If you have any queries about how we can solve your organization's insurance needs, then please book an appointment with us. We promise to answer your questions to your satisfaction.",
};

async function getData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/liability-lp?populate[hero][fields][0]=title&populate[hero][populate][highlights][fields][0]=highlight&populate[hero][populate][highlights][populate][icon][fields][0]=url&populate[whyUs][populate][reason][fields][0]=title&populate[whyUs][populate][reason][fields][1]=desc&populate[whyUs][populate][reason][fields][2]=backgroundColor&populate[whyUs][populate][reason][populate][thumbnail][fields][0]=url&populate[cover][populate][coverItem][fields][0]=title&populate[cover][populate][coverItem][fields][1]=description&populate[cover][populate][coverItem][populate][icon][fields][0]=url`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }

  return res;
}

export default async function LiabilityInsurancePage() {
  const data = await getData();
  return <Liability data={data.data} />;
}
