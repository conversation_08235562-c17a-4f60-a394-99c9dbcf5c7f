import { Metadata } from "next";
import { HealthProductRoot } from "@/components/HealthInsurance/Product";

import getHealthVariantData from "@/components/HealthInsurance/actions/getVariantData";
import { HealthVariantResponse } from "@/components/HealthInsurance/types";
import ProductSchema from "@/components/SchemaMarkup/ProductSchema";
import FaqSchema from "@/components/SchemaMarkup/FaqSchema";
import InsuranceSchema from "@/components/SchemaMarkup/InsuranceSchema";

export const dynamicParams = false;

/** The top-down approach does not work. See https://github.com/vercel/next.js/issues/53717 */
export async function generateStaticParams() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=health-insurance&fields[0]=slug&populate[health_variants][fields][0]=slug&pagination[pageSize]=100`,
    { headers }
  ).then((res) => res.json());

  const generatedPaths = data.data.flatMap(
    (company: {
      attributes: {
        slug: string;
        health_variants: {
          data: { attributes: { slug: string } }[];
        };
      };
    }) => {
      return company.attributes.health_variants.data.map((variant) => ({
        product: variant.attributes.slug,
        company: company.attributes.slug,
      }));
    }
  );
  // console.log("health generated product path ", generatedPaths);
  return generatedPaths;
}

export async function generateMetadata({
  params,
}: {
  params: { company: string; product: string };
}): Promise<Metadata> {
  // read route params
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };

  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/health-variants?filters[slug][$eq]=${params.product}&populate[seo][fields][0]=metaTitle&populate[seo][fields][1]=metaDescription&populate[seo][fields][2]=keyword`,
    { headers }
  ).then((res) => res.json());

  var product = data.data[0];

  var productUrl: string[] = [];

  if (product.attributes.slug === "health-insurance") {
    var url = `/health-insurance/${params.company}/${params.product}`;
    if (!productUrl.includes(url)) {
      productUrl.push(url);
    }
  }

  return {
    title: product.attributes.seo?.metaTitle
      ? product.attributes.seo?.metaTitle
      : "",
    description: product.attributes.seo?.metaDescription
      ? product.attributes.seo?.metaDescription
      : "",
    keywords: [
      product.attributes.seo?.keyword ? product.attributes.seo?.keyword : "",
    ],
    openGraph: {
      title: product.attributes.seo?.metaTitle
        ? product.attributes.seo?.metaTitle
        : "",
      description: product.attributes.seo?.metaDescription
        ? product.attributes.seo?.metaDescription
        : "",
      type: "website",
    },
    metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
    alternates: {
      canonical: `/health-insurance/${params.company}/${params.product}`,
    },
  };
}

export default async function HealthInsuranceProductPage({
  params,
}: {
  params: { company: string; product: string };
}) {
  const data = await getHealthVariantData<HealthVariantResponse>(
    params.product
  );
  return (
    <>
      <InsuranceSchema
        name={data.data[0].attributes.name}
        description={data.data[0].attributes.seo?.metaDescription || ""}
        price="0"
        sku={data.data[0].attributes.slug || ""}
        brand={data.data[0].attributes.company.data.attributes.name || ""}
        url={`${process.env.NEXT_BASE_URL}/health-insurance/${params.company}/${params.product}`}
      />

      <ProductSchema
        name={data.data[0].attributes.name}
        description={data.data[0].attributes.seo?.metaDescription || ""}
        image={
          data.data[0].attributes.company.data.attributes.logo.data.attributes
            .url || ""
        }
        // price={
        //   data.data[0].attributes.variants[0].relatedVariant.data.attributes
        //     .price.data.attributes.price || 0
        // }
        // currency={
        //   data.data[0].attributes.variants[0].relatedVariant.data.attributes
        //     .currency.data.attributes.currency || "INR"
        // }
        price="0"
        currency="INR"
        sku={data.data[0].attributes.slug || ""}
        brand={data.data[0].attributes.company.data.attributes.name || ""}
        url={`${process.env.NEXT_BASE_URL}/health-insurance/${params.company}/${params.product}`}
      />
      <FaqSchema
        faqs={data.data[0].attributes.faqs.map((faq) => ({
          question: faq.question,
          answer: faq.ans,
        }))}
      />
      <HealthProductRoot product={data.data[0].attributes} />
    </>
  );
}
