import { Metadata } from "next";

import CompanyRoot from "@/components/Company";

import { ratingCalculator } from "@/components/Company/utility";
import getCompanyData from "@/components/Company/actions/getCompanyData";
import { CompanyData, StatisticsProps } from "@/components/Company/types";

export const dynamicParams = false;

export async function generateStaticParams() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=health-insurance&fields[0]=slug&pagination[pageSize]=100&pagination[page]=1`,
    { headers }
  ).then((res) => res.json());

  const generatedPaths = data.data.map(
    (comp: {
      attributes: {
        slug: string;
      };
    }) => ({
      company: comp.attributes.slug,
    })
  );

  //console.log("health generated comp path ", generatedPaths);
  return generatedPaths;
}

export async function generateMetadata({
  params,
}: {
  params: { company: string };
}): Promise<Metadata> {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[slug][$eq]=${params.company}&populate[seo][fields][0]=metaTitle&populate[seo][fields][1]=metaDescription&populate[seo][fields][2]=keyword`,
    {
      headers,
    }
  ).then((res) => res.json());

  let companyData = data.data[0];

  return {
    title: companyData.attributes.seo.metaTitle,
    description: companyData.attributes.seo.metaDescription,
    keywords: companyData.attributes.seo.keyword,
    openGraph: {
      title: companyData.attributes.seo.metaTitle,
      description: companyData.attributes.seo.metaDescription,
      type: "website",
    },
    metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
    alternates: {
      canonical: `/health-insurance/${params.company}`,
    },
  };
}

async function getCompanySlugsData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=health-insurance&fields[0]=name&fields[1]=slug&fields[2]=category&populate[logo][fields][0]=url&populate[ratings][fields][0]=solvency&populate[ratings][fields][1]=icr&populate[ratings][fields][2]=growth`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }
  // console.log("Company slugs data : ", res);
  return res;
}

export default async function HealthInsuranceCompanyPage({
  params,
}: {
  params: { company: string };
}) {
  const data = await getCompanyData<CompanyData>(params.company);
  const allComp = await getCompanySlugsData();

  // parameter = [minValue,maxValue]

  // Solvency
  let solvency = allComp.data.map(
    (item: any) => item.attributes.ratings && item.attributes.ratings.solvency
  );
  solvency = [Math.min(...solvency), Math.max(...solvency)];

  // ICR
  let icr = allComp.data.map(
    (item: any) => item.attributes.ratings && item.attributes.ratings.icr
  );
  icr = [Math.min(...icr), Math.max(...icr)];

  // Growth
  let growth = allComp.data.map(
    (item: any) => item.attributes.ratings && item.attributes.ratings.growth
  );
  growth = [Math.min(...growth), Math.max(...growth)];

  const rating = [
    {
      title: "Solvency",
      rating: ratingCalculator({
        parameter: "SOLVENCY",
        minRating: 2,
        maxRating: 4,
        paramCurrentValue: data.data[0].attributes.ratings.solvency!,
        paramMaxValue: solvency[1],
        paramMinValue: solvency[0],
      }),
      outOf: 4,
    },
    {
      title: "ICR",
      rating: ratingCalculator({
        parameter: "ICR",
        minRating: 1.5,
        maxRating: 3,
        paramCurrentValue: data.data[0].attributes.ratings.icr!,
        paramMaxValue: icr[1],
        paramMinValue: icr[0],
      }),
      outOf: 3,
    },
    {
      title: "Growth",
      rating: ratingCalculator({
        parameter: "GROWTH",
        minRating: 1.5,
        maxRating: 3,
        paramCurrentValue: data.data[0].attributes.ratings.growth!,
        paramMaxValue: growth[1],
        paramMinValue: growth[0],
      }),
      outOf: 3,
    },
  ];
  const statisticsData: StatisticsProps = {
    companyName: data.data[0].attributes.name,
    graphs: [
      {
        title: "Gross Direct Premium",
        industry:
          data.data[0].attributes.statistics.grossDirectPremium!.industry,
        company: data.data[0].attributes.statistics.grossDirectPremium!.company,
        suffix: "",
        description:
          data.data[0].attributes.statistics.grossDirectPremium?.description,
      },
      {
        title: "ICR%",
        industry: data.data[0].attributes.statistics.icr!.industry,
        company: data.data[0].attributes.statistics.icr!.company,
        suffix: "%",
        description: data.data[0].attributes.statistics.icr?.description,
      },
    ],
    ratingDescription: data.data[0].attributes?.ratingDescription,
    rating: rating,
  };
  const companyAttributes = data.data[0].attributes;
  const mappedProduct = {
    ...companyAttributes,
    hero: {
      // Use the hero title if it exists; otherwise, fallback to the company name.
      title: companyAttributes.hero?.title || companyAttributes.name,
      claimSettlementRatio: {
        value: companyAttributes.claimSettlementPercentage, // from company data
        description: "Claim Settlement Ratio", // default fallback
      },
      networkHospitals: {
        value: String(companyAttributes.networkHospitals), // ensure it's a string
        description: "Network Hospitals", // default fallback, since the field doesn’t exist
      },
    },
  };

  return (
    <CompanyRoot
      company={data.data[0]}
      allComp={allComp.data}
      statistics={statisticsData}
    />
  );
}
