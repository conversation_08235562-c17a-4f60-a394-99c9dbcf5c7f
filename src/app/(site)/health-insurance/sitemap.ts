interface companyData {
  attributes: {
    id: string;
    name: string;
    slug: string;
    health_variants: { data: { attributes: { slug: string } }[] };
  };
}
export default async function sitemap() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const data = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=health-insurance&populate[health_variants][fields][0]=slug&pagination[pageSize]=100`,
    { headers }
  ).then((res) => res.json());

  const companyPaths = data.data.map((company: companyData) => ({
    url: `${process.env.NEXT_BASE_URL}/health-insurance/${company.attributes.slug}`,
    lastModified: new Date(),
  }));

  // @ts-ignore
  const productPaths = [];

  data.data.map((company: companyData) => {
    company.attributes.health_variants.data.map((plan) => {
      // const temp = [company.slug, variant.slug];
      const temp = {
        url: `${process.env.NEXT_BASE_URL}/health-insurance/${company.attributes.slug}/${plan.attributes.slug}`,
        lastModified: new Date(),
      };
      productPaths.push(temp);
    });
  });

  return [
    ...companyPaths,
    // @ts-ignore
    ...productPaths,
  ];
}
