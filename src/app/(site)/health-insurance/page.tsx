import HealthInsurance from "@/components/HealthInsurance";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Buy Health Insurance Plan Online - OneAssure",
  description:
    "Health insurance takes care of your medical expenses. Buy best health insurance which suits your requirement. Check out guide for choosing the right health insurance plan, how it works, types of health insurance plans we offer, benefits of health insurance plans & what to look for while buying health insurance cover",
  keywords: [
    "Health Insurance",
    "Medical Health Insurance",
    "private medical insurance",
    "health cover plans",
    "medical insurance",
    "best health cover",
    "best health plan",
    "best health coverage",
    "best health coverage insurance",
  ],
  openGraph: {
    title: "Medical Health Insurance: Plans | Coverage | Policy - Oneassure",
    description:
      "Explore top Medical Health Insurance plans at Oneassure! Get comprehensive coverage and flexible policies tailored for your needs.",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/health-insurance",
  },
};

async function getHealthPageData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  // const res = await fetch(
  //   `${process.env.STRAPI_BASEURL}/api/health-lp?populate[hero][fields][0]=title&populate[hero][fields][1]=subtitle&populate[hero][fields][2]=getQuoteUrl&populate[hero][populate][highlights][fields][0]=highlight&populate[hero][populate][highlights][populate][icon][fields][0]=url&populate[ourExperts][populate][experts][fields][0]=title&populate[ourExperts][populate][experts][fields][1]=description&populate[ourExperts][populate][experts][populate][icon][fields][0]=url&populate[faqs][populate][faq][fields][0]=question&populate[faqs][populate][faq][fields][1]=ans`,
  //   { headers }
  // ).then((res) => res.json());

  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/health-lp?populate[hero][fields][0]=title&populate[hero][fields][1]=subTitleRichText&populate[whatIsHI][fields][0]=title&populate[whatIsHI][fields][1]=description&populate[testimonials][populate][testimonial][fields][0]=name&populate[testimonials][populate][testimonial][fields][1]=statement&populate[testimonials][populate][testimonial][fields][2]=backgroundColor&populate[testimonials][populate][testimonial][populate][thumbnail][fields][0]=url&populate[HIEligibilty][fields][0]=title&populate[HIEligibilty][fields][1]=description&populate[HIEligibilty][populate][sectionData][fields][0]=title&populate[HIEligibilty][populate][sectionData][fields][1]=description&populate[benefitsOfHI][fields][0]=title&populate[benefitsOfHI][fields][1]=description&populate[benefitsOfHI][populate][sectionData][fields][0]=title&populate[benefitsOfHI][populate][sectionData][fields][1]=description&populate[benefitsOfHI][populate][sectionData][populate][icon][fields][0]=url&populate[howToChoose][fields][0]=title&populate[howToChoose][fields][1]=description&populate[howToChoose][populate][sectionData][fields][0]=title&populate[howToChoose][populate][sectionData][fields][1]=description&populate[howToChoose][populate][sectionData][populate][icon][fields][0]=url&populate[requiredDocs][fields][0]=title&populate[requiredDocs][fields][1]=description&populate[requiredDocs][populate][sectionData][fields][0]=title&populate[requiredDocs][populate][sectionData][fields][1]=description&populate[requiredDocs][populate][sectionData][populate][icon][fields][0]=url&populate[HITopPlans][fields][0]=title&populate[HITopPlans][fields][1]=description&populate[HITopPlans][populate][types][fields][0]=title&populate[HITopPlans][populate][types][fields][1]=description&populate[HITopPlans][populate][types][fields][2]=table&populate[HITopPlans][populate][types][populate][icon][fields][0]=url&populate[whyBuyHI][populate][hIImporantce][fields][0]=title&populate[whyBuyHI][populate][hIImporantce][populate][hi_importances][populate][hIImporantceReasons][fields][0]=title&populate[whyBuyHI][populate][hIImporantce][populate][hi_importances][populate][hIImporantceReasons][fields][1]=description&populate[whyBuyHI][populate][hIImporantce][populate][hi_importances][populate][hIImporantceReasons][populate][iconUrl][fields][0]=url&populate[howToBuy][fields][0]=title&populate[howToBuy][fields][1]=description&populate[howToBuy][populate][sectionData][fields][0]=title&populate[howToBuy][populate][sectionData][fields][1]=description&populate[HIChecklist][fields][0]=title&populate[HIChecklist][fields][1]=description&populate[HIChecklist][populate][sectionData][fields][0]=title&populate[HIChecklist][populate][sectionData][fields][1]=description`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }

  return res;
}

async function getCompanySlugsData() {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/companies?filters[category][$eq]=health-insurance&fields[0]=name&fields[1]=slug&fields[2]=category&populate[logo][fields][0]=url&populate[health_variants][fields][0]=name&populate[health_variants][fields][1]=slug&populate[term_variants][fields][0]=name&populate[term_variants][fields][1]=slug`,
    { headers }
  ).then((res) => res.json());
  // The return value is *not* serialized
  // You can return Date, Map, Set, etc.

  // if (!res.ok) {
  //   // This will activate the closest `error.js` Error Boundary
  //   throw new Error("Failed to fetch data");
  // }
  // console.log("Company slugs data : ", res);
  return res;
}

export default async function HealthInsurancePage() {
  const healthPageData = await getHealthPageData();
  const companySlugData = await getCompanySlugsData();

  return (
    <HealthInsurance
      data={healthPageData.data}
      otherCompanies={companySlugData.data}
    />
  );
}
