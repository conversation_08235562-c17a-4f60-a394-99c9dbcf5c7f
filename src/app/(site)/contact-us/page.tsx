import ContactUsNew from "@/components/ContactUsNew";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Contact us - Oneassure",
  description:
    "Reach out to Oneassure for all your insurance inquiries. Our expert team is ready to assist you with solutions and support, ensuring your insurance experience is smooth and hassle-free.",
  openGraph: {
    title: "Contact us - Oneassure",
    description:
      "Reach out to Oneassure for all your insurance inquiries. Our expert team is ready to assist you with solutions and support, ensuring your insurance experience is smooth and hassle-free.",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/contact-us",
  },
};

const getContactUs = async () => {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/contact-us?populate[hero][fields][0]=title&populate[hero][fields][1]=subtitle&populate[contactCards][fields][0]=title&populate[contactCards][fields][1]=value&populate[contactCards][populate][icon][fields][0]=url`,
    { headers }
  );
  const data = await res.json();
  return data;
};

export default async function ContactUsPage() {
  const data = await getContactUs();
  return <ContactUsNew data={data.data.attributes} />;
}
