import AllComparePage from "@/components/Compare/AllComparePage/index";
import { Suspense } from "react";
import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import { transformData } from "@/components/Compare/Dtos/dto";
import { CompareIndexPageAPIResponse } from "@/components/Compare/type";

import axios from "axios";
import BreadcrumbsSchema from "@/components/SchemaMarkup/Breadcrumbs";
import { PageSchema } from "@/components/SchemaMarkup/PageSchema";
import FaqSchema from "@/components/SchemaMarkup/FaqSchema";
import TableOfContentSchema from "@/components/SchemaMarkup/TableOfContentSchema";

// Define the types based on the API response
type ProductVariant = {
  id: string;
  variant_name: string;
  variant_slug: string;
  product: {
    insurer: {
      name: string;
      slug: string;
    };
  };
};

type SiteHealthVariantStaticContent = {
  product_variant: ProductVariant;
};

type ApiResponse = {
  payload: {
    data: {
      site_health_variant_static_content: SiteHealthVariantStaticContent[];
    };
  };
  metadata: null;
};

export const dynamic = "force-static";
export const dynamicParams = false;
export const revalidate = false;

export async function generateStaticParams() {
  // Since this is a single page without dynamic routes, return empty array
  return [];
}

// Loading component for better UX
function LoadingComponent() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading comparison data...</p>
      </div>
    </div>
  );
}

async function getComparisonData(): Promise<SiteHealthVariantStaticContent[]> {
  try {
    const query = `query MyQuery2 {
      site_health_variant_static_content(where: {comparison_enabled: {_eq: true}}) {
        product_variant {
          id
          variant_name
          variant_slug
          product {
            insurer {
              name
              slug
            }
          }
        }
      }
    }`;
    const operationName = "MyQuery2";
    const variables = {};

    const data: ApiResponse = await fetchStudioCmsData(
      query,
      operationName,
      variables
    );

    // Check if response has the expected structure
    if (
      data &&
      data.payload &&
      data.payload.data.site_health_variant_static_content
    ) {
      return data.payload.data.site_health_variant_static_content;
    } else {
      return [];
    }
  } catch (error) {
    return [];
  }
}

export async function generateMetadata() {
  try {
    const compareIndexPageData = await getCompareIndexPageData();
    const transformedCompareIndexPageData = transformData(compareIndexPageData);

    const seoData = transformedCompareIndexPageData?.seo;
    // const seoData = seo;

    const title = seoData?.meta_title;

    const description = seoData?.meta_description;

    const pageUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans`;

    const keywords = seoData?.keywords.split(",").map((k) => k.trim());

    return {
      title,
      description,
      keywords,
      openGraph: {
        title,
        description,
        type: "website",
        url: pageUrl,
        siteName: "OneAssure",
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        domain: "oneassure.in",
        url: pageUrl,
      },
      metadataBase: new URL(
        process.env.NEXT_PUBLIC_BASE_URL || "https://www.oneassure.in"
      ),
      alternates: {
        canonical: seoData?.canonical,
      },
      other: {
        // Facebook Meta Tags
        "og:url": pageUrl,
        "og:type": "website",
        "og:title": title,
        "og:description": description,

        // Twitter Meta Tags
        "twitter:card": "summary_large_image",
        "twitter:domain": "oneassure.in",
        "twitter:url": pageUrl,
        "twitter:title": title,
        "twitter:description": description,
      },
      // Handle prevent_indexing flag from SEO data
      ...(seoData?.prevent_indexing && {
        robots: {
          index: false,
          follow: false,
        },
      }),
    };
  } catch (error) {
    console.error("Error fetching metadata:", error);
  }
}

async function getCompareIndexPageData(): Promise<CompareIndexPageAPIResponse | null> {
  try {
    const query = `
      query MyQuery {
        site_compare_index_page_static_content {
          id
          hero_title
          hero_description
          note
          pill_content
          need_to_compare_title
          need_to_compare_description
          compare_index_page_sections {
            id
            title
            type
            description
            compare_index_page_section_points {
              id
              title
              description
            }
          }
          compare_index_page_features_to_considers {
            id
            title
            description
            compare_index_page_features_to_consider_points {
              id
              title
              type
              points
            }
          }
          compare_index_page_insurance_categories {
            id
            title
            pill_content
            description
            compare_index_page_insurance_category_cards {
              id
              title
              points
              button_text
            }
          }
          compare_index_page_top_comparisons {
            id
            title
            description
            compare_index_page_top_comparison_cards {
              id
              variant_one_id
              variant_two_id
              variant_one {
                id
                variant_name
                variant_slug
                product {
                  insurer {
                    logo_url
                    slug
                    name
                  }
                }
              }
              variant_two {
                id
                product {
                  insurer {
                    logo_url
                    slug
                    name
                  }
                }
                variant_name
                variant_slug
              }
            }
          }
          compare_index_page_seo {
            id
            meta_title
            meta_description
            prevent_indexing
            keywords
            canonical
          }
        }
      }
    `;

    const operationName = "MyQuery";
    const variables = {};
    const response = await fetchStudioCmsData(query, operationName, variables);
    return response.payload.data.site_compare_index_page_static_content[0];
  } catch (error) {
    console.log(error);
    return null;
  }
}

async function fetchAllInsurerData() {
  try {
    // Check if environment variables are available
    if (
      !process.env.NEXT_PUBLIC_STRAPI_BASEURL ||
      !process.env.NEXT_PUBLIC_STRAPI_TOKEN
    ) {
      return [];
    }

    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_STRAPI_BASEURL}/api/companies?filters[category][$eq]=health-insurance&fields[0]=name&fields[1]=slug&fields[2]=category&populate[logo][fields][0]=url&populate[ratings][fields][0]=solvency&populate[ratings][fields][1]=icr&populate[ratings][fields][2]=growth`,
      {
        headers: {
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
        },
      }
    );

    // Enhanced data transformation with error handling
    const transformedData = response.data.data
      .map((item: any, index: number) => {
        try {
          const slug = item.attributes?.slug;
          const name = item.attributes?.name;
          const logoUrl = item.attributes?.logo?.data?.attributes?.url;

          if (!slug || !name) {
            return null;
          }

          return {
            company_slug: slug,
            company_name: name,
            logo_url: logoUrl || "",
          };
        } catch (error) {
          return null;
        }
      })
      .filter(Boolean); // Remove null items

    return transformedData;
  } catch (error) {
    // Return empty array instead of throwing
    return [];
  }
}

const breadcrumbs = [
  { name: "Oneassure", item: `${process.env.NEXT_PUBLIC_BASE_URL}/` },
  {
    name: "Compare Health Insurance Plans",
    item: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans`,
  },
];

const pageNavigationSection = {
  name: "top-comparisons",
  itemListElement: [
    {
      position: 1,
      name: "Top Comparisons",
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans#top-comparisons`,
    },
    {
      position: 2,
      name: "Assess Healthcare Need",
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans#assess_healthcare_need`,
    },
    {
      position: 3,
      name: "Features To Consider",
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans#features_to_consider`,
    },
    {
      position: 4,
      name: "Read Inclusion Exclusion",
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans#read_inclusion_exclusion`,
    },
    {
      position: 5,
      name: "Review Claim Settlement Ratio",
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans#review_claim_settlement_ratio`,
    },
    {
      position: 6,
      name: "Policy Conditions",
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans#policy_conditions`,
    },
    {
      position: 7,
      name: "What Experts Help You With",
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans#what_experts_help_you_with`,
    },
    {
      position: 8,
      name: "FAQs",
      url: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans#faqs`,
    },
  ],
};

export default async function AllComparePageComponent() {
  const comparisonData = await getComparisonData();
  const allInsurerData = await fetchAllInsurerData();
  const compareIndexPageData = await getCompareIndexPageData();
  const transformedCompareIndexPageData = transformData(compareIndexPageData);

  if (!transformedCompareIndexPageData) {
    return <div>No data available</div>;
  }


  const pageSchema = {
    name: "Compare Health Insurance Plans",
    url: `${process.env.NEXT_PUBLIC_BASE_URL}/compare-health-insurance-plans`,
    headline: transformedCompareIndexPageData.staticContent.hero_title
  };

  const pageContent = "Compare Health Insurance Plans – Confused between multiple health plans? Don’t worry - we’ve got you covered! With simple comparisons, you’ll see exactly what each plan offers, making your decision quick, clear, and stress-free. TOC – Assess your Healthcare needs: Start by reviewing your healthcare needs, choose the right plan, and stay covered as life changes. Review your health and family history: Check for any existing health issues that could affect your coverage. Match coverage with your budget: Find the right balance between affordable premiums and the protection you actually need. Check hospital access near you: Ensure the insurer covers preferred hospitals near your home. Plan for future medical needs: Consider rising medical costs, lifestyle risks, and long-term protection. Essential Features to Consider and Compare – Before you decide, take a closer look at the details that matter. Look at what’s covered, any limits or exclusions carefully, and explore the benefits and perks that can make a difference. Benefits You Should Look Out For: Check for valuable add-ons like Cumulative Bonus, No-Claim Bonus, or Restoration Benefit that enhance your coverage. Ensure the policy supports alternative and modern treatments, giving you more treatment options. Look for plans with no caps on room rent, ICU charges, or similar expenses, so you get full flexibility during hospitalization. Limitations to Be Aware Of: Check for deductibles and co-payments that may require you to pay from your own pocket. Watch out for weak claim settlement support, as it can delay or reduce your reimbursement. Review for disease-specific sub-limits or restrictions on daycare and domiciliary treatments that could limit your coverage. For more information, refer to the official policy wording and brochure of your health insurance plan for complete details on coverage, terms, and conditions. Read Inclusions & Exclusions – Know what’s covered and what’s not, so there are no surprises at the time of your claim. Understand the key benefits and services included in your policy. Be aware of treatments or situations that the policy doesn’t cover. Check the timeframes before certain benefits become active. Review any caps or limits on how much you can claim. Review Claim Settlement Ratio – Evaluate the insurer’s claim settlement ratio and track record to ensure reliability. Check the insurer’s claim settlement track record. Check if the insurer maintains a steady track record over the years. See how simple and hassle-free the documentation and approval steps are. Ensure the insurer shares clear information on claim approvals and rejections. Policy Conditions and Add-ons – Understand the key rules and explore add-ons that can enhance your coverage. Know the basic rules of your policy before you buy. Watch out for limits, co-pays, and special terms. Explore add-ons that can boost your coverage. Pick riders that suit your lifestyle and needs. Common Mistakes to Avoid When Comparing Health Insurance Plans – Stay mindful of exclusions, waiting periods, and networks so your plan never disappoints. Don’t just pick the cheapest premium - check the coverage too. Avoid ignoring waiting periods and hidden limits. Don’t skip reading inclusions and exclusions carefully. Don’t forget to review claim settlement history and customer reviews. What is the Need to Compare Health Insurance Plans – Buying health insurance is one of the most important financial decisions you’ll make, but with so many options available, it’s easy to feel confused. This is where comparing health insurance plans becomes essential. Every plan comes with its own mix of benefits, limits, exclusions, and add-ons - and the right plan for one person may not be the right plan for another. By comparing plans, you can understand the real value behind the premium you pay. It helps you identify which policy offers wider coverage, shorter waiting periods, and access to a strong hospital network. It also ensures you don’t miss out on features like cashless treatment, critical illness cover, or useful add-ons that may fit your lifestyle. Most importantly, comparison helps you avoid common mistakes - like choosing a plan only because it’s cheap, or overlooking hidden conditions that may impact your claims later. A well-compared plan means peace of mind, knowing that you’re covered for both routine medical needs and unexpected emergencies. In short, comparing health insurance plans empowers you to make an informed choice, protect your health better, and get true value for your money. Still Confused? Get Expert Advice – Our insurance experts are here to help you make the right choice. Get personalized recommendations based on your specific needs and budget. Why Choose Our Experts Consultation? End-to-End Support: From choosing the right policy to managing claims, every step is handled for you. Zero Spam. Zero Hassle: Pure advice, no unwanted calls, no unnecessary push. Free Expert Consultation: Talk to experienced advisors at no cost, and make confident decisions. 24/7 Claim Assistance: Get a dedicated expert managing your claim end-to-end at no added cost. What Our Experts Help You With – Personalised Recommendations: Every suggestion is backed by expert analysis of your life stage, goals, and budget. Expert-Led Policy Review: We decode the fine print - identifying risks, sub-limits, and gaps you may have missed. No surprises later. Smart, Tech-Enabled Experience: From digital onboarding to real-time claim tracking, our platform makes insurance easy, accessible, and stress-free. Explore Health Insurance Category – Discover the right insurance plan with our easy category guide. Senior Citizen Health Plan: Secure against age-related medical costs. Tailored for seniors healthcare needs. Family Health Plan: One policy covers the entire family. High sum insured with cashless care. Multiple coverage options based on your family needs. Maternity Health Plan: Covers delivery, newborn care, and maternity expenses. Reduces financial stress of childbirth costs. Frequently Asked Questions (FAQs) – Have questions? We've got answers. Explore our FAQs to find the information you need. How does OneAssure help me compare health insurance policies and choose the best plan? OneAssure allows you to compare multiple health insurance policies side by side, analyzing coverage, premiums, hospital networks, and benefits to choose the most suitable plan for your needs. What is the difference between cashless and reimbursement claims in health insurance policies? Cashless claims let you get treatment at network hospitals without paying upfront, while reimbursement claims require you to pay first and get refunded later. Does OneAssure provide expert guidance while selecting health insurance with maternity or newborn coverage? Yes, OneAssure’s insurance experts guide you in selecting plans with maternity benefits, newborn cover, and short waiting periods for family planning needs. How do co-payment clauses impact health insurance premiums and claim settlements? Co-payment means you share a part of the claim cost. Opting for co-pay lowers your premium but increases your out-of-pocket expenses. Which health insurance plan offers the best coverage for critical illnesses and pre-existing diseases? Plans with comprehensive coverage, minimal waiting periods, and optional critical illness riders are ideal. OneAssure helps identify policies that best meet these requirements. Can I compare waiting periods and sub-limits for hospitalisation expenses on OneAssure? Yes, OneAssure’s comparison tool highlights waiting periods, sub-limits, and exclusions so you can choose a policy that maximizes benefits and minimizes restrictions. What are room rent limits in health insurance, and how do they affect claims? Room rent limits cap the daily room cost covered by your insurer. Choosing a plan without strict limits ensures higher claim payouts. Does OneAssure assist with health insurance claims and renewals after I purchase a policy? Absolutely. OneAssure provides end-to-end support for claims, renewals, and queries, ensuring a hassle-free experience throughout your policy tenure. How can I check if a health insurance plan covers daycare procedures and modern treatments? OneAssure’s policy comparison clearly lists daycare and advanced treatment coverage, helping you pick a plan aligned with your healthcare needs. What documents are needed to buy and claim a health insurance policy online via OneAssure? To buy a policy, you need ID proof, address proof, and medical details. For claims, hospital bills, prescriptions, and discharge summaries are required.";


  return (
    <>
      <BreadcrumbsSchema breadcrumbs={breadcrumbs} />
      <PageSchema
        name={pageSchema.name}
        url={pageSchema.url}
        headline={pageSchema.headline}
        description={pageContent}
      />
      <FaqSchema faqs={transformedCompareIndexPageData.faqData.faqs} />
      {/* <HowToSchema data={howToSchema} />
          <RatingAndReviewSchema data={ratingAndReviewSchema} /> */}
      <TableOfContentSchema data={pageNavigationSection} />
      <Suspense fallback={<LoadingComponent />}>
        <AllComparePage
          compareIndexPageData={transformedCompareIndexPageData}
          comparisonData={comparisonData}
          allInsurerData={allInsurerData}
        />
      </Suspense>
    </>
  );
}
