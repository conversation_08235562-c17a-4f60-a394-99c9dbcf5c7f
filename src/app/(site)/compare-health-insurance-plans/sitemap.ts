import axios from "axios";
import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";

// Define the types based on the API response
type ProductVariant = {
  id: string;
  variant_name: string;
  variant_slug: string;
  product: {
    insurer: {
      name: string;
      slug: string;
    };
  };
};

type SiteHealthVariantStaticContent = {
  product_variant: ProductVariant;
};

type ApiResponse = {
  payload: {
    data: {
      site_health_variant_static_content: SiteHealthVariantStaticContent[];
    };
  };
  metadata: null;
};

// Generate all possible comparisons for sitemap
const generateComparisons = (products: SiteHealthVariantStaticContent[]) => {
  const comparisons: Array<{
    product1: ProductVariant;
    product2: ProductVariant;
    url: string;
  }> = [];

  if (!Array.isArray(products)) {
    console.warn("Products data is not an array");
    return comparisons;
  }

  for (let i = 0; i < products.length; i++) {
    for (let j = i + 1; j < products.length; j++) {
      const product1 = products[i]?.product_variant;
      const product2 = products[j]?.product_variant;

      if (
        !product1?.variant_slug ||
        !product2?.variant_slug ||
        !product1?.id ||
        !product2?.id
      ) {
        continue;
      }

      const url = `/compare-health-insurance-plans/${product1.product.insurer.slug}-${product1.variant_slug}-vs-${product2.product.insurer.slug}-${product2.variant_slug}/${product1.id}-${product2.id}`;

      comparisons.push({ product1, product2, url });
    }
  }

  return comparisons;
};

async function getComparisonData(): Promise<SiteHealthVariantStaticContent[]> {
  try {
    const query = `query MyQuery2 {
      site_health_variant_static_content(where: {comparison_enabled: {_eq: true}}) {
        product_variant {
          id
          variant_name
          variant_slug
          product {
            insurer {
              name
              slug
            }
          }
        }
      }
    }`;
    const operationName = "MyQuery2";
    const variables = {};
    const response = await fetchStudioCmsData(query, operationName, variables);

    // Check if response has the expected structure
    if (
      response.payload.data &&
      response.payload.data.site_health_variant_static_content
    ) {
      return response.payload.data.site_health_variant_static_content;
    } else {
      return [];
    }
  } catch (error) {
    return [];
  }
}

export default async function sitemap() {
  const baseUrl = process.env.NEXT_BASE_URL || "https://oneassure.in";
  
  try {
    const comparisonData = await getComparisonData();
    const comparisons = generateComparisons(comparisonData);

    // Generate sitemap entries for all comparison URLs
    const comparisonUrls = comparisons.map((comparison) => ({
      url: `${baseUrl}${comparison.url}`,
      lastModified: new Date(),
      changeFrequency: "weekly" as const,
      priority: 0.7,
    }));

    return comparisonUrls;
  } catch (error) {
    console.error("Error generating sitemap:", error);
    return [];
  }
}