import InfluencerRoot from "@/components/Influencer";
import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import { transformInfluencerData } from "@/components/Influencer/dto";
import FaqSchema from "@/components/SchemaMarkup/FaqSchema";
import BreadcrumbsSchema from "@/components/SchemaMarkup/Breadcrumbs";

export const dynamicParams = true;
export const dynamic = 'force-dynamic';

export async function generateStaticParams() {
  // For now, using static data - replace with API call when available
  const query = `
  query MyQuery {
    site_influencer(where:{is_published:{_eq: true}}) {
      id
      name
      slug
    }
  }
  `;
  const operationName = "MyQuery";
  const variables = {};
  const response = await fetchStudioCmsData(query, operationName, variables);
  const data = response.payload.data.site_influencer;

  const generatedPaths = data.map((influencer: any) => ({
    influencer: `${influencer.slug}-insurance-recommendation`,
  }));

  return generatedPaths;
}

export async function generateMetadata({params,}: {params: { influencer: string };}) {
  const slug = params.influencer.replace("-insurance-recommendation", "");

  const query = `
  query MyQuery($slug: String!) {
    site_influencer(where:{slug :{_eq: $slug}, is_published: {_eq: true}}) {
      id
      name
      slug
      influencer_seo {
        id
        canonical
        meta_description
        meta_keyword
        meta_title
        prevent_indexing
        source
      }
    }
  }
  `;

  const operationName = "MyQuery";
  const variables = {slug: slug};
  const response = await fetchStudioCmsData(query, operationName, variables);
  const influencerData = response.payload.data.site_influencer[0].influencer_seo;
  const pageUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/partner/${params.influencer}`;
  return {
    title: influencerData.meta_title,
    description: influencerData.meta_description,
    keywords: influencerData.meta_keyword,
    openGraph: {
      title: influencerData.meta_title,
      description: influencerData.meta_description,
      type: "website",
      url: pageUrl
    },
    twitter: {
      card: "summary_large_image",
      title: influencerData.meta_title,
      description: influencerData.meta_description,
      url: pageUrl
    },
    other: {
      // Facebook Meta Tags
      "og:url": pageUrl,
      "og:type": "website",
      "og:title": influencerData.meta_title,
      "og:description": influencerData.meta_description,

      // Twitter Meta Tags
      "twitter:card": "summary_large_image",
      "twitter:domain": "oneassure.in",
      "twitter:url": pageUrl,
      "twitter:title": influencerData.meta_title,
      "twitter:description": influencerData.meta_description,
    },
    metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
    alternates: {
      canonical: influencerData.canonical,
    },
  };
}

async function getInfluencerData(slug: string) {
  const query = `
query MyQuery($slug: String!) {
  site_influencer(where:{slug :{_eq: $slug}, is_published: {_eq: true}}) {
    id
    name
    slug
    data_access_link
    hero_title
    hero_image_url
    hero_description
    influencer_faqs {
      id
      question
      answer
    }
    influencer_one_assure_backgrounds {
      id
      points
      title
    }
    influencer_recommendations {
      id
      name
      pills_content
      social_handles
      title
      video_description
      video_title
      video_url
      designation
      image_url
      content
    }
    influencer_testimonials {
      id
      name
      content
    }
    influencer_why_choose_one_assures {
      id
      title
      description
      influencer_why_choose_one_assure_points {
        id
        title
        description
      }
    }
    influencer_why_trust_us {
      id
      title
      description
      influencer_why_trust_us_points {
        id
        title
        description
      }
    }
  }
}
  `;

  const operationName = "MyQuery";
  const variables = {slug: slug};
  const response = await fetchStudioCmsData(query, operationName, variables);
  return response.payload.data.site_influencer[0];
}

export default async function InfluencerPage({
  params,
}: {
  params: { influencer: string };
}) {

  
  const slug = params.influencer.replace("-insurance-recommendation", "");
  const influencerData = await getInfluencerData(slug);
  const influencer = transformInfluencerData(influencerData);

  const breadcrumbs = [
    { name: "OneAssure", item: `${process.env.NEXT_PUBLIC_BASE_URL}/` },
    {
      name: "Our Partners",
      item: `${process.env.NEXT_PUBLIC_BASE_URL}/partner`,
    },
    {
      name: influencer.name,
      item: `${process.env.NEXT_PUBLIC_BASE_URL}/partner/${slug}-insurance-recommendation`,
    },
  ];

  return <>
  <BreadcrumbsSchema breadcrumbs={breadcrumbs} />
  <FaqSchema faqs={influencer.influencerFaqs.map((faq) => ({
          question: faq.question,
          answer: faq.answer,
        }))}
  ></FaqSchema>
  <InfluencerRoot influencer={influencer} />
  </>;
}
