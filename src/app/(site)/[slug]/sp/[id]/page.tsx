import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";
import FallBack from "@/components/globals/DSComponentsV0/FallBack";
import BreadcrumbsSchema from "@/components/SchemaMarkup/Breadcrumbs";
import FaqSchema from "@/components/SchemaMarkup/FaqSchema";
import { PageSchema } from "@/components/SchemaMarkup/PageSchema";
import TableOfContentSchema from "@/components/SchemaMarkup/TableOfContentSchema";
import Standalone from "@/components/StandalonePage";
import transformStandalonePageData from "@/components/StandalonePage/dto";
import { fetchAllInsurers } from "@/scripts/fetchAllInsurers";

export async function generateMetadata({
  params,
}: {
  params: { id: string; slug: string };
}) {
  try {
    const query = `
	query MyQuery($id: String!) {
		site_standalone_page(where:{id:{_eq: $id}}) {
			id
			hero_title
			hero_description
			hero_image_url
			slug
			standalone_page_seo {
				id
				keywords
				meta_description
				meta_title
				prevent_indexing
				canonical
			}
		}
	}
	`;
    const operationName = "MyQuery";
    const variables = { id: params.id };
    const response = await fetchStudioCmsData(query, operationName, variables);
    const pageData = response.payload.data?.site_standalone_page?.[0];
    const seoData = pageData?.standalone_page_seo;

    const title =
      seoData?.meta_title ||
      pageData?.hero_title ||
      "Senior Citizen Health Insurance - Buy Medical Insurance for Senior Citizens - Oneassure";
    const description =
      seoData?.meta_description ||
      pageData?.hero_description ||
      "Explore Best Health Insurance Plans for Senior Citizens at OneAssure. Check Coverage, Cashless Hospitalization, Easy claim process & Section 80D tax savings. Compare & Buy Online.";
    const keywords =
      seoData?.keywords ||
      "Senior citizen health insurance, Medical insurance for senior citizens, Health insurance for senior citizen, Insurance for senior citizens, Health insurance plans for senior citizens, Mediclaim policy for senior citizens";
    const canonical =
      seoData?.canonical ||
      `${process.env.NEXT_PUBLIC_BASE_URL}/${params.slug}/sp/${params.id}`;

    return {
      title,
      description,
      keywords,
      openGraph: {
        title,
        description,
        type: "website",
        url: canonical,
        images: [
          {
            url: pageData?.hero_image_url || "",
            width: 1200,
            height: 630,
            alt: title,
          },
        ],
        siteName: "OneAssure",
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: [pageData?.hero_image_url || ""],
        domain: "oneassure.in",
        url: canonical,
      },
      metadataBase: new URL(
        process.env.NEXT_PUBLIC_BASE_URL || "https://www.oneassure.in"
      ),
      alternates: {
        canonical: canonical,
      },
      other: {
        // Facebook Meta Tags
        "og:url": canonical,
        "og:type": "website",
        "og:title": title,
        "og:description": description,
        "og:image": pageData?.hero_image_url || "",

        // Twitter Meta Tags
        "twitter:card": "summary_large_image",
        "twitter:domain": "oneassure.in",
        "twitter:url": canonical,
        "twitter:title": title,
        "twitter:description": description,
        "twitter:image": pageData?.hero_image_url || "",
      },
    };
  } catch (error) {
    console.log(error);
  }
}

async function getPageData(id: string) {
  const query = `
			query MyQuery($id: String!) {
				site_standalone_page(where:{id:{_eq: $id}}) {
					id
					hero_title
					hero_description
					hero_image_url
					pill_content
					slug
					standalone_benefits_section {
						id
						pill_content
						section_description
						section_title
						standalone_benefits_section_points {
							id
							title
							description
							icon_url
						}
					}
					standalone_claim_settlement_section {
						id
						section_title
						section_description
						pill_content
						standalone_claim_settlement_types {
							id
							title
							type
							standalone_claim_settlement_steps {
								id
								title
								description
							}
						}
					}
					standalone_documents_section {
						id
						pill_content
						section_title
						section_description
						standalone_documents_section_points {
							id
							title
							description
							icon_url
						}
					}
					standalone_faq_section {
						id
						section_title
						section_description
						pill_content
						standalone_faq_section_points {
							id
							question
							answer
						}
					}
					standalone_inclusion_section {
						id
						pill_content
						section_title
						section_description
						standalone_inclusion_section_points {
							id
							points
							type
						}
					}
					standalone_insurance_category_section {
						id
						pill_content
						section_title
						section_description
						standalone_insurance_category_section_cards {
							id
							most_popular
							title
							points
							icon_url
						}
					}
					standalone_key_factors_section {
						id
						pill_content
						section_title
						section_description
						standalone_key_factors_section_points {
							id
							title
							description
							icon_url
						}
					}
					standalone_plans_section {
						id
						section_title
						section_description
						pill_content
						standalone_plans_section_plans {
							id
							health_product_variant {
								id
								variant_name
								variant_slug
								temp_slug
								product {
									insurer {
										id
										name
										logo_url
										slug
										temp_slug
									}
								}
							}
						}
					}
					standalone_renewal_section {
						id
						pill_content
						section_title
						section_description
						standalone_renewal_types {
							id
							title
							type
							standalone_renewal_steps {
								id
								title
								description
							}
						}
					}
					standalone_tax_advantage_section {
						id
						pill_content
						section_title
						section_description
						standalone_tax_advantage_section_points {
							id
							title
							description
							icon_url
						}
					}
					standalone_verdict_section {
						id
						pill_content
						section_title
						section_description
						verdict
						standalone_verdict_section_pros_cons {
							id
							points
							title
							type
						}
					}
					standalone_what_to_look_for_section {
						id
						pill_content
						section_description
						section_title
						standalone_what_to_look_for_section_points {
							id
							title
							description
							icon_url
						}
					}
					standalone_why_plans_section {
						id
						pill_content
						section_title
						section_description
						standalone_why_plans_section_points {
							id
							title
							description
							icon_url
						}
					}
					standalone_hero_cards {
						id
						title
						description
						icon_url
					}
				}
				site_testimonials {
					id
					image_url
					name
					content
				}
			}
    `;
  const operationName = "MyQuery";
  const variables = { id: id };
  const response = await fetchStudioCmsData(query, operationName, variables);
  if (response.payload.data) {
    return response.payload.data;
  }
  return null;
}

async function getBlogData() {
  try {
    // Check if environment variables are available
    if (
      !process.env.NEXT_PUBLIC_STRAPI_BASEURL ||
      !process.env.NEXT_PUBLIC_STRAPI_TOKEN
    ) {
      return {
        heading: "Latest Blog Posts",
        blogs: [],
      };
    }

    const headers = {
      Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
    };

    const res = await fetch(
      `${process.env.NEXT_PUBLIC_STRAPI_BASEURL}/api/blogs?filters[topBlog][$eq]=true&populate[Thumbnail][fields][0]=url&populate[category][fields][0]=name&populate[category][fields][1]=slug&populate[author][fields][0]=name&fields[0]=title&fields[1]=subtitle&fields[2]=createdAt&fields[3]=slug&populate[subCategory][fields][0]=name&populate[subCategory][fields][1]=slug&pagination[pageSize]=100&pagination[page]=1&sort[0]=createdAt:desc`,
      {
        headers,
        next: { revalidate: 0 }, // Force fresh data
      }
    );

    if (!res.ok) {
      throw new Error(`Blog API responded with status: ${res.status}`);
    }

    const data = await res.json();

    // Enhanced data transformation with error handling
    const transformedData = {
      heading: "Latest Blog Posts",
      blogs: data.data
        .map((blog: any, index: number) => {
          try {
            return {
              title: blog.attributes?.Title || `Blog Post ${index + 1}`,
              date: blog.attributes?.createdAt
                ? new Date(blog.attributes.createdAt).toLocaleDateString(
                    "en-US",
                    {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    }
                  )
                : "Recent",
              author:
                blog.attributes?.author?.data?.attributes?.name ||
                "OneAssure Team",
              description: blog.attributes?.subtitle || "",
              imageUrl: blog.attributes?.Thumbnail?.data?.attributes?.url || "",
              url: `/insurance/${
                blog.attributes?.category?.data?.attributes?.slug || "blog"
              }/${blog.attributes?.slug || ""}`,
            };
          } catch (error) {
            return null;
          }
        })
        .filter(Boolean)
        .slice(0, 6), // Limit to 6 blogs and remove null items
    };

    return transformedData;
  } catch (error) {
    // Return fallback data instead of throwing
    return {
      heading: "Latest Blog Posts",
      blogs: [],
    };
  }
}

const StandalonePage = async ({
  params,
}: {
  params: { slug: string; id: string };
}) => {
  const pageData = await getPageData(params.id);
  const transformedData = transformStandalonePageData(pageData);

  if (!transformedData) {
    return <FallBack />;
  }

  const pageSchema = {
    name: transformedData.heroSection.title,
    url: `${process.env.NEXT_PUBLIC_BASE_URL}/${transformedData.slug}/sp/${transformedData.id}`,
    headline: transformedData.heroSection.title,
    description: transformedData.heroSection.description,
  };
  const pageNavigationSection = {
    name: "expert-review",
    itemListElement: [
      {
        position: 1,
        name: "Verdict",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${transformedData.slug}/sp/${transformedData.id}#expert-review`,
      },
      {
        position: 2,
        name: "Testimonials",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${transformedData.slug}/sp/${transformedData.id}#testimonials`,
      },
      {
        position: 3,
        name: "What's Included",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${transformedData.slug}/sp/${transformedData.id}#inclusions-and-exclusions`,
      },
      {
        position: 4,
        name: "Key Factors",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${transformedData.slug}/sp/${transformedData.id}#key-factors`,
      },
      {
        position: 5,
        name: "Claim Settlement Process",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${transformedData.slug}/sp/${transformedData.id}#claim-settlement`,
      },
      {
        position: 6,
        name: "Renewal Process",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${transformedData.slug}/sp/${transformedData.id}#renewal-process`,
      },
      {
        position: 7,
        name: "FAQs",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${transformedData.slug}/sp/${transformedData.id}#faqs`,
      },
    ],
  };

  const breadCrumbs = [
    {
      name: "OneAssure",
      item: `${process.env.NEXT_PUBLIC_BASE_URL}/`,
    },
    {
      name: transformedData.heroSection.title,
      item: `${process.env.NEXT_PUBLIC_BASE_URL}/${transformedData.slug}/sp/${transformedData.id}`,
    },
  ];

  const allInsurerData = await fetchAllInsurers();
  const blogData = await getBlogData();
  return (
    <>
      <BreadcrumbsSchema breadcrumbs={breadCrumbs} />
      <PageSchema
        name={pageSchema.name}
        url={pageSchema.url}
        headline={pageSchema.headline}
        description={pageSchema.description}
      />
      <TableOfContentSchema data={pageNavigationSection} />
      <FaqSchema faqs={transformedData.faqSection.faqs} />
      <Standalone
        data={transformedData}
        allInsurerData={allInsurerData}
        blogData={blogData}
      />
    </>
  );
};

export default StandalonePage;
