import CareersNew from "@/components/CareersNew";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Careers | Oneassure",
  openGraph: {
    title: "Careers | Oneassure",
    type: "website",
  },
  metadataBase: new URL(`${process.env.NEXT_BASE_URL}`),
  alternates: {
    canonical: "/careers",
  },
};

const getCareers = async () => {
  const headers = {
    Authorization: `Bearer ${process.env.STRAPI_TOKEN}`,
  };
  const res = await fetch(
    `${process.env.STRAPI_BASEURL}/api/career?populate[hero][fields][0]=title&populate[hero][fields][1]=subtitle&populate[benefits][fields][0]=title&populate[benefits][fields][1]=disclaimer&populate[benefits][populate][benefitItems][fields][0]=title&populate[benefits][populate][benefitItems][fields][1]=subtitle&populate[benefits][populate][benefitItems][populate][icon][fields][0]=url&populate[testimonials][fields][0]=title&populate[testimonials][populate][testimonial][fields][0]=name&populate[testimonials][populate][testimonial][fields][1]=statement&populate[testimonials][populate][testimonial][fields][2]=backgroundColor&populate[testimonials][populate][testimonial][populate][thumbnail][fields][0]=url&populate[team][fields][0]=title&populate[team][fields][1]=subtitle&populate[team][fields][2]=termTitle&populate[team][populate][images][fields][0]=url&populate[job][fields][0]=title&populate[job][populate][jobItems][fields][0]=postedDate&populate[job][populate][jobItems][fields][1]=jobTitle&populate[job][populate][jobItems][fields][2]=department&populate[job][populate][jobItems][fields][3]=type&populate[job][populate][jobItems][fields][4]=location&populate[job][populate][jobItems][fields][5]=link`,
    {
      headers,
    }
  );
  const data = await res.json();
  return data;
};

export default async function CareersPage() {
  const data = await getCareers();
  return <CareersNew {...data.data.attributes} />;
}
