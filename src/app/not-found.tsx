import Link from "next/link";
import Container from "@/components/globals/Container";
import Image from "next/image";

export default function NotFound() {
  return (
    <div>
      <Container>
        <div className="py-20 md:pb-32 pt-20">
          <div className="md:w-[450px] md:h-[450px] w-64 h-64 mx-auto relative">
            <Image
              src="https://cdn.oasr.in/oa-site/cms-uploads/media/Find_The_Way_3_Streamline_Milano_12e31af2e8.svg"
              alt=""
              fill={true}
              style={{ objectFit: "cover" }}
            />
          </div>
          <h3 className="mt-10 text-center md:text-2xl text-xl text-ntrl-black">
            Oops! This page took a detour—let’s get you{" "}
            <span className="underline text-primary-1 cursor-pointer">
              <Link href="/">back</Link>
            </span>{" "}
            on track!
          </h3>
        </div>
      </Container>
    </div>
  );
}
