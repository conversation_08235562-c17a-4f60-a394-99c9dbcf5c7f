// import { create } from "zustand";

// interface NavbarState {
//   navbarHeight: string;
//   isNavbarVisible: boolean;
//   updateHeight: (a: string) => void;
//   updateVisibility: (a: boolean) => void;
// }

// const useNavbarStore = create<NavbarState>((set) => ({
//   navbarHeight: "0px",
//   isNavbarVisible: true,
//   updateHeight: (height) =>
//     set(() => ({
//       navbarHeight: height,
//     })),
//   updateVisibility: (visible) =>
//     set(() => ({
//       isNavbarVisible: visible,
//     })),
// }));

// export default useNavbarStore;

import { create } from "zustand";

interface NavbarState {
  navbarHeight: string;
  isNavbarVisible: boolean;
  updateHeight: (a: string) => void;
  updateVisibility: (a: boolean) => void;
}

const useNavbarStore = create<NavbarState>((set) => ({
  navbarHeight: "84px", // Set this to match your navbar height
  isNavbarVisible: true,
  updateHeight: (height) =>
    set(() => ({
      navbarHeight: height,
    })),
  updateVisibility: (visible) =>
    set((state) => ({
      isNavbarVisible: visible,
    })),
}));

export default useNavbarStore;
