import axios, { AxiosRequestConfig, InternalAxiosRequestConfig } from "axios";

interface RequestConfig extends AxiosRequestConfig {
  requireAuth?: boolean;
}

interface InternalRequestConfig extends InternalAxiosRequestConfig {
  requireAuth?: boolean;
}

axios.interceptors.request.use(
  (config: InternalRequestConfig) => {
    if (!config.hasOwnProperty("requireAuth")) {
      const params = new URLSearchParams(window.location.search);
      const token =
        params.get("token") ||
        params.get("form_token") ||
        localStorage.getItem("token");
      if (token) {
        config.headers["token"] = token;
      }
      // return config
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

const getHeaders = ({
  attachToken,
}: {
  attachToken: boolean;
  hostUrl?: string;
}) => {
  let headers: any = {
    "Content-Type": "application/json",
  };

  if (attachToken) {
    const params = new URLSearchParams(window.location.search);
    const token =
      params.get("token") ||
      params.get("form_token") ||
      localStorage.getItem("token");
    if (token) {
      headers["token"] = token;
    }
  }

  return headers;
};

export const getRequest = async <TResponse = any>(
  url: string,
  config?: Config & RequestConfig
) => {
  const { attachToken = true, ...rest } = config || {};

  return await axios.get<TResponse>(url, {
    ...rest,
  });
};

export const postRequest = async <TRequest = any, TResponse = any>(
  url: string,
  body: TRequest,
  config?: Config & RequestConfig
) => {
  return await axios.post<TResponse>(url, body, config);
};

export const putRequest = async <TRequest = any, TResponse = any>(
  url: string,
  body: TRequest,
  config?: Config & RequestConfig
) => {
  const { attachToken = true, ...rest } = config || {};

  return await axios.put<TResponse>(url, body, {
    ...rest,
  });
};

interface Config {
  attachToken?: boolean;
  //  hostUrl?:string
}

export const formPostRequest = async <TRequest = any, TResponse = any>(
  url: string,
  body: TRequest,
  config?: Config
) => {
  const { attachToken = true } = config || {};

  return await axios.post<TResponse>(url, body, {
    withCredentials: true,
    headers: {
      token: localStorage.getItem("token"),
    },
  });
};

export const fetchGET = async <TResponse = any>(
  url: string,
  config?: Config & AxiosRequestConfig
) => {
  const { attachToken = true, ...rest } = config || {};

  const response = await fetch(url, {
    method: "GET",
    cache: "no-store",
    headers: getHeaders({ attachToken: attachToken }),
  });

  return response.json();
};

export const fetchPOST = async <TRequest = any, TResponse = any>(
  url: string,
  body: TRequest,
  config?: Config & AxiosRequestConfig
) => {
  const { attachToken = true, ...rest } = config || {};

  const response = await fetch(url, {
    method: "POST",
    cache: "no-store",
    headers: getHeaders({ attachToken: attachToken }),
    body: JSON.stringify(body),
  });

  return response.json();
};
