import { fetchStudioCmsData } from "@/app/api/studio-cms/apis/fetchDataCMS";

export const fetchAllInsurers = async () => {
  try {
    const query = `
    query MyQuery {
      health_insurers(where:{temp_slug:{_is_null: false}}) {
        id
        name
        logo_url
        temp_slug
      }
    }
    `;
    const operationName = "MyQuery";
    const variables = {};
    const response = await fetchStudioCmsData(query, operationName, variables);
    const insurers = response.payload.data.health_insurers;

    const transformedData = insurers.map((insurer: any) => ({
      company_name: insurer.name,
      logo_url: insurer.logo_url,
      company_slug: insurer.temp_slug,
    }));

    return transformedData;
  } catch (error) {
    console.error("Error fetching insurers:", error);
    return [];
  }
};
