import parse, { <PERSON><PERSON>N<PERSON>, domToReact, Element } from "html-react-parser";
import Link from "next/link";
import { ReactElement, ComponentType } from "react";
import { BodyLarge, HeadingMedium, HeadingSmall, HeadingXLarge } from "@/components/UI/Typography";

type ComponentMap = {
  p?: ComponentType<any>;
  h1?: ComponentType<any>;
  h2?: ComponentType<any>;
  h3?: ComponentType<any>;
  h4?: ComponentType<any>;
  h5?: ComponentType<any>;
  h6?: ComponentType<any>;
  span?: ComponentType<any>;
  a?: ComponentType<any>;
  strong?: ComponentType<any>;
  em?: ComponentType<any>;
  ul?: ComponentType<any>;
  ol?: ComponentType<any>;
  li?: ComponentType<any>;
  img?: ComponentType<any>;
  iframe?: ComponentType<any>;
  table?: ComponentType<any>;
  tr?: ComponentType<any>;
  td?: ComponentType<any>;
  th?: ComponentType<any>;
  // Fallback components for groups of elements
  heading?: ComponentType<any>;
  body?: ComponentType<any>;
};

type ClassMap = {
  p?: string;
  h1?: string;
  h2?: string;
  h3?: string;
  h4?: string;
  h5?: string;
  h6?: string;
  span?: string;
  a?: string;
  strong?: string;
  ul?: string;
  ol?: string;
  li?: string;
  img?: string;
  iframe?: string;
  table?: string;
  tr?: string;
  td?: string;
  th?: string;
  em?: string;
  heading?: string;
  body?: string;
  headingSpan?: string;
  bodySpan?: string;
};

export type HtmlParserOptions = {
  components?: ComponentMap;
  className?: string;
  classNames?: ClassMap;
  stripStyles?: boolean;
  imageProps?: {
    fill?: boolean;
    style?: React.CSSProperties;
    className?: string;
  };
};

export const htmlParser = (
  html: string,
  options: HtmlParserOptions
): ReactElement => {
  // Helper function to check if the input is plain text (no HTML tags)
  const isPlainText = (text: string): boolean => {
    // Remove whitespace and check if there are any HTML tags
    const trimmed = text.trim();
    // Simple regex to detect HTML tags
    const htmlTagRegex = /<[^>]*>/;
    return !htmlTagRegex.test(trimmed) && trimmed.length > 0;
  };

  // If the input is plain text, wrap it in a <p> tag
  let processedHtml = html;
  if (isPlainText(html)) {
    processedHtml = `<p>${html}</p>`;
  }

  // Default components when none are provided
  const defaultComponents: ComponentMap = {
    p: BodyLarge,
    span: BodyLarge,
    li: BodyLarge,
    h1: HeadingXLarge,
    h2: HeadingXLarge,
    h3: HeadingMedium,
    h4: HeadingMedium,
    h5: HeadingSmall,
    h6: HeadingSmall,
  };

  const {
    components = defaultComponents,
    className = "",
    classNames = {},
    stripStyles = true,
    imageProps,
  } = options;

  // Helper function to get the appropriate component with fallback hierarchy
  const getComponent = (tagName: string): ComponentType<any> | null => {
    // First, check for specific tag component (h1, h2, p, span, etc.)
    const specificComponent = components[tagName as keyof ComponentMap];
    if (specificComponent) {
      return specificComponent;
    }

    // Then check for group fallbacks
    const headingTags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
    const bodyTags = ['p', 'span'];

    if (headingTags.includes(tagName) && components.heading) {
      return components.heading;
    }

    if (bodyTags.includes(tagName) && components.body) {
      return components.body;
    }

    // Finally, fall back to default components
    const defaultComponent = defaultComponents[tagName as keyof ComponentMap];
    if (defaultComponent) {
      return defaultComponent;
    }

    return null;
  };

  const htmlRenderer = (domNode: DOMNode) => {
    if (domNode.type === "tag" && domNode.attribs) {
      let attrs = domNode.attribs;

      if (stripStyles && attrs.style) {
        attrs.style = "";
      }

      const Component = getComponent(domNode.name);
      const tagClassName =
        classNames[domNode.name as keyof ClassMap] || className;
      const headingClassName = classNames.heading;
      const bodyClassName = classNames.body;
      const headingSpanClassName = classNames.headingSpan;
      const bodySpanClassName = classNames.bodySpan;

      if (Component) {
        // Special handling for images
        if (domNode.name === "img" && attrs.src) {
          return (
            <Component
              src={attrs.src}
              alt={attrs.alt || "image"}
              fill={imageProps?.fill || true}
              style={imageProps?.style || { objectFit: "contain" }}
              className={imageProps?.className || tagClassName}
            />
          );
        }

        // Helper function to determine the appropriate className with fallback hierarchy
        const getClassName = (tagName: string): string => {
          const headingTags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
          const bodyTags = ['p', 'span'];

          // Check for span-specific classes first
          if (tagName === "p" && bodySpanClassName) {
            return bodySpanClassName;
          }
          if (headingTags.includes(tagName) && headingSpanClassName) {
            return headingSpanClassName;
          }

          // Then check for group classes
          if (headingTags.includes(tagName) && headingClassName) {
            return headingClassName;
          }
          if (bodyTags.includes(tagName) && bodyClassName) {
            return bodyClassName;
          }

          // Finally fall back to tag-specific or default className
          return tagClassName;
        };

        // Handle p with span extraction - only if span is the ONLY child
        if (domNode.name === "p" && domNode.children?.length === 1) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            return (
              <Component
                as="p"
                className={getClassName("p")}
              >
                {domToReact(spanChild.children as DOMNode[], { replace: htmlRenderer })}
              </Component>
            );
          }
        }

        // Handle headings with span extraction - only if span is the ONLY child
        const headingTags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
        if (headingTags.includes(domNode.name) && domNode.children?.length === 1) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            return (
              <Component
                as={domNode.name}
                className={getClassName(domNode.name)}
              >
                {domToReact(spanChild.children as DOMNode[], { replace: htmlRenderer })}
              </Component>
            );
          }
        }

        // Handle links
        if (domNode.name === "a" && attrs.href) {
          return (
            <Link href={attrs.href} className={getClassName(domNode.name)}>
              {domToReact(domNode.children as DOMNode[], { replace: htmlRenderer })}
            </Link>
          );
        }

        // Default handling for other elements
        return (
          <Component
            as={domNode.name}
            className={getClassName(domNode.name)}
          >
            {domToReact(domNode.children as DOMNode[], { replace: htmlRenderer })}
          </Component>
        );
      }
    }
    return domNode;
  };

  return <>{parse(processedHtml, { replace: htmlRenderer })}</>;
};
