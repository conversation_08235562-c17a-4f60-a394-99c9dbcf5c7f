export const formatPlanVariantName = (
  planName: string,
  variantName: string
) => {
  // Normalize strings for comparison
  const normalizedPlan = planName?.trim().toLowerCase() || "";
  const normalizedVariant = variantName?.trim().toLowerCase() || "";

  // If plan and variant names are the same, return only variant name
  if (normalizedPlan === normalizedVariant) {
    return variantName?.trim() || "";
  }

  // If different, return plan + variant name
  const cleanPlan = planName?.trim() || "";
  const cleanVariant = variantName?.trim() || "";

  if (cleanPlan && cleanVariant) {
    return `${cleanPlan} ${cleanVariant}`;
  }

  return cleanPlan || cleanVariant || "";
};
