import {
  BodyLarge,
  HeadingMedium,
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import { domToReact, DOMNode, Element } from "html-react-parser";
import Image from "next/image";

export const htmlRenderer = (domNode: DOMNode, index: number) => {
  if (domNode.type === "tag" && domNode.attribs) {
    let attrs = domNode.attribs;
    if (attrs.style) {
      attrs.style = "";
    }

    switch (domNode.name) {
      case "h1":
      case "h2":
        // Check if h2 contains a span and extract its content
        if (domNode.children && domNode.children.length > 0) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            // Create a new h2 element with the span's content
            return (
              <HeadingXLarge as="h2">
                {domToReact(spanChild.children as DOMNode[])}
              </HeadingXLarge>
            );
          }
        }
        return (
          <HeadingXLarge as="h2">
            {domToReact(domNode.children as DOMNode[])}
          </HeadingXLarge>
        );

      case "h3":
        return (
          <HeadingMedium as="h3">
            {domToReact(domNode.children as DOMNode[])}
          </HeadingMedium>
        );

      case "h4":
        return (
          <HeadingSmall as="h4">
            {domToReact(domNode.children as DOMNode[])}
          </HeadingSmall>
        );

      case "h5":
      case "h6":
        return (
          <HeadingSmall as="h5">
            {domToReact(domNode.children as DOMNode[])}
          </HeadingSmall>
        );

      case "p":
        // Check if p contains a span and extract its content
        if (domNode.children && domNode.children.length > 0) {
          const spanChild = domNode.children.find(
            (child): child is Element =>
              "name" in child && child.name === "span"
          );

          if (spanChild) {
            // Create a new p element with the span's content
            return (
              <BodyLarge as="p">
                {domToReact(spanChild.children as DOMNode[])}
              </BodyLarge>
            );
          }
        }
        return (
          <BodyLarge as="p">
            {domToReact(domNode.children as DOMNode[])}
          </BodyLarge>
        );

      case "span":
        break;

      case "u":
      case "a":
        break;

      case "ul":
      case "li":
        return (
          <BodyLarge as="li">
            {domToReact(domNode.children as DOMNode[])}
          </BodyLarge>
        );

      case "tr":
      case "td":
      case "th":
        break;

      case "figure":
        break;

      case "img":
        return (
          <div>
            <Image
              src={attrs.src}
              fill={true}
              style={{ objectFit: "contain" }}
              alt="alt"
            />
          </div>
        );

      case "iframe":
        break;
    }
    return domNode;
  }
};
