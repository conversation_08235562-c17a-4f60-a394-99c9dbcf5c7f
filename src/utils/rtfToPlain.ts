import parse from "html-react-parser";
export const convertToPlain = (html: string) => {
  // const nodes = parse(html, {
  //   replace: (node) => {
  //     if (node.type === "text") {
  //       return node.data;
  //     }
  //     return null;
  //   },
  // });
  // return Array.isArray(nodes) ? nodes.join("") : (nodes as string);
  return html
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
    .replace(/&amp;/g, '&')  // Replace &amp; with &
    .replace(/&lt;/g, '<')   // Replace &lt; with 
    .replace(/&gt;/g, '>')   // Replace &gt; with >
    .replace(/&quot;/g, '"') // Replace &quot; with "
    .trim();
};
