/**
 * Format a number as Indian currency (₹)
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    maximumFractionDigits: 0,
  }).format(amount);
};


export function formatProductName(
  productName: string,
  variantName: string,
  isTerm: boolean = false
) {
  if (isTerm) {
      // Remove repeated phrases in term insurance product names
      const cleanedName = `${productName} ${variantName}`
          .replace(/\s+/g, ' ')
          .trim();

      const words = cleanedName.split(' ');
      const uniqueWords = words.filter((word, index) => {
          const remainingPhrase = words
              .slice(index + 1)
              .join(' ')
              .toLowerCase();
          return !remainingPhrase.includes(word.toLowerCase());
      });
      return uniqueWords.join(' ');
  }
  if (
      variantName.toLowerCase() === productName.toLowerCase() ||
      variantName.toLowerCase().includes(productName.toLowerCase())
  ) {
      return variantName;
  } else {
      return `${productName} ${variantName}`;
  }
}