// next-sitemap.config.js

/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: `${process.env.NEXT_BASE_URL}`,
  // generateRobotsTxt: true,
  exclude: [
    "/health-insurance/*",
    "/term-insurance/*",
    "/insurance/*",
    "/robots.txt",
    "/icon.svg",
    "/health-insurance-guide",
    "/pages/*",
    "/compare-health-insurance-plans/*",
  ], // <= exclude here
  robotsTxtOptions: {
    additionalSitemaps: [
      `${process.env.NEXT_BASE_URL}/health-insurance/sitemap.xml`, // <==== Add here
      `${process.env.NEXT_BASE_URL}/term-insurance/sitemap.xml`, // <==== Add here
      `${process.env.NEXT_BASE_URL}/insurance/sitemap.xml`, // <==== Add here
      // `${process.env.NEXT_BASE_URL}/compare-health-insurance-plans/sitemap.xml`, // <==== Add here
    ],
  },
  sourceDir: "build",
};
