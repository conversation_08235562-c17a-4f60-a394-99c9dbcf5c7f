apiVersion: apps/v1
kind: Deployment
metadata:
  name: oa-website-deployment-{{env_name}}
  namespace: oa-{{env_name}}
  # labels:
  #   k8s-app: oa-{{env_name}}-app
spec:
  replicas: { { replica_count } }
  revisionHistoryLimit: 1
  progressDeadlineSeconds: 420
  selector:
    matchLabels:
      app-name: oa-website-pod-{{env_name}}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app-name: oa-website-pod-{{env_name}}
    spec:
      containers:
        - name: oa-website-{{env_name}}
          image: "asia-south1-docker.pkg.dev/oneassure-prod/oneassure/oa-website-{{env_name}}:{{image_id}}"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 3000
          readinessProbe:
            tcpSocket:
              port: 3000
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 60
            initialDelaySeconds: 30
          livenessProbe:
            tcpSocket:
              port: 3000
            periodSeconds: 30
            timeoutSeconds: 5
            failureThreshold: 60
            initialDelaySeconds: 30
          resources:
            limits:
              cpu: "500m"
              memory: "1Gi"
            requests:
              cpu: "50m"
              memory: "256Mi"
          # volumeMounts:
          #   - mountPath: "/app/.env.local"
          #     subPath: .env.local
          #     name: oa-{{env_name}}-app-secret
          #     readOnly: true
      # volumes:
      #   - name: oa-{{env_name}}-app-secret
      #     secret:
      #       secretName: oa-{{env_name}}-app-secret
      #       items:
      #         - key: .env.local
      #           path: .env.local
      # nodeSelector:
      #   type: "{{node_selector}}"
---
apiVersion: v1
kind: Service
metadata:
  name: oa-website-service-{{env_name}}
  namespace: oa-{{env_name}}
  # labels:
  #   k8s-svc: oa-{{env_name}}-website-service
spec:
  ports:
    - port: 80
      name: http
      protocol: TCP
      targetPort: http
  selector:
    app-name: oa-website-pod-{{env_name}}
  type: ClusterIP
---

apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: oa-website-httproute-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  parentRefs:
    - kind: Gateway
      name: gke-gateway-pub-prod
      sectionName: websecure-wildcard
      namespace: entrypoint-pub
  hostnames:
    - {{mapping_hostname}}
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: oa-website-service-{{env_name}}
          port: 80
---
apiVersion: networking.gke.io/v1
kind: HealthCheckPolicy
metadata:
  name: oa-website-healthcheck-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  default:
    checkIntervalSec: 15
    timeoutSec: 10
    healthyThreshold: 2
    unhealthyThreshold: 5
    logConfig:
      enabled: true
    config:
      type: HTTP
      httpHealthCheck:
        portSpecification: USE_FIXED_PORT
        port: 3000
        requestPath: /
  targetRef:
    group: ""
    kind: Service
    name: oa-website-service-{{env_name}}